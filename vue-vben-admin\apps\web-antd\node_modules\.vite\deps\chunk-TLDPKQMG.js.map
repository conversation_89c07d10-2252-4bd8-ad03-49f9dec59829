{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/checkbox/style/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Keyframes } from '../../_util/cssinjs';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport { genFocusOutline, resetComponent } from '../../style';\n// ============================== Motion ==============================\nconst antCheckboxEffect = new Keyframes('antCheckboxEffect', {\n  '0%': {\n    transform: 'scale(1)',\n    opacity: 0.5\n  },\n  '100%': {\n    transform: 'scale(1.6)',\n    opacity: 0\n  }\n});\n// ============================== Styles ==============================\nexport const genCheckboxStyle = token => {\n  const {\n    checkboxCls\n  } = token;\n  const wrapperCls = `${checkboxCls}-wrapper`;\n  return [\n  // ===================== Basic =====================\n  {\n    // Group\n    [`${checkboxCls}-group`]: _extends(_extends({}, resetComponent(token)), {\n      display: 'inline-flex',\n      flexWrap: 'wrap',\n      columnGap: token.marginXS,\n      // Group > Grid\n      [`> ${token.antCls}-row`]: {\n        flex: 1\n      }\n    }),\n    // Wrapper\n    [wrapperCls]: _extends(_extends({}, resetComponent(token)), {\n      display: 'inline-flex',\n      alignItems: 'baseline',\n      cursor: 'pointer',\n      // Fix checkbox & radio in flex align #30260\n      '&:after': {\n        display: 'inline-block',\n        width: 0,\n        overflow: 'hidden',\n        content: \"'\\\\a0'\"\n      },\n      // Checkbox near checkbox\n      [`& + ${wrapperCls}`]: {\n        marginInlineStart: 0\n      },\n      [`&${wrapperCls}-in-form-item`]: {\n        'input[type=\"checkbox\"]': {\n          width: 14,\n          height: 14 // FIXME: magic\n        }\n      }\n    }),\n    // Wrapper > Checkbox\n    [checkboxCls]: _extends(_extends({}, resetComponent(token)), {\n      position: 'relative',\n      whiteSpace: 'nowrap',\n      lineHeight: 1,\n      cursor: 'pointer',\n      // To make alignment right when `controlHeight` is changed\n      // Ref: https://github.com/ant-design/ant-design/issues/41564\n      alignSelf: 'center',\n      // Wrapper > Checkbox > input\n      [`${checkboxCls}-input`]: {\n        position: 'absolute',\n        // Since baseline align will get additional space offset,\n        // we need to move input to top to make it align with text.\n        // Ref: https://github.com/ant-design/ant-design/issues/38926#issuecomment-1486137799\n        inset: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        opacity: 0,\n        margin: 0,\n        [`&:focus-visible + ${checkboxCls}-inner`]: _extends({}, genFocusOutline(token))\n      },\n      // Wrapper > Checkbox > inner\n      [`${checkboxCls}-inner`]: {\n        boxSizing: 'border-box',\n        position: 'relative',\n        top: 0,\n        insetInlineStart: 0,\n        display: 'block',\n        width: token.checkboxSize,\n        height: token.checkboxSize,\n        direction: 'ltr',\n        backgroundColor: token.colorBgContainer,\n        border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,\n        borderRadius: token.borderRadiusSM,\n        borderCollapse: 'separate',\n        transition: `all ${token.motionDurationSlow}`,\n        '&:after': {\n          boxSizing: 'border-box',\n          position: 'absolute',\n          top: '50%',\n          insetInlineStart: '21.5%',\n          display: 'table',\n          width: token.checkboxSize / 14 * 5,\n          height: token.checkboxSize / 14 * 8,\n          border: `${token.lineWidthBold}px solid ${token.colorWhite}`,\n          borderTop: 0,\n          borderInlineStart: 0,\n          transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',\n          opacity: 0,\n          content: '\"\"',\n          transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`\n        }\n      },\n      // Wrapper > Checkbox + Text\n      '& + span': {\n        paddingInlineStart: token.paddingXS,\n        paddingInlineEnd: token.paddingXS\n      }\n    })\n  },\n  // ================= Indeterminate =================\n  {\n    [checkboxCls]: {\n      '&-indeterminate': {\n        // Wrapper > Checkbox > inner\n        [`${checkboxCls}-inner`]: {\n          '&:after': {\n            top: '50%',\n            insetInlineStart: '50%',\n            width: token.fontSizeLG / 2,\n            height: token.fontSizeLG / 2,\n            backgroundColor: token.colorPrimary,\n            border: 0,\n            transform: 'translate(-50%, -50%) scale(1)',\n            opacity: 1,\n            content: '\"\"'\n          }\n        }\n      }\n    }\n  },\n  // ===================== Hover =====================\n  {\n    // Wrapper\n    [`${wrapperCls}:hover ${checkboxCls}:after`]: {\n      visibility: 'visible'\n    },\n    // Wrapper & Wrapper > Checkbox\n    [`\n        ${wrapperCls}:not(${wrapperCls}-disabled),\n        ${checkboxCls}:not(${checkboxCls}-disabled)\n      `]: {\n      [`&:hover ${checkboxCls}-inner`]: {\n        borderColor: token.colorPrimary\n      }\n    },\n    [`${wrapperCls}:not(${wrapperCls}-disabled)`]: {\n      [`&:hover ${checkboxCls}-checked:not(${checkboxCls}-disabled) ${checkboxCls}-inner`]: {\n        backgroundColor: token.colorPrimaryHover,\n        borderColor: 'transparent'\n      },\n      [`&:hover ${checkboxCls}-checked:not(${checkboxCls}-disabled):after`]: {\n        borderColor: token.colorPrimaryHover\n      }\n    }\n  },\n  // ==================== Checked ====================\n  {\n    // Wrapper > Checkbox\n    [`${checkboxCls}-checked`]: {\n      [`${checkboxCls}-inner`]: {\n        backgroundColor: token.colorPrimary,\n        borderColor: token.colorPrimary,\n        '&:after': {\n          opacity: 1,\n          transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',\n          transition: `all ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`\n        }\n      },\n      // Checked Effect\n      '&:after': {\n        position: 'absolute',\n        top: 0,\n        insetInlineStart: 0,\n        width: '100%',\n        height: '100%',\n        borderRadius: token.borderRadiusSM,\n        visibility: 'hidden',\n        border: `${token.lineWidthBold}px solid ${token.colorPrimary}`,\n        animationName: antCheckboxEffect,\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: 'ease-in-out',\n        animationFillMode: 'backwards',\n        content: '\"\"',\n        transition: `all ${token.motionDurationSlow}`\n      }\n    },\n    [`\n        ${wrapperCls}-checked:not(${wrapperCls}-disabled),\n        ${checkboxCls}-checked:not(${checkboxCls}-disabled)\n      `]: {\n      [`&:hover ${checkboxCls}-inner`]: {\n        backgroundColor: token.colorPrimaryHover,\n        borderColor: 'transparent'\n      },\n      [`&:hover ${checkboxCls}:after`]: {\n        borderColor: token.colorPrimaryHover\n      }\n    }\n  },\n  // ==================== Disable ====================\n  {\n    // Wrapper\n    [`${wrapperCls}-disabled`]: {\n      cursor: 'not-allowed'\n    },\n    // Wrapper > Checkbox\n    [`${checkboxCls}-disabled`]: {\n      // Wrapper > Checkbox > input\n      [`&, ${checkboxCls}-input`]: {\n        cursor: 'not-allowed',\n        // Disabled for native input to enable Tooltip event handler\n        // ref: https://github.com/ant-design/ant-design/issues/39822#issuecomment-1365075901\n        pointerEvents: 'none'\n      },\n      // Wrapper > Checkbox > inner\n      [`${checkboxCls}-inner`]: {\n        background: token.colorBgContainerDisabled,\n        borderColor: token.colorBorder,\n        '&:after': {\n          borderColor: token.colorTextDisabled\n        }\n      },\n      '&:after': {\n        display: 'none'\n      },\n      '& + span': {\n        color: token.colorTextDisabled\n      },\n      [`&${checkboxCls}-indeterminate ${checkboxCls}-inner::after`]: {\n        background: token.colorTextDisabled\n      }\n    }\n  }];\n};\n// ============================== Export ==============================\nexport function getStyle(prefixCls, token) {\n  const checkboxToken = mergeToken(token, {\n    checkboxCls: `.${prefixCls}`,\n    checkboxSize: token.controlInteractiveSize\n  });\n  return [genCheckboxStyle(checkboxToken)];\n}\nexport default genComponentStyleHook('Checkbox', (token, _ref) => {\n  let {\n    prefixCls\n  } = _ref;\n  return [getStyle(prefixCls, token)];\n});"], "mappings": ";;;;;;;;;;;;AAKA,IAAM,oBAAoB,IAAI,kBAAU,qBAAqB;AAAA,EAC3D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AACF,CAAC;AAEM,IAAM,mBAAmB,WAAS;AACvC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,GAAG,WAAW;AACjC,SAAO;AAAA;AAAA,IAEP;AAAA;AAAA,MAEE,CAAC,GAAG,WAAW,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,QACtE,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,MAAM;AAAA;AAAA,QAEjB,CAAC,KAAK,MAAM,MAAM,MAAM,GAAG;AAAA,UACzB,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA;AAAA,MAED,CAAC,UAAU,GAAG,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,QAC1D,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA;AAAA,QAER,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU;AAAA,UACV,SAAS;AAAA,QACX;AAAA;AAAA,QAEA,CAAC,OAAO,UAAU,EAAE,GAAG;AAAA,UACrB,mBAAmB;AAAA,QACrB;AAAA,QACA,CAAC,IAAI,UAAU,eAAe,GAAG;AAAA,UAC/B,0BAA0B;AAAA,YACxB,OAAO;AAAA,YACP,QAAQ;AAAA;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AAAA;AAAA,MAED,CAAC,WAAW,GAAG,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,QAC3D,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA;AAAA;AAAA,QAGR,WAAW;AAAA;AAAA,QAEX,CAAC,GAAG,WAAW,QAAQ,GAAG;AAAA,UACxB,UAAU;AAAA;AAAA;AAAA;AAAA,UAIV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,CAAC,qBAAqB,WAAW,QAAQ,GAAG,SAAS,CAAC,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACjF;AAAA;AAAA,QAEA,CAAC,GAAG,WAAW,QAAQ,GAAG;AAAA,UACxB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,KAAK;AAAA,UACL,kBAAkB;AAAA,UAClB,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,UACb,QAAQ,MAAM;AAAA,UACd,WAAW;AAAA,UACX,iBAAiB,MAAM;AAAA,UACvB,QAAQ,GAAG,MAAM,SAAS,MAAM,MAAM,QAAQ,IAAI,MAAM,WAAW;AAAA,UACnE,cAAc,MAAM;AAAA,UACpB,gBAAgB;AAAA,UAChB,YAAY,OAAO,MAAM,kBAAkB;AAAA,UAC3C,WAAW;AAAA,YACT,WAAW;AAAA,YACX,UAAU;AAAA,YACV,KAAK;AAAA,YACL,kBAAkB;AAAA,YAClB,SAAS;AAAA,YACT,OAAO,MAAM,eAAe,KAAK;AAAA,YACjC,QAAQ,MAAM,eAAe,KAAK;AAAA,YAClC,QAAQ,GAAG,MAAM,aAAa,YAAY,MAAM,UAAU;AAAA,YAC1D,WAAW;AAAA,YACX,mBAAmB;AAAA,YACnB,WAAW;AAAA,YACX,SAAS;AAAA,YACT,SAAS;AAAA,YACT,YAAY,OAAO,MAAM,kBAAkB,IAAI,MAAM,gBAAgB,aAAa,MAAM,kBAAkB;AAAA,UAC5G;AAAA,QACF;AAAA;AAAA,QAEA,YAAY;AAAA,UACV,oBAAoB,MAAM;AAAA,UAC1B,kBAAkB,MAAM;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,WAAW,GAAG;AAAA,QACb,mBAAmB;AAAA;AAAA,UAEjB,CAAC,GAAG,WAAW,QAAQ,GAAG;AAAA,YACxB,WAAW;AAAA,cACT,KAAK;AAAA,cACL,kBAAkB;AAAA,cAClB,OAAO,MAAM,aAAa;AAAA,cAC1B,QAAQ,MAAM,aAAa;AAAA,cAC3B,iBAAiB,MAAM;AAAA,cACvB,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA;AAAA;AAAA,MAEE,CAAC,GAAG,UAAU,UAAU,WAAW,QAAQ,GAAG;AAAA,QAC5C,YAAY;AAAA,MACd;AAAA;AAAA,MAEA,CAAC;AAAA,UACK,UAAU,QAAQ,UAAU;AAAA,UAC5B,WAAW,QAAQ,WAAW;AAAA,OACjC,GAAG;AAAA,QACJ,CAAC,WAAW,WAAW,QAAQ,GAAG;AAAA,UAChC,aAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,MACA,CAAC,GAAG,UAAU,QAAQ,UAAU,YAAY,GAAG;AAAA,QAC7C,CAAC,WAAW,WAAW,gBAAgB,WAAW,cAAc,WAAW,QAAQ,GAAG;AAAA,UACpF,iBAAiB,MAAM;AAAA,UACvB,aAAa;AAAA,QACf;AAAA,QACA,CAAC,WAAW,WAAW,gBAAgB,WAAW,kBAAkB,GAAG;AAAA,UACrE,aAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA;AAAA;AAAA,MAEE,CAAC,GAAG,WAAW,UAAU,GAAG;AAAA,QAC1B,CAAC,GAAG,WAAW,QAAQ,GAAG;AAAA,UACxB,iBAAiB,MAAM;AAAA,UACvB,aAAa,MAAM;AAAA,UACnB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,WAAW;AAAA,YACX,YAAY,OAAO,MAAM,iBAAiB,IAAI,MAAM,iBAAiB,IAAI,MAAM,kBAAkB;AAAA,UACnG;AAAA,QACF;AAAA;AAAA,QAEA,WAAW;AAAA,UACT,UAAU;AAAA,UACV,KAAK;AAAA,UACL,kBAAkB;AAAA,UAClB,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,cAAc,MAAM;AAAA,UACpB,YAAY;AAAA,UACZ,QAAQ,GAAG,MAAM,aAAa,YAAY,MAAM,YAAY;AAAA,UAC5D,eAAe;AAAA,UACf,mBAAmB,MAAM;AAAA,UACzB,yBAAyB;AAAA,UACzB,mBAAmB;AAAA,UACnB,SAAS;AAAA,UACT,YAAY,OAAO,MAAM,kBAAkB;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,CAAC;AAAA,UACK,UAAU,gBAAgB,UAAU;AAAA,UACpC,WAAW,gBAAgB,WAAW;AAAA,OACzC,GAAG;AAAA,QACJ,CAAC,WAAW,WAAW,QAAQ,GAAG;AAAA,UAChC,iBAAiB,MAAM;AAAA,UACvB,aAAa;AAAA,QACf;AAAA,QACA,CAAC,WAAW,WAAW,QAAQ,GAAG;AAAA,UAChC,aAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA;AAAA;AAAA,MAEE,CAAC,GAAG,UAAU,WAAW,GAAG;AAAA,QAC1B,QAAQ;AAAA,MACV;AAAA;AAAA,MAEA,CAAC,GAAG,WAAW,WAAW,GAAG;AAAA;AAAA,QAE3B,CAAC,MAAM,WAAW,QAAQ,GAAG;AAAA,UAC3B,QAAQ;AAAA;AAAA;AAAA,UAGR,eAAe;AAAA,QACjB;AAAA;AAAA,QAEA,CAAC,GAAG,WAAW,QAAQ,GAAG;AAAA,UACxB,YAAY,MAAM;AAAA,UAClB,aAAa,MAAM;AAAA,UACnB,WAAW;AAAA,YACT,aAAa,MAAM;AAAA,UACrB;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,QACX;AAAA,QACA,YAAY;AAAA,UACV,OAAO,MAAM;AAAA,QACf;AAAA,QACA,CAAC,IAAI,WAAW,kBAAkB,WAAW,eAAe,GAAG;AAAA,UAC7D,YAAY,MAAM;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EAAC;AACH;AAEO,SAAS,SAAS,WAAW,OAAO;AACzC,QAAM,gBAAgB,MAAW,OAAO;AAAA,IACtC,aAAa,IAAI,SAAS;AAAA,IAC1B,cAAc,MAAM;AAAA,EACtB,CAAC;AACD,SAAO,CAAC,iBAAiB,aAAa,CAAC;AACzC;AACA,IAAO,gBAAQ,sBAAsB,YAAY,CAAC,OAAO,SAAS;AAChE,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,CAAC,SAAS,WAAW,KAAK,CAAC;AACpC,CAAC;", "names": []}