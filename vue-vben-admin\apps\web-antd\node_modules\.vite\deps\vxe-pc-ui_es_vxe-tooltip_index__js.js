import {
  tooltip_default
} from "./chunk-KQZ2MQHY.js";
import "./chunk-H6EAC26D.js";
import "./chunk-A3JYOOKK.js";
import "./chunk-EYZRMTBP.js";
import {
  dynamicApp
} from "./chunk-BUOIGA6Q.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-MSIZQRL4.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

// ../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-tooltip/index.js
var vxe_tooltip_default = tooltip_default2;
export {
  Tooltip,
  VxeTooltip,
  vxe_tooltip_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-tooltip_index__js.js.map
