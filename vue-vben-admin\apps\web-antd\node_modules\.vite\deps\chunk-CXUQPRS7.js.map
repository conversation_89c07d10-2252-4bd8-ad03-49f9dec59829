{"version": 3, "sources": ["../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/zrender.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/mixin/Draggable.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/core/event.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/core/GestureMgr.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/Handler.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/core/timsort.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/Storage.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/animation/requestAnimationFrame.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/animation/Animation.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/dom/HandlerProxy.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/canvas/helper.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/canvas/dashStyle.js", "../../../../../node_modules/.pnpm/zrender@5.6.1/node_modules/zrender/lib/canvas/graphic.js"], "sourcesContent": ["/*!\n* ZRender, a high performance 2d drawing library.\n*\n* Copyright (c) 2013, Baidu Inc.\n* All rights reserved.\n*\n* LICENSE\n* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt\n*/\nimport env from './core/env.js';\nimport * as zrUtil from './core/util.js';\nimport Handler from './Handler.js';\nimport Storage from './Storage.js';\nimport Animation, { getTime } from './animation/Animation.js';\nimport HandlerProxy from './dom/HandlerProxy.js';\nimport { lum } from './tool/color.js';\nimport { DARK_MODE_THRESHOLD } from './config.js';\nimport Group from './graphic/Group.js';\nvar painterCtors = {};\nvar instances = {};\nfunction delInstance(id) {\n    delete instances[id];\n}\nfunction isDarkMode(backgroundColor) {\n    if (!backgroundColor) {\n        return false;\n    }\n    if (typeof backgroundColor === 'string') {\n        return lum(backgroundColor, 1) < DARK_MODE_THRESHOLD;\n    }\n    else if (backgroundColor.colorStops) {\n        var colorStops = backgroundColor.colorStops;\n        var totalLum = 0;\n        var len = colorStops.length;\n        for (var i = 0; i < len; i++) {\n            totalLum += lum(colorStops[i].color, 1);\n        }\n        totalLum /= len;\n        return totalLum < DARK_MODE_THRESHOLD;\n    }\n    return false;\n}\nvar ZRender = (function () {\n    function ZRender(id, dom, opts) {\n        var _this = this;\n        this._sleepAfterStill = 10;\n        this._stillFrameAccum = 0;\n        this._needsRefresh = true;\n        this._needsRefreshHover = true;\n        this._darkMode = false;\n        opts = opts || {};\n        this.dom = dom;\n        this.id = id;\n        var storage = new Storage();\n        var rendererType = opts.renderer || 'canvas';\n        if (!painterCtors[rendererType]) {\n            rendererType = zrUtil.keys(painterCtors)[0];\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!painterCtors[rendererType]) {\n                throw new Error(\"Renderer '\" + rendererType + \"' is not imported. Please import it first.\");\n            }\n        }\n        opts.useDirtyRect = opts.useDirtyRect == null\n            ? false\n            : opts.useDirtyRect;\n        var painter = new painterCtors[rendererType](dom, storage, opts, id);\n        var ssrMode = opts.ssr || painter.ssrOnly;\n        this.storage = storage;\n        this.painter = painter;\n        var handlerProxy = (!env.node && !env.worker && !ssrMode)\n            ? new HandlerProxy(painter.getViewportRoot(), painter.root)\n            : null;\n        var useCoarsePointer = opts.useCoarsePointer;\n        var usePointerSize = (useCoarsePointer == null || useCoarsePointer === 'auto')\n            ? env.touchEventsSupported\n            : !!useCoarsePointer;\n        var defaultPointerSize = 44;\n        var pointerSize;\n        if (usePointerSize) {\n            pointerSize = zrUtil.retrieve2(opts.pointerSize, defaultPointerSize);\n        }\n        this.handler = new Handler(storage, painter, handlerProxy, painter.root, pointerSize);\n        this.animation = new Animation({\n            stage: {\n                update: ssrMode ? null : function () { return _this._flush(true); }\n            }\n        });\n        if (!ssrMode) {\n            this.animation.start();\n        }\n    }\n    ZRender.prototype.add = function (el) {\n        if (this._disposed || !el) {\n            return;\n        }\n        this.storage.addRoot(el);\n        el.addSelfToZr(this);\n        this.refresh();\n    };\n    ZRender.prototype.remove = function (el) {\n        if (this._disposed || !el) {\n            return;\n        }\n        this.storage.delRoot(el);\n        el.removeSelfFromZr(this);\n        this.refresh();\n    };\n    ZRender.prototype.configLayer = function (zLevel, config) {\n        if (this._disposed) {\n            return;\n        }\n        if (this.painter.configLayer) {\n            this.painter.configLayer(zLevel, config);\n        }\n        this.refresh();\n    };\n    ZRender.prototype.setBackgroundColor = function (backgroundColor) {\n        if (this._disposed) {\n            return;\n        }\n        if (this.painter.setBackgroundColor) {\n            this.painter.setBackgroundColor(backgroundColor);\n        }\n        this.refresh();\n        this._backgroundColor = backgroundColor;\n        this._darkMode = isDarkMode(backgroundColor);\n    };\n    ZRender.prototype.getBackgroundColor = function () {\n        return this._backgroundColor;\n    };\n    ZRender.prototype.setDarkMode = function (darkMode) {\n        this._darkMode = darkMode;\n    };\n    ZRender.prototype.isDarkMode = function () {\n        return this._darkMode;\n    };\n    ZRender.prototype.refreshImmediately = function (fromInside) {\n        if (this._disposed) {\n            return;\n        }\n        if (!fromInside) {\n            this.animation.update(true);\n        }\n        this._needsRefresh = false;\n        this.painter.refresh();\n        this._needsRefresh = false;\n    };\n    ZRender.prototype.refresh = function () {\n        if (this._disposed) {\n            return;\n        }\n        this._needsRefresh = true;\n        this.animation.start();\n    };\n    ZRender.prototype.flush = function () {\n        if (this._disposed) {\n            return;\n        }\n        this._flush(false);\n    };\n    ZRender.prototype._flush = function (fromInside) {\n        var triggerRendered;\n        var start = getTime();\n        if (this._needsRefresh) {\n            triggerRendered = true;\n            this.refreshImmediately(fromInside);\n        }\n        if (this._needsRefreshHover) {\n            triggerRendered = true;\n            this.refreshHoverImmediately();\n        }\n        var end = getTime();\n        if (triggerRendered) {\n            this._stillFrameAccum = 0;\n            this.trigger('rendered', {\n                elapsedTime: end - start\n            });\n        }\n        else if (this._sleepAfterStill > 0) {\n            this._stillFrameAccum++;\n            if (this._stillFrameAccum > this._sleepAfterStill) {\n                this.animation.stop();\n            }\n        }\n    };\n    ZRender.prototype.setSleepAfterStill = function (stillFramesCount) {\n        this._sleepAfterStill = stillFramesCount;\n    };\n    ZRender.prototype.wakeUp = function () {\n        if (this._disposed) {\n            return;\n        }\n        this.animation.start();\n        this._stillFrameAccum = 0;\n    };\n    ZRender.prototype.refreshHover = function () {\n        this._needsRefreshHover = true;\n    };\n    ZRender.prototype.refreshHoverImmediately = function () {\n        if (this._disposed) {\n            return;\n        }\n        this._needsRefreshHover = false;\n        if (this.painter.refreshHover && this.painter.getType() === 'canvas') {\n            this.painter.refreshHover();\n        }\n    };\n    ZRender.prototype.resize = function (opts) {\n        if (this._disposed) {\n            return;\n        }\n        opts = opts || {};\n        this.painter.resize(opts.width, opts.height);\n        this.handler.resize();\n    };\n    ZRender.prototype.clearAnimation = function () {\n        if (this._disposed) {\n            return;\n        }\n        this.animation.clear();\n    };\n    ZRender.prototype.getWidth = function () {\n        if (this._disposed) {\n            return;\n        }\n        return this.painter.getWidth();\n    };\n    ZRender.prototype.getHeight = function () {\n        if (this._disposed) {\n            return;\n        }\n        return this.painter.getHeight();\n    };\n    ZRender.prototype.setCursorStyle = function (cursorStyle) {\n        if (this._disposed) {\n            return;\n        }\n        this.handler.setCursorStyle(cursorStyle);\n    };\n    ZRender.prototype.findHover = function (x, y) {\n        if (this._disposed) {\n            return;\n        }\n        return this.handler.findHover(x, y);\n    };\n    ZRender.prototype.on = function (eventName, eventHandler, context) {\n        if (!this._disposed) {\n            this.handler.on(eventName, eventHandler, context);\n        }\n        return this;\n    };\n    ZRender.prototype.off = function (eventName, eventHandler) {\n        if (this._disposed) {\n            return;\n        }\n        this.handler.off(eventName, eventHandler);\n    };\n    ZRender.prototype.trigger = function (eventName, event) {\n        if (this._disposed) {\n            return;\n        }\n        this.handler.trigger(eventName, event);\n    };\n    ZRender.prototype.clear = function () {\n        if (this._disposed) {\n            return;\n        }\n        var roots = this.storage.getRoots();\n        for (var i = 0; i < roots.length; i++) {\n            if (roots[i] instanceof Group) {\n                roots[i].removeSelfFromZr(this);\n            }\n        }\n        this.storage.delAllRoots();\n        this.painter.clear();\n    };\n    ZRender.prototype.dispose = function () {\n        if (this._disposed) {\n            return;\n        }\n        this.animation.stop();\n        this.clear();\n        this.storage.dispose();\n        this.painter.dispose();\n        this.handler.dispose();\n        this.animation =\n            this.storage =\n                this.painter =\n                    this.handler = null;\n        this._disposed = true;\n        delInstance(this.id);\n    };\n    return ZRender;\n}());\nexport function init(dom, opts) {\n    var zr = new ZRender(zrUtil.guid(), dom, opts);\n    instances[zr.id] = zr;\n    return zr;\n}\nexport function dispose(zr) {\n    zr.dispose();\n}\nexport function disposeAll() {\n    for (var key in instances) {\n        if (instances.hasOwnProperty(key)) {\n            instances[key].dispose();\n        }\n    }\n    instances = {};\n}\nexport function getInstance(id) {\n    return instances[id];\n}\nexport function registerPainter(name, Ctor) {\n    painterCtors[name] = Ctor;\n}\nvar ssrDataGetter;\nexport function getElementSSRData(el) {\n    if (typeof ssrDataGetter === 'function') {\n        return ssrDataGetter(el);\n    }\n}\nexport function registerSSRDataGetter(getter) {\n    ssrDataGetter = getter;\n}\nexport var version = '5.6.1';\n;\n", "var Param = (function () {\n    function Param(target, e) {\n        this.target = target;\n        this.topTarget = e && e.topTarget;\n    }\n    return Param;\n}());\nvar Draggable = (function () {\n    function Draggable(handler) {\n        this.handler = handler;\n        handler.on('mousedown', this._dragStart, this);\n        handler.on('mousemove', this._drag, this);\n        handler.on('mouseup', this._dragEnd, this);\n    }\n    Draggable.prototype._dragStart = function (e) {\n        var draggingTarget = e.target;\n        while (draggingTarget && !draggingTarget.draggable) {\n            draggingTarget = draggingTarget.parent || draggingTarget.__hostTarget;\n        }\n        if (draggingTarget) {\n            this._draggingTarget = draggingTarget;\n            draggingTarget.dragging = true;\n            this._x = e.offsetX;\n            this._y = e.offsetY;\n            this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragstart', e.event);\n        }\n    };\n    Draggable.prototype._drag = function (e) {\n        var draggingTarget = this._draggingTarget;\n        if (draggingTarget) {\n            var x = e.offsetX;\n            var y = e.offsetY;\n            var dx = x - this._x;\n            var dy = y - this._y;\n            this._x = x;\n            this._y = y;\n            draggingTarget.drift(dx, dy, e);\n            this.handler.dispatchToElement(new Param(draggingTarget, e), 'drag', e.event);\n            var dropTarget = this.handler.findHover(x, y, draggingTarget).target;\n            var lastDropTarget = this._dropTarget;\n            this._dropTarget = dropTarget;\n            if (draggingTarget !== dropTarget) {\n                if (lastDropTarget && dropTarget !== lastDropTarget) {\n                    this.handler.dispatchToElement(new Param(lastDropTarget, e), 'dragleave', e.event);\n                }\n                if (dropTarget && dropTarget !== lastDropTarget) {\n                    this.handler.dispatchToElement(new Param(dropTarget, e), 'dragenter', e.event);\n                }\n            }\n        }\n    };\n    Draggable.prototype._dragEnd = function (e) {\n        var draggingTarget = this._draggingTarget;\n        if (draggingTarget) {\n            draggingTarget.dragging = false;\n        }\n        this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragend', e.event);\n        if (this._dropTarget) {\n            this.handler.dispatchToElement(new Param(this._dropTarget, e), 'drop', e.event);\n        }\n        this._draggingTarget = null;\n        this._dropTarget = null;\n    };\n    return Draggable;\n}());\nexport default Draggable;\n", "import Eventful from './Eventful.js';\nimport env from './env.js';\nimport { isCanvasEl, transformCoordWithViewport } from './dom.js';\nvar MOUSE_EVENT_REG = /^(?:mouse|pointer|contextmenu|drag|drop)|click/;\nvar _calcOut = [];\nvar firefoxNotSupportOffsetXY = env.browser.firefox\n    && +env.browser.version.split('.')[0] < 39;\nexport function clientToLocal(el, e, out, calculate) {\n    out = out || {};\n    if (calculate) {\n        calculateZrXY(el, e, out);\n    }\n    else if (firefoxNotSupportOffsetXY\n        && e.layerX != null\n        && e.layerX !== e.offsetX) {\n        out.zrX = e.layerX;\n        out.zrY = e.layerY;\n    }\n    else if (e.offsetX != null) {\n        out.zrX = e.offsetX;\n        out.zrY = e.offsetY;\n    }\n    else {\n        calculateZrXY(el, e, out);\n    }\n    return out;\n}\nfunction calculateZrXY(el, e, out) {\n    if (env.domSupported && el.getBoundingClientRect) {\n        var ex = e.clientX;\n        var ey = e.clientY;\n        if (isCanvasEl(el)) {\n            var box = el.getBoundingClientRect();\n            out.zrX = ex - box.left;\n            out.zrY = ey - box.top;\n            return;\n        }\n        else {\n            if (transformCoordWithViewport(_calcOut, el, ex, ey)) {\n                out.zrX = _calcOut[0];\n                out.zrY = _calcOut[1];\n                return;\n            }\n        }\n    }\n    out.zrX = out.zrY = 0;\n}\nexport function getNativeEvent(e) {\n    return e\n        || window.event;\n}\nexport function normalizeEvent(el, e, calculate) {\n    e = getNativeEvent(e);\n    if (e.zrX != null) {\n        return e;\n    }\n    var eventType = e.type;\n    var isTouch = eventType && eventType.indexOf('touch') >= 0;\n    if (!isTouch) {\n        clientToLocal(el, e, e, calculate);\n        var wheelDelta = getWheelDeltaMayPolyfill(e);\n        e.zrDelta = wheelDelta ? wheelDelta / 120 : -(e.detail || 0) / 3;\n    }\n    else {\n        var touch = eventType !== 'touchend'\n            ? e.targetTouches[0]\n            : e.changedTouches[0];\n        touch && clientToLocal(el, touch, e, calculate);\n    }\n    var button = e.button;\n    if (e.which == null && button !== undefined && MOUSE_EVENT_REG.test(e.type)) {\n        e.which = (button & 1 ? 1 : (button & 2 ? 3 : (button & 4 ? 2 : 0)));\n    }\n    return e;\n}\nfunction getWheelDeltaMayPolyfill(e) {\n    var rawWheelDelta = e.wheelDelta;\n    if (rawWheelDelta) {\n        return rawWheelDelta;\n    }\n    var deltaX = e.deltaX;\n    var deltaY = e.deltaY;\n    if (deltaX == null || deltaY == null) {\n        return rawWheelDelta;\n    }\n    var delta = deltaY !== 0 ? Math.abs(deltaY) : Math.abs(deltaX);\n    var sign = deltaY > 0 ? -1\n        : deltaY < 0 ? 1\n            : deltaX > 0 ? -1\n                : 1;\n    return 3 * delta * sign;\n}\nexport function addEventListener(el, name, handler, opt) {\n    el.addEventListener(name, handler, opt);\n}\nexport function removeEventListener(el, name, handler, opt) {\n    el.removeEventListener(name, handler, opt);\n}\nexport var stop = function (e) {\n    e.preventDefault();\n    e.stopPropagation();\n    e.cancelBubble = true;\n};\nexport function isMiddleOrRightButtonOnMouseUpDown(e) {\n    return e.which === 2 || e.which === 3;\n}\nexport { Eventful as Dispatcher };\n", "import * as eventUtil from './event.js';\nvar GestureMgr = (function () {\n    function GestureMgr() {\n        this._track = [];\n    }\n    GestureMgr.prototype.recognize = function (event, target, root) {\n        this._doTrack(event, target, root);\n        return this._recognize(event);\n    };\n    GestureMgr.prototype.clear = function () {\n        this._track.length = 0;\n        return this;\n    };\n    GestureMgr.prototype._doTrack = function (event, target, root) {\n        var touches = event.touches;\n        if (!touches) {\n            return;\n        }\n        var trackItem = {\n            points: [],\n            touches: [],\n            target: target,\n            event: event\n        };\n        for (var i = 0, len = touches.length; i < len; i++) {\n            var touch = touches[i];\n            var pos = eventUtil.clientToLocal(root, touch, {});\n            trackItem.points.push([pos.zrX, pos.zrY]);\n            trackItem.touches.push(touch);\n        }\n        this._track.push(trackItem);\n    };\n    GestureMgr.prototype._recognize = function (event) {\n        for (var eventName in recognizers) {\n            if (recognizers.hasOwnProperty(eventName)) {\n                var gestureInfo = recognizers[eventName](this._track, event);\n                if (gestureInfo) {\n                    return gestureInfo;\n                }\n            }\n        }\n    };\n    return GestureMgr;\n}());\nexport { GestureMgr };\nfunction dist(pointPair) {\n    var dx = pointPair[1][0] - pointPair[0][0];\n    var dy = pointPair[1][1] - pointPair[0][1];\n    return Math.sqrt(dx * dx + dy * dy);\n}\nfunction center(pointPair) {\n    return [\n        (pointPair[0][0] + pointPair[1][0]) / 2,\n        (pointPair[0][1] + pointPair[1][1]) / 2\n    ];\n}\nvar recognizers = {\n    pinch: function (tracks, event) {\n        var trackLen = tracks.length;\n        if (!trackLen) {\n            return;\n        }\n        var pinchEnd = (tracks[trackLen - 1] || {}).points;\n        var pinchPre = (tracks[trackLen - 2] || {}).points || pinchEnd;\n        if (pinchPre\n            && pinchPre.length > 1\n            && pinchEnd\n            && pinchEnd.length > 1) {\n            var pinchScale = dist(pinchEnd) / dist(pinchPre);\n            !isFinite(pinchScale) && (pinchScale = 1);\n            event.pinchScale = pinchScale;\n            var pinchCenter = center(pinchEnd);\n            event.pinchX = pinchCenter[0];\n            event.pinchY = pinchCenter[1];\n            return {\n                type: 'pinch',\n                target: tracks[0].target,\n                event: event\n            };\n        }\n    }\n};\n", "import { __extends } from \"tslib\";\nimport * as util from './core/util.js';\nimport * as vec2 from './core/vector.js';\nimport Draggable from './mixin/Draggable.js';\nimport Eventful from './core/Eventful.js';\nimport * as eventTool from './core/event.js';\nimport { GestureMgr } from './core/GestureMgr.js';\nimport BoundingRect from './core/BoundingRect.js';\nvar SILENT = 'silent';\nfunction makeEventPacket(eveType, targetInfo, event) {\n    return {\n        type: eveType,\n        event: event,\n        target: targetInfo.target,\n        topTarget: targetInfo.topTarget,\n        cancelBubble: false,\n        offsetX: event.zrX,\n        offsetY: event.zrY,\n        gestureEvent: event.gestureEvent,\n        pinchX: event.pinchX,\n        pinchY: event.pinchY,\n        pinchScale: event.pinchScale,\n        wheelDelta: event.zrDelta,\n        zrByTouch: event.zrByTouch,\n        which: event.which,\n        stop: stopEvent\n    };\n}\nfunction stopEvent() {\n    eventTool.stop(this.event);\n}\nvar EmptyProxy = (function (_super) {\n    __extends(EmptyProxy, _super);\n    function EmptyProxy() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.handler = null;\n        return _this;\n    }\n    EmptyProxy.prototype.dispose = function () { };\n    EmptyProxy.prototype.setCursor = function () { };\n    return EmptyProxy;\n}(Eventful));\nvar HoveredResult = (function () {\n    function HoveredResult(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    return HoveredResult;\n}());\nvar handlerNames = [\n    'click', 'dblclick', 'mousewheel', 'mouseout',\n    'mouseup', 'mousedown', 'mousemove', 'contextmenu'\n];\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar Handler = (function (_super) {\n    __extends(Handler, _super);\n    function Handler(storage, painter, proxy, painterRoot, pointerSize) {\n        var _this = _super.call(this) || this;\n        _this._hovered = new HoveredResult(0, 0);\n        _this.storage = storage;\n        _this.painter = painter;\n        _this.painterRoot = painterRoot;\n        _this._pointerSize = pointerSize;\n        proxy = proxy || new EmptyProxy();\n        _this.proxy = null;\n        _this.setHandlerProxy(proxy);\n        _this._draggingMgr = new Draggable(_this);\n        return _this;\n    }\n    Handler.prototype.setHandlerProxy = function (proxy) {\n        if (this.proxy) {\n            this.proxy.dispose();\n        }\n        if (proxy) {\n            util.each(handlerNames, function (name) {\n                proxy.on && proxy.on(name, this[name], this);\n            }, this);\n            proxy.handler = this;\n        }\n        this.proxy = proxy;\n    };\n    Handler.prototype.mousemove = function (event) {\n        var x = event.zrX;\n        var y = event.zrY;\n        var isOutside = isOutsideBoundary(this, x, y);\n        var lastHovered = this._hovered;\n        var lastHoveredTarget = lastHovered.target;\n        if (lastHoveredTarget && !lastHoveredTarget.__zr) {\n            lastHovered = this.findHover(lastHovered.x, lastHovered.y);\n            lastHoveredTarget = lastHovered.target;\n        }\n        var hovered = this._hovered = isOutside ? new HoveredResult(x, y) : this.findHover(x, y);\n        var hoveredTarget = hovered.target;\n        var proxy = this.proxy;\n        proxy.setCursor && proxy.setCursor(hoveredTarget ? hoveredTarget.cursor : 'default');\n        if (lastHoveredTarget && hoveredTarget !== lastHoveredTarget) {\n            this.dispatchToElement(lastHovered, 'mouseout', event);\n        }\n        this.dispatchToElement(hovered, 'mousemove', event);\n        if (hoveredTarget && hoveredTarget !== lastHoveredTarget) {\n            this.dispatchToElement(hovered, 'mouseover', event);\n        }\n    };\n    Handler.prototype.mouseout = function (event) {\n        var eventControl = event.zrEventControl;\n        if (eventControl !== 'only_globalout') {\n            this.dispatchToElement(this._hovered, 'mouseout', event);\n        }\n        if (eventControl !== 'no_globalout') {\n            this.trigger('globalout', { type: 'globalout', event: event });\n        }\n    };\n    Handler.prototype.resize = function () {\n        this._hovered = new HoveredResult(0, 0);\n    };\n    Handler.prototype.dispatch = function (eventName, eventArgs) {\n        var handler = this[eventName];\n        handler && handler.call(this, eventArgs);\n    };\n    Handler.prototype.dispose = function () {\n        this.proxy.dispose();\n        this.storage = null;\n        this.proxy = null;\n        this.painter = null;\n    };\n    Handler.prototype.setCursorStyle = function (cursorStyle) {\n        var proxy = this.proxy;\n        proxy.setCursor && proxy.setCursor(cursorStyle);\n    };\n    Handler.prototype.dispatchToElement = function (targetInfo, eventName, event) {\n        targetInfo = targetInfo || {};\n        var el = targetInfo.target;\n        if (el && el.silent) {\n            return;\n        }\n        var eventKey = ('on' + eventName);\n        var eventPacket = makeEventPacket(eventName, targetInfo, event);\n        while (el) {\n            el[eventKey]\n                && (eventPacket.cancelBubble = !!el[eventKey].call(el, eventPacket));\n            el.trigger(eventName, eventPacket);\n            el = el.__hostTarget ? el.__hostTarget : el.parent;\n            if (eventPacket.cancelBubble) {\n                break;\n            }\n        }\n        if (!eventPacket.cancelBubble) {\n            this.trigger(eventName, eventPacket);\n            if (this.painter && this.painter.eachOtherLayer) {\n                this.painter.eachOtherLayer(function (layer) {\n                    if (typeof (layer[eventKey]) === 'function') {\n                        layer[eventKey].call(layer, eventPacket);\n                    }\n                    if (layer.trigger) {\n                        layer.trigger(eventName, eventPacket);\n                    }\n                });\n            }\n        }\n    };\n    Handler.prototype.findHover = function (x, y, exclude) {\n        var list = this.storage.getDisplayList();\n        var out = new HoveredResult(x, y);\n        setHoverTarget(list, out, x, y, exclude);\n        if (this._pointerSize && !out.target) {\n            var candidates = [];\n            var pointerSize = this._pointerSize;\n            var targetSizeHalf = pointerSize / 2;\n            var pointerRect = new BoundingRect(x - targetSizeHalf, y - targetSizeHalf, pointerSize, pointerSize);\n            for (var i = list.length - 1; i >= 0; i--) {\n                var el = list[i];\n                if (el !== exclude\n                    && !el.ignore\n                    && !el.ignoreCoarsePointer\n                    && (!el.parent || !el.parent.ignoreCoarsePointer)) {\n                    tmpRect.copy(el.getBoundingRect());\n                    if (el.transform) {\n                        tmpRect.applyTransform(el.transform);\n                    }\n                    if (tmpRect.intersect(pointerRect)) {\n                        candidates.push(el);\n                    }\n                }\n            }\n            if (candidates.length) {\n                var rStep = 4;\n                var thetaStep = Math.PI / 12;\n                var PI2 = Math.PI * 2;\n                for (var r = 0; r < targetSizeHalf; r += rStep) {\n                    for (var theta = 0; theta < PI2; theta += thetaStep) {\n                        var x1 = x + r * Math.cos(theta);\n                        var y1 = y + r * Math.sin(theta);\n                        setHoverTarget(candidates, out, x1, y1, exclude);\n                        if (out.target) {\n                            return out;\n                        }\n                    }\n                }\n            }\n        }\n        return out;\n    };\n    Handler.prototype.processGesture = function (event, stage) {\n        if (!this._gestureMgr) {\n            this._gestureMgr = new GestureMgr();\n        }\n        var gestureMgr = this._gestureMgr;\n        stage === 'start' && gestureMgr.clear();\n        var gestureInfo = gestureMgr.recognize(event, this.findHover(event.zrX, event.zrY, null).target, this.proxy.dom);\n        stage === 'end' && gestureMgr.clear();\n        if (gestureInfo) {\n            var type = gestureInfo.type;\n            event.gestureEvent = type;\n            var res = new HoveredResult();\n            res.target = gestureInfo.target;\n            this.dispatchToElement(res, type, gestureInfo.event);\n        }\n    };\n    return Handler;\n}(Eventful));\nutil.each(['click', 'mousedown', 'mouseup', 'mousewheel', 'dblclick', 'contextmenu'], function (name) {\n    Handler.prototype[name] = function (event) {\n        var x = event.zrX;\n        var y = event.zrY;\n        var isOutside = isOutsideBoundary(this, x, y);\n        var hovered;\n        var hoveredTarget;\n        if (name !== 'mouseup' || !isOutside) {\n            hovered = this.findHover(x, y);\n            hoveredTarget = hovered.target;\n        }\n        if (name === 'mousedown') {\n            this._downEl = hoveredTarget;\n            this._downPoint = [event.zrX, event.zrY];\n            this._upEl = hoveredTarget;\n        }\n        else if (name === 'mouseup') {\n            this._upEl = hoveredTarget;\n        }\n        else if (name === 'click') {\n            if (this._downEl !== this._upEl\n                || !this._downPoint\n                || vec2.dist(this._downPoint, [event.zrX, event.zrY]) > 4) {\n                return;\n            }\n            this._downPoint = null;\n        }\n        this.dispatchToElement(hovered, name, event);\n    };\n});\nfunction isHover(displayable, x, y) {\n    if (displayable[displayable.rectHover ? 'rectContain' : 'contain'](x, y)) {\n        var el = displayable;\n        var isSilent = void 0;\n        var ignoreClip = false;\n        while (el) {\n            if (el.ignoreClip) {\n                ignoreClip = true;\n            }\n            if (!ignoreClip) {\n                var clipPath = el.getClipPath();\n                if (clipPath && !clipPath.contain(x, y)) {\n                    return false;\n                }\n            }\n            if (el.silent) {\n                isSilent = true;\n            }\n            var hostEl = el.__hostTarget;\n            el = hostEl ? hostEl : el.parent;\n        }\n        return isSilent ? SILENT : true;\n    }\n    return false;\n}\nfunction setHoverTarget(list, out, x, y, exclude) {\n    for (var i = list.length - 1; i >= 0; i--) {\n        var el = list[i];\n        var hoverCheckResult = void 0;\n        if (el !== exclude\n            && !el.ignore\n            && (hoverCheckResult = isHover(el, x, y))) {\n            !out.topTarget && (out.topTarget = el);\n            if (hoverCheckResult !== SILENT) {\n                out.target = el;\n                break;\n            }\n        }\n    }\n}\nfunction isOutsideBoundary(handlerInstance, x, y) {\n    var painter = handlerInstance.painter;\n    return x < 0 || x > painter.getWidth() || y < 0 || y > painter.getHeight();\n}\nexport default Handler;\n", "var DEFAULT_MIN_MERGE = 32;\nvar DEFAULT_MIN_GALLOPING = 7;\nfunction minRunLength(n) {\n    var r = 0;\n    while (n >= DEFAULT_MIN_MERGE) {\n        r |= n & 1;\n        n >>= 1;\n    }\n    return n + r;\n}\nfunction makeAscendingRun(array, lo, hi, compare) {\n    var runHi = lo + 1;\n    if (runHi === hi) {\n        return 1;\n    }\n    if (compare(array[runHi++], array[lo]) < 0) {\n        while (runHi < hi && compare(array[runHi], array[runHi - 1]) < 0) {\n            runHi++;\n        }\n        reverseRun(array, lo, runHi);\n    }\n    else {\n        while (runHi < hi && compare(array[runHi], array[runHi - 1]) >= 0) {\n            runHi++;\n        }\n    }\n    return runHi - lo;\n}\nfunction reverseRun(array, lo, hi) {\n    hi--;\n    while (lo < hi) {\n        var t = array[lo];\n        array[lo++] = array[hi];\n        array[hi--] = t;\n    }\n}\nfunction binaryInsertionSort(array, lo, hi, start, compare) {\n    if (start === lo) {\n        start++;\n    }\n    for (; start < hi; start++) {\n        var pivot = array[start];\n        var left = lo;\n        var right = start;\n        var mid;\n        while (left < right) {\n            mid = left + right >>> 1;\n            if (compare(pivot, array[mid]) < 0) {\n                right = mid;\n            }\n            else {\n                left = mid + 1;\n            }\n        }\n        var n = start - left;\n        switch (n) {\n            case 3:\n                array[left + 3] = array[left + 2];\n            case 2:\n                array[left + 2] = array[left + 1];\n            case 1:\n                array[left + 1] = array[left];\n                break;\n            default:\n                while (n > 0) {\n                    array[left + n] = array[left + n - 1];\n                    n--;\n                }\n        }\n        array[left] = pivot;\n    }\n}\nfunction gallopLeft(value, array, start, length, hint, compare) {\n    var lastOffset = 0;\n    var maxOffset = 0;\n    var offset = 1;\n    if (compare(value, array[start + hint]) > 0) {\n        maxOffset = length - hint;\n        while (offset < maxOffset && compare(value, array[start + hint + offset]) > 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        lastOffset += hint;\n        offset += hint;\n    }\n    else {\n        maxOffset = hint + 1;\n        while (offset < maxOffset && compare(value, array[start + hint - offset]) <= 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        var tmp = lastOffset;\n        lastOffset = hint - offset;\n        offset = hint - tmp;\n    }\n    lastOffset++;\n    while (lastOffset < offset) {\n        var m = lastOffset + (offset - lastOffset >>> 1);\n        if (compare(value, array[start + m]) > 0) {\n            lastOffset = m + 1;\n        }\n        else {\n            offset = m;\n        }\n    }\n    return offset;\n}\nfunction gallopRight(value, array, start, length, hint, compare) {\n    var lastOffset = 0;\n    var maxOffset = 0;\n    var offset = 1;\n    if (compare(value, array[start + hint]) < 0) {\n        maxOffset = hint + 1;\n        while (offset < maxOffset && compare(value, array[start + hint - offset]) < 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        var tmp = lastOffset;\n        lastOffset = hint - offset;\n        offset = hint - tmp;\n    }\n    else {\n        maxOffset = length - hint;\n        while (offset < maxOffset && compare(value, array[start + hint + offset]) >= 0) {\n            lastOffset = offset;\n            offset = (offset << 1) + 1;\n            if (offset <= 0) {\n                offset = maxOffset;\n            }\n        }\n        if (offset > maxOffset) {\n            offset = maxOffset;\n        }\n        lastOffset += hint;\n        offset += hint;\n    }\n    lastOffset++;\n    while (lastOffset < offset) {\n        var m = lastOffset + (offset - lastOffset >>> 1);\n        if (compare(value, array[start + m]) < 0) {\n            offset = m;\n        }\n        else {\n            lastOffset = m + 1;\n        }\n    }\n    return offset;\n}\nfunction TimSort(array, compare) {\n    var minGallop = DEFAULT_MIN_GALLOPING;\n    var runStart;\n    var runLength;\n    var stackSize = 0;\n    var tmp = [];\n    runStart = [];\n    runLength = [];\n    function pushRun(_runStart, _runLength) {\n        runStart[stackSize] = _runStart;\n        runLength[stackSize] = _runLength;\n        stackSize += 1;\n    }\n    function mergeRuns() {\n        while (stackSize > 1) {\n            var n = stackSize - 2;\n            if ((n >= 1 && runLength[n - 1] <= runLength[n] + runLength[n + 1])\n                || (n >= 2 && runLength[n - 2] <= runLength[n] + runLength[n - 1])) {\n                if (runLength[n - 1] < runLength[n + 1]) {\n                    n--;\n                }\n            }\n            else if (runLength[n] > runLength[n + 1]) {\n                break;\n            }\n            mergeAt(n);\n        }\n    }\n    function forceMergeRuns() {\n        while (stackSize > 1) {\n            var n = stackSize - 2;\n            if (n > 0 && runLength[n - 1] < runLength[n + 1]) {\n                n--;\n            }\n            mergeAt(n);\n        }\n    }\n    function mergeAt(i) {\n        var start1 = runStart[i];\n        var length1 = runLength[i];\n        var start2 = runStart[i + 1];\n        var length2 = runLength[i + 1];\n        runLength[i] = length1 + length2;\n        if (i === stackSize - 3) {\n            runStart[i + 1] = runStart[i + 2];\n            runLength[i + 1] = runLength[i + 2];\n        }\n        stackSize--;\n        var k = gallopRight(array[start2], array, start1, length1, 0, compare);\n        start1 += k;\n        length1 -= k;\n        if (length1 === 0) {\n            return;\n        }\n        length2 = gallopLeft(array[start1 + length1 - 1], array, start2, length2, length2 - 1, compare);\n        if (length2 === 0) {\n            return;\n        }\n        if (length1 <= length2) {\n            mergeLow(start1, length1, start2, length2);\n        }\n        else {\n            mergeHigh(start1, length1, start2, length2);\n        }\n    }\n    function mergeLow(start1, length1, start2, length2) {\n        var i = 0;\n        for (i = 0; i < length1; i++) {\n            tmp[i] = array[start1 + i];\n        }\n        var cursor1 = 0;\n        var cursor2 = start2;\n        var dest = start1;\n        array[dest++] = array[cursor2++];\n        if (--length2 === 0) {\n            for (i = 0; i < length1; i++) {\n                array[dest + i] = tmp[cursor1 + i];\n            }\n            return;\n        }\n        if (length1 === 1) {\n            for (i = 0; i < length2; i++) {\n                array[dest + i] = array[cursor2 + i];\n            }\n            array[dest + length2] = tmp[cursor1];\n            return;\n        }\n        var _minGallop = minGallop;\n        var count1;\n        var count2;\n        var exit;\n        while (1) {\n            count1 = 0;\n            count2 = 0;\n            exit = false;\n            do {\n                if (compare(array[cursor2], tmp[cursor1]) < 0) {\n                    array[dest++] = array[cursor2++];\n                    count2++;\n                    count1 = 0;\n                    if (--length2 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                else {\n                    array[dest++] = tmp[cursor1++];\n                    count1++;\n                    count2 = 0;\n                    if (--length1 === 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n            } while ((count1 | count2) < _minGallop);\n            if (exit) {\n                break;\n            }\n            do {\n                count1 = gallopRight(array[cursor2], tmp, cursor1, length1, 0, compare);\n                if (count1 !== 0) {\n                    for (i = 0; i < count1; i++) {\n                        array[dest + i] = tmp[cursor1 + i];\n                    }\n                    dest += count1;\n                    cursor1 += count1;\n                    length1 -= count1;\n                    if (length1 <= 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest++] = array[cursor2++];\n                if (--length2 === 0) {\n                    exit = true;\n                    break;\n                }\n                count2 = gallopLeft(tmp[cursor1], array, cursor2, length2, 0, compare);\n                if (count2 !== 0) {\n                    for (i = 0; i < count2; i++) {\n                        array[dest + i] = array[cursor2 + i];\n                    }\n                    dest += count2;\n                    cursor2 += count2;\n                    length2 -= count2;\n                    if (length2 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest++] = tmp[cursor1++];\n                if (--length1 === 1) {\n                    exit = true;\n                    break;\n                }\n                _minGallop--;\n            } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n            if (exit) {\n                break;\n            }\n            if (_minGallop < 0) {\n                _minGallop = 0;\n            }\n            _minGallop += 2;\n        }\n        minGallop = _minGallop;\n        minGallop < 1 && (minGallop = 1);\n        if (length1 === 1) {\n            for (i = 0; i < length2; i++) {\n                array[dest + i] = array[cursor2 + i];\n            }\n            array[dest + length2] = tmp[cursor1];\n        }\n        else if (length1 === 0) {\n            throw new Error();\n        }\n        else {\n            for (i = 0; i < length1; i++) {\n                array[dest + i] = tmp[cursor1 + i];\n            }\n        }\n    }\n    function mergeHigh(start1, length1, start2, length2) {\n        var i = 0;\n        for (i = 0; i < length2; i++) {\n            tmp[i] = array[start2 + i];\n        }\n        var cursor1 = start1 + length1 - 1;\n        var cursor2 = length2 - 1;\n        var dest = start2 + length2 - 1;\n        var customCursor = 0;\n        var customDest = 0;\n        array[dest--] = array[cursor1--];\n        if (--length1 === 0) {\n            customCursor = dest - (length2 - 1);\n            for (i = 0; i < length2; i++) {\n                array[customCursor + i] = tmp[i];\n            }\n            return;\n        }\n        if (length2 === 1) {\n            dest -= length1;\n            cursor1 -= length1;\n            customDest = dest + 1;\n            customCursor = cursor1 + 1;\n            for (i = length1 - 1; i >= 0; i--) {\n                array[customDest + i] = array[customCursor + i];\n            }\n            array[dest] = tmp[cursor2];\n            return;\n        }\n        var _minGallop = minGallop;\n        while (true) {\n            var count1 = 0;\n            var count2 = 0;\n            var exit = false;\n            do {\n                if (compare(tmp[cursor2], array[cursor1]) < 0) {\n                    array[dest--] = array[cursor1--];\n                    count1++;\n                    count2 = 0;\n                    if (--length1 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                else {\n                    array[dest--] = tmp[cursor2--];\n                    count2++;\n                    count1 = 0;\n                    if (--length2 === 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n            } while ((count1 | count2) < _minGallop);\n            if (exit) {\n                break;\n            }\n            do {\n                count1 = length1 - gallopRight(tmp[cursor2], array, start1, length1, length1 - 1, compare);\n                if (count1 !== 0) {\n                    dest -= count1;\n                    cursor1 -= count1;\n                    length1 -= count1;\n                    customDest = dest + 1;\n                    customCursor = cursor1 + 1;\n                    for (i = count1 - 1; i >= 0; i--) {\n                        array[customDest + i] = array[customCursor + i];\n                    }\n                    if (length1 === 0) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest--] = tmp[cursor2--];\n                if (--length2 === 1) {\n                    exit = true;\n                    break;\n                }\n                count2 = length2 - gallopLeft(array[cursor1], tmp, 0, length2, length2 - 1, compare);\n                if (count2 !== 0) {\n                    dest -= count2;\n                    cursor2 -= count2;\n                    length2 -= count2;\n                    customDest = dest + 1;\n                    customCursor = cursor2 + 1;\n                    for (i = 0; i < count2; i++) {\n                        array[customDest + i] = tmp[customCursor + i];\n                    }\n                    if (length2 <= 1) {\n                        exit = true;\n                        break;\n                    }\n                }\n                array[dest--] = array[cursor1--];\n                if (--length1 === 0) {\n                    exit = true;\n                    break;\n                }\n                _minGallop--;\n            } while (count1 >= DEFAULT_MIN_GALLOPING || count2 >= DEFAULT_MIN_GALLOPING);\n            if (exit) {\n                break;\n            }\n            if (_minGallop < 0) {\n                _minGallop = 0;\n            }\n            _minGallop += 2;\n        }\n        minGallop = _minGallop;\n        if (minGallop < 1) {\n            minGallop = 1;\n        }\n        if (length2 === 1) {\n            dest -= length1;\n            cursor1 -= length1;\n            customDest = dest + 1;\n            customCursor = cursor1 + 1;\n            for (i = length1 - 1; i >= 0; i--) {\n                array[customDest + i] = array[customCursor + i];\n            }\n            array[dest] = tmp[cursor2];\n        }\n        else if (length2 === 0) {\n            throw new Error();\n        }\n        else {\n            customCursor = dest - (length2 - 1);\n            for (i = 0; i < length2; i++) {\n                array[customCursor + i] = tmp[i];\n            }\n        }\n    }\n    return {\n        mergeRuns: mergeRuns,\n        forceMergeRuns: forceMergeRuns,\n        pushRun: pushRun\n    };\n}\nexport default function sort(array, compare, lo, hi) {\n    if (!lo) {\n        lo = 0;\n    }\n    if (!hi) {\n        hi = array.length;\n    }\n    var remaining = hi - lo;\n    if (remaining < 2) {\n        return;\n    }\n    var runLength = 0;\n    if (remaining < DEFAULT_MIN_MERGE) {\n        runLength = makeAscendingRun(array, lo, hi, compare);\n        binaryInsertionSort(array, lo, hi, lo + runLength, compare);\n        return;\n    }\n    var ts = TimSort(array, compare);\n    var minRun = minRunLength(remaining);\n    do {\n        runLength = makeAscendingRun(array, lo, hi, compare);\n        if (runLength < minRun) {\n            var force = remaining;\n            if (force > minRun) {\n                force = minRun;\n            }\n            binaryInsertionSort(array, lo, lo + force, lo + runLength, compare);\n            runLength = force;\n        }\n        ts.pushRun(lo, runLength);\n        ts.mergeRuns();\n        remaining -= runLength;\n        lo += runLength;\n    } while (remaining !== 0);\n    ts.forceMergeRuns();\n}\n", "import * as util from './core/util.js';\nimport timsort from './core/timsort.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nvar invalidZErrorLogged = false;\nfunction logInvalidZError() {\n    if (invalidZ<PERSON>rrorLogged) {\n        return;\n    }\n    invalidZErrorLogged = true;\n    console.warn('z / z2 / zlevel of displayable is invalid, which may cause unexpected errors');\n}\nfunction shapeCompareFunc(a, b) {\n    if (a.zlevel === b.zlevel) {\n        if (a.z === b.z) {\n            return a.z2 - b.z2;\n        }\n        return a.z - b.z;\n    }\n    return a.zlevel - b.zlevel;\n}\nvar Storage = (function () {\n    function Storage() {\n        this._roots = [];\n        this._displayList = [];\n        this._displayListLen = 0;\n        this.displayableSortFunc = shapeCompareFunc;\n    }\n    Storage.prototype.traverse = function (cb, context) {\n        for (var i = 0; i < this._roots.length; i++) {\n            this._roots[i].traverse(cb, context);\n        }\n    };\n    Storage.prototype.getDisplayList = function (update, includeIgnore) {\n        includeIgnore = includeIgnore || false;\n        var displayList = this._displayList;\n        if (update || !displayList.length) {\n            this.updateDisplayList(includeIgnore);\n        }\n        return displayList;\n    };\n    Storage.prototype.updateDisplayList = function (includeIgnore) {\n        this._displayListLen = 0;\n        var roots = this._roots;\n        var displayList = this._displayList;\n        for (var i = 0, len = roots.length; i < len; i++) {\n            this._updateAndAddDisplayable(roots[i], null, includeIgnore);\n        }\n        displayList.length = this._displayListLen;\n        timsort(displayList, shapeCompareFunc);\n    };\n    Storage.prototype._updateAndAddDisplayable = function (el, clipPaths, includeIgnore) {\n        if (el.ignore && !includeIgnore) {\n            return;\n        }\n        el.beforeUpdate();\n        el.update();\n        el.afterUpdate();\n        var userSetClipPath = el.getClipPath();\n        if (el.ignoreClip) {\n            clipPaths = null;\n        }\n        else if (userSetClipPath) {\n            if (clipPaths) {\n                clipPaths = clipPaths.slice();\n            }\n            else {\n                clipPaths = [];\n            }\n            var currentClipPath = userSetClipPath;\n            var parentClipPath = el;\n            while (currentClipPath) {\n                currentClipPath.parent = parentClipPath;\n                currentClipPath.updateTransform();\n                clipPaths.push(currentClipPath);\n                parentClipPath = currentClipPath;\n                currentClipPath = currentClipPath.getClipPath();\n            }\n        }\n        if (el.childrenRef) {\n            var children = el.childrenRef();\n            for (var i = 0; i < children.length; i++) {\n                var child = children[i];\n                if (el.__dirty) {\n                    child.__dirty |= REDRAW_BIT;\n                }\n                this._updateAndAddDisplayable(child, clipPaths, includeIgnore);\n            }\n            el.__dirty = 0;\n        }\n        else {\n            var disp = el;\n            if (clipPaths && clipPaths.length) {\n                disp.__clipPaths = clipPaths;\n            }\n            else if (disp.__clipPaths && disp.__clipPaths.length > 0) {\n                disp.__clipPaths = [];\n            }\n            if (isNaN(disp.z)) {\n                logInvalidZError();\n                disp.z = 0;\n            }\n            if (isNaN(disp.z2)) {\n                logInvalidZError();\n                disp.z2 = 0;\n            }\n            if (isNaN(disp.zlevel)) {\n                logInvalidZError();\n                disp.zlevel = 0;\n            }\n            this._displayList[this._displayListLen++] = disp;\n        }\n        var decalEl = el.getDecalElement && el.getDecalElement();\n        if (decalEl) {\n            this._updateAndAddDisplayable(decalEl, clipPaths, includeIgnore);\n        }\n        var textGuide = el.getTextGuideLine();\n        if (textGuide) {\n            this._updateAndAddDisplayable(textGuide, clipPaths, includeIgnore);\n        }\n        var textEl = el.getTextContent();\n        if (textEl) {\n            this._updateAndAddDisplayable(textEl, clipPaths, includeIgnore);\n        }\n    };\n    Storage.prototype.addRoot = function (el) {\n        if (el.__zr && el.__zr.storage === this) {\n            return;\n        }\n        this._roots.push(el);\n    };\n    Storage.prototype.delRoot = function (el) {\n        if (el instanceof Array) {\n            for (var i = 0, l = el.length; i < l; i++) {\n                this.delRoot(el[i]);\n            }\n            return;\n        }\n        var idx = util.indexOf(this._roots, el);\n        if (idx >= 0) {\n            this._roots.splice(idx, 1);\n        }\n    };\n    Storage.prototype.delAllRoots = function () {\n        this._roots = [];\n        this._displayList = [];\n        this._displayListLen = 0;\n        return;\n    };\n    Storage.prototype.getRoots = function () {\n        return this._roots;\n    };\n    Storage.prototype.dispose = function () {\n        this._displayList = null;\n        this._roots = null;\n    };\n    return Storage;\n}());\nexport default Storage;\n", "import env from '../core/env.js';\nvar requestAnimationFrame;\nrequestAnimationFrame = (env.hasGlobalWindow\n    && ((window.requestAnimationFrame && window.requestAnimationFrame.bind(window))\n        || (window.msRequestAnimationFrame && window.msRequestAnimationFrame.bind(window))\n        || window.mozRequestAnimationFrame\n        || window.webkitRequestAnimationFrame)) || function (func) {\n    return setTimeout(func, 16);\n};\nexport default requestAnimationFrame;\n", "import { __extends } from \"tslib\";\nimport Eventful from '../core/Eventful.js';\nimport requestAnimationFrame from './requestAnimationFrame.js';\nimport Animator from './Animator.js';\nexport function getTime() {\n    return new Date().getTime();\n}\nvar Animation = (function (_super) {\n    __extends(Animation, _super);\n    function Animation(opts) {\n        var _this = _super.call(this) || this;\n        _this._running = false;\n        _this._time = 0;\n        _this._pausedTime = 0;\n        _this._pauseStart = 0;\n        _this._paused = false;\n        opts = opts || {};\n        _this.stage = opts.stage || {};\n        return _this;\n    }\n    Animation.prototype.addClip = function (clip) {\n        if (clip.animation) {\n            this.removeClip(clip);\n        }\n        if (!this._head) {\n            this._head = this._tail = clip;\n        }\n        else {\n            this._tail.next = clip;\n            clip.prev = this._tail;\n            clip.next = null;\n            this._tail = clip;\n        }\n        clip.animation = this;\n    };\n    Animation.prototype.addAnimator = function (animator) {\n        animator.animation = this;\n        var clip = animator.getClip();\n        if (clip) {\n            this.addClip(clip);\n        }\n    };\n    Animation.prototype.removeClip = function (clip) {\n        if (!clip.animation) {\n            return;\n        }\n        var prev = clip.prev;\n        var next = clip.next;\n        if (prev) {\n            prev.next = next;\n        }\n        else {\n            this._head = next;\n        }\n        if (next) {\n            next.prev = prev;\n        }\n        else {\n            this._tail = prev;\n        }\n        clip.next = clip.prev = clip.animation = null;\n    };\n    Animation.prototype.removeAnimator = function (animator) {\n        var clip = animator.getClip();\n        if (clip) {\n            this.removeClip(clip);\n        }\n        animator.animation = null;\n    };\n    Animation.prototype.update = function (notTriggerFrameAndStageUpdate) {\n        var time = getTime() - this._pausedTime;\n        var delta = time - this._time;\n        var clip = this._head;\n        while (clip) {\n            var nextClip = clip.next;\n            var finished = clip.step(time, delta);\n            if (finished) {\n                clip.ondestroy();\n                this.removeClip(clip);\n                clip = nextClip;\n            }\n            else {\n                clip = nextClip;\n            }\n        }\n        this._time = time;\n        if (!notTriggerFrameAndStageUpdate) {\n            this.trigger('frame', delta);\n            this.stage.update && this.stage.update();\n        }\n    };\n    Animation.prototype._startLoop = function () {\n        var self = this;\n        this._running = true;\n        function step() {\n            if (self._running) {\n                requestAnimationFrame(step);\n                !self._paused && self.update();\n            }\n        }\n        requestAnimationFrame(step);\n    };\n    Animation.prototype.start = function () {\n        if (this._running) {\n            return;\n        }\n        this._time = getTime();\n        this._pausedTime = 0;\n        this._startLoop();\n    };\n    Animation.prototype.stop = function () {\n        this._running = false;\n    };\n    Animation.prototype.pause = function () {\n        if (!this._paused) {\n            this._pauseStart = getTime();\n            this._paused = true;\n        }\n    };\n    Animation.prototype.resume = function () {\n        if (this._paused) {\n            this._pausedTime += getTime() - this._pauseStart;\n            this._paused = false;\n        }\n    };\n    Animation.prototype.clear = function () {\n        var clip = this._head;\n        while (clip) {\n            var nextClip = clip.next;\n            clip.prev = clip.next = clip.animation = null;\n            clip = nextClip;\n        }\n        this._head = this._tail = null;\n    };\n    Animation.prototype.isFinished = function () {\n        return this._head == null;\n    };\n    Animation.prototype.animate = function (target, options) {\n        options = options || {};\n        this.start();\n        var animator = new Animator(target, options.loop);\n        this.addAnimator(animator);\n        return animator;\n    };\n    return Animation;\n}(Eventful));\nexport default Animation;\n", "import { __extends } from \"tslib\";\nimport { addEventListener, removeEventListener, normalizeEvent, getNativeEvent } from '../core/event.js';\nimport * as zrUtil from '../core/util.js';\nimport Eventful from '../core/Eventful.js';\nimport env from '../core/env.js';\nvar TOUCH_CLICK_DELAY = 300;\nvar globalEventSupported = env.domSupported;\nvar localNativeListenerNames = (function () {\n    var mouseHandlerNames = [\n        'click', 'dblclick', 'mousewheel', 'wheel', 'mouseout',\n        'mouseup', 'mousedown', 'mousemove', 'contextmenu'\n    ];\n    var touchHandlerNames = [\n        'touchstart', 'touchend', 'touchmove'\n    ];\n    var pointerEventNameMap = {\n        pointerdown: 1, pointerup: 1, pointermove: 1, pointerout: 1\n    };\n    var pointerHandlerNames = zrUtil.map(mouseHandlerNames, function (name) {\n        var nm = name.replace('mouse', 'pointer');\n        return pointerEventNameMap.hasOwnProperty(nm) ? nm : name;\n    });\n    return {\n        mouse: mouseHandlerNames,\n        touch: touchHandlerNames,\n        pointer: pointerHandlerNames\n    };\n})();\nvar globalNativeListenerNames = {\n    mouse: ['mousemove', 'mouseup'],\n    pointer: ['pointermove', 'pointerup']\n};\nvar wheelEventSupported = false;\nfunction isPointerFromTouch(event) {\n    var pointerType = event.pointerType;\n    return pointerType === 'pen' || pointerType === 'touch';\n}\nfunction setTouchTimer(scope) {\n    scope.touching = true;\n    if (scope.touchTimer != null) {\n        clearTimeout(scope.touchTimer);\n        scope.touchTimer = null;\n    }\n    scope.touchTimer = setTimeout(function () {\n        scope.touching = false;\n        scope.touchTimer = null;\n    }, 700);\n}\nfunction markTouch(event) {\n    event && (event.zrByTouch = true);\n}\nfunction normalizeGlobalEvent(instance, event) {\n    return normalizeEvent(instance.dom, new FakeGlobalEvent(instance, event), true);\n}\nfunction isLocalEl(instance, el) {\n    var elTmp = el;\n    var isLocal = false;\n    while (elTmp && elTmp.nodeType !== 9\n        && !(isLocal = elTmp.domBelongToZr\n            || (elTmp !== el && elTmp === instance.painterRoot))) {\n        elTmp = elTmp.parentNode;\n    }\n    return isLocal;\n}\nvar FakeGlobalEvent = (function () {\n    function FakeGlobalEvent(instance, event) {\n        this.stopPropagation = zrUtil.noop;\n        this.stopImmediatePropagation = zrUtil.noop;\n        this.preventDefault = zrUtil.noop;\n        this.type = event.type;\n        this.target = this.currentTarget = instance.dom;\n        this.pointerType = event.pointerType;\n        this.clientX = event.clientX;\n        this.clientY = event.clientY;\n    }\n    return FakeGlobalEvent;\n}());\nvar localDOMHandlers = {\n    mousedown: function (event) {\n        event = normalizeEvent(this.dom, event);\n        this.__mayPointerCapture = [event.zrX, event.zrY];\n        this.trigger('mousedown', event);\n    },\n    mousemove: function (event) {\n        event = normalizeEvent(this.dom, event);\n        var downPoint = this.__mayPointerCapture;\n        if (downPoint && (event.zrX !== downPoint[0] || event.zrY !== downPoint[1])) {\n            this.__togglePointerCapture(true);\n        }\n        this.trigger('mousemove', event);\n    },\n    mouseup: function (event) {\n        event = normalizeEvent(this.dom, event);\n        this.__togglePointerCapture(false);\n        this.trigger('mouseup', event);\n    },\n    mouseout: function (event) {\n        event = normalizeEvent(this.dom, event);\n        var element = event.toElement || event.relatedTarget;\n        if (!isLocalEl(this, element)) {\n            if (this.__pointerCapturing) {\n                event.zrEventControl = 'no_globalout';\n            }\n            this.trigger('mouseout', event);\n        }\n    },\n    wheel: function (event) {\n        wheelEventSupported = true;\n        event = normalizeEvent(this.dom, event);\n        this.trigger('mousewheel', event);\n    },\n    mousewheel: function (event) {\n        if (wheelEventSupported) {\n            return;\n        }\n        event = normalizeEvent(this.dom, event);\n        this.trigger('mousewheel', event);\n    },\n    touchstart: function (event) {\n        event = normalizeEvent(this.dom, event);\n        markTouch(event);\n        this.__lastTouchMoment = new Date();\n        this.handler.processGesture(event, 'start');\n        localDOMHandlers.mousemove.call(this, event);\n        localDOMHandlers.mousedown.call(this, event);\n    },\n    touchmove: function (event) {\n        event = normalizeEvent(this.dom, event);\n        markTouch(event);\n        this.handler.processGesture(event, 'change');\n        localDOMHandlers.mousemove.call(this, event);\n    },\n    touchend: function (event) {\n        event = normalizeEvent(this.dom, event);\n        markTouch(event);\n        this.handler.processGesture(event, 'end');\n        localDOMHandlers.mouseup.call(this, event);\n        if (+new Date() - (+this.__lastTouchMoment) < TOUCH_CLICK_DELAY) {\n            localDOMHandlers.click.call(this, event);\n        }\n    },\n    pointerdown: function (event) {\n        localDOMHandlers.mousedown.call(this, event);\n    },\n    pointermove: function (event) {\n        if (!isPointerFromTouch(event)) {\n            localDOMHandlers.mousemove.call(this, event);\n        }\n    },\n    pointerup: function (event) {\n        localDOMHandlers.mouseup.call(this, event);\n    },\n    pointerout: function (event) {\n        if (!isPointerFromTouch(event)) {\n            localDOMHandlers.mouseout.call(this, event);\n        }\n    }\n};\nzrUtil.each(['click', 'dblclick', 'contextmenu'], function (name) {\n    localDOMHandlers[name] = function (event) {\n        event = normalizeEvent(this.dom, event);\n        this.trigger(name, event);\n    };\n});\nvar globalDOMHandlers = {\n    pointermove: function (event) {\n        if (!isPointerFromTouch(event)) {\n            globalDOMHandlers.mousemove.call(this, event);\n        }\n    },\n    pointerup: function (event) {\n        globalDOMHandlers.mouseup.call(this, event);\n    },\n    mousemove: function (event) {\n        this.trigger('mousemove', event);\n    },\n    mouseup: function (event) {\n        var pointerCaptureReleasing = this.__pointerCapturing;\n        this.__togglePointerCapture(false);\n        this.trigger('mouseup', event);\n        if (pointerCaptureReleasing) {\n            event.zrEventControl = 'only_globalout';\n            this.trigger('mouseout', event);\n        }\n    }\n};\nfunction mountLocalDOMEventListeners(instance, scope) {\n    var domHandlers = scope.domHandlers;\n    if (env.pointerEventsSupported) {\n        zrUtil.each(localNativeListenerNames.pointer, function (nativeEventName) {\n            mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n                domHandlers[nativeEventName].call(instance, event);\n            });\n        });\n    }\n    else {\n        if (env.touchEventsSupported) {\n            zrUtil.each(localNativeListenerNames.touch, function (nativeEventName) {\n                mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n                    domHandlers[nativeEventName].call(instance, event);\n                    setTouchTimer(scope);\n                });\n            });\n        }\n        zrUtil.each(localNativeListenerNames.mouse, function (nativeEventName) {\n            mountSingleDOMEventListener(scope, nativeEventName, function (event) {\n                event = getNativeEvent(event);\n                if (!scope.touching) {\n                    domHandlers[nativeEventName].call(instance, event);\n                }\n            });\n        });\n    }\n}\nfunction mountGlobalDOMEventListeners(instance, scope) {\n    if (env.pointerEventsSupported) {\n        zrUtil.each(globalNativeListenerNames.pointer, mount);\n    }\n    else if (!env.touchEventsSupported) {\n        zrUtil.each(globalNativeListenerNames.mouse, mount);\n    }\n    function mount(nativeEventName) {\n        function nativeEventListener(event) {\n            event = getNativeEvent(event);\n            if (!isLocalEl(instance, event.target)) {\n                event = normalizeGlobalEvent(instance, event);\n                scope.domHandlers[nativeEventName].call(instance, event);\n            }\n        }\n        mountSingleDOMEventListener(scope, nativeEventName, nativeEventListener, { capture: true });\n    }\n}\nfunction mountSingleDOMEventListener(scope, nativeEventName, listener, opt) {\n    scope.mounted[nativeEventName] = listener;\n    scope.listenerOpts[nativeEventName] = opt;\n    addEventListener(scope.domTarget, nativeEventName, listener, opt);\n}\nfunction unmountDOMEventListeners(scope) {\n    var mounted = scope.mounted;\n    for (var nativeEventName in mounted) {\n        if (mounted.hasOwnProperty(nativeEventName)) {\n            removeEventListener(scope.domTarget, nativeEventName, mounted[nativeEventName], scope.listenerOpts[nativeEventName]);\n        }\n    }\n    scope.mounted = {};\n}\nvar DOMHandlerScope = (function () {\n    function DOMHandlerScope(domTarget, domHandlers) {\n        this.mounted = {};\n        this.listenerOpts = {};\n        this.touching = false;\n        this.domTarget = domTarget;\n        this.domHandlers = domHandlers;\n    }\n    return DOMHandlerScope;\n}());\nvar HandlerDomProxy = (function (_super) {\n    __extends(HandlerDomProxy, _super);\n    function HandlerDomProxy(dom, painterRoot) {\n        var _this = _super.call(this) || this;\n        _this.__pointerCapturing = false;\n        _this.dom = dom;\n        _this.painterRoot = painterRoot;\n        _this._localHandlerScope = new DOMHandlerScope(dom, localDOMHandlers);\n        if (globalEventSupported) {\n            _this._globalHandlerScope = new DOMHandlerScope(document, globalDOMHandlers);\n        }\n        mountLocalDOMEventListeners(_this, _this._localHandlerScope);\n        return _this;\n    }\n    HandlerDomProxy.prototype.dispose = function () {\n        unmountDOMEventListeners(this._localHandlerScope);\n        if (globalEventSupported) {\n            unmountDOMEventListeners(this._globalHandlerScope);\n        }\n    };\n    HandlerDomProxy.prototype.setCursor = function (cursorStyle) {\n        this.dom.style && (this.dom.style.cursor = cursorStyle || 'default');\n    };\n    HandlerDomProxy.prototype.__togglePointerCapture = function (isPointerCapturing) {\n        this.__mayPointerCapture = null;\n        if (globalEventSupported\n            && ((+this.__pointerCapturing) ^ (+isPointerCapturing))) {\n            this.__pointerCapturing = isPointerCapturing;\n            var globalHandlerScope = this._globalHandlerScope;\n            isPointerCapturing\n                ? mountGlobalDOMEventListeners(this, globalHandlerScope)\n                : unmountDOMEventListeners(globalHandlerScope);\n        }\n    };\n    return HandlerDomProxy;\n}(Eventful));\nexport default HandlerDomProxy;\n", "function isSafeNum(num) {\n    return isFinite(num);\n}\nexport function createLinearGradient(ctx, obj, rect) {\n    var x = obj.x == null ? 0 : obj.x;\n    var x2 = obj.x2 == null ? 1 : obj.x2;\n    var y = obj.y == null ? 0 : obj.y;\n    var y2 = obj.y2 == null ? 0 : obj.y2;\n    if (!obj.global) {\n        x = x * rect.width + rect.x;\n        x2 = x2 * rect.width + rect.x;\n        y = y * rect.height + rect.y;\n        y2 = y2 * rect.height + rect.y;\n    }\n    x = isSafeNum(x) ? x : 0;\n    x2 = isSafeNum(x2) ? x2 : 1;\n    y = isSafeNum(y) ? y : 0;\n    y2 = isSafeNum(y2) ? y2 : 0;\n    var canvasGradient = ctx.createLinearGradient(x, y, x2, y2);\n    return canvasGradient;\n}\nexport function createRadialGradient(ctx, obj, rect) {\n    var width = rect.width;\n    var height = rect.height;\n    var min = Math.min(width, height);\n    var x = obj.x == null ? 0.5 : obj.x;\n    var y = obj.y == null ? 0.5 : obj.y;\n    var r = obj.r == null ? 0.5 : obj.r;\n    if (!obj.global) {\n        x = x * width + rect.x;\n        y = y * height + rect.y;\n        r = r * min;\n    }\n    x = isSafeNum(x) ? x : 0.5;\n    y = isSafeNum(y) ? y : 0.5;\n    r = r >= 0 && isSafeNum(r) ? r : 0.5;\n    var canvasGradient = ctx.createRadialGradient(x, y, 0, x, y, r);\n    return canvasGradient;\n}\nexport function getCanvasGradient(ctx, obj, rect) {\n    var canvasGradient = obj.type === 'radial'\n        ? createRadialGradient(ctx, obj, rect)\n        : createLinearGradient(ctx, obj, rect);\n    var colorStops = obj.colorStops;\n    for (var i = 0; i < colorStops.length; i++) {\n        canvasGradient.addColorStop(colorStops[i].offset, colorStops[i].color);\n    }\n    return canvasGradient;\n}\nexport function isClipPathChanged(clipPaths, prevClipPaths) {\n    if (clipPaths === prevClipPaths || (!clipPaths && !prevClipPaths)) {\n        return false;\n    }\n    if (!clipPaths || !prevClipPaths || (clipPaths.length !== prevClipPaths.length)) {\n        return true;\n    }\n    for (var i = 0; i < clipPaths.length; i++) {\n        if (clipPaths[i] !== prevClipPaths[i]) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction parseInt10(val) {\n    return parseInt(val, 10);\n}\nexport function getSize(root, whIdx, opts) {\n    var wh = ['width', 'height'][whIdx];\n    var cwh = ['clientWidth', 'clientHeight'][whIdx];\n    var plt = ['paddingLeft', 'paddingTop'][whIdx];\n    var prb = ['paddingRight', 'paddingBottom'][whIdx];\n    if (opts[wh] != null && opts[wh] !== 'auto') {\n        return parseFloat(opts[wh]);\n    }\n    var stl = document.defaultView.getComputedStyle(root);\n    return ((root[cwh] || parseInt10(stl[wh]) || parseInt10(root.style[wh]))\n        - (parseInt10(stl[plt]) || 0)\n        - (parseInt10(stl[prb]) || 0)) | 0;\n}\n", "import { isArray, isNumber, map } from '../core/util.js';\nexport function normalizeLineDash(lineType, lineWidth) {\n    if (!lineType || lineType === 'solid' || !(lineWidth > 0)) {\n        return null;\n    }\n    return lineType === 'dashed'\n        ? [4 * lineWidth, 2 * lineWidth]\n        : lineType === 'dotted'\n            ? [lineWidth]\n            : isNumber(lineType)\n                ? [lineType] : isArray(lineType) ? lineType : null;\n}\nexport function getLineDash(el) {\n    var style = el.style;\n    var lineDash = style.lineDash && style.lineWidth > 0 && normalizeLineDash(style.lineDash, style.lineWidth);\n    var lineDashOffset = style.lineDashOffset;\n    if (lineDash) {\n        var lineScale_1 = (style.strokeNoScale && el.getLineScale) ? el.getLineScale() : 1;\n        if (lineScale_1 && lineScale_1 !== 1) {\n            lineDash = map(lineDash, function (rawVal) {\n                return rawVal / lineScale_1;\n            });\n            lineDashOffset /= lineScale_1;\n        }\n    }\n    return [lineDash, lineDashOffset];\n}\n", "import { DEFAULT_COMMON_STYLE } from '../graphic/Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { createOrUpdateImage, isImageReady } from '../graphic/helper/image.js';\nimport { getCanvasGradient, isClipPathChanged } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { RADIAN_TO_DEGREE } from '../core/util.js';\nimport { getLineDash } from './dashStyle.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT } from '../graphic/constants.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nvar pathProxyForDraw = new PathProxy(true);\nfunction styleHasStroke(style) {\n    var stroke = style.stroke;\n    return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n}\nfunction isValidStrokeFillStyle(strokeOrFill) {\n    return typeof strokeOrFill === 'string' && strokeOrFill !== 'none';\n}\nfunction styleHasFill(style) {\n    var fill = style.fill;\n    return fill != null && fill !== 'none';\n}\nfunction doFillPath(ctx, style) {\n    if (style.fillOpacity != null && style.fillOpacity !== 1) {\n        var originalGlobalAlpha = ctx.globalAlpha;\n        ctx.globalAlpha = style.fillOpacity * style.opacity;\n        ctx.fill();\n        ctx.globalAlpha = originalGlobalAlpha;\n    }\n    else {\n        ctx.fill();\n    }\n}\nfunction doStrokePath(ctx, style) {\n    if (style.strokeOpacity != null && style.strokeOpacity !== 1) {\n        var originalGlobalAlpha = ctx.globalAlpha;\n        ctx.globalAlpha = style.strokeOpacity * style.opacity;\n        ctx.stroke();\n        ctx.globalAlpha = originalGlobalAlpha;\n    }\n    else {\n        ctx.stroke();\n    }\n}\nexport function createCanvasPattern(ctx, pattern, el) {\n    var image = createOrUpdateImage(pattern.image, pattern.__image, el);\n    if (isImageReady(image)) {\n        var canvasPattern = ctx.createPattern(image, pattern.repeat || 'repeat');\n        if (typeof DOMMatrix === 'function'\n            && canvasPattern\n            && canvasPattern.setTransform) {\n            var matrix = new DOMMatrix();\n            matrix.translateSelf((pattern.x || 0), (pattern.y || 0));\n            matrix.rotateSelf(0, 0, (pattern.rotation || 0) * RADIAN_TO_DEGREE);\n            matrix.scaleSelf((pattern.scaleX || 1), (pattern.scaleY || 1));\n            canvasPattern.setTransform(matrix);\n        }\n        return canvasPattern;\n    }\n}\nfunction brushPath(ctx, el, style, inBatch) {\n    var _a;\n    var hasStroke = styleHasStroke(style);\n    var hasFill = styleHasFill(style);\n    var strokePercent = style.strokePercent;\n    var strokePart = strokePercent < 1;\n    var firstDraw = !el.path;\n    if ((!el.silent || strokePart) && firstDraw) {\n        el.createPathProxy();\n    }\n    var path = el.path || pathProxyForDraw;\n    var dirtyFlag = el.__dirty;\n    if (!inBatch) {\n        var fill = style.fill;\n        var stroke = style.stroke;\n        var hasFillGradient = hasFill && !!fill.colorStops;\n        var hasStrokeGradient = hasStroke && !!stroke.colorStops;\n        var hasFillPattern = hasFill && !!fill.image;\n        var hasStrokePattern = hasStroke && !!stroke.image;\n        var fillGradient = void 0;\n        var strokeGradient = void 0;\n        var fillPattern = void 0;\n        var strokePattern = void 0;\n        var rect = void 0;\n        if (hasFillGradient || hasStrokeGradient) {\n            rect = el.getBoundingRect();\n        }\n        if (hasFillGradient) {\n            fillGradient = dirtyFlag\n                ? getCanvasGradient(ctx, fill, rect)\n                : el.__canvasFillGradient;\n            el.__canvasFillGradient = fillGradient;\n        }\n        if (hasStrokeGradient) {\n            strokeGradient = dirtyFlag\n                ? getCanvasGradient(ctx, stroke, rect)\n                : el.__canvasStrokeGradient;\n            el.__canvasStrokeGradient = strokeGradient;\n        }\n        if (hasFillPattern) {\n            fillPattern = (dirtyFlag || !el.__canvasFillPattern)\n                ? createCanvasPattern(ctx, fill, el)\n                : el.__canvasFillPattern;\n            el.__canvasFillPattern = fillPattern;\n        }\n        if (hasStrokePattern) {\n            strokePattern = (dirtyFlag || !el.__canvasStrokePattern)\n                ? createCanvasPattern(ctx, stroke, el)\n                : el.__canvasStrokePattern;\n            el.__canvasStrokePattern = fillPattern;\n        }\n        if (hasFillGradient) {\n            ctx.fillStyle = fillGradient;\n        }\n        else if (hasFillPattern) {\n            if (fillPattern) {\n                ctx.fillStyle = fillPattern;\n            }\n            else {\n                hasFill = false;\n            }\n        }\n        if (hasStrokeGradient) {\n            ctx.strokeStyle = strokeGradient;\n        }\n        else if (hasStrokePattern) {\n            if (strokePattern) {\n                ctx.strokeStyle = strokePattern;\n            }\n            else {\n                hasStroke = false;\n            }\n        }\n    }\n    var scale = el.getGlobalScale();\n    path.setScale(scale[0], scale[1], el.segmentIgnoreThreshold);\n    var lineDash;\n    var lineDashOffset;\n    if (ctx.setLineDash && style.lineDash) {\n        _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n    }\n    var needsRebuild = true;\n    if (firstDraw || (dirtyFlag & SHAPE_CHANGED_BIT)) {\n        path.setDPR(ctx.dpr);\n        if (strokePart) {\n            path.setContext(null);\n        }\n        else {\n            path.setContext(ctx);\n            needsRebuild = false;\n        }\n        path.reset();\n        el.buildPath(path, el.shape, inBatch);\n        path.toStatic();\n        el.pathUpdated();\n    }\n    if (needsRebuild) {\n        path.rebuildPath(ctx, strokePart ? strokePercent : 1);\n    }\n    if (lineDash) {\n        ctx.setLineDash(lineDash);\n        ctx.lineDashOffset = lineDashOffset;\n    }\n    if (!inBatch) {\n        if (style.strokeFirst) {\n            if (hasStroke) {\n                doStrokePath(ctx, style);\n            }\n            if (hasFill) {\n                doFillPath(ctx, style);\n            }\n        }\n        else {\n            if (hasFill) {\n                doFillPath(ctx, style);\n            }\n            if (hasStroke) {\n                doStrokePath(ctx, style);\n            }\n        }\n    }\n    if (lineDash) {\n        ctx.setLineDash([]);\n    }\n}\nfunction brushImage(ctx, el, style) {\n    var image = el.__image = createOrUpdateImage(style.image, el.__image, el, el.onload);\n    if (!image || !isImageReady(image)) {\n        return;\n    }\n    var x = style.x || 0;\n    var y = style.y || 0;\n    var width = el.getWidth();\n    var height = el.getHeight();\n    var aspect = image.width / image.height;\n    if (width == null && height != null) {\n        width = height * aspect;\n    }\n    else if (height == null && width != null) {\n        height = width / aspect;\n    }\n    else if (width == null && height == null) {\n        width = image.width;\n        height = image.height;\n    }\n    if (style.sWidth && style.sHeight) {\n        var sx = style.sx || 0;\n        var sy = style.sy || 0;\n        ctx.drawImage(image, sx, sy, style.sWidth, style.sHeight, x, y, width, height);\n    }\n    else if (style.sx && style.sy) {\n        var sx = style.sx;\n        var sy = style.sy;\n        var sWidth = width - sx;\n        var sHeight = height - sy;\n        ctx.drawImage(image, sx, sy, sWidth, sHeight, x, y, width, height);\n    }\n    else {\n        ctx.drawImage(image, x, y, width, height);\n    }\n}\nfunction brushText(ctx, el, style) {\n    var _a;\n    var text = style.text;\n    text != null && (text += '');\n    if (text) {\n        ctx.font = style.font || DEFAULT_FONT;\n        ctx.textAlign = style.textAlign;\n        ctx.textBaseline = style.textBaseline;\n        var lineDash = void 0;\n        var lineDashOffset = void 0;\n        if (ctx.setLineDash && style.lineDash) {\n            _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n        }\n        if (lineDash) {\n            ctx.setLineDash(lineDash);\n            ctx.lineDashOffset = lineDashOffset;\n        }\n        if (style.strokeFirst) {\n            if (styleHasStroke(style)) {\n                ctx.strokeText(text, style.x, style.y);\n            }\n            if (styleHasFill(style)) {\n                ctx.fillText(text, style.x, style.y);\n            }\n        }\n        else {\n            if (styleHasFill(style)) {\n                ctx.fillText(text, style.x, style.y);\n            }\n            if (styleHasStroke(style)) {\n                ctx.strokeText(text, style.x, style.y);\n            }\n        }\n        if (lineDash) {\n            ctx.setLineDash([]);\n        }\n    }\n}\nvar SHADOW_NUMBER_PROPS = ['shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nvar STROKE_PROPS = [\n    ['lineCap', 'butt'], ['lineJoin', 'miter'], ['miterLimit', 10]\n];\nfunction bindCommonProps(ctx, style, prevStyle, forceSetAll, scope) {\n    var styleChanged = false;\n    if (!forceSetAll) {\n        prevStyle = prevStyle || {};\n        if (style === prevStyle) {\n            return false;\n        }\n    }\n    if (forceSetAll || style.opacity !== prevStyle.opacity) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n        var opacity = Math.max(Math.min(style.opacity, 1), 0);\n        ctx.globalAlpha = isNaN(opacity) ? DEFAULT_COMMON_STYLE.opacity : opacity;\n    }\n    if (forceSetAll || style.blend !== prevStyle.blend) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        ctx.globalCompositeOperation = style.blend || DEFAULT_COMMON_STYLE.blend;\n    }\n    for (var i = 0; i < SHADOW_NUMBER_PROPS.length; i++) {\n        var propName = SHADOW_NUMBER_PROPS[i];\n        if (forceSetAll || style[propName] !== prevStyle[propName]) {\n            if (!styleChanged) {\n                flushPathDrawn(ctx, scope);\n                styleChanged = true;\n            }\n            ctx[propName] = ctx.dpr * (style[propName] || 0);\n        }\n    }\n    if (forceSetAll || style.shadowColor !== prevStyle.shadowColor) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        ctx.shadowColor = style.shadowColor || DEFAULT_COMMON_STYLE.shadowColor;\n    }\n    return styleChanged;\n}\nfunction bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetAll, scope) {\n    var style = getStyle(el, scope.inHover);\n    var prevStyle = forceSetAll\n        ? null\n        : (prevEl && getStyle(prevEl, scope.inHover) || {});\n    if (style === prevStyle) {\n        return false;\n    }\n    var styleChanged = bindCommonProps(ctx, style, prevStyle, forceSetAll, scope);\n    if (forceSetAll || style.fill !== prevStyle.fill) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        isValidStrokeFillStyle(style.fill) && (ctx.fillStyle = style.fill);\n    }\n    if (forceSetAll || style.stroke !== prevStyle.stroke) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        isValidStrokeFillStyle(style.stroke) && (ctx.strokeStyle = style.stroke);\n    }\n    if (forceSetAll || style.opacity !== prevStyle.opacity) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        ctx.globalAlpha = style.opacity == null ? 1 : style.opacity;\n    }\n    if (el.hasStroke()) {\n        var lineWidth = style.lineWidth;\n        var newLineWidth = lineWidth / ((style.strokeNoScale && el.getLineScale) ? el.getLineScale() : 1);\n        if (ctx.lineWidth !== newLineWidth) {\n            if (!styleChanged) {\n                flushPathDrawn(ctx, scope);\n                styleChanged = true;\n            }\n            ctx.lineWidth = newLineWidth;\n        }\n    }\n    for (var i = 0; i < STROKE_PROPS.length; i++) {\n        var prop = STROKE_PROPS[i];\n        var propName = prop[0];\n        if (forceSetAll || style[propName] !== prevStyle[propName]) {\n            if (!styleChanged) {\n                flushPathDrawn(ctx, scope);\n                styleChanged = true;\n            }\n            ctx[propName] = style[propName] || prop[1];\n        }\n    }\n    return styleChanged;\n}\nfunction bindImageStyle(ctx, el, prevEl, forceSetAll, scope) {\n    return bindCommonProps(ctx, getStyle(el, scope.inHover), prevEl && getStyle(prevEl, scope.inHover), forceSetAll, scope);\n}\nfunction setContextTransform(ctx, el) {\n    var m = el.transform;\n    var dpr = ctx.dpr || 1;\n    if (m) {\n        ctx.setTransform(dpr * m[0], dpr * m[1], dpr * m[2], dpr * m[3], dpr * m[4], dpr * m[5]);\n    }\n    else {\n        ctx.setTransform(dpr, 0, 0, dpr, 0, 0);\n    }\n}\nfunction updateClipStatus(clipPaths, ctx, scope) {\n    var allClipped = false;\n    for (var i = 0; i < clipPaths.length; i++) {\n        var clipPath = clipPaths[i];\n        allClipped = allClipped || clipPath.isZeroArea();\n        setContextTransform(ctx, clipPath);\n        ctx.beginPath();\n        clipPath.buildPath(ctx, clipPath.shape);\n        ctx.clip();\n    }\n    scope.allClipped = allClipped;\n}\nfunction isTransformChanged(m0, m1) {\n    if (m0 && m1) {\n        return m0[0] !== m1[0]\n            || m0[1] !== m1[1]\n            || m0[2] !== m1[2]\n            || m0[3] !== m1[3]\n            || m0[4] !== m1[4]\n            || m0[5] !== m1[5];\n    }\n    else if (!m0 && !m1) {\n        return false;\n    }\n    return true;\n}\nvar DRAW_TYPE_PATH = 1;\nvar DRAW_TYPE_IMAGE = 2;\nvar DRAW_TYPE_TEXT = 3;\nvar DRAW_TYPE_INCREMENTAL = 4;\nfunction canPathBatch(style) {\n    var hasFill = styleHasFill(style);\n    var hasStroke = styleHasStroke(style);\n    return !(style.lineDash\n        || !(+hasFill ^ +hasStroke)\n        || (hasFill && typeof style.fill !== 'string')\n        || (hasStroke && typeof style.stroke !== 'string')\n        || style.strokePercent < 1\n        || style.strokeOpacity < 1\n        || style.fillOpacity < 1);\n}\nfunction flushPathDrawn(ctx, scope) {\n    scope.batchFill && ctx.fill();\n    scope.batchStroke && ctx.stroke();\n    scope.batchFill = '';\n    scope.batchStroke = '';\n}\nfunction getStyle(el, inHover) {\n    return inHover ? (el.__hoverStyle || el.style) : el.style;\n}\nexport function brushSingle(ctx, el) {\n    brush(ctx, el, { inHover: false, viewWidth: 0, viewHeight: 0 }, true);\n}\nexport function brush(ctx, el, scope, isLast) {\n    var m = el.transform;\n    if (!el.shouldBePainted(scope.viewWidth, scope.viewHeight, false, false)) {\n        el.__dirty &= ~REDRAW_BIT;\n        el.__isRendered = false;\n        return;\n    }\n    var clipPaths = el.__clipPaths;\n    var prevElClipPaths = scope.prevElClipPaths;\n    var forceSetTransform = false;\n    var forceSetStyle = false;\n    if (!prevElClipPaths || isClipPathChanged(clipPaths, prevElClipPaths)) {\n        if (prevElClipPaths && prevElClipPaths.length) {\n            flushPathDrawn(ctx, scope);\n            ctx.restore();\n            forceSetStyle = forceSetTransform = true;\n            scope.prevElClipPaths = null;\n            scope.allClipped = false;\n            scope.prevEl = null;\n        }\n        if (clipPaths && clipPaths.length) {\n            flushPathDrawn(ctx, scope);\n            ctx.save();\n            updateClipStatus(clipPaths, ctx, scope);\n            forceSetTransform = true;\n        }\n        scope.prevElClipPaths = clipPaths;\n    }\n    if (scope.allClipped) {\n        el.__isRendered = false;\n        return;\n    }\n    el.beforeBrush && el.beforeBrush();\n    el.innerBeforeBrush();\n    var prevEl = scope.prevEl;\n    if (!prevEl) {\n        forceSetStyle = forceSetTransform = true;\n    }\n    var canBatchPath = el instanceof Path\n        && el.autoBatch\n        && canPathBatch(el.style);\n    if (forceSetTransform || isTransformChanged(m, prevEl.transform)) {\n        flushPathDrawn(ctx, scope);\n        setContextTransform(ctx, el);\n    }\n    else if (!canBatchPath) {\n        flushPathDrawn(ctx, scope);\n    }\n    var style = getStyle(el, scope.inHover);\n    if (el instanceof Path) {\n        if (scope.lastDrawType !== DRAW_TYPE_PATH) {\n            forceSetStyle = true;\n            scope.lastDrawType = DRAW_TYPE_PATH;\n        }\n        bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n        if (!canBatchPath || (!scope.batchFill && !scope.batchStroke)) {\n            ctx.beginPath();\n        }\n        brushPath(ctx, el, style, canBatchPath);\n        if (canBatchPath) {\n            scope.batchFill = style.fill || '';\n            scope.batchStroke = style.stroke || '';\n        }\n    }\n    else {\n        if (el instanceof TSpan) {\n            if (scope.lastDrawType !== DRAW_TYPE_TEXT) {\n                forceSetStyle = true;\n                scope.lastDrawType = DRAW_TYPE_TEXT;\n            }\n            bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n            brushText(ctx, el, style);\n        }\n        else if (el instanceof ZRImage) {\n            if (scope.lastDrawType !== DRAW_TYPE_IMAGE) {\n                forceSetStyle = true;\n                scope.lastDrawType = DRAW_TYPE_IMAGE;\n            }\n            bindImageStyle(ctx, el, prevEl, forceSetStyle, scope);\n            brushImage(ctx, el, style);\n        }\n        else if (el.getTemporalDisplayables) {\n            if (scope.lastDrawType !== DRAW_TYPE_INCREMENTAL) {\n                forceSetStyle = true;\n                scope.lastDrawType = DRAW_TYPE_INCREMENTAL;\n            }\n            brushIncremental(ctx, el, scope);\n        }\n    }\n    if (canBatchPath && isLast) {\n        flushPathDrawn(ctx, scope);\n    }\n    el.innerAfterBrush();\n    el.afterBrush && el.afterBrush();\n    scope.prevEl = el;\n    el.__dirty = 0;\n    el.__isRendered = true;\n}\nfunction brushIncremental(ctx, el, scope) {\n    var displayables = el.getDisplayables();\n    var temporalDisplayables = el.getTemporalDisplayables();\n    ctx.save();\n    var innerScope = {\n        prevElClipPaths: null,\n        prevEl: null,\n        allClipped: false,\n        viewWidth: scope.viewWidth,\n        viewHeight: scope.viewHeight,\n        inHover: scope.inHover\n    };\n    var i;\n    var len;\n    for (i = el.getCursor(), len = displayables.length; i < len; i++) {\n        var displayable = displayables[i];\n        displayable.beforeBrush && displayable.beforeBrush();\n        displayable.innerBeforeBrush();\n        brush(ctx, displayable, innerScope, i === len - 1);\n        displayable.innerAfterBrush();\n        displayable.afterBrush && displayable.afterBrush();\n        innerScope.prevEl = displayable;\n    }\n    for (var i_1 = 0, len_1 = temporalDisplayables.length; i_1 < len_1; i_1++) {\n        var displayable = temporalDisplayables[i_1];\n        displayable.beforeBrush && displayable.beforeBrush();\n        displayable.innerBeforeBrush();\n        brush(ctx, displayable, innerScope, i_1 === len_1 - 1);\n        displayable.innerAfterBrush();\n        displayable.afterBrush && displayable.afterBrush();\n        innerScope.prevEl = displayable;\n    }\n    el.clearTemporalDisplayables();\n    el.notClear = true;\n    ctx.restore();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI,QAAS,2BAAY;AACrB,WAASA,OAAM,QAAQ,GAAG;AACtB,SAAK,SAAS;AACd,SAAK,YAAY,KAAK,EAAE;AAAA,EAC5B;AACA,SAAOA;AACX,EAAE;AACF,IAAI,YAAa,WAAY;AACzB,WAASC,WAAU,SAAS;AACxB,SAAK,UAAU;AACf,YAAQ,GAAG,aAAa,KAAK,YAAY,IAAI;AAC7C,YAAQ,GAAG,aAAa,KAAK,OAAO,IAAI;AACxC,YAAQ,GAAG,WAAW,KAAK,UAAU,IAAI;AAAA,EAC7C;AACA,EAAAA,WAAU,UAAU,aAAa,SAAU,GAAG;AAC1C,QAAI,iBAAiB,EAAE;AACvB,WAAO,kBAAkB,CAAC,eAAe,WAAW;AAChD,uBAAiB,eAAe,UAAU,eAAe;AAAA,IAC7D;AACA,QAAI,gBAAgB;AAChB,WAAK,kBAAkB;AACvB,qBAAe,WAAW;AAC1B,WAAK,KAAK,EAAE;AACZ,WAAK,KAAK,EAAE;AACZ,WAAK,QAAQ,kBAAkB,IAAI,MAAM,gBAAgB,CAAC,GAAG,aAAa,EAAE,KAAK;AAAA,IACrF;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,QAAQ,SAAU,GAAG;AACrC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,gBAAgB;AAChB,UAAI,IAAI,EAAE;AACV,UAAI,IAAI,EAAE;AACV,UAAI,KAAK,IAAI,KAAK;AAClB,UAAI,KAAK,IAAI,KAAK;AAClB,WAAK,KAAK;AACV,WAAK,KAAK;AACV,qBAAe,MAAM,IAAI,IAAI,CAAC;AAC9B,WAAK,QAAQ,kBAAkB,IAAI,MAAM,gBAAgB,CAAC,GAAG,QAAQ,EAAE,KAAK;AAC5E,UAAI,aAAa,KAAK,QAAQ,UAAU,GAAG,GAAG,cAAc,EAAE;AAC9D,UAAI,iBAAiB,KAAK;AAC1B,WAAK,cAAc;AACnB,UAAI,mBAAmB,YAAY;AAC/B,YAAI,kBAAkB,eAAe,gBAAgB;AACjD,eAAK,QAAQ,kBAAkB,IAAI,MAAM,gBAAgB,CAAC,GAAG,aAAa,EAAE,KAAK;AAAA,QACrF;AACA,YAAI,cAAc,eAAe,gBAAgB;AAC7C,eAAK,QAAQ,kBAAkB,IAAI,MAAM,YAAY,CAAC,GAAG,aAAa,EAAE,KAAK;AAAA,QACjF;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,WAAW,SAAU,GAAG;AACxC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,gBAAgB;AAChB,qBAAe,WAAW;AAAA,IAC9B;AACA,SAAK,QAAQ,kBAAkB,IAAI,MAAM,gBAAgB,CAAC,GAAG,WAAW,EAAE,KAAK;AAC/E,QAAI,KAAK,aAAa;AAClB,WAAK,QAAQ,kBAAkB,IAAI,MAAM,KAAK,aAAa,CAAC,GAAG,QAAQ,EAAE,KAAK;AAAA,IAClF;AACA,SAAK,kBAAkB;AACvB,SAAK,cAAc;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AACF,IAAO,oBAAQ;;;AC9Df,IAAI,kBAAkB;AACtB,IAAI,WAAW,CAAC;AAChB,IAAI,4BAA4B,YAAI,QAAQ,WACrC,CAAC,YAAI,QAAQ,QAAQ,MAAM,GAAG,EAAE,CAAC,IAAI;AACrC,SAAS,cAAc,IAAI,GAAG,KAAK,WAAW;AACjD,QAAM,OAAO,CAAC;AACd,MAAI,WAAW;AACX,kBAAc,IAAI,GAAG,GAAG;AAAA,EAC5B,WACS,6BACF,EAAE,UAAU,QACZ,EAAE,WAAW,EAAE,SAAS;AAC3B,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE;AAAA,EAChB,WACS,EAAE,WAAW,MAAM;AACxB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE;AAAA,EAChB,OACK;AACD,kBAAc,IAAI,GAAG,GAAG;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,cAAc,IAAI,GAAG,KAAK;AAC/B,MAAI,YAAI,gBAAgB,GAAG,uBAAuB;AAC9C,QAAI,KAAK,EAAE;AACX,QAAI,KAAK,EAAE;AACX,QAAI,WAAW,EAAE,GAAG;AAChB,UAAI,MAAM,GAAG,sBAAsB;AACnC,UAAI,MAAM,KAAK,IAAI;AACnB,UAAI,MAAM,KAAK,IAAI;AACnB;AAAA,IACJ,OACK;AACD,UAAI,2BAA2B,UAAU,IAAI,IAAI,EAAE,GAAG;AAClD,YAAI,MAAM,SAAS,CAAC;AACpB,YAAI,MAAM,SAAS,CAAC;AACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,MAAM,IAAI,MAAM;AACxB;AACO,SAAS,eAAe,GAAG;AAC9B,SAAO,KACA,OAAO;AAClB;AACO,SAAS,eAAe,IAAI,GAAG,WAAW;AAC7C,MAAI,eAAe,CAAC;AACpB,MAAI,EAAE,OAAO,MAAM;AACf,WAAO;AAAA,EACX;AACA,MAAI,YAAY,EAAE;AAClB,MAAI,UAAU,aAAa,UAAU,QAAQ,OAAO,KAAK;AACzD,MAAI,CAAC,SAAS;AACV,kBAAc,IAAI,GAAG,GAAG,SAAS;AACjC,QAAI,aAAa,yBAAyB,CAAC;AAC3C,MAAE,UAAU,aAAa,aAAa,MAAM,EAAE,EAAE,UAAU,KAAK;AAAA,EACnE,OACK;AACD,QAAI,QAAQ,cAAc,aACpB,EAAE,cAAc,CAAC,IACjB,EAAE,eAAe,CAAC;AACxB,aAAS,cAAc,IAAI,OAAO,GAAG,SAAS;AAAA,EAClD;AACA,MAAI,SAAS,EAAE;AACf,MAAI,EAAE,SAAS,QAAQ,WAAW,UAAa,gBAAgB,KAAK,EAAE,IAAI,GAAG;AACzE,MAAE,QAAS,SAAS,IAAI,IAAK,SAAS,IAAI,IAAK,SAAS,IAAI,IAAI;AAAA,EACpE;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,GAAG;AACjC,MAAI,gBAAgB,EAAE;AACtB,MAAI,eAAe;AACf,WAAO;AAAA,EACX;AACA,MAAI,SAAS,EAAE;AACf,MAAI,SAAS,EAAE;AACf,MAAI,UAAU,QAAQ,UAAU,MAAM;AAClC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,WAAW,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AAC7D,MAAI,OAAO,SAAS,IAAI,KAClB,SAAS,IAAI,IACT,SAAS,IAAI,KACT;AACd,SAAO,IAAI,QAAQ;AACvB;AACO,SAAS,iBAAiB,IAAI,MAAM,SAAS,KAAK;AACrD,KAAG,iBAAiB,MAAM,SAAS,GAAG;AAC1C;AACO,SAAS,oBAAoB,IAAI,MAAM,SAAS,KAAK;AACxD,KAAG,oBAAoB,MAAM,SAAS,GAAG;AAC7C;AACO,IAAI,OAAO,SAAU,GAAG;AAC3B,IAAE,eAAe;AACjB,IAAE,gBAAgB;AAClB,IAAE,eAAe;AACrB;AACO,SAAS,mCAAmC,GAAG;AAClD,SAAO,EAAE,UAAU,KAAK,EAAE,UAAU;AACxC;;;ACxGA,IAAI,aAAc,WAAY;AAC1B,WAASC,cAAa;AAClB,SAAK,SAAS,CAAC;AAAA,EACnB;AACA,EAAAA,YAAW,UAAU,YAAY,SAAU,OAAO,QAAQ,MAAM;AAC5D,SAAK,SAAS,OAAO,QAAQ,IAAI;AACjC,WAAO,KAAK,WAAW,KAAK;AAAA,EAChC;AACA,EAAAA,YAAW,UAAU,QAAQ,WAAY;AACrC,SAAK,OAAO,SAAS;AACrB,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,UAAU,WAAW,SAAU,OAAO,QAAQ,MAAM;AAC3D,QAAI,UAAU,MAAM;AACpB,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,QAAI,YAAY;AAAA,MACZ,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC;AAAA,MACV;AAAA,MACA;AAAA,IACJ;AACA,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAChD,UAAI,QAAQ,QAAQ,CAAC;AACrB,UAAI,MAAgB,cAAc,MAAM,OAAO,CAAC,CAAC;AACjD,gBAAU,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC;AACxC,gBAAU,QAAQ,KAAK,KAAK;AAAA,IAChC;AACA,SAAK,OAAO,KAAK,SAAS;AAAA,EAC9B;AACA,EAAAA,YAAW,UAAU,aAAa,SAAU,OAAO;AAC/C,aAAS,aAAa,aAAa;AAC/B,UAAI,YAAY,eAAe,SAAS,GAAG;AACvC,YAAI,cAAc,YAAY,SAAS,EAAE,KAAK,QAAQ,KAAK;AAC3D,YAAI,aAAa;AACb,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAOA;AACX,EAAE;AAEF,SAASC,MAAK,WAAW;AACrB,MAAI,KAAK,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC;AACzC,MAAI,KAAK,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC;AACzC,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACtC;AACA,SAAS,OAAO,WAAW;AACvB,SAAO;AAAA,KACF,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,KAAK;AAAA,KACrC,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,KAAK;AAAA,EAC1C;AACJ;AACA,IAAI,cAAc;AAAA,EACd,OAAO,SAAU,QAAQ,OAAO;AAC5B,QAAI,WAAW,OAAO;AACtB,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AACA,QAAI,YAAY,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG;AAC5C,QAAI,YAAY,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,UAAU;AACtD,QAAI,YACG,SAAS,SAAS,KAClB,YACA,SAAS,SAAS,GAAG;AACxB,UAAI,aAAaA,MAAK,QAAQ,IAAIA,MAAK,QAAQ;AAC/C,OAAC,SAAS,UAAU,MAAM,aAAa;AACvC,YAAM,aAAa;AACnB,UAAI,cAAc,OAAO,QAAQ;AACjC,YAAM,SAAS,YAAY,CAAC;AAC5B,YAAM,SAAS,YAAY,CAAC;AAC5B,aAAO;AAAA,QACH,MAAM;AAAA,QACN,QAAQ,OAAO,CAAC,EAAE;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACzEA,IAAI,SAAS;AACb,SAAS,gBAAgB,SAAS,YAAY,OAAO;AACjD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,QAAQ,WAAW;AAAA,IACnB,WAAW,WAAW;AAAA,IACtB,cAAc;AAAA,IACd,SAAS,MAAM;AAAA,IACf,SAAS,MAAM;AAAA,IACf,cAAc,MAAM;AAAA,IACpB,QAAQ,MAAM;AAAA,IACd,QAAQ,MAAM;AAAA,IACd,YAAY,MAAM;AAAA,IAClB,YAAY,MAAM;AAAA,IAClB,WAAW,MAAM;AAAA,IACjB,OAAO,MAAM;AAAA,IACb,MAAM;AAAA,EACV;AACJ;AACA,SAAS,YAAY;AACjB,EAAU,KAAK,KAAK,KAAK;AAC7B;AACA,IAAI,aAAc,SAAU,QAAQ;AAChC,YAAUC,aAAY,MAAM;AAC5B,WAASA,cAAa;AAClB,QAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,UAAM,UAAU;AAChB,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AAAA,EAAE;AAC7C,EAAAA,YAAW,UAAU,YAAY,WAAY;AAAA,EAAE;AAC/C,SAAOA;AACX,EAAE,gBAAQ;AACV,IAAI,gBAAiB,2BAAY;AAC7B,WAASC,eAAc,GAAG,GAAG;AACzB,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACb;AACA,SAAOA;AACX,EAAE;AACF,IAAI,eAAe;AAAA,EACf;AAAA,EAAS;AAAA,EAAY;AAAA,EAAc;AAAA,EACnC;AAAA,EAAW;AAAA,EAAa;AAAA,EAAa;AACzC;AACA,IAAI,UAAU,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACzC,IAAI,UAAW,SAAU,QAAQ;AAC7B,YAAUC,UAAS,MAAM;AACzB,WAASA,SAAQ,SAAS,SAAS,OAAO,aAAa,aAAa;AAChE,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,WAAW,IAAI,cAAc,GAAG,CAAC;AACvC,UAAM,UAAU;AAChB,UAAM,UAAU;AAChB,UAAM,cAAc;AACpB,UAAM,eAAe;AACrB,YAAQ,SAAS,IAAI,WAAW;AAChC,UAAM,QAAQ;AACd,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,IAAI,kBAAU,KAAK;AACxC,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,kBAAkB,SAAU,OAAO;AACjD,QAAI,KAAK,OAAO;AACZ,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,QAAI,OAAO;AACP,MAAK,KAAK,cAAc,SAAU,MAAM;AACpC,cAAM,MAAM,MAAM,GAAG,MAAM,KAAK,IAAI,GAAG,IAAI;AAAA,MAC/C,GAAG,IAAI;AACP,YAAM,UAAU;AAAA,IACpB;AACA,SAAK,QAAQ;AAAA,EACjB;AACA,EAAAA,SAAQ,UAAU,YAAY,SAAU,OAAO;AAC3C,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,YAAY,kBAAkB,MAAM,GAAG,CAAC;AAC5C,QAAI,cAAc,KAAK;AACvB,QAAI,oBAAoB,YAAY;AACpC,QAAI,qBAAqB,CAAC,kBAAkB,MAAM;AAC9C,oBAAc,KAAK,UAAU,YAAY,GAAG,YAAY,CAAC;AACzD,0BAAoB,YAAY;AAAA,IACpC;AACA,QAAI,UAAU,KAAK,WAAW,YAAY,IAAI,cAAc,GAAG,CAAC,IAAI,KAAK,UAAU,GAAG,CAAC;AACvF,QAAI,gBAAgB,QAAQ;AAC5B,QAAI,QAAQ,KAAK;AACjB,UAAM,aAAa,MAAM,UAAU,gBAAgB,cAAc,SAAS,SAAS;AACnF,QAAI,qBAAqB,kBAAkB,mBAAmB;AAC1D,WAAK,kBAAkB,aAAa,YAAY,KAAK;AAAA,IACzD;AACA,SAAK,kBAAkB,SAAS,aAAa,KAAK;AAClD,QAAI,iBAAiB,kBAAkB,mBAAmB;AACtD,WAAK,kBAAkB,SAAS,aAAa,KAAK;AAAA,IACtD;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,OAAO;AAC1C,QAAI,eAAe,MAAM;AACzB,QAAI,iBAAiB,kBAAkB;AACnC,WAAK,kBAAkB,KAAK,UAAU,YAAY,KAAK;AAAA,IAC3D;AACA,QAAI,iBAAiB,gBAAgB;AACjC,WAAK,QAAQ,aAAa,EAAE,MAAM,aAAa,MAAa,CAAC;AAAA,IACjE;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,SAAS,WAAY;AACnC,SAAK,WAAW,IAAI,cAAc,GAAG,CAAC;AAAA,EAC1C;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,WAAW,WAAW;AACzD,QAAI,UAAU,KAAK,SAAS;AAC5B,eAAW,QAAQ,KAAK,MAAM,SAAS;AAAA,EAC3C;AACA,EAAAA,SAAQ,UAAU,UAAU,WAAY;AACpC,SAAK,MAAM,QAAQ;AACnB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,aAAa;AACtD,QAAI,QAAQ,KAAK;AACjB,UAAM,aAAa,MAAM,UAAU,WAAW;AAAA,EAClD;AACA,EAAAA,SAAQ,UAAU,oBAAoB,SAAU,YAAY,WAAW,OAAO;AAC1E,iBAAa,cAAc,CAAC;AAC5B,QAAI,KAAK,WAAW;AACpB,QAAI,MAAM,GAAG,QAAQ;AACjB;AAAA,IACJ;AACA,QAAI,WAAY,OAAO;AACvB,QAAI,cAAc,gBAAgB,WAAW,YAAY,KAAK;AAC9D,WAAO,IAAI;AACP,SAAG,QAAQ,MACH,YAAY,eAAe,CAAC,CAAC,GAAG,QAAQ,EAAE,KAAK,IAAI,WAAW;AACtE,SAAG,QAAQ,WAAW,WAAW;AACjC,WAAK,GAAG,eAAe,GAAG,eAAe,GAAG;AAC5C,UAAI,YAAY,cAAc;AAC1B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,YAAY,cAAc;AAC3B,WAAK,QAAQ,WAAW,WAAW;AACnC,UAAI,KAAK,WAAW,KAAK,QAAQ,gBAAgB;AAC7C,aAAK,QAAQ,eAAe,SAAU,OAAO;AACzC,cAAI,OAAQ,MAAM,QAAQ,MAAO,YAAY;AACzC,kBAAM,QAAQ,EAAE,KAAK,OAAO,WAAW;AAAA,UAC3C;AACA,cAAI,MAAM,SAAS;AACf,kBAAM,QAAQ,WAAW,WAAW;AAAA,UACxC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,YAAY,SAAU,GAAG,GAAG,SAAS;AACnD,QAAI,OAAO,KAAK,QAAQ,eAAe;AACvC,QAAI,MAAM,IAAI,cAAc,GAAG,CAAC;AAChC,mBAAe,MAAM,KAAK,GAAG,GAAG,OAAO;AACvC,QAAI,KAAK,gBAAgB,CAAC,IAAI,QAAQ;AAClC,UAAI,aAAa,CAAC;AAClB,UAAI,cAAc,KAAK;AACvB,UAAI,iBAAiB,cAAc;AACnC,UAAI,cAAc,IAAI,qBAAa,IAAI,gBAAgB,IAAI,gBAAgB,aAAa,WAAW;AACnG,eAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC;AACf,YAAI,OAAO,WACJ,CAAC,GAAG,UACJ,CAAC,GAAG,wBACH,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,sBAAsB;AACnD,kBAAQ,KAAK,GAAG,gBAAgB,CAAC;AACjC,cAAI,GAAG,WAAW;AACd,oBAAQ,eAAe,GAAG,SAAS;AAAA,UACvC;AACA,cAAI,QAAQ,UAAU,WAAW,GAAG;AAChC,uBAAW,KAAK,EAAE;AAAA,UACtB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,WAAW,QAAQ;AACnB,YAAI,QAAQ;AACZ,YAAI,YAAY,KAAK,KAAK;AAC1B,YAAI,MAAM,KAAK,KAAK;AACpB,iBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK,OAAO;AAC5C,mBAAS,QAAQ,GAAG,QAAQ,KAAK,SAAS,WAAW;AACjD,gBAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK;AAC/B,gBAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK;AAC/B,2BAAe,YAAY,KAAK,IAAI,IAAI,OAAO;AAC/C,gBAAI,IAAI,QAAQ;AACZ,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,OAAO,OAAO;AACvD,QAAI,CAAC,KAAK,aAAa;AACnB,WAAK,cAAc,IAAI,WAAW;AAAA,IACtC;AACA,QAAI,aAAa,KAAK;AACtB,cAAU,WAAW,WAAW,MAAM;AACtC,QAAI,cAAc,WAAW,UAAU,OAAO,KAAK,UAAU,MAAM,KAAK,MAAM,KAAK,IAAI,EAAE,QAAQ,KAAK,MAAM,GAAG;AAC/G,cAAU,SAAS,WAAW,MAAM;AACpC,QAAI,aAAa;AACb,UAAI,OAAO,YAAY;AACvB,YAAM,eAAe;AACrB,UAAI,MAAM,IAAI,cAAc;AAC5B,UAAI,SAAS,YAAY;AACzB,WAAK,kBAAkB,KAAK,MAAM,YAAY,KAAK;AAAA,IACvD;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,gBAAQ;AACL,KAAK,CAAC,SAAS,aAAa,WAAW,cAAc,YAAY,aAAa,GAAG,SAAU,MAAM;AAClG,UAAQ,UAAU,IAAI,IAAI,SAAU,OAAO;AACvC,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,YAAY,kBAAkB,MAAM,GAAG,CAAC;AAC5C,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,aAAa,CAAC,WAAW;AAClC,gBAAU,KAAK,UAAU,GAAG,CAAC;AAC7B,sBAAgB,QAAQ;AAAA,IAC5B;AACA,QAAI,SAAS,aAAa;AACtB,WAAK,UAAU;AACf,WAAK,aAAa,CAAC,MAAM,KAAK,MAAM,GAAG;AACvC,WAAK,QAAQ;AAAA,IACjB,WACS,SAAS,WAAW;AACzB,WAAK,QAAQ;AAAA,IACjB,WACS,SAAS,SAAS;AACvB,UAAI,KAAK,YAAY,KAAK,SACnB,CAAC,KAAK,cACD,KAAK,KAAK,YAAY,CAAC,MAAM,KAAK,MAAM,GAAG,CAAC,IAAI,GAAG;AAC3D;AAAA,MACJ;AACA,WAAK,aAAa;AAAA,IACtB;AACA,SAAK,kBAAkB,SAAS,MAAM,KAAK;AAAA,EAC/C;AACJ,CAAC;AACD,SAAS,QAAQ,aAAa,GAAG,GAAG;AAChC,MAAI,YAAY,YAAY,YAAY,gBAAgB,SAAS,EAAE,GAAG,CAAC,GAAG;AACtE,QAAI,KAAK;AACT,QAAI,WAAW;AACf,QAAI,aAAa;AACjB,WAAO,IAAI;AACP,UAAI,GAAG,YAAY;AACf,qBAAa;AAAA,MACjB;AACA,UAAI,CAAC,YAAY;AACb,YAAI,WAAW,GAAG,YAAY;AAC9B,YAAI,YAAY,CAAC,SAAS,QAAQ,GAAG,CAAC,GAAG;AACrC,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,GAAG,QAAQ;AACX,mBAAW;AAAA,MACf;AACA,UAAI,SAAS,GAAG;AAChB,WAAK,SAAS,SAAS,GAAG;AAAA,IAC9B;AACA,WAAO,WAAW,SAAS;AAAA,EAC/B;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM,KAAK,GAAG,GAAG,SAAS;AAC9C,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,QAAI,KAAK,KAAK,CAAC;AACf,QAAI,mBAAmB;AACvB,QAAI,OAAO,WACJ,CAAC,GAAG,WACH,mBAAmB,QAAQ,IAAI,GAAG,CAAC,IAAI;AAC3C,OAAC,IAAI,cAAc,IAAI,YAAY;AACnC,UAAI,qBAAqB,QAAQ;AAC7B,YAAI,SAAS;AACb;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,iBAAiB,GAAG,GAAG;AAC9C,MAAI,UAAU,gBAAgB;AAC9B,SAAO,IAAI,KAAK,IAAI,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU;AAC7E;AACA,IAAO,kBAAQ;;;ACtSf,IAAI,oBAAoB;AACxB,IAAI,wBAAwB;AAC5B,SAAS,aAAa,GAAG;AACrB,MAAI,IAAI;AACR,SAAO,KAAK,mBAAmB;AAC3B,SAAK,IAAI;AACT,UAAM;AAAA,EACV;AACA,SAAO,IAAI;AACf;AACA,SAAS,iBAAiB,OAAO,IAAI,IAAI,SAAS;AAC9C,MAAI,QAAQ,KAAK;AACjB,MAAI,UAAU,IAAI;AACd,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,IAAI,GAAG;AACxC,WAAO,QAAQ,MAAM,QAAQ,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,CAAC,IAAI,GAAG;AAC9D;AAAA,IACJ;AACA,eAAW,OAAO,IAAI,KAAK;AAAA,EAC/B,OACK;AACD,WAAO,QAAQ,MAAM,QAAQ,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,CAAC,KAAK,GAAG;AAC/D;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ;AACnB;AACA,SAAS,WAAW,OAAO,IAAI,IAAI;AAC/B;AACA,SAAO,KAAK,IAAI;AACZ,QAAI,IAAI,MAAM,EAAE;AAChB,UAAM,IAAI,IAAI,MAAM,EAAE;AACtB,UAAM,IAAI,IAAI;AAAA,EAClB;AACJ;AACA,SAAS,oBAAoB,OAAO,IAAI,IAAI,OAAO,SAAS;AACxD,MAAI,UAAU,IAAI;AACd;AAAA,EACJ;AACA,SAAO,QAAQ,IAAI,SAAS;AACxB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,OAAO;AACX,QAAI,QAAQ;AACZ,QAAI;AACJ,WAAO,OAAO,OAAO;AACjB,YAAM,OAAO,UAAU;AACvB,UAAI,QAAQ,OAAO,MAAM,GAAG,CAAC,IAAI,GAAG;AAChC,gBAAQ;AAAA,MACZ,OACK;AACD,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AACA,QAAI,IAAI,QAAQ;AAChB,YAAQ,GAAG;AAAA,MACP,KAAK;AACD,cAAM,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC;AAAA,MACpC,KAAK;AACD,cAAM,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC;AAAA,MACpC,KAAK;AACD,cAAM,OAAO,CAAC,IAAI,MAAM,IAAI;AAC5B;AAAA,MACJ;AACI,eAAO,IAAI,GAAG;AACV,gBAAM,OAAO,CAAC,IAAI,MAAM,OAAO,IAAI,CAAC;AACpC;AAAA,QACJ;AAAA,IACR;AACA,UAAM,IAAI,IAAI;AAAA,EAClB;AACJ;AACA,SAAS,WAAW,OAAO,OAAO,OAAO,QAAQ,MAAM,SAAS;AAC5D,MAAI,aAAa;AACjB,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI,QAAQ,OAAO,MAAM,QAAQ,IAAI,CAAC,IAAI,GAAG;AACzC,gBAAY,SAAS;AACrB,WAAO,SAAS,aAAa,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM,CAAC,IAAI,GAAG;AAC3E,mBAAa;AACb,gBAAU,UAAU,KAAK;AACzB,UAAI,UAAU,GAAG;AACb,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,QAAI,SAAS,WAAW;AACpB,eAAS;AAAA,IACb;AACA,kBAAc;AACd,cAAU;AAAA,EACd,OACK;AACD,gBAAY,OAAO;AACnB,WAAO,SAAS,aAAa,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM,CAAC,KAAK,GAAG;AAC5E,mBAAa;AACb,gBAAU,UAAU,KAAK;AACzB,UAAI,UAAU,GAAG;AACb,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,QAAI,SAAS,WAAW;AACpB,eAAS;AAAA,IACb;AACA,QAAI,MAAM;AACV,iBAAa,OAAO;AACpB,aAAS,OAAO;AAAA,EACpB;AACA;AACA,SAAO,aAAa,QAAQ;AACxB,QAAI,IAAI,cAAc,SAAS,eAAe;AAC9C,QAAI,QAAQ,OAAO,MAAM,QAAQ,CAAC,CAAC,IAAI,GAAG;AACtC,mBAAa,IAAI;AAAA,IACrB,OACK;AACD,eAAS;AAAA,IACb;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO,OAAO,OAAO,QAAQ,MAAM,SAAS;AAC7D,MAAI,aAAa;AACjB,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI,QAAQ,OAAO,MAAM,QAAQ,IAAI,CAAC,IAAI,GAAG;AACzC,gBAAY,OAAO;AACnB,WAAO,SAAS,aAAa,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM,CAAC,IAAI,GAAG;AAC3E,mBAAa;AACb,gBAAU,UAAU,KAAK;AACzB,UAAI,UAAU,GAAG;AACb,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,QAAI,SAAS,WAAW;AACpB,eAAS;AAAA,IACb;AACA,QAAI,MAAM;AACV,iBAAa,OAAO;AACpB,aAAS,OAAO;AAAA,EACpB,OACK;AACD,gBAAY,SAAS;AACrB,WAAO,SAAS,aAAa,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM,CAAC,KAAK,GAAG;AAC5E,mBAAa;AACb,gBAAU,UAAU,KAAK;AACzB,UAAI,UAAU,GAAG;AACb,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,QAAI,SAAS,WAAW;AACpB,eAAS;AAAA,IACb;AACA,kBAAc;AACd,cAAU;AAAA,EACd;AACA;AACA,SAAO,aAAa,QAAQ;AACxB,QAAI,IAAI,cAAc,SAAS,eAAe;AAC9C,QAAI,QAAQ,OAAO,MAAM,QAAQ,CAAC,CAAC,IAAI,GAAG;AACtC,eAAS;AAAA,IACb,OACK;AACD,mBAAa,IAAI;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,QAAQ,OAAO,SAAS;AAC7B,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY;AAChB,MAAI,MAAM,CAAC;AACX,aAAW,CAAC;AACZ,cAAY,CAAC;AACb,WAAS,QAAQ,WAAW,YAAY;AACpC,aAAS,SAAS,IAAI;AACtB,cAAU,SAAS,IAAI;AACvB,iBAAa;AAAA,EACjB;AACA,WAAS,YAAY;AACjB,WAAO,YAAY,GAAG;AAClB,UAAI,IAAI,YAAY;AACpB,UAAK,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK,UAAU,CAAC,IAAI,UAAU,IAAI,CAAC,KACzD,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK,UAAU,CAAC,IAAI,UAAU,IAAI,CAAC,GAAI;AACpE,YAAI,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG;AACrC;AAAA,QACJ;AAAA,MACJ,WACS,UAAU,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG;AACtC;AAAA,MACJ;AACA,cAAQ,CAAC;AAAA,IACb;AAAA,EACJ;AACA,WAAS,iBAAiB;AACtB,WAAO,YAAY,GAAG;AAClB,UAAI,IAAI,YAAY;AACpB,UAAI,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG;AAC9C;AAAA,MACJ;AACA,cAAQ,CAAC;AAAA,IACb;AAAA,EACJ;AACA,WAAS,QAAQ,GAAG;AAChB,QAAI,SAAS,SAAS,CAAC;AACvB,QAAI,UAAU,UAAU,CAAC;AACzB,QAAI,SAAS,SAAS,IAAI,CAAC;AAC3B,QAAI,UAAU,UAAU,IAAI,CAAC;AAC7B,cAAU,CAAC,IAAI,UAAU;AACzB,QAAI,MAAM,YAAY,GAAG;AACrB,eAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC;AAChC,gBAAU,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC;AAAA,IACtC;AACA;AACA,QAAI,IAAI,YAAY,MAAM,MAAM,GAAG,OAAO,QAAQ,SAAS,GAAG,OAAO;AACrE,cAAU;AACV,eAAW;AACX,QAAI,YAAY,GAAG;AACf;AAAA,IACJ;AACA,cAAU,WAAW,MAAM,SAAS,UAAU,CAAC,GAAG,OAAO,QAAQ,SAAS,UAAU,GAAG,OAAO;AAC9F,QAAI,YAAY,GAAG;AACf;AAAA,IACJ;AACA,QAAI,WAAW,SAAS;AACpB,eAAS,QAAQ,SAAS,QAAQ,OAAO;AAAA,IAC7C,OACK;AACD,gBAAU,QAAQ,SAAS,QAAQ,OAAO;AAAA,IAC9C;AAAA,EACJ;AACA,WAAS,SAAS,QAAQ,SAAS,QAAQ,SAAS;AAChD,QAAI,IAAI;AACR,SAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,UAAI,CAAC,IAAI,MAAM,SAAS,CAAC;AAAA,IAC7B;AACA,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,OAAO;AACX,UAAM,MAAM,IAAI,MAAM,SAAS;AAC/B,QAAI,EAAE,YAAY,GAAG;AACjB,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,cAAM,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC;AAAA,MACrC;AACA;AAAA,IACJ;AACA,QAAI,YAAY,GAAG;AACf,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,cAAM,OAAO,CAAC,IAAI,MAAM,UAAU,CAAC;AAAA,MACvC;AACA,YAAM,OAAO,OAAO,IAAI,IAAI,OAAO;AACnC;AAAA,IACJ;AACA,QAAI,aAAa;AACjB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,WAAO,GAAG;AACN,eAAS;AACT,eAAS;AACT,aAAO;AACP,SAAG;AACC,YAAI,QAAQ,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG;AAC3C,gBAAM,MAAM,IAAI,MAAM,SAAS;AAC/B;AACA,mBAAS;AACT,cAAI,EAAE,YAAY,GAAG;AACjB,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ,OACK;AACD,gBAAM,MAAM,IAAI,IAAI,SAAS;AAC7B;AACA,mBAAS;AACT,cAAI,EAAE,YAAY,GAAG;AACjB,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,UAAU,SAAS,UAAU;AAC7B,UAAI,MAAM;AACN;AAAA,MACJ;AACA,SAAG;AACC,iBAAS,YAAY,MAAM,OAAO,GAAG,KAAK,SAAS,SAAS,GAAG,OAAO;AACtE,YAAI,WAAW,GAAG;AACd,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,kBAAM,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC;AAAA,UACrC;AACA,kBAAQ;AACR,qBAAW;AACX,qBAAW;AACX,cAAI,WAAW,GAAG;AACd,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,MAAM,IAAI,MAAM,SAAS;AAC/B,YAAI,EAAE,YAAY,GAAG;AACjB,iBAAO;AACP;AAAA,QACJ;AACA,iBAAS,WAAW,IAAI,OAAO,GAAG,OAAO,SAAS,SAAS,GAAG,OAAO;AACrE,YAAI,WAAW,GAAG;AACd,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,kBAAM,OAAO,CAAC,IAAI,MAAM,UAAU,CAAC;AAAA,UACvC;AACA,kBAAQ;AACR,qBAAW;AACX,qBAAW;AACX,cAAI,YAAY,GAAG;AACf,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,MAAM,IAAI,IAAI,SAAS;AAC7B,YAAI,EAAE,YAAY,GAAG;AACjB,iBAAO;AACP;AAAA,QACJ;AACA;AAAA,MACJ,SAAS,UAAU,yBAAyB,UAAU;AACtD,UAAI,MAAM;AACN;AAAA,MACJ;AACA,UAAI,aAAa,GAAG;AAChB,qBAAa;AAAA,MACjB;AACA,oBAAc;AAAA,IAClB;AACA,gBAAY;AACZ,gBAAY,MAAM,YAAY;AAC9B,QAAI,YAAY,GAAG;AACf,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,cAAM,OAAO,CAAC,IAAI,MAAM,UAAU,CAAC;AAAA,MACvC;AACA,YAAM,OAAO,OAAO,IAAI,IAAI,OAAO;AAAA,IACvC,WACS,YAAY,GAAG;AACpB,YAAM,IAAI,MAAM;AAAA,IACpB,OACK;AACD,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,cAAM,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,UAAU,QAAQ,SAAS,QAAQ,SAAS;AACjD,QAAI,IAAI;AACR,SAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,UAAI,CAAC,IAAI,MAAM,SAAS,CAAC;AAAA,IAC7B;AACA,QAAI,UAAU,SAAS,UAAU;AACjC,QAAI,UAAU,UAAU;AACxB,QAAI,OAAO,SAAS,UAAU;AAC9B,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,UAAM,MAAM,IAAI,MAAM,SAAS;AAC/B,QAAI,EAAE,YAAY,GAAG;AACjB,qBAAe,QAAQ,UAAU;AACjC,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,cAAM,eAAe,CAAC,IAAI,IAAI,CAAC;AAAA,MACnC;AACA;AAAA,IACJ;AACA,QAAI,YAAY,GAAG;AACf,cAAQ;AACR,iBAAW;AACX,mBAAa,OAAO;AACpB,qBAAe,UAAU;AACzB,WAAK,IAAI,UAAU,GAAG,KAAK,GAAG,KAAK;AAC/B,cAAM,aAAa,CAAC,IAAI,MAAM,eAAe,CAAC;AAAA,MAClD;AACA,YAAM,IAAI,IAAI,IAAI,OAAO;AACzB;AAAA,IACJ;AACA,QAAI,aAAa;AACjB,WAAO,MAAM;AACT,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,OAAO;AACX,SAAG;AACC,YAAI,QAAQ,IAAI,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,GAAG;AAC3C,gBAAM,MAAM,IAAI,MAAM,SAAS;AAC/B;AACA,mBAAS;AACT,cAAI,EAAE,YAAY,GAAG;AACjB,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ,OACK;AACD,gBAAM,MAAM,IAAI,IAAI,SAAS;AAC7B;AACA,mBAAS;AACT,cAAI,EAAE,YAAY,GAAG;AACjB,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,UAAU,SAAS,UAAU;AAC7B,UAAI,MAAM;AACN;AAAA,MACJ;AACA,SAAG;AACC,iBAAS,UAAU,YAAY,IAAI,OAAO,GAAG,OAAO,QAAQ,SAAS,UAAU,GAAG,OAAO;AACzF,YAAI,WAAW,GAAG;AACd,kBAAQ;AACR,qBAAW;AACX,qBAAW;AACX,uBAAa,OAAO;AACpB,yBAAe,UAAU;AACzB,eAAK,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9B,kBAAM,aAAa,CAAC,IAAI,MAAM,eAAe,CAAC;AAAA,UAClD;AACA,cAAI,YAAY,GAAG;AACf,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,MAAM,IAAI,IAAI,SAAS;AAC7B,YAAI,EAAE,YAAY,GAAG;AACjB,iBAAO;AACP;AAAA,QACJ;AACA,iBAAS,UAAU,WAAW,MAAM,OAAO,GAAG,KAAK,GAAG,SAAS,UAAU,GAAG,OAAO;AACnF,YAAI,WAAW,GAAG;AACd,kBAAQ;AACR,qBAAW;AACX,qBAAW;AACX,uBAAa,OAAO;AACpB,yBAAe,UAAU;AACzB,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,kBAAM,aAAa,CAAC,IAAI,IAAI,eAAe,CAAC;AAAA,UAChD;AACA,cAAI,WAAW,GAAG;AACd,mBAAO;AACP;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,MAAM,IAAI,MAAM,SAAS;AAC/B,YAAI,EAAE,YAAY,GAAG;AACjB,iBAAO;AACP;AAAA,QACJ;AACA;AAAA,MACJ,SAAS,UAAU,yBAAyB,UAAU;AACtD,UAAI,MAAM;AACN;AAAA,MACJ;AACA,UAAI,aAAa,GAAG;AAChB,qBAAa;AAAA,MACjB;AACA,oBAAc;AAAA,IAClB;AACA,gBAAY;AACZ,QAAI,YAAY,GAAG;AACf,kBAAY;AAAA,IAChB;AACA,QAAI,YAAY,GAAG;AACf,cAAQ;AACR,iBAAW;AACX,mBAAa,OAAO;AACpB,qBAAe,UAAU;AACzB,WAAK,IAAI,UAAU,GAAG,KAAK,GAAG,KAAK;AAC/B,cAAM,aAAa,CAAC,IAAI,MAAM,eAAe,CAAC;AAAA,MAClD;AACA,YAAM,IAAI,IAAI,IAAI,OAAO;AAAA,IAC7B,WACS,YAAY,GAAG;AACpB,YAAM,IAAI,MAAM;AAAA,IACpB,OACK;AACD,qBAAe,QAAQ,UAAU;AACjC,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC1B,cAAM,eAAe,CAAC,IAAI,IAAI,CAAC;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACe,SAAR,KAAsB,OAAO,SAAS,IAAI,IAAI;AACjD,MAAI,CAAC,IAAI;AACL,SAAK;AAAA,EACT;AACA,MAAI,CAAC,IAAI;AACL,SAAK,MAAM;AAAA,EACf;AACA,MAAI,YAAY,KAAK;AACrB,MAAI,YAAY,GAAG;AACf;AAAA,EACJ;AACA,MAAI,YAAY;AAChB,MAAI,YAAY,mBAAmB;AAC/B,gBAAY,iBAAiB,OAAO,IAAI,IAAI,OAAO;AACnD,wBAAoB,OAAO,IAAI,IAAI,KAAK,WAAW,OAAO;AAC1D;AAAA,EACJ;AACA,MAAI,KAAK,QAAQ,OAAO,OAAO;AAC/B,MAAI,SAAS,aAAa,SAAS;AACnC,KAAG;AACC,gBAAY,iBAAiB,OAAO,IAAI,IAAI,OAAO;AACnD,QAAI,YAAY,QAAQ;AACpB,UAAI,QAAQ;AACZ,UAAI,QAAQ,QAAQ;AAChB,gBAAQ;AAAA,MACZ;AACA,0BAAoB,OAAO,IAAI,KAAK,OAAO,KAAK,WAAW,OAAO;AAClE,kBAAY;AAAA,IAChB;AACA,OAAG,QAAQ,IAAI,SAAS;AACxB,OAAG,UAAU;AACb,iBAAa;AACb,UAAM;AAAA,EACV,SAAS,cAAc;AACvB,KAAG,eAAe;AACtB;;;ACtgBA,IAAI,sBAAsB;AAC1B,SAAS,mBAAmB;AACxB,MAAI,qBAAqB;AACrB;AAAA,EACJ;AACA,wBAAsB;AACtB,UAAQ,KAAK,8EAA8E;AAC/F;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,MAAI,EAAE,WAAW,EAAE,QAAQ;AACvB,QAAI,EAAE,MAAM,EAAE,GAAG;AACb,aAAO,EAAE,KAAK,EAAE;AAAA,IACpB;AACA,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AACA,SAAO,EAAE,SAAS,EAAE;AACxB;AACA,IAAI,UAAW,WAAY;AACvB,WAASC,WAAU;AACf,SAAK,SAAS,CAAC;AACf,SAAK,eAAe,CAAC;AACrB,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAAA,EAC/B;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,IAAI,SAAS;AAChD,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AACzC,WAAK,OAAO,CAAC,EAAE,SAAS,IAAI,OAAO;AAAA,IACvC;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,QAAQ,eAAe;AAChE,oBAAgB,iBAAiB;AACjC,QAAI,cAAc,KAAK;AACvB,QAAI,UAAU,CAAC,YAAY,QAAQ;AAC/B,WAAK,kBAAkB,aAAa;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,oBAAoB,SAAU,eAAe;AAC3D,SAAK,kBAAkB;AACvB,QAAI,QAAQ,KAAK;AACjB,QAAI,cAAc,KAAK;AACvB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAC9C,WAAK,yBAAyB,MAAM,CAAC,GAAG,MAAM,aAAa;AAAA,IAC/D;AACA,gBAAY,SAAS,KAAK;AAC1B,SAAQ,aAAa,gBAAgB;AAAA,EACzC;AACA,EAAAA,SAAQ,UAAU,2BAA2B,SAAU,IAAI,WAAW,eAAe;AACjF,QAAI,GAAG,UAAU,CAAC,eAAe;AAC7B;AAAA,IACJ;AACA,OAAG,aAAa;AAChB,OAAG,OAAO;AACV,OAAG,YAAY;AACf,QAAI,kBAAkB,GAAG,YAAY;AACrC,QAAI,GAAG,YAAY;AACf,kBAAY;AAAA,IAChB,WACS,iBAAiB;AACtB,UAAI,WAAW;AACX,oBAAY,UAAU,MAAM;AAAA,MAChC,OACK;AACD,oBAAY,CAAC;AAAA,MACjB;AACA,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,aAAO,iBAAiB;AACpB,wBAAgB,SAAS;AACzB,wBAAgB,gBAAgB;AAChC,kBAAU,KAAK,eAAe;AAC9B,yBAAiB;AACjB,0BAAkB,gBAAgB,YAAY;AAAA,MAClD;AAAA,IACJ;AACA,QAAI,GAAG,aAAa;AAChB,UAAI,WAAW,GAAG,YAAY;AAC9B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAI,QAAQ,SAAS,CAAC;AACtB,YAAI,GAAG,SAAS;AACZ,gBAAM,WAAW;AAAA,QACrB;AACA,aAAK,yBAAyB,OAAO,WAAW,aAAa;AAAA,MACjE;AACA,SAAG,UAAU;AAAA,IACjB,OACK;AACD,UAAI,OAAO;AACX,UAAI,aAAa,UAAU,QAAQ;AAC/B,aAAK,cAAc;AAAA,MACvB,WACS,KAAK,eAAe,KAAK,YAAY,SAAS,GAAG;AACtD,aAAK,cAAc,CAAC;AAAA,MACxB;AACA,UAAI,MAAM,KAAK,CAAC,GAAG;AACf,yBAAiB;AACjB,aAAK,IAAI;AAAA,MACb;AACA,UAAI,MAAM,KAAK,EAAE,GAAG;AAChB,yBAAiB;AACjB,aAAK,KAAK;AAAA,MACd;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,yBAAiB;AACjB,aAAK,SAAS;AAAA,MAClB;AACA,WAAK,aAAa,KAAK,iBAAiB,IAAI;AAAA,IAChD;AACA,QAAI,UAAU,GAAG,mBAAmB,GAAG,gBAAgB;AACvD,QAAI,SAAS;AACT,WAAK,yBAAyB,SAAS,WAAW,aAAa;AAAA,IACnE;AACA,QAAI,YAAY,GAAG,iBAAiB;AACpC,QAAI,WAAW;AACX,WAAK,yBAAyB,WAAW,WAAW,aAAa;AAAA,IACrE;AACA,QAAI,SAAS,GAAG,eAAe;AAC/B,QAAI,QAAQ;AACR,WAAK,yBAAyB,QAAQ,WAAW,aAAa;AAAA,IAClE;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,UAAU,SAAU,IAAI;AACtC,QAAI,GAAG,QAAQ,GAAG,KAAK,YAAY,MAAM;AACrC;AAAA,IACJ;AACA,SAAK,OAAO,KAAK,EAAE;AAAA,EACvB;AACA,EAAAA,SAAQ,UAAU,UAAU,SAAU,IAAI;AACtC,QAAI,cAAc,OAAO;AACrB,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACvC,aAAK,QAAQ,GAAG,CAAC,CAAC;AAAA,MACtB;AACA;AAAA,IACJ;AACA,QAAI,MAAW,QAAQ,KAAK,QAAQ,EAAE;AACtC,QAAI,OAAO,GAAG;AACV,WAAK,OAAO,OAAO,KAAK,CAAC;AAAA,IAC7B;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,cAAc,WAAY;AACxC,SAAK,SAAS,CAAC;AACf,SAAK,eAAe,CAAC;AACrB,SAAK,kBAAkB;AACvB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,SAAQ,UAAU,UAAU,WAAY;AACpC,SAAK,eAAe;AACpB,SAAK,SAAS;AAAA,EAClB;AACA,SAAOA;AACX,EAAE;AACF,IAAO,kBAAQ;;;AC5Jf,IAAI;AACJ,wBAAyB,YAAI,oBACpB,OAAO,yBAAyB,OAAO,sBAAsB,KAAK,MAAM,KACrE,OAAO,2BAA2B,OAAO,wBAAwB,KAAK,MAAM,KAC7E,OAAO,4BACP,OAAO,gCAAiC,SAAU,MAAM;AAC/D,SAAO,WAAW,MAAM,EAAE;AAC9B;AACA,IAAO,gCAAQ;;;ACLR,SAAS,UAAU;AACtB,UAAO,oBAAI,KAAK,GAAE,QAAQ;AAC9B;AACA,IAAI,YAAa,SAAU,QAAQ;AAC/B,YAAUC,YAAW,MAAM;AAC3B,WAASA,WAAU,MAAM;AACrB,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,WAAW;AACjB,UAAM,QAAQ;AACd,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,UAAM,UAAU;AAChB,WAAO,QAAQ,CAAC;AAChB,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,UAAU,SAAU,MAAM;AAC1C,QAAI,KAAK,WAAW;AAChB,WAAK,WAAW,IAAI;AAAA,IACxB;AACA,QAAI,CAAC,KAAK,OAAO;AACb,WAAK,QAAQ,KAAK,QAAQ;AAAA,IAC9B,OACK;AACD,WAAK,MAAM,OAAO;AAClB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACjB;AACA,SAAK,YAAY;AAAA,EACrB;AACA,EAAAA,WAAU,UAAU,cAAc,SAAU,UAAU;AAClD,aAAS,YAAY;AACrB,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,MAAM;AACN,WAAK,QAAQ,IAAI;AAAA,IACrB;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,aAAa,SAAU,MAAM;AAC7C,QAAI,CAAC,KAAK,WAAW;AACjB;AAAA,IACJ;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK;AAChB,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AACA,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AACA,SAAK,OAAO,KAAK,OAAO,KAAK,YAAY;AAAA,EAC7C;AACA,EAAAA,WAAU,UAAU,iBAAiB,SAAU,UAAU;AACrD,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,MAAM;AACN,WAAK,WAAW,IAAI;AAAA,IACxB;AACA,aAAS,YAAY;AAAA,EACzB;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,+BAA+B;AAClE,QAAI,OAAO,QAAQ,IAAI,KAAK;AAC5B,QAAI,QAAQ,OAAO,KAAK;AACxB,QAAI,OAAO,KAAK;AAChB,WAAO,MAAM;AACT,UAAI,WAAW,KAAK;AACpB,UAAI,WAAW,KAAK,KAAK,MAAM,KAAK;AACpC,UAAI,UAAU;AACV,aAAK,UAAU;AACf,aAAK,WAAW,IAAI;AACpB,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,SAAK,QAAQ;AACb,QAAI,CAAC,+BAA+B;AAChC,WAAK,QAAQ,SAAS,KAAK;AAC3B,WAAK,MAAM,UAAU,KAAK,MAAM,OAAO;AAAA,IAC3C;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,QAAI,OAAO;AACX,SAAK,WAAW;AAChB,aAAS,OAAO;AACZ,UAAI,KAAK,UAAU;AACf,sCAAsB,IAAI;AAC1B,SAAC,KAAK,WAAW,KAAK,OAAO;AAAA,MACjC;AAAA,IACJ;AACA,kCAAsB,IAAI;AAAA,EAC9B;AACA,EAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,QAAI,KAAK,UAAU;AACf;AAAA,IACJ;AACA,SAAK,QAAQ,QAAQ;AACrB,SAAK,cAAc;AACnB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,WAAU,UAAU,OAAO,WAAY;AACnC,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,cAAc,QAAQ;AAC3B,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,QAAI,KAAK,SAAS;AACd,WAAK,eAAe,QAAQ,IAAI,KAAK;AACrC,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,QAAI,OAAO,KAAK;AAChB,WAAO,MAAM;AACT,UAAI,WAAW,KAAK;AACpB,WAAK,OAAO,KAAK,OAAO,KAAK,YAAY;AACzC,aAAO;AAAA,IACX;AACA,SAAK,QAAQ,KAAK,QAAQ;AAAA,EAC9B;AACA,EAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,WAAO,KAAK,SAAS;AAAA,EACzB;AACA,EAAAA,WAAU,UAAU,UAAU,SAAU,QAAQ,SAAS;AACrD,cAAU,WAAW,CAAC;AACtB,SAAK,MAAM;AACX,QAAI,WAAW,IAAI,iBAAS,QAAQ,QAAQ,IAAI;AAChD,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACX;AACA,SAAOA;AACX,EAAE,gBAAQ;AACV,IAAO,oBAAQ;;;AC7If,IAAI,oBAAoB;AACxB,IAAI,uBAAuB,YAAI;AAC/B,IAAI,2BAA4B,WAAY;AACxC,MAAI,oBAAoB;AAAA,IACpB;AAAA,IAAS;AAAA,IAAY;AAAA,IAAc;AAAA,IAAS;AAAA,IAC5C;AAAA,IAAW;AAAA,IAAa;AAAA,IAAa;AAAA,EACzC;AACA,MAAI,oBAAoB;AAAA,IACpB;AAAA,IAAc;AAAA,IAAY;AAAA,EAC9B;AACA,MAAI,sBAAsB;AAAA,IACtB,aAAa;AAAA,IAAG,WAAW;AAAA,IAAG,aAAa;AAAA,IAAG,YAAY;AAAA,EAC9D;AACA,MAAI,sBAA6B,IAAI,mBAAmB,SAAU,MAAM;AACpE,QAAI,KAAK,KAAK,QAAQ,SAAS,SAAS;AACxC,WAAO,oBAAoB,eAAe,EAAE,IAAI,KAAK;AAAA,EACzD,CAAC;AACD,SAAO;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACb;AACJ,EAAG;AACH,IAAI,4BAA4B;AAAA,EAC5B,OAAO,CAAC,aAAa,SAAS;AAAA,EAC9B,SAAS,CAAC,eAAe,WAAW;AACxC;AACA,IAAI,sBAAsB;AAC1B,SAAS,mBAAmB,OAAO;AAC/B,MAAI,cAAc,MAAM;AACxB,SAAO,gBAAgB,SAAS,gBAAgB;AACpD;AACA,SAAS,cAAc,OAAO;AAC1B,QAAM,WAAW;AACjB,MAAI,MAAM,cAAc,MAAM;AAC1B,iBAAa,MAAM,UAAU;AAC7B,UAAM,aAAa;AAAA,EACvB;AACA,QAAM,aAAa,WAAW,WAAY;AACtC,UAAM,WAAW;AACjB,UAAM,aAAa;AAAA,EACvB,GAAG,GAAG;AACV;AACA,SAAS,UAAU,OAAO;AACtB,YAAU,MAAM,YAAY;AAChC;AACA,SAAS,qBAAqB,UAAU,OAAO;AAC3C,SAAO,eAAe,SAAS,KAAK,IAAI,gBAAgB,UAAU,KAAK,GAAG,IAAI;AAClF;AACA,SAAS,UAAU,UAAU,IAAI;AAC7B,MAAI,QAAQ;AACZ,MAAI,UAAU;AACd,SAAO,SAAS,MAAM,aAAa,KAC5B,EAAE,UAAU,MAAM,iBACb,UAAU,MAAM,UAAU,SAAS,cAAe;AAC1D,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO;AACX;AACA,IAAI,kBAAmB,2BAAY;AAC/B,WAASC,iBAAgB,UAAU,OAAO;AACtC,SAAK,kBAAyB;AAC9B,SAAK,2BAAkC;AACvC,SAAK,iBAAwB;AAC7B,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,KAAK,gBAAgB,SAAS;AAC5C,SAAK,cAAc,MAAM;AACzB,SAAK,UAAU,MAAM;AACrB,SAAK,UAAU,MAAM;AAAA,EACzB;AACA,SAAOA;AACX,EAAE;AACF,IAAI,mBAAmB;AAAA,EACnB,WAAW,SAAU,OAAO;AACxB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,SAAK,sBAAsB,CAAC,MAAM,KAAK,MAAM,GAAG;AAChD,SAAK,QAAQ,aAAa,KAAK;AAAA,EACnC;AAAA,EACA,WAAW,SAAU,OAAO;AACxB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,QAAI,YAAY,KAAK;AACrB,QAAI,cAAc,MAAM,QAAQ,UAAU,CAAC,KAAK,MAAM,QAAQ,UAAU,CAAC,IAAI;AACzE,WAAK,uBAAuB,IAAI;AAAA,IACpC;AACA,SAAK,QAAQ,aAAa,KAAK;AAAA,EACnC;AAAA,EACA,SAAS,SAAU,OAAO;AACtB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,SAAK,uBAAuB,KAAK;AACjC,SAAK,QAAQ,WAAW,KAAK;AAAA,EACjC;AAAA,EACA,UAAU,SAAU,OAAO;AACvB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,QAAI,UAAU,MAAM,aAAa,MAAM;AACvC,QAAI,CAAC,UAAU,MAAM,OAAO,GAAG;AAC3B,UAAI,KAAK,oBAAoB;AACzB,cAAM,iBAAiB;AAAA,MAC3B;AACA,WAAK,QAAQ,YAAY,KAAK;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,OAAO,SAAU,OAAO;AACpB,0BAAsB;AACtB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,SAAK,QAAQ,cAAc,KAAK;AAAA,EACpC;AAAA,EACA,YAAY,SAAU,OAAO;AACzB,QAAI,qBAAqB;AACrB;AAAA,IACJ;AACA,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,SAAK,QAAQ,cAAc,KAAK;AAAA,EACpC;AAAA,EACA,YAAY,SAAU,OAAO;AACzB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,cAAU,KAAK;AACf,SAAK,oBAAoB,oBAAI,KAAK;AAClC,SAAK,QAAQ,eAAe,OAAO,OAAO;AAC1C,qBAAiB,UAAU,KAAK,MAAM,KAAK;AAC3C,qBAAiB,UAAU,KAAK,MAAM,KAAK;AAAA,EAC/C;AAAA,EACA,WAAW,SAAU,OAAO;AACxB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,cAAU,KAAK;AACf,SAAK,QAAQ,eAAe,OAAO,QAAQ;AAC3C,qBAAiB,UAAU,KAAK,MAAM,KAAK;AAAA,EAC/C;AAAA,EACA,UAAU,SAAU,OAAO;AACvB,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,cAAU,KAAK;AACf,SAAK,QAAQ,eAAe,OAAO,KAAK;AACxC,qBAAiB,QAAQ,KAAK,MAAM,KAAK;AACzC,QAAI,CAAC,oBAAI,KAAK,IAAK,CAAC,KAAK,oBAAqB,mBAAmB;AAC7D,uBAAiB,MAAM,KAAK,MAAM,KAAK;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,aAAa,SAAU,OAAO;AAC1B,qBAAiB,UAAU,KAAK,MAAM,KAAK;AAAA,EAC/C;AAAA,EACA,aAAa,SAAU,OAAO;AAC1B,QAAI,CAAC,mBAAmB,KAAK,GAAG;AAC5B,uBAAiB,UAAU,KAAK,MAAM,KAAK;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,WAAW,SAAU,OAAO;AACxB,qBAAiB,QAAQ,KAAK,MAAM,KAAK;AAAA,EAC7C;AAAA,EACA,YAAY,SAAU,OAAO;AACzB,QAAI,CAAC,mBAAmB,KAAK,GAAG;AAC5B,uBAAiB,SAAS,KAAK,MAAM,KAAK;AAAA,IAC9C;AAAA,EACJ;AACJ;AACO,KAAK,CAAC,SAAS,YAAY,aAAa,GAAG,SAAU,MAAM;AAC9D,mBAAiB,IAAI,IAAI,SAAU,OAAO;AACtC,YAAQ,eAAe,KAAK,KAAK,KAAK;AACtC,SAAK,QAAQ,MAAM,KAAK;AAAA,EAC5B;AACJ,CAAC;AACD,IAAI,oBAAoB;AAAA,EACpB,aAAa,SAAU,OAAO;AAC1B,QAAI,CAAC,mBAAmB,KAAK,GAAG;AAC5B,wBAAkB,UAAU,KAAK,MAAM,KAAK;AAAA,IAChD;AAAA,EACJ;AAAA,EACA,WAAW,SAAU,OAAO;AACxB,sBAAkB,QAAQ,KAAK,MAAM,KAAK;AAAA,EAC9C;AAAA,EACA,WAAW,SAAU,OAAO;AACxB,SAAK,QAAQ,aAAa,KAAK;AAAA,EACnC;AAAA,EACA,SAAS,SAAU,OAAO;AACtB,QAAI,0BAA0B,KAAK;AACnC,SAAK,uBAAuB,KAAK;AACjC,SAAK,QAAQ,WAAW,KAAK;AAC7B,QAAI,yBAAyB;AACzB,YAAM,iBAAiB;AACvB,WAAK,QAAQ,YAAY,KAAK;AAAA,IAClC;AAAA,EACJ;AACJ;AACA,SAAS,4BAA4B,UAAU,OAAO;AAClD,MAAI,cAAc,MAAM;AACxB,MAAI,YAAI,wBAAwB;AAC5B,IAAO,KAAK,yBAAyB,SAAS,SAAU,iBAAiB;AACrE,kCAA4B,OAAO,iBAAiB,SAAU,OAAO;AACjE,oBAAY,eAAe,EAAE,KAAK,UAAU,KAAK;AAAA,MACrD,CAAC;AAAA,IACL,CAAC;AAAA,EACL,OACK;AACD,QAAI,YAAI,sBAAsB;AAC1B,MAAO,KAAK,yBAAyB,OAAO,SAAU,iBAAiB;AACnE,oCAA4B,OAAO,iBAAiB,SAAU,OAAO;AACjE,sBAAY,eAAe,EAAE,KAAK,UAAU,KAAK;AACjD,wBAAc,KAAK;AAAA,QACvB,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAO,KAAK,yBAAyB,OAAO,SAAU,iBAAiB;AACnE,kCAA4B,OAAO,iBAAiB,SAAU,OAAO;AACjE,gBAAQ,eAAe,KAAK;AAC5B,YAAI,CAAC,MAAM,UAAU;AACjB,sBAAY,eAAe,EAAE,KAAK,UAAU,KAAK;AAAA,QACrD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;AACA,SAAS,6BAA6B,UAAU,OAAO;AACnD,MAAI,YAAI,wBAAwB;AAC5B,IAAO,KAAK,0BAA0B,SAAS,KAAK;AAAA,EACxD,WACS,CAAC,YAAI,sBAAsB;AAChC,IAAO,KAAK,0BAA0B,OAAO,KAAK;AAAA,EACtD;AACA,WAAS,MAAM,iBAAiB;AAC5B,aAAS,oBAAoB,OAAO;AAChC,cAAQ,eAAe,KAAK;AAC5B,UAAI,CAAC,UAAU,UAAU,MAAM,MAAM,GAAG;AACpC,gBAAQ,qBAAqB,UAAU,KAAK;AAC5C,cAAM,YAAY,eAAe,EAAE,KAAK,UAAU,KAAK;AAAA,MAC3D;AAAA,IACJ;AACA,gCAA4B,OAAO,iBAAiB,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAAA,EAC9F;AACJ;AACA,SAAS,4BAA4B,OAAO,iBAAiB,UAAU,KAAK;AACxE,QAAM,QAAQ,eAAe,IAAI;AACjC,QAAM,aAAa,eAAe,IAAI;AACtC,mBAAiB,MAAM,WAAW,iBAAiB,UAAU,GAAG;AACpE;AACA,SAAS,yBAAyB,OAAO;AACrC,MAAI,UAAU,MAAM;AACpB,WAAS,mBAAmB,SAAS;AACjC,QAAI,QAAQ,eAAe,eAAe,GAAG;AACzC,0BAAoB,MAAM,WAAW,iBAAiB,QAAQ,eAAe,GAAG,MAAM,aAAa,eAAe,CAAC;AAAA,IACvH;AAAA,EACJ;AACA,QAAM,UAAU,CAAC;AACrB;AACA,IAAI,kBAAmB,2BAAY;AAC/B,WAASC,iBAAgB,WAAW,aAAa;AAC7C,SAAK,UAAU,CAAC;AAChB,SAAK,eAAe,CAAC;AACrB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AACF,IAAI,kBAAmB,SAAU,QAAQ;AACrC,YAAUC,kBAAiB,MAAM;AACjC,WAASA,iBAAgB,KAAK,aAAa;AACvC,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,qBAAqB;AAC3B,UAAM,MAAM;AACZ,UAAM,cAAc;AACpB,UAAM,qBAAqB,IAAI,gBAAgB,KAAK,gBAAgB;AACpE,QAAI,sBAAsB;AACtB,YAAM,sBAAsB,IAAI,gBAAgB,UAAU,iBAAiB;AAAA,IAC/E;AACA,gCAA4B,OAAO,MAAM,kBAAkB;AAC3D,WAAO;AAAA,EACX;AACA,EAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,6BAAyB,KAAK,kBAAkB;AAChD,QAAI,sBAAsB;AACtB,+BAAyB,KAAK,mBAAmB;AAAA,IACrD;AAAA,EACJ;AACA,EAAAA,iBAAgB,UAAU,YAAY,SAAU,aAAa;AACzD,SAAK,IAAI,UAAU,KAAK,IAAI,MAAM,SAAS,eAAe;AAAA,EAC9D;AACA,EAAAA,iBAAgB,UAAU,yBAAyB,SAAU,oBAAoB;AAC7E,SAAK,sBAAsB;AAC3B,QAAI,wBACK,CAAC,KAAK,qBAAuB,CAAC,oBAAsB;AACzD,WAAK,qBAAqB;AAC1B,UAAI,qBAAqB,KAAK;AAC9B,2BACM,6BAA6B,MAAM,kBAAkB,IACrD,yBAAyB,kBAAkB;AAAA,IACrD;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,gBAAQ;AACV,IAAO,uBAAQ;;;ATlRf,IAAI,eAAe,CAAC;AACpB,IAAI,YAAY,CAAC;AACjB,SAAS,YAAY,IAAI;AACrB,SAAO,UAAU,EAAE;AACvB;AACA,SAAS,WAAW,iBAAiB;AACjC,MAAI,CAAC,iBAAiB;AAClB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,oBAAoB,UAAU;AACrC,WAAO,IAAI,iBAAiB,CAAC,IAAI;AAAA,EACrC,WACS,gBAAgB,YAAY;AACjC,QAAI,aAAa,gBAAgB;AACjC,QAAI,WAAW;AACf,QAAI,MAAM,WAAW;AACrB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,kBAAY,IAAI,WAAW,CAAC,EAAE,OAAO,CAAC;AAAA,IAC1C;AACA,gBAAY;AACZ,WAAO,WAAW;AAAA,EACtB;AACA,SAAO;AACX;AACA,IAAI,UAAW,WAAY;AACvB,WAASC,SAAQ,IAAI,KAAK,MAAM;AAC5B,QAAI,QAAQ;AACZ,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,WAAO,QAAQ,CAAC;AAChB,SAAK,MAAM;AACX,SAAK,KAAK;AACV,QAAI,UAAU,IAAI,gBAAQ;AAC1B,QAAI,eAAe,KAAK,YAAY;AACpC,QAAI,CAAC,aAAa,YAAY,GAAG;AAC7B,qBAAsB,KAAK,YAAY,EAAE,CAAC;AAAA,IAC9C;AACA,QAAI,MAAuC;AACvC,UAAI,CAAC,aAAa,YAAY,GAAG;AAC7B,cAAM,IAAI,MAAM,eAAe,eAAe,4CAA4C;AAAA,MAC9F;AAAA,IACJ;AACA,SAAK,eAAe,KAAK,gBAAgB,OACnC,QACA,KAAK;AACX,QAAI,UAAU,IAAI,aAAa,YAAY,EAAE,KAAK,SAAS,MAAM,EAAE;AACnE,QAAI,UAAU,KAAK,OAAO,QAAQ;AAClC,SAAK,UAAU;AACf,SAAK,UAAU;AACf,QAAI,eAAgB,CAAC,YAAI,QAAQ,CAAC,YAAI,UAAU,CAAC,UAC3C,IAAI,qBAAa,QAAQ,gBAAgB,GAAG,QAAQ,IAAI,IACxD;AACN,QAAI,mBAAmB,KAAK;AAC5B,QAAI,iBAAkB,oBAAoB,QAAQ,qBAAqB,SACjE,YAAI,uBACJ,CAAC,CAAC;AACR,QAAI,qBAAqB;AACzB,QAAI;AACJ,QAAI,gBAAgB;AAChB,oBAAqB,UAAU,KAAK,aAAa,kBAAkB;AAAA,IACvE;AACA,SAAK,UAAU,IAAI,gBAAQ,SAAS,SAAS,cAAc,QAAQ,MAAM,WAAW;AACpF,SAAK,YAAY,IAAI,kBAAU;AAAA,MAC3B,OAAO;AAAA,QACH,QAAQ,UAAU,OAAO,WAAY;AAAE,iBAAO,MAAM,OAAO,IAAI;AAAA,QAAG;AAAA,MACtE;AAAA,IACJ,CAAC;AACD,QAAI,CAAC,SAAS;AACV,WAAK,UAAU,MAAM;AAAA,IACzB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,IAAI;AAClC,QAAI,KAAK,aAAa,CAAC,IAAI;AACvB;AAAA,IACJ;AACA,SAAK,QAAQ,QAAQ,EAAE;AACvB,OAAG,YAAY,IAAI;AACnB,SAAK,QAAQ;AAAA,EACjB;AACA,EAAAA,SAAQ,UAAU,SAAS,SAAU,IAAI;AACrC,QAAI,KAAK,aAAa,CAAC,IAAI;AACvB;AAAA,IACJ;AACA,SAAK,QAAQ,QAAQ,EAAE;AACvB,OAAG,iBAAiB,IAAI;AACxB,SAAK,QAAQ;AAAA,EACjB;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,QAAQ,QAAQ;AACtD,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,aAAa;AAC1B,WAAK,QAAQ,YAAY,QAAQ,MAAM;AAAA,IAC3C;AACA,SAAK,QAAQ;AAAA,EACjB;AACA,EAAAA,SAAQ,UAAU,qBAAqB,SAAU,iBAAiB;AAC9D,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,oBAAoB;AACjC,WAAK,QAAQ,mBAAmB,eAAe;AAAA,IACnD;AACA,SAAK,QAAQ;AACb,SAAK,mBAAmB;AACxB,SAAK,YAAY,WAAW,eAAe;AAAA,EAC/C;AACA,EAAAA,SAAQ,UAAU,qBAAqB,WAAY;AAC/C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,UAAU;AAChD,SAAK,YAAY;AAAA,EACrB;AACA,EAAAA,SAAQ,UAAU,aAAa,WAAY;AACvC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,SAAQ,UAAU,qBAAqB,SAAU,YAAY;AACzD,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,QAAI,CAAC,YAAY;AACb,WAAK,UAAU,OAAO,IAAI;AAAA,IAC9B;AACA,SAAK,gBAAgB;AACrB,SAAK,QAAQ,QAAQ;AACrB,SAAK,gBAAgB;AAAA,EACzB;AACA,EAAAA,SAAQ,UAAU,UAAU,WAAY;AACpC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,gBAAgB;AACrB,SAAK,UAAU,MAAM;AAAA,EACzB;AACA,EAAAA,SAAQ,UAAU,QAAQ,WAAY;AAClC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,OAAO,KAAK;AAAA,EACrB;AACA,EAAAA,SAAQ,UAAU,SAAS,SAAU,YAAY;AAC7C,QAAI;AACJ,QAAI,QAAQ,QAAQ;AACpB,QAAI,KAAK,eAAe;AACpB,wBAAkB;AAClB,WAAK,mBAAmB,UAAU;AAAA,IACtC;AACA,QAAI,KAAK,oBAAoB;AACzB,wBAAkB;AAClB,WAAK,wBAAwB;AAAA,IACjC;AACA,QAAI,MAAM,QAAQ;AAClB,QAAI,iBAAiB;AACjB,WAAK,mBAAmB;AACxB,WAAK,QAAQ,YAAY;AAAA,QACrB,aAAa,MAAM;AAAA,MACvB,CAAC;AAAA,IACL,WACS,KAAK,mBAAmB,GAAG;AAChC,WAAK;AACL,UAAI,KAAK,mBAAmB,KAAK,kBAAkB;AAC/C,aAAK,UAAU,KAAK;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,qBAAqB,SAAU,kBAAkB;AAC/D,SAAK,mBAAmB;AAAA,EAC5B;AACA,EAAAA,SAAQ,UAAU,SAAS,WAAY;AACnC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,UAAU,MAAM;AACrB,SAAK,mBAAmB;AAAA,EAC5B;AACA,EAAAA,SAAQ,UAAU,eAAe,WAAY;AACzC,SAAK,qBAAqB;AAAA,EAC9B;AACA,EAAAA,SAAQ,UAAU,0BAA0B,WAAY;AACpD,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,qBAAqB;AAC1B,QAAI,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,QAAQ,MAAM,UAAU;AAClE,WAAK,QAAQ,aAAa;AAAA,IAC9B;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,SAAS,SAAU,MAAM;AACvC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,WAAO,QAAQ,CAAC;AAChB,SAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,MAAM;AAC3C,SAAK,QAAQ,OAAO;AAAA,EACxB;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC3C,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,UAAU,MAAM;AAAA,EACzB;AACA,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,WAAO,KAAK,QAAQ,SAAS;AAAA,EACjC;AACA,EAAAA,SAAQ,UAAU,YAAY,WAAY;AACtC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,WAAO,KAAK,QAAQ,UAAU;AAAA,EAClC;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,aAAa;AACtD,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,QAAQ,eAAe,WAAW;AAAA,EAC3C;AACA,EAAAA,SAAQ,UAAU,YAAY,SAAU,GAAG,GAAG;AAC1C,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,WAAO,KAAK,QAAQ,UAAU,GAAG,CAAC;AAAA,EACtC;AACA,EAAAA,SAAQ,UAAU,KAAK,SAAU,WAAW,cAAc,SAAS;AAC/D,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,QAAQ,GAAG,WAAW,cAAc,OAAO;AAAA,IACpD;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,WAAW,cAAc;AACvD,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,QAAQ,IAAI,WAAW,YAAY;AAAA,EAC5C;AACA,EAAAA,SAAQ,UAAU,UAAU,SAAU,WAAW,OAAO;AACpD,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,QAAQ,QAAQ,WAAW,KAAK;AAAA,EACzC;AACA,EAAAA,SAAQ,UAAU,QAAQ,WAAY;AAClC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,MAAM,CAAC,aAAa,eAAO;AAC3B,cAAM,CAAC,EAAE,iBAAiB,IAAI;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,QAAQ,YAAY;AACzB,SAAK,QAAQ,MAAM;AAAA,EACvB;AACA,EAAAA,SAAQ,UAAU,UAAU,WAAY;AACpC,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,MAAM;AACX,SAAK,QAAQ,QAAQ;AACrB,SAAK,QAAQ,QAAQ;AACrB,SAAK,QAAQ,QAAQ;AACrB,SAAK,YACD,KAAK,UACD,KAAK,UACD,KAAK,UAAU;AAC3B,SAAK,YAAY;AACjB,gBAAY,KAAK,EAAE;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AACK,SAAS,KAAK,KAAK,MAAM;AAC5B,MAAI,KAAK,IAAI,QAAe,KAAK,GAAG,KAAK,IAAI;AAC7C,YAAU,GAAG,EAAE,IAAI;AACnB,SAAO;AACX;AACO,SAAS,QAAQ,IAAI;AACxB,KAAG,QAAQ;AACf;AACO,SAAS,aAAa;AACzB,WAAS,OAAO,WAAW;AACvB,QAAI,UAAU,eAAe,GAAG,GAAG;AAC/B,gBAAU,GAAG,EAAE,QAAQ;AAAA,IAC3B;AAAA,EACJ;AACA,cAAY,CAAC;AACjB;AACO,SAAS,YAAY,IAAI;AAC5B,SAAO,UAAU,EAAE;AACvB;AACO,SAAS,gBAAgB,MAAM,MAAM;AACxC,eAAa,IAAI,IAAI;AACzB;AACA,IAAI;AACG,SAAS,kBAAkB,IAAI;AAClC,MAAI,OAAO,kBAAkB,YAAY;AACrC,WAAO,cAAc,EAAE;AAAA,EAC3B;AACJ;AACO,SAAS,sBAAsB,QAAQ;AAC1C,kBAAgB;AACpB;AACO,IAAI,UAAU;;;AUtUrB,SAAS,UAAU,KAAK;AACpB,SAAO,SAAS,GAAG;AACvB;AACO,SAAS,qBAAqB,KAAK,KAAK,MAAM;AACjD,MAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI;AAChC,MAAI,KAAK,IAAI,MAAM,OAAO,IAAI,IAAI;AAClC,MAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI;AAChC,MAAI,KAAK,IAAI,MAAM,OAAO,IAAI,IAAI;AAClC,MAAI,CAAC,IAAI,QAAQ;AACb,QAAI,IAAI,KAAK,QAAQ,KAAK;AAC1B,SAAK,KAAK,KAAK,QAAQ,KAAK;AAC5B,QAAI,IAAI,KAAK,SAAS,KAAK;AAC3B,SAAK,KAAK,KAAK,SAAS,KAAK;AAAA,EACjC;AACA,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,OAAK,UAAU,EAAE,IAAI,KAAK;AAC1B,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,OAAK,UAAU,EAAE,IAAI,KAAK;AAC1B,MAAI,iBAAiB,IAAI,qBAAqB,GAAG,GAAG,IAAI,EAAE;AAC1D,SAAO;AACX;AACO,SAAS,qBAAqB,KAAK,KAAK,MAAM;AACjD,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM,KAAK,IAAI,OAAO,MAAM;AAChC,MAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI;AAClC,MAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI;AAClC,MAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI;AAClC,MAAI,CAAC,IAAI,QAAQ;AACb,QAAI,IAAI,QAAQ,KAAK;AACrB,QAAI,IAAI,SAAS,KAAK;AACtB,QAAI,IAAI;AAAA,EACZ;AACA,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,MAAI,UAAU,CAAC,IAAI,IAAI;AACvB,MAAI,KAAK,KAAK,UAAU,CAAC,IAAI,IAAI;AACjC,MAAI,iBAAiB,IAAI,qBAAqB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9D,SAAO;AACX;AACO,SAAS,kBAAkB,KAAK,KAAK,MAAM;AAC9C,MAAI,iBAAiB,IAAI,SAAS,WAC5B,qBAAqB,KAAK,KAAK,IAAI,IACnC,qBAAqB,KAAK,KAAK,IAAI;AACzC,MAAI,aAAa,IAAI;AACrB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,mBAAe,aAAa,WAAW,CAAC,EAAE,QAAQ,WAAW,CAAC,EAAE,KAAK;AAAA,EACzE;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,WAAW,eAAe;AACxD,MAAI,cAAc,iBAAkB,CAAC,aAAa,CAAC,eAAgB;AAC/D,WAAO;AAAA,EACX;AACA,MAAI,CAAC,aAAa,CAAC,iBAAkB,UAAU,WAAW,cAAc,QAAS;AAC7E,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,UAAU,CAAC,MAAM,cAAc,CAAC,GAAG;AACnC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,KAAK;AACrB,SAAO,SAAS,KAAK,EAAE;AAC3B;AACO,SAAS,QAAQ,MAAM,OAAO,MAAM;AACvC,MAAI,KAAK,CAAC,SAAS,QAAQ,EAAE,KAAK;AAClC,MAAI,MAAM,CAAC,eAAe,cAAc,EAAE,KAAK;AAC/C,MAAI,MAAM,CAAC,eAAe,YAAY,EAAE,KAAK;AAC7C,MAAI,MAAM,CAAC,gBAAgB,eAAe,EAAE,KAAK;AACjD,MAAI,KAAK,EAAE,KAAK,QAAQ,KAAK,EAAE,MAAM,QAAQ;AACzC,WAAO,WAAW,KAAK,EAAE,CAAC;AAAA,EAC9B;AACA,MAAI,MAAM,SAAS,YAAY,iBAAiB,IAAI;AACpD,UAAS,KAAK,GAAG,KAAK,WAAW,IAAI,EAAE,CAAC,KAAK,WAAW,KAAK,MAAM,EAAE,CAAC,MAC/D,WAAW,IAAI,GAAG,CAAC,KAAK,MACxB,WAAW,IAAI,GAAG,CAAC,KAAK,KAAM;AACzC;;;AC7EO,SAAS,kBAAkB,UAAU,WAAW;AACnD,MAAI,CAAC,YAAY,aAAa,WAAW,EAAE,YAAY,IAAI;AACvD,WAAO;AAAA,EACX;AACA,SAAO,aAAa,WACd,CAAC,IAAI,WAAW,IAAI,SAAS,IAC7B,aAAa,WACT,CAAC,SAAS,IACV,SAAS,QAAQ,IACb,CAAC,QAAQ,IAAI,QAAQ,QAAQ,IAAI,WAAW;AAC9D;AACO,SAAS,YAAY,IAAI;AAC5B,MAAI,QAAQ,GAAG;AACf,MAAI,WAAW,MAAM,YAAY,MAAM,YAAY,KAAK,kBAAkB,MAAM,UAAU,MAAM,SAAS;AACzG,MAAI,iBAAiB,MAAM;AAC3B,MAAI,UAAU;AACV,QAAI,cAAe,MAAM,iBAAiB,GAAG,eAAgB,GAAG,aAAa,IAAI;AACjF,QAAI,eAAe,gBAAgB,GAAG;AAClC,iBAAW,IAAI,UAAU,SAAU,QAAQ;AACvC,eAAO,SAAS;AAAA,MACpB,CAAC;AACD,wBAAkB;AAAA,IACtB;AAAA,EACJ;AACA,SAAO,CAAC,UAAU,cAAc;AACpC;;;ACfA,IAAI,mBAAmB,IAAI,kBAAU,IAAI;AACzC,SAAS,eAAe,OAAO;AAC3B,MAAI,SAAS,MAAM;AACnB,SAAO,EAAE,UAAU,QAAQ,WAAW,UAAU,EAAE,MAAM,YAAY;AACxE;AACA,SAAS,uBAAuB,cAAc;AAC1C,SAAO,OAAO,iBAAiB,YAAY,iBAAiB;AAChE;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,OAAO,MAAM;AACjB,SAAO,QAAQ,QAAQ,SAAS;AACpC;AACA,SAAS,WAAW,KAAK,OAAO;AAC5B,MAAI,MAAM,eAAe,QAAQ,MAAM,gBAAgB,GAAG;AACtD,QAAI,sBAAsB,IAAI;AAC9B,QAAI,cAAc,MAAM,cAAc,MAAM;AAC5C,QAAI,KAAK;AACT,QAAI,cAAc;AAAA,EACtB,OACK;AACD,QAAI,KAAK;AAAA,EACb;AACJ;AACA,SAAS,aAAa,KAAK,OAAO;AAC9B,MAAI,MAAM,iBAAiB,QAAQ,MAAM,kBAAkB,GAAG;AAC1D,QAAI,sBAAsB,IAAI;AAC9B,QAAI,cAAc,MAAM,gBAAgB,MAAM;AAC9C,QAAI,OAAO;AACX,QAAI,cAAc;AAAA,EACtB,OACK;AACD,QAAI,OAAO;AAAA,EACf;AACJ;AACO,SAAS,oBAAoB,KAAK,SAAS,IAAI;AAClD,MAAI,QAAQ,oBAAoB,QAAQ,OAAO,QAAQ,SAAS,EAAE;AAClE,MAAI,aAAa,KAAK,GAAG;AACrB,QAAI,gBAAgB,IAAI,cAAc,OAAO,QAAQ,UAAU,QAAQ;AACvE,QAAI,OAAO,cAAc,cAClB,iBACA,cAAc,cAAc;AAC/B,UAAI,SAAS,IAAI,UAAU;AAC3B,aAAO,cAAe,QAAQ,KAAK,GAAK,QAAQ,KAAK,CAAE;AACvD,aAAO,WAAW,GAAG,IAAI,QAAQ,YAAY,KAAK,gBAAgB;AAClE,aAAO,UAAW,QAAQ,UAAU,GAAK,QAAQ,UAAU,CAAE;AAC7D,oBAAc,aAAa,MAAM;AAAA,IACrC;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,KAAK,IAAI,OAAO,SAAS;AACxC,MAAI;AACJ,MAAI,YAAY,eAAe,KAAK;AACpC,MAAI,UAAU,aAAa,KAAK;AAChC,MAAI,gBAAgB,MAAM;AAC1B,MAAI,aAAa,gBAAgB;AACjC,MAAI,YAAY,CAAC,GAAG;AACpB,OAAK,CAAC,GAAG,UAAU,eAAe,WAAW;AACzC,OAAG,gBAAgB;AAAA,EACvB;AACA,MAAI,OAAO,GAAG,QAAQ;AACtB,MAAI,YAAY,GAAG;AACnB,MAAI,CAAC,SAAS;AACV,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,QAAI,kBAAkB,WAAW,CAAC,CAAC,KAAK;AACxC,QAAI,oBAAoB,aAAa,CAAC,CAAC,OAAO;AAC9C,QAAI,iBAAiB,WAAW,CAAC,CAAC,KAAK;AACvC,QAAI,mBAAmB,aAAa,CAAC,CAAC,OAAO;AAC7C,QAAI,eAAe;AACnB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,OAAO;AACX,QAAI,mBAAmB,mBAAmB;AACtC,aAAO,GAAG,gBAAgB;AAAA,IAC9B;AACA,QAAI,iBAAiB;AACjB,qBAAe,YACT,kBAAkB,KAAK,MAAM,IAAI,IACjC,GAAG;AACT,SAAG,uBAAuB;AAAA,IAC9B;AACA,QAAI,mBAAmB;AACnB,uBAAiB,YACX,kBAAkB,KAAK,QAAQ,IAAI,IACnC,GAAG;AACT,SAAG,yBAAyB;AAAA,IAChC;AACA,QAAI,gBAAgB;AAChB,oBAAe,aAAa,CAAC,GAAG,sBAC1B,oBAAoB,KAAK,MAAM,EAAE,IACjC,GAAG;AACT,SAAG,sBAAsB;AAAA,IAC7B;AACA,QAAI,kBAAkB;AAClB,sBAAiB,aAAa,CAAC,GAAG,wBAC5B,oBAAoB,KAAK,QAAQ,EAAE,IACnC,GAAG;AACT,SAAG,wBAAwB;AAAA,IAC/B;AACA,QAAI,iBAAiB;AACjB,UAAI,YAAY;AAAA,IACpB,WACS,gBAAgB;AACrB,UAAI,aAAa;AACb,YAAI,YAAY;AAAA,MACpB,OACK;AACD,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,QAAI,mBAAmB;AACnB,UAAI,cAAc;AAAA,IACtB,WACS,kBAAkB;AACvB,UAAI,eAAe;AACf,YAAI,cAAc;AAAA,MACtB,OACK;AACD,oBAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,QAAQ,GAAG,eAAe;AAC9B,OAAK,SAAS,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,sBAAsB;AAC3D,MAAI;AACJ,MAAI;AACJ,MAAI,IAAI,eAAe,MAAM,UAAU;AACnC,SAAK,YAAY,EAAE,GAAG,WAAW,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAAA,EACjE;AACA,MAAI,eAAe;AACnB,MAAI,aAAc,YAAY,mBAAoB;AAC9C,SAAK,OAAO,IAAI,GAAG;AACnB,QAAI,YAAY;AACZ,WAAK,WAAW,IAAI;AAAA,IACxB,OACK;AACD,WAAK,WAAW,GAAG;AACnB,qBAAe;AAAA,IACnB;AACA,SAAK,MAAM;AACX,OAAG,UAAU,MAAM,GAAG,OAAO,OAAO;AACpC,SAAK,SAAS;AACd,OAAG,YAAY;AAAA,EACnB;AACA,MAAI,cAAc;AACd,SAAK,YAAY,KAAK,aAAa,gBAAgB,CAAC;AAAA,EACxD;AACA,MAAI,UAAU;AACV,QAAI,YAAY,QAAQ;AACxB,QAAI,iBAAiB;AAAA,EACzB;AACA,MAAI,CAAC,SAAS;AACV,QAAI,MAAM,aAAa;AACnB,UAAI,WAAW;AACX,qBAAa,KAAK,KAAK;AAAA,MAC3B;AACA,UAAI,SAAS;AACT,mBAAW,KAAK,KAAK;AAAA,MACzB;AAAA,IACJ,OACK;AACD,UAAI,SAAS;AACT,mBAAW,KAAK,KAAK;AAAA,MACzB;AACA,UAAI,WAAW;AACX,qBAAa,KAAK,KAAK;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU;AACV,QAAI,YAAY,CAAC,CAAC;AAAA,EACtB;AACJ;AACA,SAAS,WAAW,KAAK,IAAI,OAAO;AAChC,MAAI,QAAQ,GAAG,UAAU,oBAAoB,MAAM,OAAO,GAAG,SAAS,IAAI,GAAG,MAAM;AACnF,MAAI,CAAC,SAAS,CAAC,aAAa,KAAK,GAAG;AAChC;AAAA,EACJ;AACA,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,QAAQ,GAAG,SAAS;AACxB,MAAI,SAAS,GAAG,UAAU;AAC1B,MAAI,SAAS,MAAM,QAAQ,MAAM;AACjC,MAAI,SAAS,QAAQ,UAAU,MAAM;AACjC,YAAQ,SAAS;AAAA,EACrB,WACS,UAAU,QAAQ,SAAS,MAAM;AACtC,aAAS,QAAQ;AAAA,EACrB,WACS,SAAS,QAAQ,UAAU,MAAM;AACtC,YAAQ,MAAM;AACd,aAAS,MAAM;AAAA,EACnB;AACA,MAAI,MAAM,UAAU,MAAM,SAAS;AAC/B,QAAI,KAAK,MAAM,MAAM;AACrB,QAAI,KAAK,MAAM,MAAM;AACrB,QAAI,UAAU,OAAO,IAAI,IAAI,MAAM,QAAQ,MAAM,SAAS,GAAG,GAAG,OAAO,MAAM;AAAA,EACjF,WACS,MAAM,MAAM,MAAM,IAAI;AAC3B,QAAI,KAAK,MAAM;AACf,QAAI,KAAK,MAAM;AACf,QAAI,SAAS,QAAQ;AACrB,QAAI,UAAU,SAAS;AACvB,QAAI,UAAU,OAAO,IAAI,IAAI,QAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AAAA,EACrE,OACK;AACD,QAAI,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAAA,EAC5C;AACJ;AACA,SAAS,UAAU,KAAK,IAAI,OAAO;AAC/B,MAAI;AACJ,MAAI,OAAO,MAAM;AACjB,UAAQ,SAAS,QAAQ;AACzB,MAAI,MAAM;AACN,QAAI,OAAO,MAAM,QAAQ;AACzB,QAAI,YAAY,MAAM;AACtB,QAAI,eAAe,MAAM;AACzB,QAAI,WAAW;AACf,QAAI,iBAAiB;AACrB,QAAI,IAAI,eAAe,MAAM,UAAU;AACnC,WAAK,YAAY,EAAE,GAAG,WAAW,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAAA,IACjE;AACA,QAAI,UAAU;AACV,UAAI,YAAY,QAAQ;AACxB,UAAI,iBAAiB;AAAA,IACzB;AACA,QAAI,MAAM,aAAa;AACnB,UAAI,eAAe,KAAK,GAAG;AACvB,YAAI,WAAW,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACzC;AACA,UAAI,aAAa,KAAK,GAAG;AACrB,YAAI,SAAS,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACvC;AAAA,IACJ,OACK;AACD,UAAI,aAAa,KAAK,GAAG;AACrB,YAAI,SAAS,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACvC;AACA,UAAI,eAAe,KAAK,GAAG;AACvB,YAAI,WAAW,MAAM,MAAM,GAAG,MAAM,CAAC;AAAA,MACzC;AAAA,IACJ;AACA,QAAI,UAAU;AACV,UAAI,YAAY,CAAC,CAAC;AAAA,IACtB;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,CAAC,cAAc,iBAAiB,eAAe;AACzE,IAAI,eAAe;AAAA,EACf,CAAC,WAAW,MAAM;AAAA,EAAG,CAAC,YAAY,OAAO;AAAA,EAAG,CAAC,cAAc,EAAE;AACjE;AACA,SAAS,gBAAgB,KAAK,OAAO,WAAW,aAAa,OAAO;AAChE,MAAI,eAAe;AACnB,MAAI,CAAC,aAAa;AACd,gBAAY,aAAa,CAAC;AAC1B,QAAI,UAAU,WAAW;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,eAAe,MAAM,YAAY,UAAU,SAAS;AACpD,mBAAe,KAAK,KAAK;AACzB,mBAAe;AACf,QAAI,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS,CAAC,GAAG,CAAC;AACpD,QAAI,cAAc,MAAM,OAAO,IAAI,qBAAqB,UAAU;AAAA,EACtE;AACA,MAAI,eAAe,MAAM,UAAU,UAAU,OAAO;AAChD,QAAI,CAAC,cAAc;AACf,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACnB;AACA,QAAI,2BAA2B,MAAM,SAAS,qBAAqB;AAAA,EACvE;AACA,WAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,QAAI,WAAW,oBAAoB,CAAC;AACpC,QAAI,eAAe,MAAM,QAAQ,MAAM,UAAU,QAAQ,GAAG;AACxD,UAAI,CAAC,cAAc;AACf,uBAAe,KAAK,KAAK;AACzB,uBAAe;AAAA,MACnB;AACA,UAAI,QAAQ,IAAI,IAAI,OAAO,MAAM,QAAQ,KAAK;AAAA,IAClD;AAAA,EACJ;AACA,MAAI,eAAe,MAAM,gBAAgB,UAAU,aAAa;AAC5D,QAAI,CAAC,cAAc;AACf,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACnB;AACA,QAAI,cAAc,MAAM,eAAe,qBAAqB;AAAA,EAChE;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,KAAK,IAAI,QAAQ,aAAa,OAAO;AACrE,MAAI,QAAQ,SAAS,IAAI,MAAM,OAAO;AACtC,MAAI,YAAY,cACV,OACC,UAAU,SAAS,QAAQ,MAAM,OAAO,KAAK,CAAC;AACrD,MAAI,UAAU,WAAW;AACrB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,gBAAgB,KAAK,OAAO,WAAW,aAAa,KAAK;AAC5E,MAAI,eAAe,MAAM,SAAS,UAAU,MAAM;AAC9C,QAAI,CAAC,cAAc;AACf,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACnB;AACA,2BAAuB,MAAM,IAAI,MAAM,IAAI,YAAY,MAAM;AAAA,EACjE;AACA,MAAI,eAAe,MAAM,WAAW,UAAU,QAAQ;AAClD,QAAI,CAAC,cAAc;AACf,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACnB;AACA,2BAAuB,MAAM,MAAM,MAAM,IAAI,cAAc,MAAM;AAAA,EACrE;AACA,MAAI,eAAe,MAAM,YAAY,UAAU,SAAS;AACpD,QAAI,CAAC,cAAc;AACf,qBAAe,KAAK,KAAK;AACzB,qBAAe;AAAA,IACnB;AACA,QAAI,cAAc,MAAM,WAAW,OAAO,IAAI,MAAM;AAAA,EACxD;AACA,MAAI,GAAG,UAAU,GAAG;AAChB,QAAI,YAAY,MAAM;AACtB,QAAI,eAAe,aAAc,MAAM,iBAAiB,GAAG,eAAgB,GAAG,aAAa,IAAI;AAC/F,QAAI,IAAI,cAAc,cAAc;AAChC,UAAI,CAAC,cAAc;AACf,uBAAe,KAAK,KAAK;AACzB,uBAAe;AAAA,MACnB;AACA,UAAI,YAAY;AAAA,IACpB;AAAA,EACJ;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,QAAI,OAAO,aAAa,CAAC;AACzB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,eAAe,MAAM,QAAQ,MAAM,UAAU,QAAQ,GAAG;AACxD,UAAI,CAAC,cAAc;AACf,uBAAe,KAAK,KAAK;AACzB,uBAAe;AAAA,MACnB;AACA,UAAI,QAAQ,IAAI,MAAM,QAAQ,KAAK,KAAK,CAAC;AAAA,IAC7C;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,KAAK,IAAI,QAAQ,aAAa,OAAO;AACzD,SAAO,gBAAgB,KAAK,SAAS,IAAI,MAAM,OAAO,GAAG,UAAU,SAAS,QAAQ,MAAM,OAAO,GAAG,aAAa,KAAK;AAC1H;AACA,SAAS,oBAAoB,KAAK,IAAI;AAClC,MAAI,IAAI,GAAG;AACX,MAAI,MAAM,IAAI,OAAO;AACrB,MAAI,GAAG;AACH,QAAI,aAAa,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;AAAA,EAC3F,OACK;AACD,QAAI,aAAa,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC;AAAA,EACzC;AACJ;AACA,SAAS,iBAAiB,WAAW,KAAK,OAAO;AAC7C,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,WAAW,UAAU,CAAC;AAC1B,iBAAa,cAAc,SAAS,WAAW;AAC/C,wBAAoB,KAAK,QAAQ;AACjC,QAAI,UAAU;AACd,aAAS,UAAU,KAAK,SAAS,KAAK;AACtC,QAAI,KAAK;AAAA,EACb;AACA,QAAM,aAAa;AACvB;AACA,SAAS,mBAAmB,IAAI,IAAI;AAChC,MAAI,MAAM,IAAI;AACV,WAAO,GAAG,CAAC,MAAM,GAAG,CAAC,KACd,GAAG,CAAC,MAAM,GAAG,CAAC,KACd,GAAG,CAAC,MAAM,GAAG,CAAC,KACd,GAAG,CAAC,MAAM,GAAG,CAAC,KACd,GAAG,CAAC,MAAM,GAAG,CAAC,KACd,GAAG,CAAC,MAAM,GAAG,CAAC;AAAA,EACzB,WACS,CAAC,MAAM,CAAC,IAAI;AACjB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,IAAI,wBAAwB;AAC5B,SAAS,aAAa,OAAO;AACzB,MAAI,UAAU,aAAa,KAAK;AAChC,MAAI,YAAY,eAAe,KAAK;AACpC,SAAO,EAAE,MAAM,YACR,EAAE,CAAC,UAAU,CAAC,cACb,WAAW,OAAO,MAAM,SAAS,YACjC,aAAa,OAAO,MAAM,WAAW,YACtC,MAAM,gBAAgB,KACtB,MAAM,gBAAgB,KACtB,MAAM,cAAc;AAC/B;AACA,SAAS,eAAe,KAAK,OAAO;AAChC,QAAM,aAAa,IAAI,KAAK;AAC5B,QAAM,eAAe,IAAI,OAAO;AAChC,QAAM,YAAY;AAClB,QAAM,cAAc;AACxB;AACA,SAAS,SAAS,IAAI,SAAS;AAC3B,SAAO,UAAW,GAAG,gBAAgB,GAAG,QAAS,GAAG;AACxD;AACO,SAAS,YAAY,KAAK,IAAI;AACjC,QAAM,KAAK,IAAI,EAAE,SAAS,OAAO,WAAW,GAAG,YAAY,EAAE,GAAG,IAAI;AACxE;AACO,SAAS,MAAM,KAAK,IAAI,OAAO,QAAQ;AAC1C,MAAI,IAAI,GAAG;AACX,MAAI,CAAC,GAAG,gBAAgB,MAAM,WAAW,MAAM,YAAY,OAAO,KAAK,GAAG;AACtE,OAAG,WAAW,CAAC;AACf,OAAG,eAAe;AAClB;AAAA,EACJ;AACA,MAAI,YAAY,GAAG;AACnB,MAAI,kBAAkB,MAAM;AAC5B,MAAI,oBAAoB;AACxB,MAAI,gBAAgB;AACpB,MAAI,CAAC,mBAAmB,kBAAkB,WAAW,eAAe,GAAG;AACnE,QAAI,mBAAmB,gBAAgB,QAAQ;AAC3C,qBAAe,KAAK,KAAK;AACzB,UAAI,QAAQ;AACZ,sBAAgB,oBAAoB;AACpC,YAAM,kBAAkB;AACxB,YAAM,aAAa;AACnB,YAAM,SAAS;AAAA,IACnB;AACA,QAAI,aAAa,UAAU,QAAQ;AAC/B,qBAAe,KAAK,KAAK;AACzB,UAAI,KAAK;AACT,uBAAiB,WAAW,KAAK,KAAK;AACtC,0BAAoB;AAAA,IACxB;AACA,UAAM,kBAAkB;AAAA,EAC5B;AACA,MAAI,MAAM,YAAY;AAClB,OAAG,eAAe;AAClB;AAAA,EACJ;AACA,KAAG,eAAe,GAAG,YAAY;AACjC,KAAG,iBAAiB;AACpB,MAAI,SAAS,MAAM;AACnB,MAAI,CAAC,QAAQ;AACT,oBAAgB,oBAAoB;AAAA,EACxC;AACA,MAAI,eAAe,cAAc,gBAC1B,GAAG,aACH,aAAa,GAAG,KAAK;AAC5B,MAAI,qBAAqB,mBAAmB,GAAG,OAAO,SAAS,GAAG;AAC9D,mBAAe,KAAK,KAAK;AACzB,wBAAoB,KAAK,EAAE;AAAA,EAC/B,WACS,CAAC,cAAc;AACpB,mBAAe,KAAK,KAAK;AAAA,EAC7B;AACA,MAAI,QAAQ,SAAS,IAAI,MAAM,OAAO;AACtC,MAAI,cAAc,cAAM;AACpB,QAAI,MAAM,iBAAiB,gBAAgB;AACvC,sBAAgB;AAChB,YAAM,eAAe;AAAA,IACzB;AACA,+BAA2B,KAAK,IAAI,QAAQ,eAAe,KAAK;AAChE,QAAI,CAAC,gBAAiB,CAAC,MAAM,aAAa,CAAC,MAAM,aAAc;AAC3D,UAAI,UAAU;AAAA,IAClB;AACA,cAAU,KAAK,IAAI,OAAO,YAAY;AACtC,QAAI,cAAc;AACd,YAAM,YAAY,MAAM,QAAQ;AAChC,YAAM,cAAc,MAAM,UAAU;AAAA,IACxC;AAAA,EACJ,OACK;AACD,QAAI,cAAc,eAAO;AACrB,UAAI,MAAM,iBAAiB,gBAAgB;AACvC,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACzB;AACA,iCAA2B,KAAK,IAAI,QAAQ,eAAe,KAAK;AAChE,gBAAU,KAAK,IAAI,KAAK;AAAA,IAC5B,WACS,cAAc,eAAS;AAC5B,UAAI,MAAM,iBAAiB,iBAAiB;AACxC,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACzB;AACA,qBAAe,KAAK,IAAI,QAAQ,eAAe,KAAK;AACpD,iBAAW,KAAK,IAAI,KAAK;AAAA,IAC7B,WACS,GAAG,yBAAyB;AACjC,UAAI,MAAM,iBAAiB,uBAAuB;AAC9C,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACzB;AACA,uBAAiB,KAAK,IAAI,KAAK;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,gBAAgB,QAAQ;AACxB,mBAAe,KAAK,KAAK;AAAA,EAC7B;AACA,KAAG,gBAAgB;AACnB,KAAG,cAAc,GAAG,WAAW;AAC/B,QAAM,SAAS;AACf,KAAG,UAAU;AACb,KAAG,eAAe;AACtB;AACA,SAAS,iBAAiB,KAAK,IAAI,OAAO;AACtC,MAAI,eAAe,GAAG,gBAAgB;AACtC,MAAI,uBAAuB,GAAG,wBAAwB;AACtD,MAAI,KAAK;AACT,MAAI,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,WAAW,MAAM;AAAA,IACjB,YAAY,MAAM;AAAA,IAClB,SAAS,MAAM;AAAA,EACnB;AACA,MAAI;AACJ,MAAI;AACJ,OAAK,IAAI,GAAG,UAAU,GAAG,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK;AAC9D,QAAI,cAAc,aAAa,CAAC;AAChC,gBAAY,eAAe,YAAY,YAAY;AACnD,gBAAY,iBAAiB;AAC7B,UAAM,KAAK,aAAa,YAAY,MAAM,MAAM,CAAC;AACjD,gBAAY,gBAAgB;AAC5B,gBAAY,cAAc,YAAY,WAAW;AACjD,eAAW,SAAS;AAAA,EACxB;AACA,WAAS,MAAM,GAAG,QAAQ,qBAAqB,QAAQ,MAAM,OAAO,OAAO;AACvE,QAAI,cAAc,qBAAqB,GAAG;AAC1C,gBAAY,eAAe,YAAY,YAAY;AACnD,gBAAY,iBAAiB;AAC7B,UAAM,KAAK,aAAa,YAAY,QAAQ,QAAQ,CAAC;AACrD,gBAAY,gBAAgB;AAC5B,gBAAY,cAAc,YAAY,WAAW;AACjD,eAAW,SAAS;AAAA,EACxB;AACA,KAAG,0BAA0B;AAC7B,KAAG,WAAW;AACd,MAAI,QAAQ;AAChB;", "names": ["Param", "Draggable", "GestureMgr", "dist", "EmptyProxy", "HoveredResult", "Handler", "Storage", "Animation", "FakeGlobalEvent", "DOMHandlerScope", "HandlerDomProxy", "ZRender"]}