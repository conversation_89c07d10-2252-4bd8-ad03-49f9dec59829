{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/vn.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nexport function getOnName(type) {\n    return 'on' + type.substring(0, 1).toLocaleUpperCase() + type.substring(1);\n}\nexport function getModelEvent(name) {\n    switch (name) {\n        case 'input':\n        case 'textarea':\n            return 'input';\n        case 'select':\n            return 'change';\n    }\n    return 'update:modelValue';\n}\nexport function getChangeEvent(name) {\n    switch (name) {\n        case 'input':\n        case 'textarea':\n        case 'VxeInput':\n        case 'VxeTextarea':\n        case '$input': // 已废弃\n        case '$textarea': // 已废弃\n            return 'input';\n    }\n    return 'change';\n}\nexport function getSlotVNs(vns) {\n    if (XEUtils.isArray(vns)) {\n        return vns;\n    }\n    return vns ? [vns] : [];\n}\n"], "mappings": ";;;;;;;;AAAA,sBAAoB;AA0Bb,SAAS,WAAW,KAAK;AAC5B,MAAI,gBAAAA,QAAQ,QAAQ,GAAG,GAAG;AACtB,WAAO;AAAA,EACX;AACA,SAAO,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B;", "names": ["XEUtils"]}