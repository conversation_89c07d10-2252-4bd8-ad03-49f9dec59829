import {
  EMPTY_ARR,
  EMPTY_OBJ,
  NO,
  NOOP,
  PatchFlagNames,
  PatchFlags,
  ShapeFlags,
  SlotFlags,
  camelize,
  capitalize,
  cssVarNameEscapeSymbolsRE,
  def,
  escapeHtml,
  escapeHtmlComment,
  extend,
  genCacheKey,
  genPropsAccessExp,
  generateCodeFrame,
  getEscapedCssVarName,
  getGlobalThis,
  hasChanged,
  hasOwn,
  hyphenate,
  includeBooleanAttr,
  init_shared_esm_bundler,
  invokeArrayFns,
  isArray,
  isBooleanAttr,
  isBuiltInDirective,
  isDate,
  isFunction,
  isGloballyAllowed,
  isGloballyWhitelisted,
  isHTMLTag,
  isIntegerKey,
  isKnownHtmlAttr,
  isKnownMathMLAttr,
  isKnownSvgAttr,
  isMap,
  isMathMLTag,
  isModelListener,
  isObject,
  isOn,
  isPlainObject,
  isPromise,
  isRegExp,
  isRenderableAttrValue,
  isReservedProp,
  isSSRSafeAttrName,
  isSVGTag,
  isSet,
  isSpecialBooleanAttr,
  isString,
  isSymbol,
  isVoidTag,
  looseEqual,
  looseIndexOf,
  looseToNumber,
  makeMap,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  objectToString,
  parseStringStyle,
  propsToAttrMap,
  remove,
  slotFlagsText,
  stringifyStyle,
  toDisplayString,
  toHandlerKey,
  toNumber,
  toRawType,
  toTypeString
} from "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";
init_shared_esm_bundler();
export {
  EMPTY_ARR,
  EMPTY_OBJ,
  NO,
  NOOP,
  PatchFlagNames,
  PatchFlags,
  ShapeFlags,
  SlotFlags,
  camelize,
  capitalize,
  cssVarNameEscapeSymbolsRE,
  def,
  escapeHtml,
  escapeHtmlComment,
  extend,
  genCacheKey,
  genPropsAccessExp,
  generateCodeFrame,
  getEscapedCssVarName,
  getGlobalThis,
  hasChanged,
  hasOwn,
  hyphenate,
  includeBooleanAttr,
  invokeArrayFns,
  isArray,
  isBooleanAttr,
  isBuiltInDirective,
  isDate,
  isFunction,
  isGloballyAllowed,
  isGloballyWhitelisted,
  isHTMLTag,
  isIntegerKey,
  isKnownHtmlAttr,
  isKnownMathMLAttr,
  isKnownSvgAttr,
  isMap,
  isMathMLTag,
  isModelListener,
  isObject,
  isOn,
  isPlainObject,
  isPromise,
  isRegExp,
  isRenderableAttrValue,
  isReservedProp,
  isSSRSafeAttrName,
  isSVGTag,
  isSet,
  isSpecialBooleanAttr,
  isString,
  isSymbol,
  isVoidTag,
  looseEqual,
  looseIndexOf,
  looseToNumber,
  makeMap,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  objectToString,
  parseStringStyle,
  propsToAttrMap,
  remove,
  slotFlagsText,
  stringifyStyle,
  toDisplayString,
  toHandlerKey,
  toNumber,
  toRawType,
  toTypeString
};
