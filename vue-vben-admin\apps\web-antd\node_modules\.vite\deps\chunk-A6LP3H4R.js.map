{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/src/toolbar.js"], "sourcesContent": ["import { h, ref, computed, inject, createCommentVNode, reactive, nextTick } from 'vue';\nimport { defineVxeComponent } from '../../ui/src/comp';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../ui';\nimport { getSlotVNs } from '../../ui/src/vn';\nimport { warnLog, errLog } from '../../ui/src/log';\nconst { getConfig, getIcon, getI18n, renderer, commands, createEvent, useFns } = VxeUI;\nexport default defineVxeComponent({\n    name: 'VxeToolbar',\n    props: {\n        loading: Boolean,\n        refresh: [Boolean, Object],\n        refreshOptions: Object,\n        import: [Boolean, Object],\n        importOptions: Object,\n        export: [Boolean, Object],\n        exportOptions: Object,\n        print: [Boolean, Object],\n        printOptions: Object,\n        zoom: [Boolean, Object],\n        zoomOptions: Object,\n        custom: [Boolean, Object],\n        customOptions: Object,\n        buttons: {\n            type: Array,\n            default: () => getConfig().toolbar.buttons\n        },\n        tools: {\n            type: Array,\n            default: () => getConfig().toolbar.tools\n        },\n        perfect: {\n            type: Boolean,\n            default: () => getConfig().toolbar.perfect\n        },\n        size: {\n            type: String,\n            default: () => getConfig().toolbar.size || getConfig().size\n        },\n        className: [String, Function]\n    },\n    emits: [\n        'button-click',\n        'tool-click'\n    ],\n    setup(props, context) {\n        const { slots, emit } = context;\n        const xID = XEUtils.uniqueId();\n        // 使用已安装的组件，如果未安装则不渲染\n        const VxeUIButtonComponent = VxeUI.getComponent('VxeButton');\n        const { computeSize } = useFns.useSize(props);\n        const reactData = reactive({\n            isRefresh: false,\n            connectFlag: 0,\n            columns: []\n        });\n        const internalData = {\n            connectTable: null\n        };\n        const refElem = ref();\n        const refMaps = {\n            refElem\n        };\n        const $xeToolbar = {\n            xID,\n            props,\n            context,\n            reactData,\n            internalData,\n            getRefMaps: () => refMaps\n        };\n        let toolbarMethods = {};\n        const $xeGrid = inject('$xeGrid', null);\n        const computeRefreshOpts = computed(() => {\n            return Object.assign({}, XEUtils.clone(getConfig().toolbar.refresh, true), props.refreshOptions, props.refresh);\n        });\n        const computeImportOpts = computed(() => {\n            return Object.assign({}, XEUtils.clone(getConfig().toolbar.import, true), props.importOptions, props.import);\n        });\n        const computeExportOpts = computed(() => {\n            return Object.assign({}, XEUtils.clone(getConfig().toolbar.export, true), props.exportOptions, props.export);\n        });\n        const computePrintOpts = computed(() => {\n            return Object.assign({}, XEUtils.clone(getConfig().toolbar.print, true), props.printOptions, props.print);\n        });\n        const computeZoomOpts = computed(() => {\n            return Object.assign({}, XEUtils.clone(getConfig().toolbar.zoom, true), props.zoomOptions, props.zoom);\n        });\n        const computeCustomOpts = computed(() => {\n            return Object.assign({}, XEUtils.clone(getConfig().toolbar.custom, true), props.customOptions, props.custom);\n        });\n        const computeTableCustomOpts = computed(() => {\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            if (reactData.connectFlag || $table) {\n                if ($table) {\n                    const { computeCustomOpts } = $table.getComputeMaps();\n                    return computeCustomOpts.value;\n                }\n            }\n            return { trigger: '' };\n        });\n        const computeTrigger = computed(() => {\n            const tableCustomOpts = computeTableCustomOpts.value;\n            return tableCustomOpts.trigger;\n        });\n        const checkTable = () => {\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            if ($table) {\n                return true;\n            }\n            errLog('vxe.error.barUnableLink');\n        };\n        const handleClickSettingEvent = ({ $event }) => {\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            if ($table) {\n                if ($table.triggerCustomEvent) {\n                    $table.triggerCustomEvent($event);\n                }\n            }\n        };\n        const handleMouseenterSettingEvent = ({ $event }) => {\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            if ($table) {\n                $table.customOpenEvent($event);\n            }\n        };\n        const handleMouseleaveSettingEvent = ({ $event }) => {\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            if ($table) {\n                const { customStore } = $table.reactData;\n                customStore.activeBtn = false;\n                setTimeout(() => {\n                    if (!customStore.activeBtn && !customStore.activeWrapper) {\n                        $table.customCloseEvent($event);\n                    }\n                }, 350);\n            }\n        };\n        const refreshEvent = ({ $event }) => {\n            const { isRefresh } = reactData;\n            const refreshOpts = computeRefreshOpts.value;\n            if (!isRefresh) {\n                const queryMethod = refreshOpts.queryMethod || refreshOpts.query;\n                if (queryMethod) {\n                    reactData.isRefresh = true;\n                    try {\n                        Promise.resolve(queryMethod({})).catch((e) => e).then(() => {\n                            reactData.isRefresh = false;\n                        });\n                    }\n                    catch (e) {\n                        reactData.isRefresh = false;\n                    }\n                }\n                else if ($xeGrid) {\n                    reactData.isRefresh = true;\n                    $xeGrid.triggerToolbarCommitEvent({ code: refreshOpts.code || 'reload' }, $event).catch((e) => e).then(() => {\n                        reactData.isRefresh = false;\n                    });\n                }\n            }\n        };\n        const zoomEvent = ({ $event }) => {\n            if ($xeGrid) {\n                $xeGrid.triggerZoomEvent($event);\n            }\n            else {\n                warnLog('vxe.error.notProp', ['zoom']);\n            }\n        };\n        const importEvent = () => {\n            if (checkTable()) {\n                const { connectTable } = internalData;\n                const $table = connectTable;\n                if ($table) {\n                    $table.importData();\n                }\n            }\n        };\n        const openImportEvent = () => {\n            if (checkTable()) {\n                const { connectTable } = internalData;\n                const $table = connectTable;\n                if ($table) {\n                    $table.openImport();\n                }\n            }\n        };\n        const exportEvent = () => {\n            if (checkTable()) {\n                const { connectTable } = internalData;\n                const $table = connectTable;\n                if ($table) {\n                    $table.exportData();\n                }\n            }\n        };\n        const openExportEvent = () => {\n            if (checkTable()) {\n                const { connectTable } = internalData;\n                const $table = connectTable;\n                if ($table) {\n                    $table.openExport();\n                }\n            }\n        };\n        const printEvent = () => {\n            if (checkTable()) {\n                const { connectTable } = internalData;\n                const $table = connectTable;\n                if ($table) {\n                    $table.print();\n                }\n            }\n        };\n        const openPrintEvent = () => {\n            if (checkTable()) {\n                const { connectTable } = internalData;\n                const $table = connectTable;\n                if ($table) {\n                    $table.openPrint();\n                }\n            }\n        };\n        const handleDefaultCodeEvent = (eventParams, item, cb) => {\n            switch (item.code) {\n                case 'print':\n                    printEvent();\n                    break;\n                case 'open_print':\n                    openPrintEvent();\n                    break;\n                case 'custom':\n                    handleClickSettingEvent(eventParams);\n                    break;\n                case 'export':\n                    exportEvent();\n                    break;\n                case 'open_export':\n                    openExportEvent();\n                    break;\n                case 'import':\n                    importEvent();\n                    break;\n                case 'open_import':\n                    openImportEvent();\n                    break;\n                case 'zoom':\n                    zoomEvent(eventParams);\n                    break;\n                case 'refresh':\n                    refreshEvent(eventParams);\n                    break;\n                default:\n                    cb();\n                    break;\n            }\n        };\n        const btnEvent = (eventParams, item) => {\n            const { $event } = eventParams;\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            const { code } = item;\n            if (code) {\n                handleDefaultCodeEvent(eventParams, item, () => {\n                    if ($xeGrid) {\n                        $xeGrid.triggerToolbarBtnEvent(item, $event);\n                    }\n                    else {\n                        const gCommandOpts = commands.get(code);\n                        const params = { code, button: item, $table: $table, $grid: $xeGrid, $event };\n                        if (gCommandOpts) {\n                            const tCommandMethod = gCommandOpts.tableCommandMethod || gCommandOpts.commandMethod;\n                            if (tCommandMethod) {\n                                tCommandMethod(params);\n                            }\n                            else {\n                                errLog('vxe.error.notCommands', [code]);\n                            }\n                        }\n                        $xeToolbar.dispatchEvent('button-click', params, $event);\n                    }\n                });\n            }\n        };\n        const tolEvent = (eventParams, item) => {\n            const { $event } = eventParams;\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            const { code } = item;\n            if (code) {\n                handleDefaultCodeEvent(eventParams, item, () => {\n                    if ($xeGrid) {\n                        $xeGrid.triggerToolbarTolEvent(item, $event);\n                    }\n                    else {\n                        const gCommandOpts = commands.get(code);\n                        const params = { code, button: null, tool: item, $table: $table, $grid: $xeGrid, $event };\n                        if (gCommandOpts) {\n                            const tCommandMethod = gCommandOpts.tableCommandMethod || gCommandOpts.commandMethod;\n                            if (tCommandMethod) {\n                                tCommandMethod(params);\n                            }\n                            else {\n                                errLog('vxe.error.notCommands', [code]);\n                            }\n                        }\n                        $xeToolbar.dispatchEvent('tool-click', params, $event);\n                    }\n                });\n            }\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $toolbar: $xeToolbar }, params));\n        };\n        toolbarMethods = {\n            dispatchEvent,\n            syncUpdate(params) {\n                internalData.connectTable = params.$table;\n                reactData.columns = params.collectColumn;\n                reactData.connectFlag++;\n            }\n        };\n        Object.assign($xeToolbar, toolbarMethods);\n        const renderDropdowns = (item, isBtn) => {\n            const { dropdowns } = item;\n            const downVNs = [];\n            if (dropdowns) {\n                return dropdowns.map((child, index) => {\n                    if (child.visible === false) {\n                        return createCommentVNode();\n                    }\n                    return VxeUIButtonComponent\n                        ? h(VxeUIButtonComponent, {\n                            key: index,\n                            disabled: child.disabled,\n                            loading: child.loading,\n                            type: child.type,\n                            mode: child.mode,\n                            icon: child.icon,\n                            circle: child.circle,\n                            round: child.round,\n                            status: child.status,\n                            content: child.name,\n                            title: child.title,\n                            routerLink: child.routerLink,\n                            permissionCode: child.permissionCode,\n                            prefixTooltip: child.prefixTooltip,\n                            suffixTooltip: child.suffixTooltip,\n                            onClick: (eventParams) => isBtn ? btnEvent(eventParams, child) : tolEvent(eventParams, child)\n                        })\n                        : createCommentVNode();\n                });\n            }\n            return downVNs;\n        };\n        /**\n         * 渲染按钮\n         */\n        const renderLeftBtns = () => {\n            const { buttons } = props;\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            const buttonPrefixSlot = slots.buttonPrefix || slots['button-prefix'];\n            const buttonSuffixSlot = slots.buttonSuffix || slots['button-suffix'];\n            const btnVNs = [];\n            if (buttonPrefixSlot) {\n                btnVNs.push(...getSlotVNs(buttonPrefixSlot({ buttons: buttons || [], $grid: $xeGrid, $table: $table })));\n            }\n            if (buttons) {\n                buttons.forEach((item, index) => {\n                    const { dropdowns, buttonRender } = item;\n                    if (item.visible !== false) {\n                        const compConf = buttonRender ? renderer.get(buttonRender.name) : null;\n                        if (buttonRender && compConf && compConf.renderToolbarButton) {\n                            const toolbarButtonClassName = compConf.toolbarButtonClassName;\n                            const params = { $grid: $xeGrid, $table: $table, button: item };\n                            btnVNs.push(h('span', {\n                                key: `br${item.code || index}`,\n                                class: ['vxe-button--item', toolbarButtonClassName ? (XEUtils.isFunction(toolbarButtonClassName) ? toolbarButtonClassName(params) : toolbarButtonClassName) : '']\n                            }, getSlotVNs(compConf.renderToolbarButton(buttonRender, params))));\n                        }\n                        else {\n                            if (VxeUIButtonComponent) {\n                                btnVNs.push(h(VxeUIButtonComponent, {\n                                    key: `bd${item.code || index}`,\n                                    disabled: item.disabled,\n                                    loading: item.loading,\n                                    type: item.type,\n                                    mode: item.mode,\n                                    icon: item.icon,\n                                    circle: item.circle,\n                                    round: item.round,\n                                    status: item.status,\n                                    content: item.name,\n                                    title: item.title,\n                                    routerLink: item.routerLink,\n                                    permissionCode: item.permissionCode,\n                                    prefixTooltip: item.prefixTooltip,\n                                    suffixTooltip: item.suffixTooltip,\n                                    destroyOnClose: item.destroyOnClose,\n                                    placement: item.placement,\n                                    transfer: item.transfer,\n                                    onClick: (eventParams) => btnEvent(eventParams, item)\n                                }, dropdowns && dropdowns.length\n                                    ? {\n                                        dropdowns: () => renderDropdowns(item, true)\n                                    }\n                                    : {}));\n                            }\n                        }\n                    }\n                });\n            }\n            if (buttonSuffixSlot) {\n                btnVNs.push(...getSlotVNs(buttonSuffixSlot({ buttons: buttons || [], $grid: $xeGrid, $table: $table })));\n            }\n            return btnVNs;\n        };\n        /**\n         * 渲染右侧工具\n         */\n        const renderRightTools = () => {\n            const { tools } = props;\n            const { connectTable } = internalData;\n            const $table = connectTable;\n            const toolPrefixSlot = slots.toolPrefix || slots['tool-prefix'];\n            const toolSuffixSlot = slots.toolSuffix || slots['tool-suffix'];\n            const btnVNs = [];\n            if (toolPrefixSlot) {\n                btnVNs.push(...getSlotVNs(toolPrefixSlot({ tools: tools || [], $grid: $xeGrid, $table: $table })));\n            }\n            if (tools) {\n                tools.forEach((item, tIndex) => {\n                    const { dropdowns, toolRender } = item;\n                    if (item.visible !== false) {\n                        const rdName = toolRender ? toolRender.name : null;\n                        const compConf = toolRender ? renderer.get(rdName) : null;\n                        if (toolRender && compConf && compConf.renderToolbarTool) {\n                            const toolbarToolClassName = compConf.toolbarToolClassName;\n                            const params = { $grid: $xeGrid, $table: $table, tool: item };\n                            btnVNs.push(h('span', {\n                                key: rdName,\n                                class: ['vxe-tool--item', toolbarToolClassName ? (XEUtils.isFunction(toolbarToolClassName) ? toolbarToolClassName(params) : toolbarToolClassName) : '']\n                            }, getSlotVNs(compConf.renderToolbarTool(toolRender, params))));\n                        }\n                        else {\n                            if (VxeUIButtonComponent) {\n                                btnVNs.push(h(VxeUIButtonComponent, {\n                                    key: tIndex,\n                                    disabled: item.disabled,\n                                    loading: item.loading,\n                                    type: item.type,\n                                    mode: item.mode,\n                                    icon: item.icon,\n                                    circle: item.circle,\n                                    round: item.round,\n                                    status: item.status,\n                                    content: item.name,\n                                    title: item.title,\n                                    routerLink: item.routerLink,\n                                    permissionCode: item.permissionCode,\n                                    prefixTooltip: item.prefixTooltip,\n                                    suffixTooltip: item.suffixTooltip,\n                                    destroyOnClose: item.destroyOnClose,\n                                    placement: item.placement,\n                                    transfer: item.transfer,\n                                    onClick: (eventParams) => tolEvent(eventParams, item)\n                                }, dropdowns && dropdowns.length\n                                    ? {\n                                        dropdowns: () => renderDropdowns(item, false)\n                                    }\n                                    : {}));\n                            }\n                        }\n                    }\n                });\n            }\n            if (toolSuffixSlot) {\n                btnVNs.push(...getSlotVNs(toolSuffixSlot({ tools: tools || [], $grid: $xeGrid, $table: $table })));\n            }\n            return btnVNs;\n        };\n        const renderToolImport = () => {\n            const importOpts = computeImportOpts.value;\n            return VxeUIButtonComponent\n                ? h(VxeUIButtonComponent, {\n                    key: 'import',\n                    circle: true,\n                    icon: importOpts.icon || getIcon().TOOLBAR_TOOLS_IMPORT,\n                    title: getI18n('vxe.toolbar.import'),\n                    onClick: openImportEvent\n                })\n                : createCommentVNode();\n        };\n        const renderToolExport = () => {\n            const exportOpts = computeExportOpts.value;\n            return VxeUIButtonComponent\n                ? h(VxeUIButtonComponent, {\n                    key: 'export',\n                    circle: true,\n                    icon: exportOpts.icon || getIcon().TOOLBAR_TOOLS_EXPORT,\n                    title: getI18n('vxe.toolbar.export'),\n                    onClick: openExportEvent\n                })\n                : createCommentVNode();\n        };\n        const renderToolPrint = () => {\n            const printOpts = computePrintOpts.value;\n            return VxeUIButtonComponent\n                ? h(VxeUIButtonComponent, {\n                    key: 'print',\n                    circle: true,\n                    icon: printOpts.icon || getIcon().TOOLBAR_TOOLS_PRINT,\n                    title: getI18n('vxe.toolbar.print'),\n                    onClick: openPrintEvent\n                })\n                : createCommentVNode();\n        };\n        const renderToolRefresh = () => {\n            const refreshOpts = computeRefreshOpts.value;\n            return VxeUIButtonComponent\n                ? h(VxeUIButtonComponent, {\n                    key: 'refresh',\n                    circle: true,\n                    icon: reactData.isRefresh ? (refreshOpts.iconLoading || getIcon().TOOLBAR_TOOLS_REFRESH_LOADING) : (refreshOpts.icon || getIcon().TOOLBAR_TOOLS_REFRESH),\n                    title: getI18n('vxe.toolbar.refresh'),\n                    onClick: refreshEvent\n                })\n                : createCommentVNode();\n        };\n        const renderToolZoom = () => {\n            const zoomOpts = computeZoomOpts.value;\n            return $xeGrid && VxeUIButtonComponent\n                ? h(VxeUIButtonComponent, {\n                    key: 'zoom',\n                    circle: true,\n                    icon: $xeGrid.isMaximized() ? (zoomOpts.iconOut || getIcon().TOOLBAR_TOOLS_MINIMIZE) : (zoomOpts.iconIn || getIcon().TOOLBAR_TOOLS_FULLSCREEN),\n                    title: getI18n(`vxe.toolbar.zoom${$xeGrid.isMaximized() ? 'Out' : 'In'}`),\n                    onClick: zoomEvent\n                })\n                : createCommentVNode();\n        };\n        const renderToolCustom = () => {\n            const customOpts = computeCustomOpts.value;\n            const btnTrigger = computeTrigger.value;\n            const customBtnOns = {};\n            if (btnTrigger === 'manual') {\n                // 手动触发\n            }\n            else if (btnTrigger === 'hover') {\n                // hover 触发\n                customBtnOns.onMouseenter = handleMouseenterSettingEvent;\n                customBtnOns.onMouseleave = handleMouseleaveSettingEvent;\n            }\n            else {\n                // 点击触发\n                customBtnOns.onClick = handleClickSettingEvent;\n            }\n            return VxeUIButtonComponent\n                ? h(VxeUIButtonComponent, Object.assign({ key: 'custom', circle: true, icon: customOpts.icon || getIcon().TOOLBAR_TOOLS_CUSTOM, title: getI18n('vxe.toolbar.custom'), className: 'vxe-toolbar-custom-target' }, customBtnOns))\n                : createCommentVNode();\n        };\n        const renderVN = () => {\n            const { perfect, loading, refresh, zoom, custom, className } = props;\n            const { connectTable } = internalData;\n            const vSize = computeSize.value;\n            const toolsSlot = slots.tools;\n            const buttonsSlot = slots.buttons;\n            const $table = connectTable;\n            return h('div', {\n                ref: refElem,\n                class: ['vxe-toolbar', className ? (XEUtils.isFunction(className) ? className({ $toolbar: $xeToolbar }) : className) : '', {\n                        [`size--${vSize}`]: vSize,\n                        'is--perfect': perfect,\n                        'is--loading': loading\n                    }]\n            }, [\n                h('div', {\n                    class: 'vxe-buttons--wrapper'\n                }, buttonsSlot ? buttonsSlot({ $grid: $xeGrid, $table: $table }) : renderLeftBtns()),\n                h('div', {\n                    class: 'vxe-tools--wrapper'\n                }, toolsSlot ? toolsSlot({ $grid: $xeGrid, $table: $table }) : renderRightTools()),\n                h('div', {\n                    class: 'vxe-tools--operate'\n                }, [\n                    props.import ? renderToolImport() : createCommentVNode(),\n                    props.export ? renderToolExport() : createCommentVNode(),\n                    props.print ? renderToolPrint() : createCommentVNode(),\n                    refresh ? renderToolRefresh() : createCommentVNode(),\n                    zoom && $xeGrid ? renderToolZoom() : createCommentVNode(),\n                    custom ? renderToolCustom() : createCommentVNode()\n                ])\n            ]);\n        };\n        $xeToolbar.renderVN = renderVN;\n        nextTick(() => {\n            const refreshOpts = computeRefreshOpts.value;\n            const queryMethod = refreshOpts.queryMethod || refreshOpts.query;\n            if (props.refresh && !$xeGrid && !queryMethod) {\n                warnLog('vxe.error.notFunc', ['queryMethod']);\n            }\n            if (XEUtils.isPlainObject(props.custom)) {\n                warnLog('vxe.error.delProp', ['custom={...}', 'custom=boolean & custom-options={...}']);\n            }\n            if (XEUtils.isPlainObject(props.print)) {\n                warnLog('vxe.error.delProp', ['print={...}', 'print=boolean & print-options={...}']);\n            }\n            if (XEUtils.isPlainObject(props.export)) {\n                warnLog('vxe.error.delProp', ['export={...}', 'export=boolean & export-options={...}']);\n            }\n            if (XEUtils.isPlainObject(props.import)) {\n                warnLog('vxe.error.delProp', ['import={...}', 'import=boolean & import-options={...}']);\n            }\n            if (XEUtils.isPlainObject(props.refresh)) {\n                warnLog('vxe.error.delProp', ['refresh={...}', 'refresh=boolean & refresh-options={...}']);\n            }\n            if (XEUtils.isPlainObject(props.refresh)) {\n                warnLog('vxe.error.delProp', ['zoom={...}', 'zoom=boolean & zoom-options={...}']);\n            }\n            const customOpts = computeCustomOpts.value;\n            if (customOpts.isFooter) {\n                warnLog('vxe.error.delProp', ['toolbar.custom.isFooter', 'table.custom-config.showFooter']);\n            }\n            if (customOpts.showFooter) {\n                warnLog('vxe.error.delProp', ['toolbar.custom.showFooter', 'table.custom-config.showFooter']);\n            }\n            if (customOpts.immediate) {\n                warnLog('vxe.error.delProp', ['toolbar.custom.immediate', 'table.custom-config.immediate']);\n            }\n            if (customOpts.trigger) {\n                warnLog('vxe.error.delProp', ['toolbar.custom.trigger', 'table.custom-config.trigger']);\n            }\n            if (props.refresh || props.import || props.export || props.print || props.zoom) {\n                if (!VxeUIButtonComponent) {\n                    errLog('vxe.error.reqComp', ['vxe-button']);\n                }\n            }\n        });\n        return $xeToolbar;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sBAAoB;AAIpB,IAAM,EAAE,WAAW,SAAS,SAAS,UAAU,UAAU,aAAa,OAAO,IAAI;AACjF,IAAO,kBAAQ,mBAAmB;AAAA,EAC9B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,SAAS;AAAA,IACT,SAAS,CAAC,SAAS,MAAM;AAAA,IACzB,gBAAgB;AAAA,IAChB,QAAQ,CAAC,SAAS,MAAM;AAAA,IACxB,eAAe;AAAA,IACf,QAAQ,CAAC,SAAS,MAAM;AAAA,IACxB,eAAe;AAAA,IACf,OAAO,CAAC,SAAS,MAAM;AAAA,IACvB,cAAc;AAAA,IACd,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,aAAa;AAAA,IACb,QAAQ,CAAC,SAAS,MAAM;AAAA,IACxB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ,QAAQ,UAAU,EAAE;AAAA,IAC3D;AAAA,IACA,WAAW,CAAC,QAAQ,QAAQ;AAAA,EAChC;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,MAAM,gBAAAA,QAAQ,SAAS;AAE7B,UAAM,uBAAuB,MAAM,aAAa,WAAW;AAC3D,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,KAAK;AAC5C,UAAM,YAAY,SAAS;AAAA,MACvB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,SAAS,CAAC;AAAA,IACd,CAAC;AACD,UAAM,eAAe;AAAA,MACjB,cAAc;AAAA,IAClB;AACA,UAAM,UAAU,IAAI;AACpB,UAAM,UAAU;AAAA,MACZ;AAAA,IACJ;AACA,UAAM,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,IACtB;AACA,QAAI,iBAAiB,CAAC;AACtB,UAAM,UAAU,OAAO,WAAW,IAAI;AACtC,UAAM,qBAAqB,SAAS,MAAM;AACtC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,QAAQ,SAAS,IAAI,GAAG,MAAM,gBAAgB,MAAM,OAAO;AAAA,IAClH,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,QAAQ,QAAQ,IAAI,GAAG,MAAM,eAAe,MAAM,MAAM;AAAA,IAC/G,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,QAAQ,QAAQ,IAAI,GAAG,MAAM,eAAe,MAAM,MAAM;AAAA,IAC/G,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,QAAQ,OAAO,IAAI,GAAG,MAAM,cAAc,MAAM,KAAK;AAAA,IAC5G,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,QAAQ,MAAM,IAAI,GAAG,MAAM,aAAa,MAAM,IAAI;AAAA,IACzG,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,QAAQ,QAAQ,IAAI,GAAG,MAAM,eAAe,MAAM,MAAM;AAAA,IAC/G,CAAC;AACD,UAAM,yBAAyB,SAAS,MAAM;AAC1C,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,UAAI,UAAU,eAAe,QAAQ;AACjC,YAAI,QAAQ;AACR,gBAAM,EAAE,mBAAAC,mBAAkB,IAAI,OAAO,eAAe;AACpD,iBAAOA,mBAAkB;AAAA,QAC7B;AAAA,MACJ;AACA,aAAO,EAAE,SAAS,GAAG;AAAA,IACzB,CAAC;AACD,UAAM,iBAAiB,SAAS,MAAM;AAClC,YAAM,kBAAkB,uBAAuB;AAC/C,aAAO,gBAAgB;AAAA,IAC3B,CAAC;AACD,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,UAAI,QAAQ;AACR,eAAO;AAAA,MACX;AACA,aAAO,yBAAyB;AAAA,IACpC;AACA,UAAM,0BAA0B,CAAC,EAAE,OAAO,MAAM;AAC5C,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,UAAI,QAAQ;AACR,YAAI,OAAO,oBAAoB;AAC3B,iBAAO,mBAAmB,MAAM;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,+BAA+B,CAAC,EAAE,OAAO,MAAM;AACjD,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,UAAI,QAAQ;AACR,eAAO,gBAAgB,MAAM;AAAA,MACjC;AAAA,IACJ;AACA,UAAM,+BAA+B,CAAC,EAAE,OAAO,MAAM;AACjD,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,UAAI,QAAQ;AACR,cAAM,EAAE,YAAY,IAAI,OAAO;AAC/B,oBAAY,YAAY;AACxB,mBAAW,MAAM;AACb,cAAI,CAAC,YAAY,aAAa,CAAC,YAAY,eAAe;AACtD,mBAAO,iBAAiB,MAAM;AAAA,UAClC;AAAA,QACJ,GAAG,GAAG;AAAA,MACV;AAAA,IACJ;AACA,UAAM,eAAe,CAAC,EAAE,OAAO,MAAM;AACjC,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,cAAc,mBAAmB;AACvC,UAAI,CAAC,WAAW;AACZ,cAAM,cAAc,YAAY,eAAe,YAAY;AAC3D,YAAI,aAAa;AACb,oBAAU,YAAY;AACtB,cAAI;AACA,oBAAQ,QAAQ,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM;AACxD,wBAAU,YAAY;AAAA,YAC1B,CAAC;AAAA,UACL,SACO,GAAG;AACN,sBAAU,YAAY;AAAA,UAC1B;AAAA,QACJ,WACS,SAAS;AACd,oBAAU,YAAY;AACtB,kBAAQ,0BAA0B,EAAE,MAAM,YAAY,QAAQ,SAAS,GAAG,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM;AACzG,sBAAU,YAAY;AAAA,UAC1B,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,YAAY,CAAC,EAAE,OAAO,MAAM;AAC9B,UAAI,SAAS;AACT,gBAAQ,iBAAiB,MAAM;AAAA,MACnC,OACK;AACD,gBAAQ,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACzC;AAAA,IACJ;AACA,UAAM,cAAc,MAAM;AACtB,UAAI,WAAW,GAAG;AACd,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,SAAS;AACf,YAAI,QAAQ;AACR,iBAAO,WAAW;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC1B,UAAI,WAAW,GAAG;AACd,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,SAAS;AACf,YAAI,QAAQ;AACR,iBAAO,WAAW;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,cAAc,MAAM;AACtB,UAAI,WAAW,GAAG;AACd,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,SAAS;AACf,YAAI,QAAQ;AACR,iBAAO,WAAW;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC1B,UAAI,WAAW,GAAG;AACd,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,SAAS;AACf,YAAI,QAAQ;AACR,iBAAO,WAAW;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,aAAa,MAAM;AACrB,UAAI,WAAW,GAAG;AACd,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,SAAS;AACf,YAAI,QAAQ;AACR,iBAAO,MAAM;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,iBAAiB,MAAM;AACzB,UAAI,WAAW,GAAG;AACd,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,SAAS;AACf,YAAI,QAAQ;AACR,iBAAO,UAAU;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,yBAAyB,CAAC,aAAa,MAAM,OAAO;AACtD,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AACD,qBAAW;AACX;AAAA,QACJ,KAAK;AACD,yBAAe;AACf;AAAA,QACJ,KAAK;AACD,kCAAwB,WAAW;AACnC;AAAA,QACJ,KAAK;AACD,sBAAY;AACZ;AAAA,QACJ,KAAK;AACD,0BAAgB;AAChB;AAAA,QACJ,KAAK;AACD,sBAAY;AACZ;AAAA,QACJ,KAAK;AACD,0BAAgB;AAChB;AAAA,QACJ,KAAK;AACD,oBAAU,WAAW;AACrB;AAAA,QACJ,KAAK;AACD,uBAAa,WAAW;AACxB;AAAA,QACJ;AACI,aAAG;AACH;AAAA,MACR;AAAA,IACJ;AACA,UAAM,WAAW,CAAC,aAAa,SAAS;AACpC,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI,MAAM;AACN,+BAAuB,aAAa,MAAM,MAAM;AAC5C,cAAI,SAAS;AACT,oBAAQ,uBAAuB,MAAM,MAAM;AAAA,UAC/C,OACK;AACD,kBAAM,eAAe,SAAS,IAAI,IAAI;AACtC,kBAAM,SAAS,EAAE,MAAM,QAAQ,MAAM,QAAgB,OAAO,SAAS,OAAO;AAC5E,gBAAI,cAAc;AACd,oBAAM,iBAAiB,aAAa,sBAAsB,aAAa;AACvE,kBAAI,gBAAgB;AAChB,+BAAe,MAAM;AAAA,cACzB,OACK;AACD,uBAAO,yBAAyB,CAAC,IAAI,CAAC;AAAA,cAC1C;AAAA,YACJ;AACA,uBAAW,cAAc,gBAAgB,QAAQ,MAAM;AAAA,UAC3D;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,WAAW,CAAC,aAAa,SAAS;AACpC,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI,MAAM;AACN,+BAAuB,aAAa,MAAM,MAAM;AAC5C,cAAI,SAAS;AACT,oBAAQ,uBAAuB,MAAM,MAAM;AAAA,UAC/C,OACK;AACD,kBAAM,eAAe,SAAS,IAAI,IAAI;AACtC,kBAAM,SAAS,EAAE,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAgB,OAAO,SAAS,OAAO;AACxF,gBAAI,cAAc;AACd,oBAAM,iBAAiB,aAAa,sBAAsB,aAAa;AACvE,kBAAI,gBAAgB;AAChB,+BAAe,MAAM;AAAA,cACzB,OACK;AACD,uBAAO,yBAAyB,CAAC,IAAI,CAAC;AAAA,cAC1C;AAAA,YACJ;AACA,uBAAW,cAAc,cAAc,QAAQ,MAAM;AAAA,UACzD;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,UAAU,WAAW,GAAG,MAAM,CAAC;AAAA,IAClE;AACA,qBAAiB;AAAA,MACb;AAAA,MACA,WAAW,QAAQ;AACf,qBAAa,eAAe,OAAO;AACnC,kBAAU,UAAU,OAAO;AAC3B,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,WAAO,OAAO,YAAY,cAAc;AACxC,UAAM,kBAAkB,CAAC,MAAM,UAAU;AACrC,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,UAAU,CAAC;AACjB,UAAI,WAAW;AACX,eAAO,UAAU,IAAI,CAAC,OAAO,UAAU;AACnC,cAAI,MAAM,YAAY,OAAO;AACzB,mBAAO,mBAAmB;AAAA,UAC9B;AACA,iBAAO,uBACD,EAAE,sBAAsB;AAAA,YACtB,KAAK;AAAA,YACL,UAAU,MAAM;AAAA,YAChB,SAAS,MAAM;AAAA,YACf,MAAM,MAAM;AAAA,YACZ,MAAM,MAAM;AAAA,YACZ,MAAM,MAAM;AAAA,YACZ,QAAQ,MAAM;AAAA,YACd,OAAO,MAAM;AAAA,YACb,QAAQ,MAAM;AAAA,YACd,SAAS,MAAM;AAAA,YACf,OAAO,MAAM;AAAA,YACb,YAAY,MAAM;AAAA,YAClB,gBAAgB,MAAM;AAAA,YACtB,eAAe,MAAM;AAAA,YACrB,eAAe,MAAM;AAAA,YACrB,SAAS,CAAC,gBAAgB,QAAQ,SAAS,aAAa,KAAK,IAAI,SAAS,aAAa,KAAK;AAAA,UAChG,CAAC,IACC,mBAAmB;AAAA,QAC7B,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AAIA,UAAM,iBAAiB,MAAM;AACzB,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,YAAM,mBAAmB,MAAM,gBAAgB,MAAM,eAAe;AACpE,YAAM,mBAAmB,MAAM,gBAAgB,MAAM,eAAe;AACpE,YAAM,SAAS,CAAC;AAChB,UAAI,kBAAkB;AAClB,eAAO,KAAK,GAAG,WAAW,iBAAiB,EAAE,SAAS,WAAW,CAAC,GAAG,OAAO,SAAS,OAAe,CAAC,CAAC,CAAC;AAAA,MAC3G;AACA,UAAI,SAAS;AACT,gBAAQ,QAAQ,CAAC,MAAM,UAAU;AAC7B,gBAAM,EAAE,WAAW,aAAa,IAAI;AACpC,cAAI,KAAK,YAAY,OAAO;AACxB,kBAAM,WAAW,eAAe,SAAS,IAAI,aAAa,IAAI,IAAI;AAClE,gBAAI,gBAAgB,YAAY,SAAS,qBAAqB;AAC1D,oBAAM,yBAAyB,SAAS;AACxC,oBAAM,SAAS,EAAE,OAAO,SAAS,QAAgB,QAAQ,KAAK;AAC9D,qBAAO,KAAK,EAAE,QAAQ;AAAA,gBAClB,KAAK,KAAK,KAAK,QAAQ,KAAK;AAAA,gBAC5B,OAAO,CAAC,oBAAoB,yBAA0B,gBAAAD,QAAQ,WAAW,sBAAsB,IAAI,uBAAuB,MAAM,IAAI,yBAA0B,EAAE;AAAA,cACpK,GAAG,WAAW,SAAS,oBAAoB,cAAc,MAAM,CAAC,CAAC,CAAC;AAAA,YACtE,OACK;AACD,kBAAI,sBAAsB;AACtB,uBAAO,KAAK,EAAE,sBAAsB;AAAA,kBAChC,KAAK,KAAK,KAAK,QAAQ,KAAK;AAAA,kBAC5B,UAAU,KAAK;AAAA,kBACf,SAAS,KAAK;AAAA,kBACd,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,QAAQ,KAAK;AAAA,kBACb,OAAO,KAAK;AAAA,kBACZ,QAAQ,KAAK;AAAA,kBACb,SAAS,KAAK;AAAA,kBACd,OAAO,KAAK;AAAA,kBACZ,YAAY,KAAK;AAAA,kBACjB,gBAAgB,KAAK;AAAA,kBACrB,eAAe,KAAK;AAAA,kBACpB,eAAe,KAAK;AAAA,kBACpB,gBAAgB,KAAK;AAAA,kBACrB,WAAW,KAAK;AAAA,kBAChB,UAAU,KAAK;AAAA,kBACf,SAAS,CAAC,gBAAgB,SAAS,aAAa,IAAI;AAAA,gBACxD,GAAG,aAAa,UAAU,SACpB;AAAA,kBACE,WAAW,MAAM,gBAAgB,MAAM,IAAI;AAAA,gBAC/C,IACE,CAAC,CAAC,CAAC;AAAA,cACb;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,kBAAkB;AAClB,eAAO,KAAK,GAAG,WAAW,iBAAiB,EAAE,SAAS,WAAW,CAAC,GAAG,OAAO,SAAS,OAAe,CAAC,CAAC,CAAC;AAAA,MAC3G;AACA,aAAO;AAAA,IACX;AAIA,UAAM,mBAAmB,MAAM;AAC3B,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS;AACf,YAAM,iBAAiB,MAAM,cAAc,MAAM,aAAa;AAC9D,YAAM,iBAAiB,MAAM,cAAc,MAAM,aAAa;AAC9D,YAAM,SAAS,CAAC;AAChB,UAAI,gBAAgB;AAChB,eAAO,KAAK,GAAG,WAAW,eAAe,EAAE,OAAO,SAAS,CAAC,GAAG,OAAO,SAAS,OAAe,CAAC,CAAC,CAAC;AAAA,MACrG;AACA,UAAI,OAAO;AACP,cAAM,QAAQ,CAAC,MAAM,WAAW;AAC5B,gBAAM,EAAE,WAAW,WAAW,IAAI;AAClC,cAAI,KAAK,YAAY,OAAO;AACxB,kBAAM,SAAS,aAAa,WAAW,OAAO;AAC9C,kBAAM,WAAW,aAAa,SAAS,IAAI,MAAM,IAAI;AACrD,gBAAI,cAAc,YAAY,SAAS,mBAAmB;AACtD,oBAAM,uBAAuB,SAAS;AACtC,oBAAM,SAAS,EAAE,OAAO,SAAS,QAAgB,MAAM,KAAK;AAC5D,qBAAO,KAAK,EAAE,QAAQ;AAAA,gBAClB,KAAK;AAAA,gBACL,OAAO,CAAC,kBAAkB,uBAAwB,gBAAAA,QAAQ,WAAW,oBAAoB,IAAI,qBAAqB,MAAM,IAAI,uBAAwB,EAAE;AAAA,cAC1J,GAAG,WAAW,SAAS,kBAAkB,YAAY,MAAM,CAAC,CAAC,CAAC;AAAA,YAClE,OACK;AACD,kBAAI,sBAAsB;AACtB,uBAAO,KAAK,EAAE,sBAAsB;AAAA,kBAChC,KAAK;AAAA,kBACL,UAAU,KAAK;AAAA,kBACf,SAAS,KAAK;AAAA,kBACd,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,MAAM,KAAK;AAAA,kBACX,QAAQ,KAAK;AAAA,kBACb,OAAO,KAAK;AAAA,kBACZ,QAAQ,KAAK;AAAA,kBACb,SAAS,KAAK;AAAA,kBACd,OAAO,KAAK;AAAA,kBACZ,YAAY,KAAK;AAAA,kBACjB,gBAAgB,KAAK;AAAA,kBACrB,eAAe,KAAK;AAAA,kBACpB,eAAe,KAAK;AAAA,kBACpB,gBAAgB,KAAK;AAAA,kBACrB,WAAW,KAAK;AAAA,kBAChB,UAAU,KAAK;AAAA,kBACf,SAAS,CAAC,gBAAgB,SAAS,aAAa,IAAI;AAAA,gBACxD,GAAG,aAAa,UAAU,SACpB;AAAA,kBACE,WAAW,MAAM,gBAAgB,MAAM,KAAK;AAAA,gBAChD,IACE,CAAC,CAAC,CAAC;AAAA,cACb;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,gBAAgB;AAChB,eAAO,KAAK,GAAG,WAAW,eAAe,EAAE,OAAO,SAAS,CAAC,GAAG,OAAO,SAAS,OAAe,CAAC,CAAC,CAAC;AAAA,MACrG;AACA,aAAO;AAAA,IACX;AACA,UAAM,mBAAmB,MAAM;AAC3B,YAAM,aAAa,kBAAkB;AACrC,aAAO,uBACD,EAAE,sBAAsB;AAAA,QACtB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,WAAW,QAAQ,QAAQ,EAAE;AAAA,QACnC,OAAO,QAAQ,oBAAoB;AAAA,QACnC,SAAS;AAAA,MACb,CAAC,IACC,mBAAmB;AAAA,IAC7B;AACA,UAAM,mBAAmB,MAAM;AAC3B,YAAM,aAAa,kBAAkB;AACrC,aAAO,uBACD,EAAE,sBAAsB;AAAA,QACtB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,WAAW,QAAQ,QAAQ,EAAE;AAAA,QACnC,OAAO,QAAQ,oBAAoB;AAAA,QACnC,SAAS;AAAA,MACb,CAAC,IACC,mBAAmB;AAAA,IAC7B;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,YAAY,iBAAiB;AACnC,aAAO,uBACD,EAAE,sBAAsB;AAAA,QACtB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,UAAU,QAAQ,QAAQ,EAAE;AAAA,QAClC,OAAO,QAAQ,mBAAmB;AAAA,QAClC,SAAS;AAAA,MACb,CAAC,IACC,mBAAmB;AAAA,IAC7B;AACA,UAAM,oBAAoB,MAAM;AAC5B,YAAM,cAAc,mBAAmB;AACvC,aAAO,uBACD,EAAE,sBAAsB;AAAA,QACtB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,UAAU,YAAa,YAAY,eAAe,QAAQ,EAAE,gCAAkC,YAAY,QAAQ,QAAQ,EAAE;AAAA,QAClI,OAAO,QAAQ,qBAAqB;AAAA,QACpC,SAAS;AAAA,MACb,CAAC,IACC,mBAAmB;AAAA,IAC7B;AACA,UAAM,iBAAiB,MAAM;AACzB,YAAM,WAAW,gBAAgB;AACjC,aAAO,WAAW,uBACZ,EAAE,sBAAsB;AAAA,QACtB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,QAAQ,YAAY,IAAK,SAAS,WAAW,QAAQ,EAAE,yBAA2B,SAAS,UAAU,QAAQ,EAAE;AAAA,QACrH,OAAO,QAAQ,mBAAmB,QAAQ,YAAY,IAAI,QAAQ,IAAI,EAAE;AAAA,QACxE,SAAS;AAAA,MACb,CAAC,IACC,mBAAmB;AAAA,IAC7B;AACA,UAAM,mBAAmB,MAAM;AAC3B,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,eAAe;AAClC,YAAM,eAAe,CAAC;AACtB,UAAI,eAAe,UAAU;AAAA,MAE7B,WACS,eAAe,SAAS;AAE7B,qBAAa,eAAe;AAC5B,qBAAa,eAAe;AAAA,MAChC,OACK;AAED,qBAAa,UAAU;AAAA,MAC3B;AACA,aAAO,uBACD,EAAE,sBAAsB,OAAO,OAAO,EAAE,KAAK,UAAU,QAAQ,MAAM,MAAM,WAAW,QAAQ,QAAQ,EAAE,sBAAsB,OAAO,QAAQ,oBAAoB,GAAG,WAAW,4BAA4B,GAAG,YAAY,CAAC,IAC3N,mBAAmB;AAAA,IAC7B;AACA,UAAM,WAAW,MAAM;AACnB,YAAM,EAAE,SAAS,SAAS,SAAS,MAAM,QAAQ,UAAU,IAAI;AAC/D,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,QAAQ,YAAY;AAC1B,YAAM,YAAY,MAAM;AACxB,YAAM,cAAc,MAAM;AAC1B,YAAM,SAAS;AACf,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO,CAAC,eAAe,YAAa,gBAAAA,QAAQ,WAAW,SAAS,IAAI,UAAU,EAAE,UAAU,WAAW,CAAC,IAAI,YAAa,IAAI;AAAA,UACnH,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACT,GAAG;AAAA,QACC,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,cAAc,YAAY,EAAE,OAAO,SAAS,OAAe,CAAC,IAAI,eAAe,CAAC;AAAA,QACnF,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,YAAY,UAAU,EAAE,OAAO,SAAS,OAAe,CAAC,IAAI,iBAAiB,CAAC;AAAA,QACjF,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG;AAAA,UACC,MAAM,SAAS,iBAAiB,IAAI,mBAAmB;AAAA,UACvD,MAAM,SAAS,iBAAiB,IAAI,mBAAmB;AAAA,UACvD,MAAM,QAAQ,gBAAgB,IAAI,mBAAmB;AAAA,UACrD,UAAU,kBAAkB,IAAI,mBAAmB;AAAA,UACnD,QAAQ,UAAU,eAAe,IAAI,mBAAmB;AAAA,UACxD,SAAS,iBAAiB,IAAI,mBAAmB;AAAA,QACrD,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,eAAW,WAAW;AACtB,aAAS,MAAM;AACX,YAAM,cAAc,mBAAmB;AACvC,YAAM,cAAc,YAAY,eAAe,YAAY;AAC3D,UAAI,MAAM,WAAW,CAAC,WAAW,CAAC,aAAa;AAC3C,gBAAQ,qBAAqB,CAAC,aAAa,CAAC;AAAA,MAChD;AACA,UAAI,gBAAAA,QAAQ,cAAc,MAAM,MAAM,GAAG;AACrC,gBAAQ,qBAAqB,CAAC,gBAAgB,uCAAuC,CAAC;AAAA,MAC1F;AACA,UAAI,gBAAAA,QAAQ,cAAc,MAAM,KAAK,GAAG;AACpC,gBAAQ,qBAAqB,CAAC,eAAe,qCAAqC,CAAC;AAAA,MACvF;AACA,UAAI,gBAAAA,QAAQ,cAAc,MAAM,MAAM,GAAG;AACrC,gBAAQ,qBAAqB,CAAC,gBAAgB,uCAAuC,CAAC;AAAA,MAC1F;AACA,UAAI,gBAAAA,QAAQ,cAAc,MAAM,MAAM,GAAG;AACrC,gBAAQ,qBAAqB,CAAC,gBAAgB,uCAAuC,CAAC;AAAA,MAC1F;AACA,UAAI,gBAAAA,QAAQ,cAAc,MAAM,OAAO,GAAG;AACtC,gBAAQ,qBAAqB,CAAC,iBAAiB,yCAAyC,CAAC;AAAA,MAC7F;AACA,UAAI,gBAAAA,QAAQ,cAAc,MAAM,OAAO,GAAG;AACtC,gBAAQ,qBAAqB,CAAC,cAAc,mCAAmC,CAAC;AAAA,MACpF;AACA,YAAM,aAAa,kBAAkB;AACrC,UAAI,WAAW,UAAU;AACrB,gBAAQ,qBAAqB,CAAC,2BAA2B,gCAAgC,CAAC;AAAA,MAC9F;AACA,UAAI,WAAW,YAAY;AACvB,gBAAQ,qBAAqB,CAAC,6BAA6B,gCAAgC,CAAC;AAAA,MAChG;AACA,UAAI,WAAW,WAAW;AACtB,gBAAQ,qBAAqB,CAAC,4BAA4B,+BAA+B,CAAC;AAAA,MAC9F;AACA,UAAI,WAAW,SAAS;AACpB,gBAAQ,qBAAqB,CAAC,0BAA0B,6BAA6B,CAAC;AAAA,MAC1F;AACA,UAAI,MAAM,WAAW,MAAM,UAAU,MAAM,UAAU,MAAM,SAAS,MAAM,MAAM;AAC5E,YAAI,CAAC,sBAAsB;AACvB,iBAAO,qBAAqB,CAAC,YAAY,CAAC;AAAA,QAC9C;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;", "names": ["XEUtils", "computeCustomOpts"]}