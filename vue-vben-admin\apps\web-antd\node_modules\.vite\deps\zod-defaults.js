import {
  external_exports
} from "./chunk-RUBEAIZZ.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/zod-defaults@0.1.3_zod@3.25.67/node_modules/zod-defaults/dist/zod-defaults.js
var a = (e, o) => e.constructor.name === o.name;
var n = /* @__PURE__ */ new Map();
n.set(external_exports.ZodBoolean.name, () => false), n.set(external_exports.ZodNumber.name, () => 0), n.set(external_exports.ZodString.name, () => ""), n.set(external_exports.ZodArray.name, () => []), n.set(external_exports.ZodRecord.name, () => ({})), n.set(external_exports.ZodDefault.name, (e) => e._def.defaultValue()), n.set(external_exports.ZodEffects.name, (e) => c(e._def.schema)), n.set(external_exports.ZodOptional.name, (e) => a(e._def.innerType, external_exports.ZodDefault) ? e._def.innerType._def.defaultValue() : void 0), n.set(external_exports.ZodTuple.name, (e) => {
  const o = [];
  for (const d of e._def.items) o.push(c(d));
  return o;
}), n.set(external_exports.ZodEffects.name, (e) => c(e._def.schema)), n.set(external_exports.ZodUnion.name, (e) => c(e._def.options[0])), n.set(external_exports.ZodObject.name, (e) => r(e)), n.set(external_exports.ZodRecord.name, (e) => r(e)), n.set(external_exports.ZodIntersection.name, (e) => r(e));
function c(e) {
  const o = e.constructor.name;
  if (!n.has(o)) {
    console.warn("getSchemaDefaultForField: Unhandled type", e.constructor.name);
    return;
  }
  return n.get(o)(e);
}
function r(e) {
  if (a(e, external_exports.ZodRecord)) return {};
  if (a(e, external_exports.ZodEffects)) return r(e._def.schema);
  if (a(e, external_exports.ZodIntersection)) return { ...r(e._def.left), ...r(e._def.right) };
  if (a(e, external_exports.ZodUnion)) {
    for (const o of e._def.options) if (a(o, external_exports.ZodObject)) return r(o);
    return console.warn("getSchemaDefaultObject: No object found in union, returning empty object"), {};
  }
  return a(e, external_exports.ZodObject) ? Object.fromEntries(Object.entries(e.shape).map(([o, d]) => [o, c(d)]).filter((o) => o[1] !== void 0)) : (console.warn(`getSchemaDefaultObject: Expected object schema, got ${e.constructor.name}`), {});
}
function s(e) {
  return r(e);
}
export {
  s as getDefaultsForSchema
};
//# sourceMappingURL=zod-defaults.js.map
