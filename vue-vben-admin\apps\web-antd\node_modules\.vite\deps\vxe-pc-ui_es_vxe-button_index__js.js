import {
  button_default
} from "./chunk-RBASGZGV.js";
import "./chunk-KQZ2MQHY.js";
import "./chunk-H6EAC26D.js";
import "./chunk-A3JYOOKK.js";
import "./chunk-EYZRMTBP.js";
import {
  dynamicApp
} from "./chunk-BUOIGA6Q.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-MSIZQRL4.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

// ../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-button/index.js
var vxe_button_default = button_default2;
export {
  Button,
  VxeButton,
  vxe_button_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-button_index__js.js.map
