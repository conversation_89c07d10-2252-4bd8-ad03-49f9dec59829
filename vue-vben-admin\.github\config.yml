# Prevent issues being created without using the template
blank_issues_enabled: false
checkIssueTemplate: true
checkPullRequestTemplate: true

contact_links:
  - name: 💬 Discord Chat
    url: https://discord.gg/8GuAdwDhj6
    about: Ask questions and discuss with other Vben users in real time.

  - name: ❓ Questions & Discussions
    url: https://github.com/@vbenjs/vue-vben-admin/discussions
    about: Use GitHub discussions for message-board style questions and discussions.

# Comment to be posted to on PRs from first time contributors in your repository
newPRWelcomeComment: |
  💖 Thanks for opening this pull request! 💖
  Please be patient and we will get back to you as soon as we can.

# Comment to be posted to on pull requests merged by a first time user
firstPRMergeComment: >
  Thanks for your contribution!  🎉🎉🎉


# Comment to be posted to on first time issues
newIssueWelcomeComment: >
  Thanks for opening your first issue! Be sure to follow the issue template and provide every bit of information to help the developers!


# *OPTIONAL* default titles to check against for lack of descriptiveness
# MUST BE ALL LOWERCASE
requestInfoDefaultTitles:
  - update readme.md
  - updates

# *Required* Comment to reply with
requestInfoReplyComment: >
  Thanks for filing this issue/PR! It would be much appreciated if you could provide us with more information so we can effectively analyze the situation in context.

