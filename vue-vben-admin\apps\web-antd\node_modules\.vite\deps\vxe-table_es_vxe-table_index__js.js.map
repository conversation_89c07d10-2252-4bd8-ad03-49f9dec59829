{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/src/use/cell-view.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/filter/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/menu/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/edit/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/export/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/export/util.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/keyboard/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/validator/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/module/custom/hook.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/render/index.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/index.js", "../../../../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-table/index.js"], "sourcesContent": ["import { computed } from 'vue';\nimport XEUtils from 'xe-utils';\nexport function useCellView(props) {\n    const currColumn = computed(() => {\n        const { renderParams } = props;\n        return renderParams.column;\n    });\n    const currRow = computed(() => {\n        const { renderParams } = props;\n        return renderParams.row;\n    });\n    const cellOptions = computed(() => {\n        const { renderOpts } = props;\n        return renderOpts.props || {};\n    });\n    const cellModel = computed({\n        get() {\n            const { renderParams } = props;\n            const { row, column } = renderParams;\n            return XEUtils.get(row, column.field);\n        },\n        set(value) {\n            const { renderParams } = props;\n            const { row, column } = renderParams;\n            return XEUtils.set(row, column.field, value);\n        }\n    });\n    return {\n        currColumn,\n        currRow,\n        cellModel,\n        cellOptions\n    };\n}\n", "import { nextTick } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../../ui';\nimport { toFilters, handleFieldOrColumn, getRefElem } from '../../src/util';\nimport { toCssUnit, triggerEvent, getDomNode } from '../../../ui/src/dom';\nimport { isEnableConf } from '../../../ui/src/utils';\nconst { renderer, hooks } = VxeUI;\nconst tableFilterMethodKeys = ['openFilter', 'setFilter', 'clearFilter', 'saveFilterPanel', 'saveFilterPanelByEvent', 'resetFilterPanel', 'resetFilterPanelByEvent', 'getCheckedFilters', 'updateFilterOptionStatus'];\nhooks.add('tableFilterModule', {\n    setupTable($xeTable) {\n        const { props, reactData, internalData } = $xeTable;\n        const { refElem, refTableFilter } = $xeTable.getRefMaps();\n        const { computeFilterOpts, computeMouseOpts } = $xeTable.getComputeMaps();\n        // 确认筛选\n        const handleFilterConfirmFilter = (evnt) => {\n            const { filterStore } = reactData;\n            filterStore.options.forEach((option) => {\n                option.checked = option._checked;\n            });\n            $xeTable.confirmFilterEvent(evnt);\n        };\n        // （单选）筛选发生改变\n        const changeRadioOption = (evnt, checked, item) => {\n            const { filterStore } = reactData;\n            filterStore.options.forEach((option) => {\n                option._checked = false;\n            });\n            item._checked = checked;\n            $xeTable.checkFilterOptions();\n            handleFilterConfirmFilter(evnt);\n        };\n        // （多选）筛选发生改变\n        const changeMultipleOption = (evnt, checked, item) => {\n            item._checked = checked;\n            $xeTable.checkFilterOptions();\n        };\n        /**\n         * 重置筛选\n         * 当筛选面板中的重置按钮被按下时触发\n         * @param {Event} evnt 事件\n         */\n        const handleFilterResetFilter = (evnt) => {\n            const { filterStore } = reactData;\n            $xeTable.handleClearFilter(filterStore.column);\n            $xeTable.confirmFilterEvent(evnt);\n            if (evnt) {\n                $xeTable.dispatchEvent('clear-filter', { filterList: [] }, evnt);\n            }\n        };\n        const filterPrivateMethods = {\n            checkFilterOptions() {\n                const { filterStore } = reactData;\n                filterStore.isAllSelected = filterStore.options.every((item) => item._checked);\n                filterStore.isIndeterminate = !filterStore.isAllSelected && filterStore.options.some((item) => item._checked);\n            },\n            /**\n             * 点击筛选事件\n             * 当筛选图标被点击时触发\n             * 更新选项是否全部状态\n             * 打开筛选面板\n             * @param {Event} evnt 事件\n             * @param {ColumnInfo} column 列配置\n             * @param {Object} params 参数\n             */\n            triggerFilterEvent(evnt, column, params) {\n                const { initStore, filterStore } = reactData;\n                const { elemStore } = internalData;\n                if (filterStore.column === column && filterStore.visible) {\n                    filterStore.visible = false;\n                }\n                else {\n                    const el = refElem.value;\n                    const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();\n                    const filterOpts = computeFilterOpts.value;\n                    const { transfer } = filterOpts;\n                    const tableRect = el.getBoundingClientRect();\n                    const btnElem = evnt.currentTarget;\n                    const { filters, filterMultiple, filterRender } = column;\n                    const compConf = isEnableConf(filterRender) ? renderer.get(filterRender.name) : null;\n                    const frMethod = column.filterRecoverMethod || (compConf ? (compConf.tableFilterRecoverMethod || compConf.filterRecoverMethod) : null);\n                    internalData._currFilterParams = params;\n                    Object.assign(filterStore, {\n                        multiple: filterMultiple,\n                        options: filters,\n                        column,\n                        style: null\n                    });\n                    // 复原状态\n                    filterStore.options.forEach((option) => {\n                        const { _checked, checked } = option;\n                        option._checked = checked;\n                        if (!checked && _checked !== checked) {\n                            if (frMethod) {\n                                frMethod({ option, column, $table: $xeTable });\n                            }\n                        }\n                    });\n                    this.checkFilterOptions();\n                    filterStore.visible = true;\n                    initStore.filter = true;\n                    nextTick(() => {\n                        const headerScrollElem = getRefElem(elemStore['main-header-scroll']);\n                        if (!headerScrollElem) {\n                            return;\n                        }\n                        const tableFilter = refTableFilter.value;\n                        const filterWrapperElem = tableFilter ? tableFilter.getRefMaps().refElem.value : null;\n                        if (!filterWrapperElem) {\n                            return;\n                        }\n                        const btnRect = btnElem.getBoundingClientRect();\n                        const filterHeadElem = filterWrapperElem.querySelector('.vxe-table--filter-header');\n                        const filterFootElem = filterWrapperElem.querySelector('.vxe-table--filter-footer');\n                        const filterWidth = filterWrapperElem.offsetWidth;\n                        const centerWidth = filterWidth / 2;\n                        let left = 0;\n                        let top = 0;\n                        let maxHeight = 0;\n                        if (transfer) {\n                            left = btnRect.left - centerWidth + scrollLeft;\n                            top = btnRect.top + btnElem.clientHeight + scrollTop;\n                            maxHeight = Math.min(Math.max(tableRect.height, Math.floor(visibleHeight / 2)), Math.max(80, visibleHeight - top - (filterHeadElem ? filterHeadElem.clientHeight : 0) - (filterFootElem ? filterFootElem.clientHeight : 0) - 28));\n                            if (left < 16) {\n                                left = 16;\n                            }\n                            else if (left > (visibleWidth - filterWidth - 16)) {\n                                left = visibleWidth - filterWidth - 16;\n                            }\n                        }\n                        else {\n                            left = btnRect.left - tableRect.left - centerWidth;\n                            top = btnRect.top - tableRect.top + btnElem.clientHeight;\n                            maxHeight = Math.max(40, el.clientHeight - top - (filterHeadElem ? filterHeadElem.clientHeight : 0) - (filterFootElem ? filterFootElem.clientHeight : 0) - 14);\n                            if (left < 1) {\n                                left = 1;\n                            }\n                            else if (left > (el.clientWidth - filterWidth - 1)) {\n                                left = el.clientWidth - filterWidth - 1;\n                            }\n                        }\n                        filterStore.style = {\n                            top: toCssUnit(top),\n                            left: toCssUnit(left)\n                        };\n                        // 判断面板不能大于表格高度\n                        filterStore.maxHeight = maxHeight;\n                    });\n                }\n                $xeTable.dispatchEvent('filter-visible', { column, field: column.field, property: column.field, filterList: $xeTable.getCheckedFilters(), visible: filterStore.visible }, evnt);\n            },\n            handleClearFilter(column) {\n                if (column) {\n                    const { filters, filterRender } = column;\n                    if (filters) {\n                        const compConf = isEnableConf(filterRender) ? renderer.get(filterRender.name) : null;\n                        const frMethod = column.filterResetMethod || (compConf ? (compConf.tableFilterResetMethod || compConf.filterResetMethod) : null);\n                        filters.forEach((item) => {\n                            item._checked = false;\n                            item.checked = false;\n                            if (!frMethod) {\n                                item.data = XEUtils.clone(item.resetValue, true);\n                            }\n                        });\n                        if (frMethod) {\n                            frMethod({ options: filters, column, $table: $xeTable });\n                        }\n                    }\n                }\n            },\n            handleColumnConfirmFilter(column, evnt) {\n                const { mouseConfig } = props;\n                const { scrollXLoad: oldScrollXLoad, scrollYLoad: oldScrollYLoad } = reactData;\n                const filterOpts = computeFilterOpts.value;\n                const mouseOpts = computeMouseOpts.value;\n                const { field } = column;\n                const values = [];\n                const datas = [];\n                column.filters.forEach((item) => {\n                    if (item.checked) {\n                        values.push(item.value);\n                        datas.push(item.data);\n                    }\n                });\n                const filterList = $xeTable.getCheckedFilters();\n                const params = { $table: $xeTable, $event: evnt, column, field, property: field, values, datas, filters: filterList, filterList };\n                // 如果是服务端筛选，则跳过本地筛选处理\n                if (!filterOpts.remote) {\n                    $xeTable.handleTableData(true);\n                    $xeTable.checkSelectionStatus();\n                }\n                if (mouseConfig && mouseOpts.area && $xeTable.handleFilterEvent) {\n                    $xeTable.handleFilterEvent(evnt, params);\n                }\n                if (evnt) {\n                    $xeTable.dispatchEvent('filter-change', params, evnt);\n                }\n                $xeTable.closeFilter();\n                return $xeTable.updateFooter().then(() => {\n                    const { scrollXLoad, scrollYLoad } = reactData;\n                    if ((oldScrollXLoad || scrollXLoad) || (oldScrollYLoad || scrollYLoad)) {\n                        if (oldScrollXLoad || scrollXLoad) {\n                            $xeTable.updateScrollXSpace();\n                        }\n                        if (oldScrollYLoad || scrollYLoad) {\n                            $xeTable.updateScrollYSpace();\n                        }\n                        return $xeTable.refreshScroll();\n                    }\n                }).then(() => {\n                    $xeTable.updateCellAreas();\n                    return $xeTable.recalculate(true);\n                }).then(() => {\n                    // 存在滚动行为未结束情况\n                    setTimeout(() => $xeTable.recalculate(), 50);\n                });\n            },\n            /**\n             * 确认筛选\n             * 当筛选面板中的确定按钮被按下时触发\n             * @param {Event} evnt 事件\n             */\n            confirmFilterEvent(evnt) {\n                const { filterStore } = reactData;\n                const { column } = filterStore;\n                $xeTable.handleColumnConfirmFilter(column, evnt);\n            },\n            handleFilterChangeRadioOption: changeRadioOption,\n            handleFilterChangeMultipleOption: changeMultipleOption,\n            // 筛选发生改变\n            handleFilterChangeOption(evnt, checked, item) {\n                const { filterStore } = reactData;\n                if (filterStore.multiple) {\n                    changeMultipleOption(evnt, checked, item);\n                }\n                else {\n                    changeRadioOption(evnt, checked, item);\n                }\n            },\n            handleFilterConfirmFilter,\n            handleFilterResetFilter\n        };\n        const filterMethods = {\n            /**\n             * 手动弹出筛选面板\n             * @param column\n             */\n            openFilter(fieldOrColumn) {\n                const column = handleFieldOrColumn($xeTable, fieldOrColumn);\n                if (column && column.filters) {\n                    const { elemStore } = internalData;\n                    const { fixed } = column;\n                    return $xeTable.scrollToColumn(column).then(() => {\n                        const headerWrapperElem = getRefElem(elemStore[`${fixed || 'main'}-header-wrapper`] || elemStore['main-header-wrapper']);\n                        if (headerWrapperElem) {\n                            const filterBtnElem = headerWrapperElem.querySelector(`.vxe-header--column.${column.id} .vxe-cell--filter`);\n                            triggerEvent(filterBtnElem, 'click');\n                        }\n                    });\n                }\n                return nextTick();\n            },\n            /**\n             * 修改筛选条件列表\n             * @param {ColumnInfo} fieldOrColumn 列或字段名\n             * @param {Array} options 选项\n             */\n            setFilter(fieldOrColumn, options, isUpdate) {\n                const column = handleFieldOrColumn($xeTable, fieldOrColumn);\n                if (column && column.filters) {\n                    column.filters = toFilters(options || []);\n                    if (isUpdate) {\n                        // 已废弃，即将去掉事件触发 new Event('click') -> null\n                        return $xeTable.handleColumnConfirmFilter(column, new Event('click'));\n                    }\n                }\n                return nextTick();\n            },\n            /**\n             * 清空指定列的筛选条件\n             * 如果为空则清空所有列的筛选条件\n             * @param {String} fieldOrColumn 列或字段名\n             */\n            clearFilter(fieldOrColumn) {\n                const { filterStore } = reactData;\n                const { tableFullColumn } = internalData;\n                const filterOpts = computeFilterOpts.value;\n                let column;\n                if (fieldOrColumn) {\n                    column = handleFieldOrColumn($xeTable, fieldOrColumn);\n                    if (column) {\n                        $xeTable.handleClearFilter(column);\n                    }\n                }\n                else {\n                    tableFullColumn.forEach($xeTable.handleClearFilter);\n                }\n                if (!fieldOrColumn || column !== filterStore.column) {\n                    Object.assign(filterStore, {\n                        isAllSelected: false,\n                        isIndeterminate: false,\n                        style: null,\n                        options: [],\n                        column: null,\n                        multiple: false,\n                        visible: false\n                    });\n                }\n                if (!filterOpts.remote) {\n                    return $xeTable.updateData();\n                }\n                return nextTick();\n            },\n            saveFilterPanel() {\n                handleFilterConfirmFilter(null);\n                return nextTick();\n            },\n            saveFilterPanelByEvent(evnt) {\n                handleFilterConfirmFilter(evnt);\n                return nextTick();\n            },\n            resetFilterPanel() {\n                handleFilterResetFilter(null);\n                return nextTick();\n            },\n            resetFilterPanelByEvent(evnt) {\n                handleFilterResetFilter(evnt);\n                return nextTick();\n            },\n            getCheckedFilters() {\n                const { tableFullColumn } = internalData;\n                const filterList = [];\n                tableFullColumn.forEach((column) => {\n                    const { field, filters } = column;\n                    const valueList = [];\n                    const dataList = [];\n                    if (filters && filters.length) {\n                        filters.forEach((item) => {\n                            if (item.checked) {\n                                valueList.push(item.value);\n                                dataList.push(item.data);\n                            }\n                        });\n                        if (valueList.length) {\n                            filterList.push({ column, field, property: field, values: valueList, datas: dataList });\n                        }\n                    }\n                });\n                return filterList;\n            },\n            updateFilterOptionStatus(item, checked) {\n                item._checked = checked;\n                item.checked = checked;\n                return nextTick();\n            }\n        };\n        return Object.assign(Object.assign({}, filterMethods), filterPrivateMethods);\n    },\n    setupGrid($xeGrid) {\n        return $xeGrid.extendTableMethods(tableFilterMethodKeys);\n    }\n});\n", "import { nextTick } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../../ui';\nimport { getDomNode, getAbsolutePos, getEventTargetNode } from '../../../ui/src/dom';\nimport { isEnableConf, hasChildrenList } from '../../../ui/src/utils';\nconst { menus, hooks, globalEvents, GLOBAL_EVENT_KEYS } = VxeUI;\nconst tableMenuMethodKeys = ['closeMenu'];\nhooks.add('tableMenuModule', {\n    setupTable($xeTable) {\n        const { xID, props, reactData, internalData } = $xeTable;\n        const { refElem, refTableFilter, refTableMenu } = $xeTable.getRefMaps();\n        const { computeMouseOpts, computeIsMenu, computeMenuOpts } = $xeTable.getComputeMaps();\n        let menuMethods = {};\n        let menuPrivateMethods = {};\n        /**\n         * 显示快捷菜单\n         */\n        const handleOpenMenuEvent = (evnt, type, params) => {\n            const { ctxMenuStore } = reactData;\n            const isMenu = computeIsMenu.value;\n            const menuOpts = computeMenuOpts.value;\n            const config = menuOpts[type];\n            const visibleMethod = menuOpts.visibleMethod;\n            if (config) {\n                const { options, disabled } = config;\n                if (disabled) {\n                    evnt.preventDefault();\n                }\n                else if (isMenu && options && options.length) {\n                    params.options = options;\n                    $xeTable.preventEvent(evnt, 'event.showMenu', params, () => {\n                        if (!visibleMethod || visibleMethod(params)) {\n                            evnt.preventDefault();\n                            $xeTable.updateZindex();\n                            const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();\n                            let top = evnt.clientY + scrollTop;\n                            let left = evnt.clientX + scrollLeft;\n                            const handleVisible = () => {\n                                internalData._currMenuParams = params;\n                                Object.assign(ctxMenuStore, {\n                                    visible: true,\n                                    list: options,\n                                    selected: null,\n                                    selectChild: null,\n                                    showChild: false,\n                                    style: {\n                                        zIndex: internalData.tZindex,\n                                        top: `${top}px`,\n                                        left: `${left}px`\n                                    }\n                                });\n                                nextTick(() => {\n                                    const tableMenu = refTableMenu.value;\n                                    const ctxElem = tableMenu.getRefMaps().refElem.value;\n                                    const clientHeight = ctxElem.clientHeight;\n                                    const clientWidth = ctxElem.clientWidth;\n                                    const { boundingTop, boundingLeft } = getAbsolutePos(ctxElem);\n                                    const offsetTop = boundingTop + clientHeight - visibleHeight;\n                                    const offsetLeft = boundingLeft + clientWidth - visibleWidth;\n                                    if (offsetTop > -10) {\n                                        ctxMenuStore.style.top = `${Math.max(scrollTop + 2, top - clientHeight - 2)}px`;\n                                    }\n                                    if (offsetLeft > -10) {\n                                        ctxMenuStore.style.left = `${Math.max(scrollLeft + 2, left - clientWidth - 2)}px`;\n                                    }\n                                });\n                            };\n                            const { keyboard, row, column } = params;\n                            if (keyboard && row && column) {\n                                $xeTable.scrollToRow(row, column).then(() => {\n                                    const cell = $xeTable.getCellElement(row, column);\n                                    if (cell) {\n                                        const { boundingTop, boundingLeft } = getAbsolutePos(cell);\n                                        top = boundingTop + scrollTop + Math.floor(cell.offsetHeight / 2);\n                                        left = boundingLeft + scrollLeft + Math.floor(cell.offsetWidth / 2);\n                                    }\n                                    handleVisible();\n                                });\n                            }\n                            else {\n                                handleVisible();\n                            }\n                        }\n                        else {\n                            menuMethods.closeMenu();\n                        }\n                    });\n                }\n            }\n            $xeTable.closeFilter();\n        };\n        menuMethods = {\n            /**\n             * 关闭快捷菜单\n             */\n            closeMenu() {\n                Object.assign(reactData.ctxMenuStore, {\n                    visible: false,\n                    selected: null,\n                    selectChild: null,\n                    showChild: false\n                });\n                return nextTick();\n            }\n        };\n        menuPrivateMethods = {\n            /**\n             * 处理菜单的移动\n             */\n            moveCtxMenu(evnt, ctxMenuStore, property, hasOper, operRest, menuList) {\n                let selectItem;\n                const selectIndex = XEUtils.findIndexOf(menuList, item => ctxMenuStore[property] === item);\n                if (hasOper) {\n                    if (operRest && hasChildrenList(ctxMenuStore.selected)) {\n                        ctxMenuStore.showChild = true;\n                    }\n                    else {\n                        ctxMenuStore.showChild = false;\n                        ctxMenuStore.selectChild = null;\n                    }\n                }\n                else if (globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ARROW_UP)) {\n                    for (let len = selectIndex - 1; len >= 0; len--) {\n                        if (menuList[len].visible !== false) {\n                            selectItem = menuList[len];\n                            break;\n                        }\n                    }\n                    ctxMenuStore[property] = selectItem || menuList[menuList.length - 1];\n                }\n                else if (globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ARROW_DOWN)) {\n                    for (let index = selectIndex + 1; index < menuList.length; index++) {\n                        if (menuList[index].visible !== false) {\n                            selectItem = menuList[index];\n                            break;\n                        }\n                    }\n                    ctxMenuStore[property] = selectItem || menuList[0];\n                }\n                else if (ctxMenuStore[property] && (globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ENTER) || globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.SPACEBAR))) {\n                    $xeTable.ctxMenuLinkEvent(evnt, ctxMenuStore[property]);\n                }\n            },\n            handleOpenMenuEvent,\n            /**\n             * 快捷菜单事件处理\n             */\n            handleGlobalContextmenuEvent(evnt) {\n                const { mouseConfig, menuConfig } = props;\n                const { editStore, ctxMenuStore } = reactData;\n                const { visibleColumn } = internalData;\n                const tableFilter = refTableFilter.value;\n                const tableMenu = refTableMenu.value;\n                const mouseOpts = computeMouseOpts.value;\n                const menuOpts = computeMenuOpts.value;\n                const el = refElem.value;\n                const { selected } = editStore;\n                const layoutList = ['header', 'body', 'footer'];\n                if (isEnableConf(menuConfig)) {\n                    if (ctxMenuStore.visible && tableMenu && getEventTargetNode(evnt, tableMenu.getRefMaps().refElem.value).flag) {\n                        evnt.preventDefault();\n                        return;\n                    }\n                    if (internalData._keyCtx) {\n                        const type = 'body';\n                        const params = { type, $table: $xeTable, keyboard: true, columns: visibleColumn.slice(0), $event: evnt };\n                        // 如果开启单元格区域\n                        if (mouseConfig && mouseOpts.area) {\n                            const activeArea = $xeTable.getActiveCellArea();\n                            if (activeArea && activeArea.row && activeArea.column) {\n                                params.row = activeArea.row;\n                                params.column = activeArea.column;\n                                handleOpenMenuEvent(evnt, type, params);\n                                return;\n                            }\n                        }\n                        else if (mouseConfig && mouseOpts.selected) {\n                            // 如果启用键盘导航且已选中单元格\n                            if (selected.row && selected.column) {\n                                params.row = selected.row;\n                                params.column = selected.column;\n                                handleOpenMenuEvent(evnt, type, params);\n                                return;\n                            }\n                        }\n                    }\n                    // 分别匹配表尾、内容、表尾的快捷菜单\n                    for (let index = 0; index < layoutList.length; index++) {\n                        const layout = layoutList[index];\n                        const columnTargetNode = getEventTargetNode(evnt, el, `vxe-${layout}--column`, (target) => {\n                            // target=td|th，直接向上找 table 去匹配即可\n                            return target.parentNode.parentNode.parentNode.getAttribute('xid') === xID;\n                        });\n                        const params = { type: layout, $table: $xeTable, columns: visibleColumn.slice(0), $event: evnt };\n                        if (columnTargetNode.flag) {\n                            const cell = columnTargetNode.targetElem;\n                            const columnNodeRest = $xeTable.getColumnNode(cell);\n                            const column = columnNodeRest ? columnNodeRest.item : null;\n                            let typePrefix = `${layout}-`;\n                            if (column) {\n                                Object.assign(params, { column, columnIndex: $xeTable.getColumnIndex(column), cell });\n                            }\n                            if (layout === 'body') {\n                                const rowNodeRest = $xeTable.getRowNode(cell.parentNode);\n                                const row = rowNodeRest ? rowNodeRest.item : null;\n                                typePrefix = '';\n                                if (row) {\n                                    params.row = row;\n                                    params.rowIndex = $xeTable.getRowIndex(row);\n                                }\n                            }\n                            const eventType = `${typePrefix}cell-menu`;\n                            handleOpenMenuEvent(evnt, layout, params);\n                            $xeTable.dispatchEvent(eventType, params, evnt);\n                            return;\n                        }\n                        else if (getEventTargetNode(evnt, el, `vxe-table--${layout}-wrapper`, target => target.getAttribute('xid') === xID).flag) {\n                            if (menuOpts.trigger === 'cell') {\n                                evnt.preventDefault();\n                            }\n                            else {\n                                handleOpenMenuEvent(evnt, layout, params);\n                            }\n                            return;\n                        }\n                    }\n                }\n                if (tableFilter && !getEventTargetNode(evnt, tableFilter.getRefMaps().refElem.value).flag) {\n                    $xeTable.closeFilter();\n                }\n                menuMethods.closeMenu();\n            },\n            ctxMenuMouseoverEvent(evnt, item, child) {\n                const menuElem = evnt.currentTarget;\n                const { ctxMenuStore } = reactData;\n                evnt.preventDefault();\n                evnt.stopPropagation();\n                ctxMenuStore.selected = item;\n                ctxMenuStore.selectChild = child;\n                if (!child) {\n                    ctxMenuStore.showChild = hasChildrenList(item);\n                    if (ctxMenuStore.showChild) {\n                        nextTick(() => {\n                            const childWrapperElem = menuElem.nextElementSibling;\n                            if (childWrapperElem) {\n                                const { boundingTop, boundingLeft, visibleHeight, visibleWidth } = getAbsolutePos(menuElem);\n                                const posTop = boundingTop + menuElem.offsetHeight;\n                                const posLeft = boundingLeft + menuElem.offsetWidth;\n                                let left = '';\n                                let right = '';\n                                // 是否超出右侧\n                                if (posLeft + childWrapperElem.offsetWidth > visibleWidth - 10) {\n                                    left = 'auto';\n                                    right = `${menuElem.offsetWidth}px`;\n                                }\n                                // 是否超出底部\n                                let top = '';\n                                let bottom = '';\n                                if (posTop + childWrapperElem.offsetHeight > visibleHeight - 10) {\n                                    top = 'auto';\n                                    bottom = '0';\n                                }\n                                childWrapperElem.style.left = left;\n                                childWrapperElem.style.right = right;\n                                childWrapperElem.style.top = top;\n                                childWrapperElem.style.bottom = bottom;\n                            }\n                        });\n                    }\n                }\n            },\n            ctxMenuMouseoutEvent(evnt, item) {\n                const { ctxMenuStore } = reactData;\n                if (!item.children) {\n                    ctxMenuStore.selected = null;\n                }\n                ctxMenuStore.selectChild = null;\n            },\n            /**\n             * 快捷菜单点击事件\n             */\n            ctxMenuLinkEvent(evnt, menu) {\n                const $xeGrid = $xeTable.xeGrid;\n                // 如果一级菜单有配置 code 则允许点击，否则不能点击\n                if (!menu.disabled && (menu.code || !menu.children || !menu.children.length)) {\n                    const gMenuOpts = menus.get(menu.code);\n                    const params = Object.assign({}, internalData._currMenuParams, { menu, $table: $xeTable, $grid: $xeGrid, $event: evnt });\n                    const tmMethod = gMenuOpts ? (gMenuOpts.tableMenuMethod || gMenuOpts.menuMethod) : null;\n                    if (tmMethod) {\n                        tmMethod(params, evnt);\n                    }\n                    $xeTable.dispatchEvent('menu-click', params, evnt);\n                    menuMethods.closeMenu();\n                }\n            }\n        };\n        return Object.assign(Object.assign({}, menuMethods), menuPrivateMethods);\n    },\n    setupGrid($xeGrid) {\n        return $xeGrid.extendTableMethods(tableMenuMethodKeys);\n    }\n});\n", "import { reactive, nextTick } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../../ui';\nimport { isEnableConf } from '../../../ui/src/utils';\nimport { getCellValue, setCellValue, getRowid } from '../../src/util';\nimport { removeClass, addClass } from '../../../ui/src/dom';\nimport { warnLog, errLog } from '../../../ui/src/log';\nconst { getConfig, renderer, hooks, getI18n } = VxeUI;\nconst tableEditMethodKeys = ['insert', 'insertAt', 'insertNextAt', 'insertChild', 'insertChildAt', 'insertChildNextAt', 'remove', 'removeCheckboxRow', 'removeRadioRow', 'removeCurrentRow', 'getRecordset', 'getInsertRecords', 'getRemoveRecords', 'getUpdateRecords', 'getEditRecord', 'getActiveRecord', 'getEditCell', 'getSelectedCell', 'clearEdit', 'clearActived', 'clearSelected', 'isEditByRow', 'isActiveByRow', 'setEditRow', 'setActiveRow', 'setEditCell', 'setActiveCell', 'setSelectCell'];\nhooks.add('tableEditModule', {\n    setupTable($xeTable) {\n        const { props, reactData, internalData } = $xeTable;\n        const { refElem } = $xeTable.getRefMaps();\n        const { computeMouseOpts, computeEditOpts, computeCheckboxOpts, computeTreeOpts, computeValidOpts } = $xeTable.getComputeMaps();\n        const browseObj = XEUtils.browse();\n        let editMethods = {};\n        let editPrivateMethods = {};\n        const getEditColumnModel = (row, column) => {\n            const { model, editRender } = column;\n            if (editRender) {\n                model.value = getCellValue(row, column);\n                model.update = false;\n            }\n        };\n        const setEditColumnModel = (row, column) => {\n            const { model, editRender } = column;\n            if (editRender && model.update) {\n                setCellValue(row, column, model.value);\n                model.update = false;\n                model.value = null;\n            }\n        };\n        const removeCellSelectedClass = () => {\n            const el = refElem.value;\n            if (el) {\n                const cell = el.querySelector('.col--selected');\n                if (cell) {\n                    removeClass(cell, 'col--selected');\n                }\n            }\n        };\n        const syncActivedCell = () => {\n            const { editStore, tableColumn } = reactData;\n            const editOpts = computeEditOpts.value;\n            const { actived } = editStore;\n            const { row, column } = actived;\n            if (row || column) {\n                if (editOpts.mode === 'row') {\n                    tableColumn.forEach((column) => setEditColumnModel(row, column));\n                }\n                else {\n                    setEditColumnModel(row, column);\n                }\n            }\n        };\n        const insertTreeRow = (newRecords, isAppend) => {\n            const { tableFullTreeData, afterFullData, fullDataRowIdData, fullAllDataRowIdData } = internalData;\n            const treeOpts = computeTreeOpts.value;\n            const { rowField, parentField, mapChildrenField } = treeOpts;\n            const childrenField = treeOpts.children || treeOpts.childrenField;\n            const funcName = isAppend ? 'push' : 'unshift';\n            newRecords.forEach(item => {\n                const parentRowId = item[parentField];\n                const rowid = getRowid($xeTable, item);\n                const matchObj = parentRowId ? XEUtils.findTree(tableFullTreeData, item => parentRowId === item[rowField], { children: mapChildrenField }) : null;\n                if (matchObj) {\n                    const { item: parentRow } = matchObj;\n                    const parentRest = fullAllDataRowIdData[getRowid($xeTable, parentRow)];\n                    const parentLevel = parentRest ? parentRest.level : 0;\n                    let parentChilds = parentRow[childrenField];\n                    let mapChilds = parentRow[mapChildrenField];\n                    if (!XEUtils.isArray(parentChilds)) {\n                        parentChilds = parentRow[childrenField] = [];\n                    }\n                    if (!XEUtils.isArray(mapChilds)) {\n                        mapChilds = parentRow[childrenField] = [];\n                    }\n                    parentChilds[funcName](item);\n                    mapChilds[funcName](item);\n                    const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: parentChilds, parent: parentRow, level: parentLevel + 1, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };\n                    fullDataRowIdData[rowid] = rest;\n                    fullAllDataRowIdData[rowid] = rest;\n                }\n                else {\n                    if (parentRowId) {\n                        warnLog('vxe.error.unableInsert');\n                    }\n                    afterFullData[funcName](item);\n                    tableFullTreeData[funcName](item);\n                    const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: tableFullTreeData, parent: null, level: 0, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };\n                    fullDataRowIdData[rowid] = rest;\n                    fullAllDataRowIdData[rowid] = rest;\n                }\n            });\n        };\n        // const insertGroupRow = (newRecords: any[], isAppend: boolean) => {\n        // }\n        const handleInsertRowAt = (records, targetRow, isInsertNextRow) => {\n            const { treeConfig } = props;\n            const { isRowGroupStatus } = reactData;\n            const { tableFullTreeData, afterFullData, mergeBodyList, tableFullData, fullDataRowIdData, fullAllDataRowIdData, insertRowMaps } = internalData;\n            const treeOpts = computeTreeOpts.value;\n            const { transform, rowField, mapChildrenField } = treeOpts;\n            const childrenField = treeOpts.children || treeOpts.childrenField;\n            if (!XEUtils.isArray(records)) {\n                records = [records];\n            }\n            const newRecords = reactive($xeTable.defineField(records.map((record) => Object.assign(treeConfig && transform ? { [mapChildrenField]: [], [childrenField]: [] } : {}, record))));\n            if (XEUtils.eqNull(targetRow)) {\n                // 如果为虚拟树\n                if (treeConfig && transform) {\n                    insertTreeRow(newRecords, false);\n                }\n                else if (isRowGroupStatus) {\n                    // 如果分组\n                    if (treeConfig) {\n                        throw new Error(getI18n('vxe.error.noTree', ['insert']));\n                    }\n                    warnLog(getI18n('vxe.error.noGroup', ['remove']));\n                    // insertGroupRow(newRecords, false)\n                }\n                else {\n                    newRecords.forEach(item => {\n                        const rowid = getRowid($xeTable, item);\n                        const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: afterFullData, parent: null, level: 0, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };\n                        fullDataRowIdData[rowid] = rest;\n                        fullAllDataRowIdData[rowid] = rest;\n                        afterFullData.unshift(item);\n                        tableFullData.unshift(item);\n                    });\n                    // 刷新单元格合并\n                    mergeBodyList.forEach((mergeItem) => {\n                        const { row: mergeRowIndex } = mergeItem;\n                        if (mergeRowIndex >= 0) {\n                            mergeItem.row = mergeRowIndex + newRecords.length;\n                        }\n                    });\n                }\n            }\n            else {\n                if (targetRow === -1) {\n                    // 如果为虚拟树\n                    if (treeConfig && transform) {\n                        insertTreeRow(newRecords, true);\n                    }\n                    else if (isRowGroupStatus) {\n                        // 如果分组\n                        if (treeConfig) {\n                            throw new Error(getI18n('vxe.error.noTree', ['insert']));\n                        }\n                        warnLog(getI18n('vxe.error.noGroup', ['remove']));\n                        // insertGroupRow(newRecords, true)\n                    }\n                    else {\n                        newRecords.forEach(item => {\n                            const rowid = getRowid($xeTable, item);\n                            const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, treeIndex: -1, $index: -1, items: afterFullData, parent: null, level: 0, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };\n                            fullDataRowIdData[rowid] = rest;\n                            fullAllDataRowIdData[rowid] = rest;\n                            afterFullData.push(item);\n                            tableFullData.push(item);\n                        });\n                    }\n                }\n                else {\n                    // 如果为虚拟树\n                    if (treeConfig && transform) {\n                        const matchMapObj = XEUtils.findTree(tableFullTreeData, item => targetRow[rowField] === item[rowField], { children: mapChildrenField });\n                        if (matchMapObj) {\n                            const { parent: parentRow } = matchMapObj;\n                            const parentMapChilds = parentRow ? parentRow[mapChildrenField] : tableFullTreeData;\n                            const parentRest = fullAllDataRowIdData[getRowid($xeTable, parentRow)];\n                            const parentLevel = parentRest ? parentRest.level : 0;\n                            newRecords.forEach((item, i) => {\n                                const rowid = getRowid($xeTable, item);\n                                if (item[treeOpts.parentField]) {\n                                    if (parentRow && item[treeOpts.parentField] !== parentRow[rowField]) {\n                                        errLog('vxe.error.errProp', [`${treeOpts.parentField}=${item[treeOpts.parentField]}`, `${treeOpts.parentField}=${parentRow[rowField]}`]);\n                                    }\n                                }\n                                if (parentRow) {\n                                    item[treeOpts.parentField] = parentRow[rowField];\n                                }\n                                let targetIndex = matchMapObj.index + i;\n                                if (isInsertNextRow) {\n                                    targetIndex = targetIndex + 1;\n                                }\n                                parentMapChilds.splice(targetIndex, 0, item);\n                                const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: parentMapChilds, parent: parentRow, level: parentLevel + 1, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };\n                                fullDataRowIdData[rowid] = rest;\n                                fullAllDataRowIdData[rowid] = rest;\n                            });\n                            // 源\n                            if (parentRow) {\n                                const matchObj = XEUtils.findTree(tableFullTreeData, item => targetRow[rowField] === item[rowField], { children: childrenField });\n                                if (matchObj) {\n                                    const parentChilds = matchObj.items;\n                                    let targetIndex = matchObj.index;\n                                    if (isInsertNextRow) {\n                                        targetIndex = targetIndex + 1;\n                                    }\n                                    parentChilds.splice(targetIndex, 0, ...newRecords);\n                                }\n                            }\n                        }\n                        else {\n                            warnLog('vxe.error.unableInsert');\n                            insertTreeRow(newRecords, true);\n                        }\n                    }\n                    else if (isRowGroupStatus) {\n                        // 如果分组\n                        if (treeConfig) {\n                            throw new Error(getI18n('vxe.error.noTree', ['insert']));\n                        }\n                        warnLog(getI18n('vxe.error.noGroup', ['remove']));\n                    }\n                    else {\n                        if (treeConfig) {\n                            throw new Error(getI18n('vxe.error.noTree', ['insert']));\n                        }\n                        let afIndex = -1;\n                        // 如果是可视索引\n                        if (XEUtils.isNumber(targetRow)) {\n                            if (targetRow < afterFullData.length) {\n                                afIndex = targetRow;\n                            }\n                        }\n                        else {\n                            afIndex = $xeTable.findRowIndexOf(afterFullData, targetRow);\n                        }\n                        // 如果是插入指定行的下一行\n                        if (isInsertNextRow) {\n                            afIndex = Math.min(afterFullData.length, afIndex + 1);\n                        }\n                        if (afIndex === -1) {\n                            throw new Error(getI18n('vxe.error.unableInsert'));\n                        }\n                        afterFullData.splice(afIndex, 0, ...newRecords);\n                        const tfIndex = $xeTable.findRowIndexOf(tableFullData, targetRow);\n                        if (tfIndex > -1) {\n                            tableFullData.splice(tfIndex + (isInsertNextRow ? 1 : 0), 0, ...newRecords);\n                        }\n                        else {\n                            tableFullData.push(...newRecords);\n                        }\n                        // 刷新单元格合并\n                        mergeBodyList.forEach((mergeItem) => {\n                            const { row: mergeRowIndex, rowspan: mergeRowspan } = mergeItem;\n                            if (mergeRowIndex >= afIndex) {\n                                mergeItem.row = mergeRowIndex + newRecords.length;\n                            }\n                            else if (isInsertNextRow ? (mergeRowIndex + mergeRowspan >= afIndex) : (mergeRowIndex + mergeRowspan > afIndex)) {\n                                mergeItem.rowspan = mergeRowspan + newRecords.length;\n                            }\n                        });\n                    }\n                }\n            }\n            newRecords.forEach(newRow => {\n                const rowid = getRowid($xeTable, newRow);\n                insertRowMaps[rowid] = newRow;\n            });\n            reactData.insertRowFlag++;\n            $xeTable.cacheRowMap(false);\n            $xeTable.updateScrollYStatus();\n            $xeTable.handleTableData(treeConfig && transform);\n            if (!(treeConfig && transform)) {\n                $xeTable.updateAfterDataIndex();\n            }\n            $xeTable.updateFooter();\n            $xeTable.handleUpdateBodyMerge();\n            $xeTable.checkSelectionStatus();\n            if (reactData.scrollYLoad) {\n                $xeTable.updateScrollYSpace();\n            }\n            return nextTick().then(() => {\n                $xeTable.updateCellAreas();\n                return $xeTable.recalculate(true);\n            }).then(() => {\n                return {\n                    row: newRecords.length ? newRecords[newRecords.length - 1] : null,\n                    rows: newRecords\n                };\n            });\n        };\n        const handleInsertChildRowAt = (records, parentRow, targetRow, isInsertNextRow) => {\n            const { treeConfig } = props;\n            const treeOpts = computeTreeOpts.value;\n            const { transform, rowField, parentField } = treeOpts;\n            if (treeConfig && transform) {\n                if (!XEUtils.isArray(records)) {\n                    records = [records];\n                }\n                return handleInsertRowAt(records.map((item) => Object.assign({}, item, { [parentField]: parentRow[rowField] })), targetRow, isInsertNextRow);\n            }\n            else {\n                errLog('vxe.error.errProp', ['tree-config.transform=false', 'tree-config.transform=true']);\n            }\n            return Promise.resolve({ row: null, rows: [] });\n        };\n        const handleClearEdit = (evnt, targetRow) => {\n            const { editStore } = reactData;\n            const { actived, focused } = editStore;\n            const { row, column } = actived;\n            const validOpts = computeValidOpts.value;\n            if (row || column) {\n                if (targetRow && getRowid($xeTable, targetRow) !== getRowid($xeTable, row)) {\n                    return nextTick();\n                }\n                syncActivedCell();\n                actived.args = null;\n                actived.row = null;\n                actived.column = null;\n                $xeTable.updateFooter();\n                $xeTable.dispatchEvent('edit-closed', {\n                    row,\n                    rowIndex: $xeTable.getRowIndex(row),\n                    $rowIndex: $xeTable.getVMRowIndex(row),\n                    column,\n                    columnIndex: $xeTable.getColumnIndex(column),\n                    $columnIndex: $xeTable.getVMColumnIndex(column)\n                }, evnt || null);\n            }\n            focused.row = null;\n            focused.column = null;\n            if (validOpts.autoClear) {\n                if (validOpts.msgMode !== 'full' || getConfig().cellVaildMode === 'obsolete') {\n                    if ($xeTable.clearValidate) {\n                        return $xeTable.clearValidate();\n                    }\n                }\n            }\n            return nextTick().then(() => $xeTable.updateCellAreas());\n        };\n        const handleEditActive = (params, evnt, isFocus, isPos) => {\n            const $xeGrid = $xeTable.xeGrid;\n            const { editConfig, mouseConfig } = props;\n            const { editStore, tableColumn } = reactData;\n            const editOpts = computeEditOpts.value;\n            const { mode } = editOpts;\n            const { actived, focused } = editStore;\n            const { row, column } = params;\n            const { editRender } = column;\n            const cell = (params.cell || $xeTable.getCellElement(row, column));\n            const beforeEditMethod = editOpts.beforeEditMethod || editOpts.activeMethod;\n            params.cell = cell;\n            if (cell && isEnableConf(editConfig) && isEnableConf(editRender)) {\n                // 激活编辑\n                if (!$xeTable.isPendingByRow(row) && !$xeTable.isAggregateRecord(row)) {\n                    if (actived.row !== row || (mode === 'cell' ? actived.column !== column : false)) {\n                        // 判断是否禁用编辑\n                        let type = 'edit-disabled';\n                        if (!beforeEditMethod || beforeEditMethod(Object.assign(Object.assign({}, params), { $table: $xeTable, $grid: $xeGrid }))) {\n                            if (mouseConfig) {\n                                $xeTable.clearSelected();\n                                if ($xeTable.clearCellAreas) {\n                                    $xeTable.clearCellAreas();\n                                    $xeTable.clearCopyCellArea();\n                                }\n                            }\n                            $xeTable.closeTooltip();\n                            if (actived.column) {\n                                handleClearEdit(evnt);\n                            }\n                            type = 'edit-activated';\n                            column.renderHeight = cell.offsetHeight;\n                            actived.args = params;\n                            actived.row = row;\n                            actived.column = column;\n                            if (mode === 'row') {\n                                tableColumn.forEach((column) => getEditColumnModel(row, column));\n                            }\n                            else {\n                                getEditColumnModel(row, column);\n                            }\n                            const afterEditMethod = editOpts.afterEditMethod;\n                            nextTick(() => {\n                                if (isFocus) {\n                                    $xeTable.handleFocus(params, evnt);\n                                }\n                                if (afterEditMethod) {\n                                    afterEditMethod(Object.assign(Object.assign({}, params), { $table: $xeTable, $grid: $xeGrid }));\n                                }\n                            });\n                        }\n                        $xeTable.dispatchEvent(type, {\n                            row,\n                            rowIndex: $xeTable.getRowIndex(row),\n                            $rowIndex: $xeTable.getVMRowIndex(row),\n                            column,\n                            columnIndex: $xeTable.getColumnIndex(column),\n                            $columnIndex: $xeTable.getVMColumnIndex(column)\n                        }, evnt);\n                        // v4已废弃\n                        if (type === 'edit-activated') {\n                            $xeTable.dispatchEvent('edit-actived', {\n                                row,\n                                rowIndex: $xeTable.getRowIndex(row),\n                                $rowIndex: $xeTable.getVMRowIndex(row),\n                                column,\n                                columnIndex: $xeTable.getColumnIndex(column),\n                                $columnIndex: $xeTable.getVMColumnIndex(column)\n                            }, evnt);\n                        }\n                    }\n                    else {\n                        const { column: oldColumn } = actived;\n                        if (mouseConfig) {\n                            $xeTable.clearSelected();\n                            if ($xeTable.clearCellAreas) {\n                                $xeTable.clearCellAreas();\n                                $xeTable.clearCopyCellArea();\n                            }\n                        }\n                        if (oldColumn !== column) {\n                            const { model: oldModel } = oldColumn;\n                            if (oldModel.update) {\n                                setCellValue(row, oldColumn, oldModel.value);\n                            }\n                            if ($xeTable.clearValidate) {\n                                $xeTable.clearValidate(row, column);\n                            }\n                        }\n                        column.renderHeight = cell.offsetHeight;\n                        actived.args = params;\n                        actived.column = column;\n                        if (isPos) {\n                            setTimeout(() => {\n                                $xeTable.handleFocus(params, evnt);\n                            });\n                        }\n                    }\n                    focused.column = null;\n                    focused.row = null;\n                    $xeTable.focus();\n                }\n            }\n            return nextTick();\n        };\n        const handleEditCell = (row, fieldOrColumn, isPos) => {\n            const { editConfig } = props;\n            const column = XEUtils.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;\n            if (row && column && isEnableConf(editConfig) && isEnableConf(column.editRender) && !$xeTable.isAggregateRecord(row)) {\n                return Promise.resolve(isPos ? $xeTable.scrollToRow(row, column) : null).then(() => {\n                    const cell = $xeTable.getCellElement(row, column);\n                    if (cell) {\n                        handleEditActive({\n                            row,\n                            rowIndex: $xeTable.getRowIndex(row),\n                            column,\n                            columnIndex: $xeTable.getColumnIndex(column),\n                            cell,\n                            $table: $xeTable\n                        }, null, isPos, isPos);\n                        internalData._lastCallTime = Date.now();\n                    }\n                    return nextTick();\n                });\n            }\n            return nextTick();\n        };\n        editMethods = {\n            /**\n             * 往表格中插入临时数据\n             *\n             * @param {*} records\n             */\n            insert(records) {\n                return handleInsertRowAt(records, null);\n            },\n            /**\n             * 往表格指定行中插入临时数据\n             * 如果 row 为空则从插入到顶部，如果为树结构，则插入到目标节点顶部\n             * 如果 row 为 -1 则从插入到底部，如果为树结构，则插入到目标节点底部\n             * 如果 row 为有效行则插入到该行的位置，如果为树结构，则有插入到效的目标节点该行的位置\n             * @param {Object/Array} records 新的数据\n             * @param {Row} targetRow 指定行\n             */\n            insertAt(records, targetRow) {\n                return handleInsertRowAt(records, targetRow);\n            },\n            insertNextAt(records, targetRow) {\n                return handleInsertRowAt(records, targetRow, true);\n            },\n            insertChild(records, parentRow) {\n                return handleInsertChildRowAt(records, parentRow, null);\n            },\n            insertChildAt(records, parentRow, targetRow) {\n                return handleInsertChildRowAt(records, parentRow, targetRow);\n            },\n            insertChildNextAt(records, parentRow, targetRow) {\n                return handleInsertChildRowAt(records, parentRow, targetRow, true);\n            },\n            /**\n             * 删除指定行数据\n             * 如果传 row 则删除一行\n             * 如果传 rows 则删除多行\n             * 如果为空则删除所有\n             */\n            remove(rows) {\n                const { treeConfig } = props;\n                const { editStore, isRowGroupStatus } = reactData;\n                const { tableFullTreeData, selectCheckboxMaps, afterFullData, mergeBodyList, tableFullData, pendingRowMaps, insertRowMaps, removeRowMaps } = internalData;\n                const checkboxOpts = computeCheckboxOpts.value;\n                const treeOpts = computeTreeOpts.value;\n                const { transform, mapChildrenField } = treeOpts;\n                const childrenField = treeOpts.children || treeOpts.childrenField;\n                const { actived } = editStore;\n                const { checkField } = checkboxOpts;\n                let delList = [];\n                if (!rows) {\n                    rows = tableFullData;\n                }\n                else if (!XEUtils.isArray(rows)) {\n                    rows = [rows];\n                }\n                // 如果是新增，则保存记录\n                rows.forEach((row) => {\n                    if (!$xeTable.isInsertByRow(row)) {\n                        const rowid = getRowid($xeTable, row);\n                        removeRowMaps[rowid] = row;\n                    }\n                });\n                // 如果绑定了多选属性，则更新状态\n                if (!checkField) {\n                    rows.forEach((row) => {\n                        const rowid = getRowid($xeTable, row);\n                        if (selectCheckboxMaps[rowid]) {\n                            delete selectCheckboxMaps[rowid];\n                        }\n                    });\n                    reactData.updateCheckboxFlag++;\n                }\n                // 从数据源中移除\n                if (tableFullData === rows) {\n                    rows = delList = tableFullData.slice(0);\n                    internalData.tableFullData = [];\n                    internalData.afterFullData = [];\n                    $xeTable.clearMergeCells();\n                }\n                else {\n                    // 如果为虚拟树\n                    if (treeConfig && transform) {\n                        rows.forEach((row) => {\n                            const rowid = getRowid($xeTable, row);\n                            const matchMapObj = XEUtils.findTree(tableFullTreeData, item => rowid === getRowid($xeTable, item), { children: mapChildrenField });\n                            if (matchMapObj) {\n                                const rItems = matchMapObj.items.splice(matchMapObj.index, 1);\n                                delList.push(rItems[0]);\n                            }\n                            const matchObj = XEUtils.findTree(tableFullTreeData, item => rowid === getRowid($xeTable, item), { children: childrenField });\n                            if (matchObj) {\n                                matchObj.items.splice(matchObj.index, 1);\n                            }\n                            const afIndex = $xeTable.findRowIndexOf(afterFullData, row);\n                            if (afIndex > -1) {\n                                afterFullData.splice(afIndex, 1);\n                            }\n                        });\n                    }\n                    else if (isRowGroupStatus) {\n                        // 如果分组\n                        warnLog(getI18n('vxe.error.noGroup', ['remove']));\n                    }\n                    else {\n                        rows.forEach((row) => {\n                            const tfIndex = $xeTable.findRowIndexOf(tableFullData, row);\n                            if (tfIndex > -1) {\n                                const rItems = tableFullData.splice(tfIndex, 1);\n                                delList.push(rItems[0]);\n                            }\n                            const afIndex = $xeTable.findRowIndexOf(afterFullData, row);\n                            if (afIndex > -1) {\n                                // 刷新单元格合并\n                                mergeBodyList.forEach((mergeItem) => {\n                                    const { row: mergeRowIndex, rowspan: mergeRowspan } = mergeItem;\n                                    if (mergeRowIndex > afIndex) {\n                                        mergeItem.row = mergeRowIndex - 1;\n                                    }\n                                    else if (mergeRowIndex + mergeRowspan > afIndex) {\n                                        mergeItem.rowspan = mergeRowspan - 1;\n                                    }\n                                });\n                                afterFullData.splice(afIndex, 1);\n                            }\n                        });\n                    }\n                }\n                // 如果当前行被激活编辑，则清除激活状态\n                if (actived.row && $xeTable.findRowIndexOf(rows, actived.row) > -1) {\n                    editMethods.clearEdit();\n                }\n                // 从新增中移除已删除的数据\n                rows.forEach((row) => {\n                    const rowid = getRowid($xeTable, row);\n                    if (insertRowMaps[rowid]) {\n                        delete insertRowMaps[rowid];\n                    }\n                    if (pendingRowMaps[rowid]) {\n                        delete pendingRowMaps[rowid];\n                    }\n                });\n                reactData.removeRowFlag++;\n                reactData.insertRowFlag++;\n                reactData.pendingRowFlag++;\n                $xeTable.cacheRowMap(false);\n                $xeTable.handleTableData(treeConfig && transform);\n                $xeTable.updateFooter();\n                $xeTable.handleUpdateBodyMerge();\n                if (!(treeConfig && transform)) {\n                    $xeTable.updateAfterDataIndex();\n                }\n                $xeTable.checkSelectionStatus();\n                if (reactData.scrollYLoad) {\n                    $xeTable.updateScrollYSpace();\n                }\n                return nextTick().then(() => {\n                    $xeTable.updateCellAreas();\n                    return $xeTable.recalculate(true);\n                }).then(() => {\n                    return { row: delList.length ? delList[delList.length - 1] : null, rows: delList };\n                });\n            },\n            /**\n             * 删除复选框选中的数据\n             */\n            removeCheckboxRow() {\n                return editMethods.remove($xeTable.getCheckboxRecords()).then((params) => {\n                    $xeTable.clearCheckboxRow();\n                    return params;\n                });\n            },\n            /**\n             * 删除单选框选中的数据\n             */\n            removeRadioRow() {\n                const radioRecord = $xeTable.getRadioRecord();\n                return editMethods.remove(radioRecord || []).then((params) => {\n                    $xeTable.clearRadioRow();\n                    return params;\n                });\n            },\n            /**\n             * 删除当前行选中的数据\n             */\n            removeCurrentRow() {\n                const currentRecord = $xeTable.getCurrentRecord();\n                return editMethods.remove(currentRecord || []).then((params) => {\n                    $xeTable.clearCurrentRow();\n                    return params;\n                });\n            },\n            /**\n             * 获取表格数据集，包含新增、删除、修改、标记\n             */\n            getRecordset() {\n                const removeRecords = editMethods.getRemoveRecords();\n                const pendingRecords = $xeTable.getPendingRecords();\n                const delRecords = removeRecords.concat(pendingRecords);\n                // 如果已经被删除，则无需放到更新数组\n                const updateRecords = editMethods.getUpdateRecords().filter(row => {\n                    return !delRecords.some(item => $xeTable.eqRow(item, row));\n                });\n                return {\n                    insertRecords: editMethods.getInsertRecords(),\n                    removeRecords,\n                    updateRecords,\n                    pendingRecords\n                };\n            },\n            /**\n             * 获取新增的临时数据\n             */\n            getInsertRecords() {\n                const { fullAllDataRowIdData, insertRowMaps } = internalData;\n                const insertRecords = [];\n                XEUtils.each(insertRowMaps, (row, rowid) => {\n                    if (fullAllDataRowIdData[rowid]) {\n                        insertRecords.push(row);\n                    }\n                });\n                return insertRecords;\n            },\n            /**\n             * 获取已删除的数据\n             */\n            getRemoveRecords() {\n                const { removeRowMaps } = internalData;\n                const removeRecords = [];\n                XEUtils.each(removeRowMaps, (row) => {\n                    removeRecords.push(row);\n                });\n                return removeRecords;\n            },\n            /**\n             * 获取更新数据\n             * 只精准匹配 row 的更改\n             * 如果是树表格，子节点更改状态不会影响父节点的更新状态\n             */\n            getUpdateRecords() {\n                const { keepSource, treeConfig } = props;\n                const { tableFullData } = internalData;\n                const treeOpts = computeTreeOpts.value;\n                if (keepSource) {\n                    syncActivedCell();\n                    if (treeConfig) {\n                        return XEUtils.filterTree(tableFullData, row => $xeTable.isUpdateByRow(row), treeOpts);\n                    }\n                    return tableFullData.filter((row) => $xeTable.isUpdateByRow(row));\n                }\n                return [];\n            },\n            getActiveRecord() {\n                warnLog('vxe.error.delFunc', ['getActiveRecord', 'getEditCell']);\n                const { editStore } = reactData;\n                const { fullAllDataRowIdData } = internalData;\n                const { args, row } = editStore.actived;\n                if (args && row && fullAllDataRowIdData[getRowid($xeTable, row)]) {\n                    return Object.assign({}, args, { row });\n                }\n                return null;\n            },\n            getEditRecord() {\n                warnLog('vxe.error.delFunc', ['getEditRecord', 'getEditCell']);\n                const { editStore } = reactData;\n                const { fullAllDataRowIdData } = internalData;\n                const { args, row } = editStore.actived;\n                if (args && row && fullAllDataRowIdData[getRowid($xeTable, row)]) {\n                    return Object.assign({}, args, { row });\n                }\n                return null;\n            },\n            getEditCell() {\n                const { editStore } = reactData;\n                const { row, column } = editStore.actived;\n                if (column && row) {\n                    return { row, column };\n                }\n                return null;\n            },\n            /**\n             * 获取选中的单元格\n             */\n            getSelectedCell() {\n                const { editStore } = reactData;\n                const { row, column } = editStore.selected;\n                if (row && column) {\n                    return { row, column };\n                }\n                return null;\n            },\n            clearActived(row) {\n                // 即将废弃\n                warnLog('vxe.error.delFunc', ['clearActived', 'clearEdit']);\n                return $xeTable.clearEdit(row);\n            },\n            /**\n             * 清除激活的编辑\n             */\n            clearEdit(row) {\n                return handleClearEdit(null, row);\n            },\n            /**\n             * 清除所选中源状态\n             */\n            clearSelected() {\n                const { editStore } = reactData;\n                const { selected } = editStore;\n                selected.row = null;\n                selected.column = null;\n                removeCellSelectedClass();\n                return nextTick();\n            },\n            isActiveByRow(row) {\n                warnLog('vxe.error.delFunc', ['isActiveByRow', 'isEditByRow']);\n                // 即将废弃\n                return $xeTable.isEditByRow(row);\n            },\n            /**\n             * 判断行是否为激活编辑状态\n             * @param {Row} row 行对象\n             */\n            isEditByRow(row) {\n                const { editStore } = reactData;\n                return editStore.actived.row === row;\n            },\n            setActiveRow(row) {\n                warnLog('vxe.error.delFunc', ['setActiveRow', 'setEditRow']);\n                // 即将废弃\n                return editMethods.setEditRow(row);\n            },\n            /**\n             * 激活行编辑\n             */\n            setEditRow(row, fieldOrColumn) {\n                const { visibleColumn } = internalData;\n                let column = XEUtils.find(visibleColumn, column => isEnableConf(column.editRender));\n                let isPos = false;\n                if (fieldOrColumn) {\n                    isPos = true;\n                    if (fieldOrColumn !== true) {\n                        column = XEUtils.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;\n                    }\n                }\n                return handleEditCell(row, column, isPos);\n            },\n            setActiveCell(row, fieldOrColumn) {\n                warnLog('vxe.error.delFunc', ['setActiveCell', 'setEditCell']);\n                // 即将废弃\n                return editMethods.setEditCell(row, fieldOrColumn);\n            },\n            /**\n             * 激活单元格编辑\n             */\n            setEditCell(row, fieldOrColumn) {\n                return handleEditCell(row, fieldOrColumn, true);\n            },\n            /**\n             * 只对 trigger=dblclick 有效，选中单元格\n             */\n            setSelectCell(row, fieldOrColumn) {\n                const { tableData } = reactData;\n                const editOpts = computeEditOpts.value;\n                const column = XEUtils.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;\n                if (row && column && editOpts.trigger !== 'manual') {\n                    const rowIndex = $xeTable.findRowIndexOf(tableData, row);\n                    if (rowIndex > -1 && column) {\n                        const cell = $xeTable.getCellElement(row, column);\n                        const params = {\n                            row,\n                            rowIndex,\n                            column,\n                            columnIndex: $xeTable.getColumnIndex(column),\n                            cell\n                        };\n                        $xeTable.handleSelected(params, {});\n                    }\n                }\n                return nextTick();\n            }\n        };\n        editPrivateMethods = {\n            /**\n             * 处理激活编辑\n             */\n            handleEdit(params, evnt) {\n                return handleEditActive(params, evnt, true, true);\n            },\n            /**\n             * @deprecated\n             */\n            handleActived(params, evnt) {\n                return editPrivateMethods.handleEdit(params, evnt);\n            },\n            /**\n             * 处理取消编辑\n             * @param evnt\n             * @returns\n             */\n            handleClearEdit,\n            /**\n             * 处理聚焦\n             */\n            handleFocus(params) {\n                const { row, column, cell } = params;\n                const { editRender } = column;\n                const editOpts = computeEditOpts.value;\n                if (isEnableConf(editRender)) {\n                    const compRender = renderer.get(editRender.name);\n                    let autoFocus = editRender.autofocus || editRender.autoFocus;\n                    let autoSelect = editRender.autoSelect || editRender.autoselect;\n                    let inputElem;\n                    // 是否启用聚焦\n                    if (editOpts.autoFocus) {\n                        if (!autoFocus && compRender) {\n                            autoFocus = compRender.tableAutoFocus || compRender.tableAutofocus || compRender.autofocus;\n                        }\n                        if (!autoSelect && compRender) {\n                            autoSelect = compRender.tableAutoSelect || compRender.autoselect;\n                        }\n                        // 如果指定了聚焦 class\n                        if (XEUtils.isFunction(autoFocus)) {\n                            inputElem = autoFocus(params);\n                        }\n                        else if (autoFocus) {\n                            if (autoFocus === true) {\n                                // 自动匹配模式，会自动匹配第一个可输入元素\n                                inputElem = cell.querySelector('input,textarea');\n                            }\n                            else {\n                                inputElem = cell.querySelector(autoFocus);\n                            }\n                            if (inputElem) {\n                                inputElem.focus();\n                            }\n                        }\n                    }\n                    if (inputElem) {\n                        if (autoSelect) {\n                            inputElem.select();\n                        }\n                        else {\n                            // 保持一致行为，光标移到末端\n                            if (browseObj.msie) {\n                                const textRange = inputElem.createTextRange();\n                                textRange.collapse(false);\n                                textRange.select();\n                            }\n                        }\n                    }\n                    else {\n                        // 是否自动定位\n                        if (editOpts.autoPos) {\n                            if (!column.fixed) {\n                                // 显示到可视区中\n                                $xeTable.scrollToRow(row, column);\n                            }\n                        }\n                    }\n                }\n            },\n            /**\n             * 处理选中源\n             */\n            handleSelected(params, evnt) {\n                const { mouseConfig } = props;\n                const { editStore } = reactData;\n                const mouseOpts = computeMouseOpts.value;\n                const editOpts = computeEditOpts.value;\n                const { actived, selected } = editStore;\n                const { row, column } = params;\n                const isMouseSelected = mouseConfig && mouseOpts.selected;\n                const selectMethod = () => {\n                    if (isMouseSelected && (selected.row !== row || selected.column !== column)) {\n                        if (actived.row !== row || (editOpts.mode === 'cell' ? actived.column !== column : false)) {\n                            handleClearEdit(evnt);\n                            $xeTable.clearSelected();\n                            if ($xeTable.clearCellAreas) {\n                                $xeTable.clearCellAreas();\n                                $xeTable.clearCopyCellArea();\n                            }\n                            selected.args = params;\n                            selected.row = row;\n                            selected.column = column;\n                            if (isMouseSelected) {\n                                editPrivateMethods.addCellSelectedClass();\n                            }\n                            $xeTable.focus();\n                            if (evnt) {\n                                $xeTable.dispatchEvent('cell-selected', params, evnt);\n                            }\n                        }\n                    }\n                    return nextTick();\n                };\n                return selectMethod();\n            },\n            addCellSelectedClass() {\n                const { editStore } = reactData;\n                const { selected } = editStore;\n                const { row, column } = selected;\n                removeCellSelectedClass();\n                if (row && column) {\n                    const cell = $xeTable.getCellElement(row, column);\n                    if (cell) {\n                        addClass(cell, 'col--selected');\n                    }\n                }\n            }\n        };\n        return Object.assign(Object.assign({}, editMethods), editPrivateMethods);\n    },\n    setupGrid($xeGrid) {\n        return $xeGrid.extendTableMethods(tableEditMethodKeys);\n    }\n});\n", "import { inject, nextTick } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../../ui';\nimport { isColumnInfo, getCellValue, createHandleGetRowId } from '../../src/util';\nimport { parseFile, formatText, eqEmptyValue } from '../../../ui/src/utils';\nimport { hasClass } from '../../../ui/src/dom';\nimport { createHtmlPage, getExportBlobByContent } from './util';\nimport { warnLog, errLog } from '../../../ui/src/log';\nconst { getI18n, hooks, renderer } = VxeUI;\nlet htmlCellElem;\nconst csvBOM = '\\ufeff';\nconst enterSymbol = '\\r\\n';\nfunction defaultFilterExportColumn(column) {\n    return !!column.field || ['seq', 'checkbox', 'radio'].indexOf(column.type || '') === -1;\n}\nconst getConvertColumns = (columns) => {\n    const result = [];\n    columns.forEach((column) => {\n        if (column.childNodes && column.childNodes.length) {\n            result.push(column);\n            result.push(...getConvertColumns(column.childNodes));\n        }\n        else {\n            result.push(column);\n        }\n    });\n    return result;\n};\nconst convertToRows = (originColumns) => {\n    let maxLevel = 1;\n    const traverse = (column, parent) => {\n        if (parent) {\n            column._level = parent._level + 1;\n            if (maxLevel < column._level) {\n                maxLevel = column._level;\n            }\n        }\n        if (column.childNodes && column.childNodes.length) {\n            let colSpan = 0;\n            column.childNodes.forEach((subColumn) => {\n                traverse(subColumn, column);\n                colSpan += subColumn._colSpan;\n            });\n            column._colSpan = colSpan;\n        }\n        else {\n            column._colSpan = 1;\n        }\n    };\n    originColumns.forEach((column) => {\n        column._level = 1;\n        traverse(column);\n    });\n    const rows = [];\n    for (let i = 0; i < maxLevel; i++) {\n        rows.push([]);\n    }\n    const allColumns = getConvertColumns(originColumns);\n    allColumns.forEach((column) => {\n        if (column.childNodes && column.childNodes.length) {\n            column._rowSpan = 1;\n        }\n        else {\n            column._rowSpan = maxLevel - column._level + 1;\n        }\n        rows[column._level - 1].push(column);\n    });\n    return rows;\n};\nfunction toTableBorder(border) {\n    if (border === true) {\n        return 'full';\n    }\n    if (border) {\n        return border;\n    }\n    return 'default';\n}\nfunction getBooleanValue(cellValue) {\n    return cellValue === 'TRUE' || cellValue === 'true' || cellValue === true;\n}\nfunction getFooterData($xeTable, opts, footerTableData) {\n    const { footerFilterMethod } = opts;\n    return footerFilterMethod ? footerTableData.filter((items, index) => footerFilterMethod({ $table: $xeTable, items, $rowIndex: index })) : footerTableData;\n}\nfunction getCsvCellTypeLabel(column, cellValue) {\n    if (cellValue) {\n        if (column.type === 'seq') {\n            return `\\t${cellValue}`;\n        }\n        switch (column.cellType) {\n            case 'string':\n                if (!isNaN(cellValue)) {\n                    return `\\t${cellValue}`;\n                }\n                break;\n            case 'number':\n                break;\n            default:\n                if (cellValue.length >= 12 && !isNaN(cellValue)) {\n                    return `\\t${cellValue}`;\n                }\n                break;\n        }\n    }\n    return cellValue;\n}\nfunction toTxtCellLabel(val) {\n    if (/[\",\\s\\n]/.test(val)) {\n        return `\"${val.replace(/\"/g, '\"\"')}\"`;\n    }\n    return val;\n}\nfunction getElementsByTagName(elem, qualifiedName) {\n    return elem.getElementsByTagName(qualifiedName);\n}\nfunction getTxtCellKey(now) {\n    return `#${now}@${XEUtils.uniqueId()}`;\n}\nfunction replaceTxtCell(cell, vMaps) {\n    return cell.replace(/#\\d+@\\d+/g, (key) => XEUtils.hasOwnProp(vMaps, key) ? vMaps[key] : key);\n}\nfunction getTxtCellValue(val, vMaps) {\n    const rest = replaceTxtCell(val, vMaps);\n    return rest.replace(/^\"+$/g, (qVal) => '\"'.repeat(Math.ceil(qVal.length / 2)));\n}\nfunction toExportField(tableConf, field) {\n    const { fieldMaps, titleMaps } = tableConf;\n    // title 转 field\n    if (!fieldMaps[field]) {\n        const teCol = titleMaps[field];\n        if (teCol && teCol.field) {\n            field = teCol.field;\n        }\n    }\n    return field;\n}\nfunction parseCsvAndTxt(tableConf, content, cellSeparator) {\n    const list = content.split(enterSymbol);\n    const rows = [];\n    let fields = [];\n    if (list.length) {\n        const vMaps = {};\n        const now = Date.now();\n        list.forEach((rVal) => {\n            if (rVal) {\n                const item = {};\n                rVal = rVal.replace(/(\"\")|(\\n)/g, (text, dVal) => {\n                    const key = getTxtCellKey(now);\n                    vMaps[key] = dVal ? '\"' : '\\n';\n                    return key;\n                }).replace(/\"(.*?)\"/g, (text, cVal) => {\n                    const key = getTxtCellKey(now);\n                    vMaps[key] = replaceTxtCell(cVal, vMaps);\n                    return key;\n                });\n                const cells = rVal.split(cellSeparator);\n                if (!fields.length) {\n                    fields = cells.map((val) => toExportField(tableConf, getTxtCellValue(val.trim(), vMaps)));\n                }\n                else {\n                    cells.forEach((val, colIndex) => {\n                        if (colIndex < fields.length) {\n                            item[fields[colIndex]] = getTxtCellValue(val.trim(), vMaps);\n                        }\n                    });\n                    rows.push(item);\n                }\n            }\n        });\n    }\n    return { fields, rows };\n}\nfunction parseCsv(tableConf, content) {\n    return parseCsvAndTxt(tableConf, content, ',');\n}\nfunction parseTxt(tableConf, content) {\n    return parseCsvAndTxt(tableConf, content, '\\t');\n}\nfunction parseHTML(tableConf, content) {\n    const domParser = new DOMParser();\n    const xmlDoc = domParser.parseFromString(content, 'text/html');\n    const bodyNodes = getElementsByTagName(xmlDoc, 'body');\n    const rows = [];\n    const fields = [];\n    if (bodyNodes.length) {\n        const tableNodes = getElementsByTagName(bodyNodes[0], 'table');\n        if (tableNodes.length) {\n            const theadNodes = getElementsByTagName(tableNodes[0], 'thead');\n            if (theadNodes.length) {\n                XEUtils.arrayEach(getElementsByTagName(theadNodes[0], 'tr'), rowNode => {\n                    XEUtils.arrayEach(getElementsByTagName(rowNode, 'th'), cellNode => {\n                        fields.push(toExportField(tableConf, cellNode.textContent || ''));\n                    });\n                });\n                const tbodyNodes = getElementsByTagName(tableNodes[0], 'tbody');\n                if (tbodyNodes.length) {\n                    XEUtils.arrayEach(getElementsByTagName(tbodyNodes[0], 'tr'), rowNode => {\n                        const item = {};\n                        XEUtils.arrayEach(getElementsByTagName(rowNode, 'td'), (cellNode, colIndex) => {\n                            if (fields[colIndex]) {\n                                item[fields[colIndex]] = cellNode.textContent || '';\n                            }\n                        });\n                        rows.push(item);\n                    });\n                }\n            }\n        }\n    }\n    return { fields, rows };\n}\nfunction parseXML(tableConf, content) {\n    const domParser = new DOMParser();\n    const xmlDoc = domParser.parseFromString(content, 'application/xml');\n    const sheetNodes = getElementsByTagName(xmlDoc, 'Worksheet');\n    const rows = [];\n    const fields = [];\n    if (sheetNodes.length) {\n        const tableNodes = getElementsByTagName(sheetNodes[0], 'Table');\n        if (tableNodes.length) {\n            const rowNodes = getElementsByTagName(tableNodes[0], 'Row');\n            if (rowNodes.length) {\n                XEUtils.arrayEach(getElementsByTagName(rowNodes[0], 'Cell'), cellNode => {\n                    fields.push(toExportField(tableConf, cellNode.textContent || ''));\n                });\n                XEUtils.arrayEach(rowNodes, (rowNode, index) => {\n                    if (index) {\n                        const item = {};\n                        const cellNodes = getElementsByTagName(rowNode, 'Cell');\n                        XEUtils.arrayEach(cellNodes, (cellNode, colIndex) => {\n                            if (fields[colIndex]) {\n                                item[fields[colIndex]] = cellNode.textContent;\n                            }\n                        });\n                        rows.push(item);\n                    }\n                });\n            }\n        }\n    }\n    return { fields, rows };\n}\nfunction clearColumnConvert(columns) {\n    XEUtils.eachTree(columns, (column) => {\n        delete column._level;\n        delete column._colSpan;\n        delete column._rowSpan;\n        delete column._children;\n        delete column.childNodes;\n    }, { children: 'children' });\n}\nconst tableExportMethodKeys = ['exportData', 'importByFile', 'importData', 'saveFile', 'readFile', 'print', 'getPrintHtml', 'openImport', 'closeImport', 'openExport', 'closeExport', 'openPrint', 'closePrint'];\nhooks.add('tableExportModule', {\n    setupTable($xeTable) {\n        const { props, reactData, internalData } = $xeTable;\n        const { computeTreeOpts, computePrintOpts, computeExportOpts, computeImportOpts, computeCustomOpts, computeSeqOpts, computeRadioOpts, computeCheckboxOpts, computeColumnOpts } = $xeTable.getComputeMaps();\n        const $xeGrid = inject('$xeGrid', null);\n        const hasTreeChildren = (row) => {\n            const treeOpts = computeTreeOpts.value;\n            const childrenField = treeOpts.children || treeOpts.childrenField;\n            return row[childrenField] && row[childrenField].length;\n        };\n        const getSeq = (cellValue, row, $rowIndex, column, $columnIndex) => {\n            const seqOpts = computeSeqOpts.value;\n            const seqMethod = seqOpts.seqMethod || column.seqMethod;\n            if (seqMethod) {\n                return seqMethod({\n                    $table: $xeTable,\n                    row,\n                    rowIndex: $xeTable.getRowIndex(row),\n                    $rowIndex,\n                    column,\n                    columnIndex: $xeTable.getColumnIndex(column),\n                    $columnIndex\n                });\n            }\n            return cellValue;\n        };\n        function getHeaderTitle(opts, column) {\n            const columnOpts = computeColumnOpts.value;\n            const headExportMethod = column.headerExportMethod || columnOpts.headerExportMethod;\n            return headExportMethod ? headExportMethod({ column, options: opts, $table: $xeTable }) : ((opts.isTitle ? column.getTitle() : column.field) || '');\n        }\n        const toBooleanValue = (cellValue) => {\n            return XEUtils.isBoolean(cellValue) ? (cellValue ? 'TRUE' : 'FALSE') : cellValue;\n        };\n        const toStringValue = (cellValue) => {\n            return eqEmptyValue(cellValue) ? '' : `${cellValue}`;\n        };\n        const getBodyLabelData = (opts, columns, datas) => {\n            const { isAllExpand, mode } = opts;\n            const { treeConfig } = props;\n            const radioOpts = computeRadioOpts.value;\n            const checkboxOpts = computeCheckboxOpts.value;\n            const treeOpts = computeTreeOpts.value;\n            const columnOpts = computeColumnOpts.value;\n            if (!htmlCellElem) {\n                htmlCellElem = document.createElement('div');\n            }\n            if (treeConfig) {\n                const childrenField = treeOpts.children || treeOpts.childrenField;\n                // 如果是树表格只允许导出数据源\n                const rest = [];\n                const expandMaps = {};\n                const useMaps = {};\n                const { handleGetRowId } = createHandleGetRowId($xeTable);\n                XEUtils.eachTree(datas, (item, $rowIndex, items, path, parent, nodes) => {\n                    const row = item._row || item;\n                    const rowid = handleGetRowId(row);\n                    if (useMaps[rowid]) {\n                        return;\n                    }\n                    const parentRow = parent && parent._row ? parent._row : parent;\n                    const pRowid = parentRow ? handleGetRowId(parentRow) : '';\n                    if ((isAllExpand || !parentRow || (expandMaps[pRowid] && $xeTable.isTreeExpandByRow(parentRow)))) {\n                        const hasRowChild = hasTreeChildren(row);\n                        const item = {\n                            _row: row,\n                            _level: nodes.length - 1,\n                            _hasChild: hasRowChild,\n                            _expand: hasRowChild && $xeTable.isTreeExpandByRow(row)\n                        };\n                        columns.forEach((column, $columnIndex) => {\n                            let cellValue = '';\n                            const renderOpts = column.editRender || column.cellRender;\n                            let bodyExportMethod = column.exportMethod || columnOpts.exportMethod;\n                            if (!bodyExportMethod && renderOpts && renderOpts.name) {\n                                const compConf = renderer.get(renderOpts.name);\n                                if (compConf) {\n                                    bodyExportMethod = compConf.tableExportMethod || compConf.exportMethod;\n                                }\n                            }\n                            if (!bodyExportMethod) {\n                                bodyExportMethod = columnOpts.exportMethod;\n                            }\n                            if (bodyExportMethod) {\n                                cellValue = bodyExportMethod({ $table: $xeTable, row, column, options: opts });\n                            }\n                            else {\n                                switch (column.type) {\n                                    case 'seq': {\n                                        const seqVal = path.map((num, i) => i % 2 === 0 ? (Number(num) + 1) : '.').join('');\n                                        cellValue = mode === 'all' ? seqVal : getSeq(seqVal, row, $rowIndex, column, $columnIndex);\n                                        break;\n                                    }\n                                    case 'checkbox':\n                                        cellValue = toBooleanValue($xeTable.isCheckedByCheckboxRow(row));\n                                        item._checkboxLabel = checkboxOpts.labelField ? XEUtils.get(row, checkboxOpts.labelField) : '';\n                                        item._checkboxDisabled = checkboxOpts.checkMethod && !checkboxOpts.checkMethod({ $table: $xeTable, row });\n                                        break;\n                                    case 'radio':\n                                        cellValue = toBooleanValue($xeTable.isCheckedByRadioRow(row));\n                                        item._radioLabel = radioOpts.labelField ? XEUtils.get(row, radioOpts.labelField) : '';\n                                        item._radioDisabled = radioOpts.checkMethod && !radioOpts.checkMethod({ $table: $xeTable, row });\n                                        break;\n                                    default:\n                                        if (opts.original) {\n                                            cellValue = getCellValue(row, column);\n                                        }\n                                        else {\n                                            cellValue = $xeTable.getCellLabel(row, column);\n                                            if (column.type === 'html') {\n                                                htmlCellElem.innerHTML = cellValue;\n                                                cellValue = htmlCellElem.innerText.trim();\n                                            }\n                                            else {\n                                                const cell = $xeTable.getCellElement(row, column);\n                                                if (cell && !hasClass(cell, 'is--progress')) {\n                                                    cellValue = cell.innerText.trim();\n                                                }\n                                            }\n                                        }\n                                }\n                            }\n                            item[column.id] = toStringValue(cellValue);\n                        });\n                        useMaps[rowid] = true;\n                        if (pRowid) {\n                            expandMaps[pRowid] = true;\n                        }\n                        rest.push(Object.assign(item, row));\n                    }\n                }, { children: childrenField });\n                return rest;\n            }\n            return datas.map((row, $rowIndex) => {\n                const item = {\n                    _row: row\n                };\n                columns.forEach((column, $columnIndex) => {\n                    let cellValue = '';\n                    const renderOpts = column.editRender || column.cellRender;\n                    let bodyExportMethod = column.exportMethod || columnOpts.exportMethod;\n                    if (!bodyExportMethod && renderOpts && renderOpts.name) {\n                        const compConf = renderer.get(renderOpts.name);\n                        if (compConf) {\n                            bodyExportMethod = compConf.tableExportMethod || compConf.exportMethod;\n                        }\n                    }\n                    if (bodyExportMethod) {\n                        cellValue = bodyExportMethod({ $table: $xeTable, row, column, options: opts });\n                    }\n                    else {\n                        switch (column.type) {\n                            case 'seq': {\n                                const seqValue = $rowIndex + 1;\n                                cellValue = mode === 'all' ? seqValue : getSeq(seqValue, row, $rowIndex, column, $columnIndex);\n                                break;\n                            }\n                            case 'checkbox':\n                                cellValue = toBooleanValue($xeTable.isCheckedByCheckboxRow(row));\n                                item._checkboxLabel = checkboxOpts.labelField ? XEUtils.get(row, checkboxOpts.labelField) : '';\n                                item._checkboxDisabled = checkboxOpts.checkMethod && !checkboxOpts.checkMethod({ $table: $xeTable, row });\n                                break;\n                            case 'radio':\n                                cellValue = toBooleanValue($xeTable.isCheckedByRadioRow(row));\n                                item._radioLabel = radioOpts.labelField ? XEUtils.get(row, radioOpts.labelField) : '';\n                                item._radioDisabled = radioOpts.checkMethod && !radioOpts.checkMethod({ $table: $xeTable, row });\n                                break;\n                            default:\n                                if (opts.original) {\n                                    cellValue = getCellValue(row, column);\n                                }\n                                else {\n                                    cellValue = $xeTable.getCellLabel(row, column);\n                                    if (column.type === 'html') {\n                                        htmlCellElem.innerHTML = cellValue;\n                                        cellValue = htmlCellElem.innerText.trim();\n                                    }\n                                    else {\n                                        const cell = $xeTable.getCellElement(row, column);\n                                        if (cell && !hasClass(cell, 'is--progress')) {\n                                            cellValue = cell.innerText.trim();\n                                        }\n                                    }\n                                }\n                        }\n                    }\n                    item[column.id] = toStringValue(cellValue);\n                });\n                return item;\n            });\n        };\n        const getExportData = (opts) => {\n            const { columns, dataFilterMethod } = opts;\n            let datas = opts.data;\n            if (dataFilterMethod) {\n                datas = datas.filter((row, index) => dataFilterMethod({ $table: $xeTable, row, $rowIndex: index }));\n            }\n            return getBodyLabelData(opts, columns, datas);\n        };\n        const getFooterCellValue = (opts, row, column) => {\n            const columnOpts = computeColumnOpts.value;\n            const renderOpts = column.editRender || column.cellRender;\n            let footLabelMethod = column.footerExportMethod;\n            if (!footLabelMethod && renderOpts && renderOpts.name) {\n                const compConf = renderer.get(renderOpts.name);\n                if (compConf) {\n                    footLabelMethod = compConf.tableFooterExportMethod || compConf.footerExportMethod;\n                }\n            }\n            if (!footLabelMethod) {\n                footLabelMethod = columnOpts.footerExportMethod;\n            }\n            const _columnIndex = $xeTable.getVTColumnIndex(column);\n            if (footLabelMethod) {\n                return footLabelMethod({ $table: $xeTable, items: row, itemIndex: _columnIndex, row, _columnIndex, column, options: opts });\n            }\n            // 兼容老模式\n            if (XEUtils.isArray(row)) {\n                return XEUtils.toValueString(row[_columnIndex]);\n            }\n            return XEUtils.get(row, column.field);\n        };\n        const toCsv = ($xeTable, opts, columns, datas) => {\n            let content = csvBOM;\n            if (opts.isHeader) {\n                content += columns.map((column) => toTxtCellLabel(getHeaderTitle(opts, column))).join(',') + enterSymbol;\n            }\n            datas.forEach((row) => {\n                content += columns.map((column) => toTxtCellLabel(getCsvCellTypeLabel(column, row[column.id]))).join(',') + enterSymbol;\n            });\n            if (opts.isFooter) {\n                const { footerTableData } = reactData;\n                const footers = getFooterData($xeTable, opts, footerTableData);\n                footers.forEach((row) => {\n                    content += columns.map((column) => toTxtCellLabel(getFooterCellValue(opts, row, column))).join(',') + enterSymbol;\n                });\n            }\n            return content;\n        };\n        const toTxt = ($xeTable, opts, columns, datas) => {\n            let content = '';\n            if (opts.isHeader) {\n                content += columns.map((column) => toTxtCellLabel(getHeaderTitle(opts, column))).join('\\t') + enterSymbol;\n            }\n            datas.forEach((row) => {\n                content += columns.map((column) => toTxtCellLabel(row[column.id])).join('\\t') + enterSymbol;\n            });\n            if (opts.isFooter) {\n                const { footerTableData } = reactData;\n                const footers = getFooterData($xeTable, opts, footerTableData);\n                footers.forEach((row) => {\n                    content += columns.map((column) => toTxtCellLabel(getFooterCellValue(opts, row, column))).join('\\t') + enterSymbol;\n                });\n            }\n            return content;\n        };\n        const hasEllipsis = (column, property, allColumnOverflow) => {\n            const columnOverflow = column[property];\n            const headOverflow = XEUtils.isUndefined(columnOverflow) || XEUtils.isNull(columnOverflow) ? allColumnOverflow : columnOverflow;\n            const showEllipsis = headOverflow === 'ellipsis';\n            const showTitle = headOverflow === 'title';\n            const showTooltip = headOverflow === true || headOverflow === 'tooltip';\n            let isEllipsis = showTitle || showTooltip || showEllipsis;\n            // 虚拟滚动不支持动态高度\n            const { scrollXLoad, scrollYLoad } = reactData;\n            if ((scrollXLoad || scrollYLoad) && !isEllipsis) {\n                isEllipsis = true;\n            }\n            return isEllipsis;\n        };\n        const toHtml = (opts, columns, datas) => {\n            const { id, border, treeConfig, headerAlign: allHeaderAlign, align: allAlign, footerAlign: allFooterAlign, showOverflow: allColumnOverflow, showHeaderOverflow: allColumnHeaderOverflow } = props;\n            const { isAllSelected, isIndeterminate } = reactData;\n            const { mergeBodyCellMaps } = internalData;\n            const treeOpts = computeTreeOpts.value;\n            const { print: isPrint, isHeader, isFooter, isColgroup, isMerge, colgroups, original } = opts;\n            const allCls = 'check-all';\n            const clss = [\n                'vxe-table',\n                `border--${toTableBorder(border)}`,\n                isPrint ? 'is--print' : '',\n                isHeader ? 'is--header' : ''\n            ].filter(cls => cls);\n            const tables = [\n                `<table class=\"${clss.join(' ')}\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">`,\n                `<colgroup>${columns.map((column) => `<col style=\"width:${column.renderWidth}px\">`).join('')}</colgroup>`\n            ];\n            if (isHeader) {\n                tables.push('<thead>');\n                if (isColgroup && !original) {\n                    colgroups.forEach((cols) => {\n                        tables.push(`<tr>${cols.map((column) => {\n                            const headAlign = column.headerAlign || column.align || allHeaderAlign || allAlign;\n                            const classNames = hasEllipsis(column, 'showHeaderOverflow', allColumnHeaderOverflow) ? ['col--ellipsis'] : [];\n                            const cellTitle = getHeaderTitle(opts, column);\n                            let childWidth = 0;\n                            let countChild = 0;\n                            XEUtils.eachTree([column], item => {\n                                if (!item.childNodes || !column.childNodes.length) {\n                                    countChild++;\n                                }\n                                childWidth += item.renderWidth;\n                            }, { children: 'childNodes' });\n                            const cellWidth = childWidth - countChild;\n                            if (headAlign) {\n                                classNames.push(`col--${headAlign}`);\n                            }\n                            if (column.type === 'checkbox') {\n                                return `<th class=\"${classNames.join(' ')}\" colspan=\"${column._colSpan}\" rowspan=\"${column._rowSpan}\"><div ${isPrint ? '' : `style=\"width: ${cellWidth}px\"`}><input type=\"checkbox\" class=\"${allCls}\" ${isAllSelected ? 'checked' : ''}><span>${cellTitle}</span></div></th>`;\n                            }\n                            return `<th class=\"${classNames.join(' ')}\" colspan=\"${column._colSpan}\" rowspan=\"${column._rowSpan}\" title=\"${cellTitle}\"><div ${isPrint ? '' : `style=\"width: ${cellWidth}px\"`}><span>${formatText(cellTitle, true)}</span></div></th>`;\n                        }).join('')}</tr>`);\n                    });\n                }\n                else {\n                    tables.push(`<tr>${columns.map((column) => {\n                        const headAlign = column.headerAlign || column.align || allHeaderAlign || allAlign;\n                        const classNames = hasEllipsis(column, 'showHeaderOverflow', allColumnHeaderOverflow) ? ['col--ellipsis'] : [];\n                        const cellTitle = getHeaderTitle(opts, column);\n                        if (headAlign) {\n                            classNames.push(`col--${headAlign}`);\n                        }\n                        if (column.type === 'checkbox') {\n                            return `<th class=\"${classNames.join(' ')}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><input type=\"checkbox\" class=\"${allCls}\" ${isAllSelected ? 'checked' : ''}><span>${cellTitle}</span></div></th>`;\n                        }\n                        return `<th class=\"${classNames.join(' ')}\" title=\"${cellTitle}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><span>${formatText(cellTitle, true)}</span></div></th>`;\n                    }).join('')}</tr>`);\n                }\n                tables.push('</thead>');\n            }\n            if (datas.length) {\n                tables.push('<tbody>');\n                if (treeConfig) {\n                    datas.forEach((item) => {\n                        tables.push('<tr>' + columns.map((column) => {\n                            const colid = column.id;\n                            const cellAlign = column.align || allAlign;\n                            const classNames = hasEllipsis(column, 'showOverflow', allColumnOverflow) ? ['col--ellipsis'] : [];\n                            const cellValue = item[colid];\n                            if (cellAlign) {\n                                classNames.push(`col--${cellAlign}`);\n                            }\n                            if (column.treeNode) {\n                                let treeIcon = '';\n                                if (item._hasChild) {\n                                    treeIcon = `<i class=\"${item._expand ? 'vxe-table--tree-fold-icon' : 'vxe-table--tree-unfold-icon'}\"></i>`;\n                                }\n                                classNames.push('vxe-table--tree-node');\n                                if (column.type === 'radio') {\n                                    return `<td class=\"${classNames.join(' ')}\" title=\"${cellValue}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><div class=\"vxe-table--tree-node-wrapper\" style=\"padding-left: ${item._level * treeOpts.indent}px\"><div class=\"vxe-table--tree-icon-wrapper\">${treeIcon}</div><div class=\"vxe-table--tree-cell\"><input type=\"radio\" name=\"radio_${id}\" ${item._radioDisabled ? 'disabled ' : ''}${getBooleanValue(cellValue) ? 'checked' : ''}><span>${item._radioLabel}</span></div></div></div></td>`;\n                                }\n                                else if (column.type === 'checkbox') {\n                                    return `<td class=\"${classNames.join(' ')}\" title=\"${cellValue}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><div class=\"vxe-table--tree-node-wrapper\" style=\"padding-left: ${item._level * treeOpts.indent}px\"><div class=\"vxe-table--tree-icon-wrapper\">${treeIcon}</div><div class=\"vxe-table--tree-cell\"><input type=\"checkbox\" ${item._checkboxDisabled ? 'disabled ' : ''}${getBooleanValue(cellValue) ? 'checked' : ''}><span>${item._checkboxLabel}</span></div></div></div></td>`;\n                                }\n                                return `<td class=\"${classNames.join(' ')}\" title=\"${cellValue}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><div class=\"vxe-table--tree-node-wrapper\" style=\"padding-left: ${item._level * treeOpts.indent}px\"><div class=\"vxe-table--tree-icon-wrapper\">${treeIcon}</div><div class=\"vxe-table--tree-cell\">${cellValue}</div></div></div></td>`;\n                            }\n                            if (column.type === 'radio') {\n                                return `<td class=\"${classNames.join(' ')}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><input type=\"radio\" name=\"radio_${id}\" ${item._radioDisabled ? 'disabled ' : ''}${getBooleanValue(cellValue) ? 'checked' : ''}><span>${item._radioLabel}</span></div></td>`;\n                            }\n                            else if (column.type === 'checkbox') {\n                                return `<td class=\"${classNames.join(' ')}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><input type=\"checkbox\" ${item._checkboxDisabled ? 'disabled ' : ''}${getBooleanValue(cellValue) ? 'checked' : ''}><span>${item._checkboxLabel}</span></div></td>`;\n                            }\n                            return `<td class=\"${classNames.join(' ')}\" title=\"${cellValue}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}>${formatText(cellValue, true)}</div></td>`;\n                        }).join('') + '</tr>');\n                    });\n                }\n                else {\n                    datas.forEach((item) => {\n                        tables.push('<tr>' + columns.map((column) => {\n                            const cellAlign = column.align || allAlign;\n                            const classNames = hasEllipsis(column, 'showOverflow', allColumnOverflow) ? ['col--ellipsis'] : [];\n                            const cellValue = item[column.id];\n                            let rowSpan = 1;\n                            let colSpan = 1;\n                            if (isMerge) {\n                                const _rowIndex = $xeTable.getVTRowIndex(item._row);\n                                const _columnIndex = $xeTable.getVTColumnIndex(column);\n                                const spanRest = mergeBodyCellMaps[`${_rowIndex}:${_columnIndex}`];\n                                if (spanRest) {\n                                    const { rowspan, colspan } = spanRest;\n                                    if (!rowspan || !colspan) {\n                                        return '';\n                                    }\n                                    if (rowspan > 1) {\n                                        rowSpan = rowspan;\n                                    }\n                                    if (colspan > 1) {\n                                        colSpan = colspan;\n                                    }\n                                }\n                            }\n                            if (cellAlign) {\n                                classNames.push(`col--${cellAlign}`);\n                            }\n                            if (column.type === 'radio') {\n                                return `<td class=\"${classNames.join(' ')}\" rowspan=\"${rowSpan}\" colspan=\"${colSpan}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><input type=\"radio\" name=\"radio_${id}\" ${item._radioDisabled ? 'disabled ' : ''}${getBooleanValue(cellValue) ? 'checked' : ''}><span>${item._radioLabel}</span></div></td>`;\n                            }\n                            else if (column.type === 'checkbox') {\n                                return `<td class=\"${classNames.join(' ')}\" rowspan=\"${rowSpan}\" colspan=\"${colSpan}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}><input type=\"checkbox\" ${item._checkboxDisabled ? 'disabled ' : ''}${getBooleanValue(cellValue) ? 'checked' : ''}><span>${item._checkboxLabel}</span></div></td>`;\n                            }\n                            return `<td class=\"${classNames.join(' ')}\" rowspan=\"${rowSpan}\" colspan=\"${colSpan}\" title=\"${cellValue}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}>${formatText(cellValue, true)}</div></td>`;\n                        }).join('') + '</tr>');\n                    });\n                }\n                tables.push('</tbody>');\n            }\n            if (isFooter) {\n                const { footerTableData } = reactData;\n                const footers = getFooterData($xeTable, opts, footerTableData);\n                if (footers.length) {\n                    tables.push('<tfoot>');\n                    footers.forEach((row) => {\n                        tables.push(`<tr>${columns.map((column) => {\n                            const footAlign = column.footerAlign || column.align || allFooterAlign || allAlign;\n                            const classNames = hasEllipsis(column, 'showOverflow', allColumnOverflow) ? ['col--ellipsis'] : [];\n                            const cellValue = getFooterCellValue(opts, row, column);\n                            if (footAlign) {\n                                classNames.push(`col--${footAlign}`);\n                            }\n                            return `<td class=\"${classNames.join(' ')}\" title=\"${cellValue}\"><div ${isPrint ? '' : `style=\"width: ${column.renderWidth}px\"`}>${formatText(cellValue, true)}</div></td>`;\n                        }).join('')}</tr>`);\n                    });\n                    tables.push('</tfoot>');\n                }\n            }\n            // 是否半选状态\n            const script = !isAllSelected && isIndeterminate ? `<script>(function(){var a=document.querySelector(\".${allCls}\");if(a){a.indeterminate=true}})()</script>` : '';\n            tables.push('</table>', script);\n            return isPrint ? tables.join('') : createHtmlPage(opts, tables.join(''));\n        };\n        const toXML = (opts, columns, datas) => {\n            let xml = [\n                '<?xml version=\"1.0\"?>',\n                '<?mso-application progid=\"Excel.Sheet\"?>',\n                '<Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:x=\"urn:schemas-microsoft-com:office:excel\" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\" xmlns:html=\"http://www.w3.org/TR/REC-html40\">',\n                '<DocumentProperties xmlns=\"urn:schemas-microsoft-com:office:office\">',\n                '<Version>16.00</Version>',\n                '</DocumentProperties>',\n                '<ExcelWorkbook xmlns=\"urn:schemas-microsoft-com:office:excel\">',\n                '<WindowHeight>7920</WindowHeight>',\n                '<WindowWidth>21570</WindowWidth>',\n                '<WindowTopX>32767</WindowTopX>',\n                '<WindowTopY>32767</WindowTopY>',\n                '<ProtectStructure>False</ProtectStructure>',\n                '<ProtectWindows>False</ProtectWindows>',\n                '</ExcelWorkbook>',\n                `<Worksheet ss:Name=\"${opts.sheetName}\">`,\n                '<Table>',\n                columns.map((column) => `<Column ss:Width=\"${column.renderWidth}\"/>`).join('')\n            ].join('');\n            if (opts.isHeader) {\n                xml += `<Row>${columns.map((column) => `<Cell><Data ss:Type=\"String\">${getHeaderTitle(opts, column)}</Data></Cell>`).join('')}</Row>`;\n            }\n            datas.forEach((row) => {\n                xml += '<Row>' + columns.map((column) => `<Cell><Data ss:Type=\"String\">${row[column.id]}</Data></Cell>`).join('') + '</Row>';\n            });\n            if (opts.isFooter) {\n                const { footerTableData } = reactData;\n                const footers = getFooterData($xeTable, opts, footerTableData);\n                footers.forEach((row) => {\n                    xml += `<Row>${columns.map((column) => `<Cell><Data ss:Type=\"String\">${getFooterCellValue(opts, row, column)}</Data></Cell>`).join('')}</Row>`;\n                });\n            }\n            return `${xml}</Table></Worksheet></Workbook>`;\n        };\n        const getContent = ($xeTable, opts, columns, datas) => {\n            if (columns.length) {\n                switch (opts.type) {\n                    case 'csv':\n                        return toCsv($xeTable, opts, columns, datas);\n                    case 'txt':\n                        return toTxt($xeTable, opts, columns, datas);\n                    case 'html':\n                        return toHtml(opts, columns, datas);\n                    case 'xml':\n                        return toXML(opts, columns, datas);\n                }\n            }\n            return '';\n        };\n        const downloadFile = (opts, content) => {\n            const { filename, type, download } = opts;\n            if (!download) {\n                const blob = getExportBlobByContent(content, opts);\n                return Promise.resolve({ type, content, blob });\n            }\n            if (VxeUI.saveFile) {\n                VxeUI.saveFile({ filename, type, content }).then(() => {\n                    if (opts.message !== false) {\n                        if (VxeUI.modal) {\n                            VxeUI.modal.message({ content: getI18n('vxe.table.expSuccess'), status: 'success' });\n                        }\n                    }\n                });\n            }\n        };\n        const handleExport = (opts) => {\n            const { remote, columns, colgroups, exportMethod, afterExportMethod } = opts;\n            return new Promise(resolve => {\n                if (remote) {\n                    const params = { options: opts, $table: $xeTable, $grid: $xeGrid };\n                    resolve(exportMethod ? exportMethod(params) : params);\n                }\n                else {\n                    const datas = getExportData(opts);\n                    resolve($xeTable.preventEvent(null, 'event.export', { options: opts, columns, colgroups, datas }, () => {\n                        return downloadFile(opts, getContent($xeTable, opts, columns, datas));\n                    }));\n                }\n            }).then((params) => {\n                clearColumnConvert(columns);\n                if (!opts.print) {\n                    if (afterExportMethod) {\n                        afterExportMethod({ status: true, options: opts, $table: $xeTable, $grid: $xeGrid });\n                    }\n                }\n                return Object.assign({ status: true }, params);\n            }).catch(() => {\n                clearColumnConvert(columns);\n                if (!opts.print) {\n                    if (afterExportMethod) {\n                        afterExportMethod({ status: false, options: opts, $table: $xeTable, $grid: $xeGrid });\n                    }\n                }\n                const params = { status: false };\n                return Promise.reject(params);\n            });\n        };\n        const handleImport = (content, opts) => {\n            const { tableFullColumn, _importResolve, _importReject } = internalData;\n            let rest = { fields: [], rows: [] };\n            const tableFieldMaps = {};\n            const tableTitleMaps = {};\n            tableFullColumn.forEach((column) => {\n                const field = column.field;\n                const title = column.getTitle();\n                if (field) {\n                    tableFieldMaps[field] = column;\n                }\n                if (title) {\n                    tableTitleMaps[column.getTitle()] = column;\n                }\n            });\n            const tableConf = {\n                fieldMaps: tableFieldMaps,\n                titleMaps: tableTitleMaps\n            };\n            switch (opts.type) {\n                case 'csv':\n                    rest = parseCsv(tableConf, content);\n                    break;\n                case 'txt':\n                    rest = parseTxt(tableConf, content);\n                    break;\n                case 'html':\n                    rest = parseHTML(tableConf, content);\n                    break;\n                case 'xml':\n                    rest = parseXML(tableConf, content);\n                    break;\n            }\n            const { fields, rows } = rest;\n            const status = fields.some(field => tableFieldMaps[field] || tableTitleMaps[field]);\n            if (status) {\n                $xeTable.createData(rows)\n                    .then((data) => {\n                    let loadRest;\n                    if (opts.mode === 'insert' || opts.mode === 'insertBottom') {\n                        loadRest = $xeTable.insertAt(data, -1);\n                    }\n                    if (opts.mode === 'insertTop') {\n                        loadRest = $xeTable.insert(data);\n                    }\n                    else {\n                        loadRest = $xeTable.reloadData(data);\n                    }\n                    if (opts.message !== false) {\n                        if (VxeUI.modal) {\n                            VxeUI.modal.message({ content: getI18n('vxe.table.impSuccess', [rows.length]), status: 'success' });\n                        }\n                    }\n                    return loadRest.then(() => {\n                        if (_importResolve) {\n                            _importResolve({ status: true });\n                        }\n                    });\n                });\n            }\n            else if (opts.message !== false) {\n                if (VxeUI.modal) {\n                    VxeUI.modal.message({ content: getI18n('vxe.error.impFields'), status: 'error' });\n                }\n                if (_importReject) {\n                    _importReject({ status: false });\n                }\n            }\n        };\n        const handleFileImport = (file, opts) => {\n            const { importMethod, afterImportMethod } = opts;\n            const { type, filename } = parseFile(file);\n            const importOpts = computeImportOpts.value;\n            // 检查类型，如果为自定义导出，则不需要校验类型\n            if (!importMethod && !XEUtils.includes(XEUtils.keys(importOpts._typeMaps), type)) {\n                if (opts.message !== false) {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.message({ content: getI18n('vxe.error.notType', [type]), status: 'error' });\n                    }\n                }\n                const params = { status: false };\n                return Promise.reject(params);\n            }\n            const rest = new Promise((resolve, reject) => {\n                const _importResolve = (params) => {\n                    resolve(params);\n                    internalData._importResolve = null;\n                    internalData._importReject = null;\n                };\n                const _importReject = (params) => {\n                    reject(params);\n                    internalData._importResolve = null;\n                    internalData._importReject = null;\n                };\n                internalData._importResolve = _importResolve;\n                internalData._importReject = _importReject;\n                if (window.FileReader) {\n                    const options = Object.assign({ mode: 'insertTop' }, opts, { type, filename });\n                    if (options.remote) {\n                        if (importMethod) {\n                            Promise.resolve(importMethod({ file, options, $table: $xeTable })).then(() => {\n                                _importResolve({ status: true });\n                            }).catch(() => {\n                                _importResolve({ status: true });\n                            });\n                        }\n                        else {\n                            _importResolve({ status: true });\n                        }\n                    }\n                    else {\n                        const { tableFullColumn } = internalData;\n                        $xeTable.preventEvent(null, 'event.import', { file, options, columns: tableFullColumn }, () => {\n                            const reader = new FileReader();\n                            reader.onerror = () => {\n                                errLog('vxe.error.notType', [type]);\n                                _importReject({ status: false });\n                            };\n                            reader.onload = (e) => {\n                                handleImport(e.target.result, options);\n                            };\n                            reader.readAsText(file, options.encoding || 'UTF-8');\n                        });\n                    }\n                }\n                else {\n                    // 不支持的浏览器\n                    errLog('vxe.error.notExp');\n                    _importResolve({ status: true });\n                }\n            });\n            return rest.then(() => {\n                if (afterImportMethod) {\n                    afterImportMethod({ status: true, options: opts, $table: $xeTable });\n                }\n            }).catch((e) => {\n                if (afterImportMethod) {\n                    afterImportMethod({ status: false, options: opts, $table: $xeTable });\n                }\n                return Promise.reject(e);\n            });\n        };\n        const handleFilterColumns = (exportOpts, column, columns) => {\n            return columns.some((item) => {\n                if (isColumnInfo(item)) {\n                    return column.id === item.id;\n                }\n                else if (XEUtils.isString(item)) {\n                    return column.field === item;\n                }\n                else {\n                    const colid = item.id || item.colId;\n                    const type = item.type;\n                    const field = item.field;\n                    if (colid) {\n                        return column.id === colid;\n                    }\n                    else if (field && type) {\n                        return column.field === field && column.type === type;\n                    }\n                    else if (field) {\n                        return column.field === field;\n                    }\n                    else if (type) {\n                        return column.type === type;\n                    }\n                }\n                return false;\n            });\n        };\n        const handleFilterFields = (exportOpts, column, includeFields, excludeFields) => {\n            if (excludeFields) {\n                if (XEUtils.includes(excludeFields, column.field)) {\n                    return false;\n                }\n            }\n            if (includeFields) {\n                if (XEUtils.includes(includeFields, column.field)) {\n                    return true;\n                }\n                return false;\n            }\n            return exportOpts.original ? !!column.field : defaultFilterExportColumn(column);\n        };\n        const handleExportAndPrint = (options, isPrint) => {\n            const { treeConfig, showHeader, showFooter } = props;\n            const { initStore, isGroup, footerTableData, exportStore, exportParams } = reactData;\n            const { collectColumn, mergeBodyList, mergeFooterList } = internalData;\n            const exportOpts = computeExportOpts.value;\n            const hasTree = treeConfig;\n            const customOpts = computeCustomOpts.value;\n            const selectRecords = $xeTable.getCheckboxRecords();\n            const proxyOpts = $xeGrid ? $xeGrid.getComputeMaps().computeProxyOpts.value : {};\n            const hasFooter = !!footerTableData.length;\n            const hasMerge = !!(mergeBodyList.length || mergeFooterList.length);\n            const defOpts = Object.assign({\n                message: true,\n                isHeader: showHeader,\n                isTitle: showHeader,\n                isFooter: showFooter,\n                isColgroup: isGroup,\n                isMerge: hasMerge,\n                useStyle: true,\n                current: 'current',\n                modes: (proxyOpts.ajax && proxyOpts.ajax.queryAll ? ['all'] : []).concat(['current', 'selected', 'empty'])\n            }, options);\n            const types = defOpts.types || XEUtils.keys(exportOpts._typeMaps);\n            const modes = defOpts.modes || [];\n            const checkMethod = customOpts.checkMethod;\n            const exportColumns = collectColumn.slice(0);\n            const { columns, excludeFields, includeFields } = defOpts;\n            // 处理类型\n            const typeList = types.map((value) => {\n                return {\n                    value,\n                    label: getI18n(`vxe.export.types.${value}`)\n                };\n            });\n            const modeList = modes.map((item) => {\n                if (item && item.value) {\n                    return {\n                        value: item.value,\n                        label: item.label || item.value\n                    };\n                }\n                return {\n                    value: item,\n                    label: getI18n(`vxe.export.modes.${item}`)\n                };\n            });\n            // 默认选中\n            XEUtils.eachTree(exportColumns, (column, index, items, path, parent) => {\n                const isColGroup = column.children && column.children.length > 0;\n                let isChecked = false;\n                if (columns && columns.length) {\n                    isChecked = handleFilterColumns(defOpts, column, columns);\n                }\n                else if (excludeFields || includeFields) {\n                    isChecked = handleFilterFields(defOpts, column, includeFields, excludeFields);\n                }\n                else {\n                    isChecked = column.visible && (isColGroup || defaultFilterExportColumn(column));\n                }\n                column.checked = isChecked;\n                column.halfChecked = false;\n                column.disabled = (parent && parent.disabled) || (checkMethod ? !checkMethod({ $table: $xeTable, column }) : false);\n            });\n            // 更新条件\n            Object.assign(exportStore, {\n                columns: exportColumns,\n                typeList,\n                modeList,\n                hasFooter,\n                hasMerge,\n                hasTree,\n                isPrint,\n                hasColgroup: isGroup,\n                visible: true\n            });\n            // 默认参数\n            Object.assign(exportParams, {\n                mode: selectRecords.length ? 'selected' : 'current'\n            }, defOpts);\n            const { filename, sheetName, mode, type } = exportParams;\n            if (filename) {\n                if (XEUtils.isFunction(filename)) {\n                    exportParams.filename = filename({\n                        options: defOpts,\n                        $table: $xeTable,\n                        $grid: $xeGrid\n                    });\n                }\n                else {\n                    exportParams.filename = `${filename}`;\n                }\n            }\n            if (sheetName) {\n                if (XEUtils.isFunction(sheetName)) {\n                    exportParams.sheetName = sheetName({\n                        options: defOpts,\n                        $table: $xeTable,\n                        $grid: $xeGrid\n                    });\n                }\n                else {\n                    exportParams.sheetName = `${sheetName}`;\n                }\n            }\n            if (!modeList.some(item => item.value === mode)) {\n                exportParams.mode = modeList[0].value;\n            }\n            if (!typeList.some(item => item.value === type)) {\n                exportParams.type = typeList[0].value;\n            }\n            initStore.export = true;\n            return nextTick();\n        };\n        const handleCloseExport = () => {\n            if (VxeUI.modal) {\n                return VxeUI.modal.close('VXE_EXPORT_MODAL');\n            }\n            return Promise.resolve();\n        };\n        const exportMethods = {\n            /**\n             * 导出文件，支持 csv/html/xml/txt\n             * 如果是树表格，则默认是导出所有节点\n             * 如果是启用了虚拟滚动，则只能导出数据源，可以配合 dataFilterMethod 函数转换数据\n             * @param {Object} options 参数\n             */\n            exportData(options) {\n                const { treeConfig, showHeader, showFooter } = props;\n                const { isGroup } = reactData;\n                const { tableFullColumn, afterFullData, afterTreeFullData, collectColumn, mergeBodyList, mergeFooterList } = internalData;\n                const exportOpts = computeExportOpts.value;\n                const treeOpts = computeTreeOpts.value;\n                const proxyOpts = $xeGrid ? $xeGrid.getComputeMaps().computeProxyOpts.value : {};\n                const hasMerge = !!(mergeBodyList.length || mergeFooterList.length);\n                const opts = Object.assign({\n                    message: true,\n                    isHeader: showHeader,\n                    isTitle: showHeader,\n                    isFooter: showFooter,\n                    isColgroup: isGroup,\n                    isMerge: hasMerge,\n                    useStyle: true,\n                    current: 'current',\n                    modes: (proxyOpts.ajax && proxyOpts.ajax.queryAll ? ['all'] : []).concat(['current', 'selected', 'empty']),\n                    download: true,\n                    type: 'csv'\n                    // filename: '',\n                    // sheetName: '',\n                    // original: false,\n                    // isAllExpand: false,\n                    // data: null,\n                    // remote: false,\n                    // dataFilterMethod: null,\n                    // footerFilterMethod: null,\n                    // exportMethod: null,\n                    // columnFilterMethod: null,\n                    // beforeExportMethod: null,\n                    // afterExportMethod: null\n                }, exportOpts, options);\n                let { filename, sheetName, type, mode, columns, original, columnFilterMethod, beforeExportMethod, includeFields, excludeFields } = opts;\n                let groups = [];\n                const selectRecords = $xeTable.getCheckboxRecords();\n                if (!mode) {\n                    mode = selectRecords.length ? 'selected' : 'current';\n                }\n                let isCustomCol = false;\n                let customCols = [];\n                if (columns && columns.length) {\n                    isCustomCol = true;\n                    customCols = columns;\n                }\n                else {\n                    customCols = XEUtils.searchTree(collectColumn, column => {\n                        const isColGroup = column.children && column.children.length > 0;\n                        let isChecked = false;\n                        if (columns && columns.length) {\n                            isChecked = handleFilterColumns(opts, column, columns);\n                        }\n                        else if (excludeFields || includeFields) {\n                            isChecked = handleFilterFields(opts, column, includeFields, excludeFields);\n                        }\n                        else {\n                            isChecked = column.visible && (isColGroup || defaultFilterExportColumn(column));\n                        }\n                        return isChecked;\n                    }, { children: 'children', mapChildren: 'childNodes', original: true });\n                }\n                const handleOptions = Object.assign({}, opts, { filename: '', sheetName: '' });\n                // 如果设置源数据，则默认导出设置了字段的列\n                if (!isCustomCol && !columnFilterMethod) {\n                    columnFilterMethod = ({ column }) => {\n                        if (excludeFields) {\n                            if (XEUtils.includes(excludeFields, column.field)) {\n                                return false;\n                            }\n                        }\n                        if (includeFields) {\n                            if (XEUtils.includes(includeFields, column.field)) {\n                                return true;\n                            }\n                            return false;\n                        }\n                        return original ? !!column.field : defaultFilterExportColumn(column);\n                    };\n                    handleOptions.columnFilterMethod = columnFilterMethod;\n                }\n                if (customCols) {\n                    handleOptions._isCustomColumn = true;\n                    groups = XEUtils.searchTree(XEUtils.mapTree(customCols, (item) => {\n                        let targetColumn;\n                        if (item) {\n                            if (isColumnInfo(item)) {\n                                targetColumn = item;\n                            }\n                            else if (XEUtils.isString(item)) {\n                                targetColumn = $xeTable.getColumnByField(item);\n                            }\n                            else {\n                                const colid = item.id || item.colId;\n                                const type = item.type;\n                                const field = item.field;\n                                if (colid) {\n                                    targetColumn = $xeTable.getColumnById(colid);\n                                }\n                                else if (field && type) {\n                                    targetColumn = tableFullColumn.find((column) => column.field === field && column.type === type);\n                                }\n                                else if (field) {\n                                    targetColumn = $xeTable.getColumnByField(field);\n                                }\n                                else if (type) {\n                                    targetColumn = tableFullColumn.find((column) => column.type === type);\n                                }\n                            }\n                            return targetColumn || {};\n                        }\n                    }, {\n                        children: 'childNodes',\n                        mapChildren: '_children'\n                    }), (column, index) => isColumnInfo(column) && (!columnFilterMethod || columnFilterMethod({ $table: $xeTable, column: column, $columnIndex: index })), {\n                        children: '_children',\n                        mapChildren: 'childNodes',\n                        original: true\n                    });\n                }\n                else {\n                    groups = XEUtils.searchTree(isGroup ? collectColumn : tableFullColumn, (column, index) => column.visible && (!columnFilterMethod || columnFilterMethod({ $table: $xeTable, column, $columnIndex: index })), { children: 'children', mapChildren: 'childNodes', original: true });\n                }\n                // 获取所有列\n                const cols = [];\n                XEUtils.eachTree(groups, column => {\n                    const isColGroup = column.children && column.children.length;\n                    if (!isColGroup) {\n                        cols.push(column);\n                    }\n                }, { children: 'childNodes' });\n                // 构建分组层级\n                handleOptions.columns = cols;\n                handleOptions.colgroups = convertToRows(groups);\n                if (filename) {\n                    if (XEUtils.isFunction(filename)) {\n                        handleOptions.filename = filename({\n                            options: opts,\n                            $table: $xeTable,\n                            $grid: $xeGrid\n                        });\n                    }\n                    else {\n                        handleOptions.filename = `${filename}`;\n                    }\n                }\n                if (!handleOptions.filename) {\n                    handleOptions.filename = getI18n(handleOptions.original ? 'vxe.table.expOriginFilename' : 'vxe.table.expFilename', [XEUtils.toDateString(Date.now(), 'yyyyMMddHHmmss')]);\n                }\n                if (sheetName) {\n                    if (XEUtils.isFunction(sheetName)) {\n                        handleOptions.sheetName = sheetName({\n                            options: opts,\n                            $table: $xeTable,\n                            $grid: $xeGrid\n                        });\n                    }\n                    else {\n                        handleOptions.sheetName = `${sheetName}`;\n                    }\n                }\n                if (!handleOptions.sheetName) {\n                    handleOptions.sheetName = document.title || '';\n                }\n                // 检查类型，如果为自定义导出，则不需要校验类型\n                if (!handleOptions.exportMethod && !XEUtils.includes(XEUtils.keys(exportOpts._typeMaps), type)) {\n                    errLog('vxe.error.notType', [type]);\n                    if (['xlsx', 'pdf'].includes(type)) {\n                        warnLog('vxe.error.reqPlugin', [4, 'plugin-export-xlsx']);\n                    }\n                    const params = { status: false };\n                    return Promise.reject(params);\n                }\n                if (!handleOptions.print) {\n                    if (beforeExportMethod) {\n                        beforeExportMethod({ options: handleOptions, $table: $xeTable, $grid: $xeGrid });\n                    }\n                }\n                if (!handleOptions.data) {\n                    handleOptions.data = [];\n                    if (mode === 'selected') {\n                        if (['html', 'pdf'].indexOf(type) > -1 && treeConfig) {\n                            handleOptions.data = XEUtils.searchTree($xeTable.getTableData().fullData, item => $xeTable.findRowIndexOf(selectRecords, item) > -1, Object.assign({}, treeOpts, { data: '_row' }));\n                        }\n                        else {\n                            handleOptions.data = selectRecords;\n                        }\n                    }\n                    else if (mode === 'all') {\n                        if (!$xeGrid) {\n                            errLog('vxe.error.errProp', ['all', 'mode=current,selected']);\n                        }\n                        if ($xeGrid && !handleOptions.remote) {\n                            const gridReactData = $xeGrid.reactData;\n                            const { computeProxyOpts } = $xeGrid.getComputeMaps();\n                            const proxyOpts = computeProxyOpts.value;\n                            const { sortData } = gridReactData;\n                            const { beforeQueryAll, afterQueryAll, ajax = {} } = proxyOpts;\n                            const resConfigs = proxyOpts.response || proxyOpts.props || {};\n                            const ajaxMethods = ajax.queryAll;\n                            const queryAllSuccessMethods = ajax.queryAllSuccess;\n                            const queryAllErrorMethods = ajax.queryAllError;\n                            if (!ajaxMethods) {\n                                errLog('vxe.error.notFunc', ['proxy-config.ajax.queryAll']);\n                            }\n                            if (ajaxMethods) {\n                                const params = {\n                                    $table: $xeTable,\n                                    $grid: $xeGrid,\n                                    sort: sortData.length ? sortData[0] : {},\n                                    sorts: sortData,\n                                    filters: gridReactData.filterData,\n                                    form: gridReactData.formData,\n                                    options: handleOptions\n                                };\n                                return Promise.resolve((beforeQueryAll || ajaxMethods)(params))\n                                    .then(rest => {\n                                    const listProp = resConfigs.list;\n                                    handleOptions.data = (listProp ? (XEUtils.isFunction(listProp) ? listProp({ data: rest, $grid: $xeGrid }) : XEUtils.get(rest, listProp)) : rest) || [];\n                                    if (afterQueryAll) {\n                                        afterQueryAll(params);\n                                    }\n                                    if (queryAllSuccessMethods) {\n                                        queryAllSuccessMethods(Object.assign(Object.assign({}, params), { response: rest }));\n                                    }\n                                    return handleExport(handleOptions);\n                                })\n                                    .catch((rest) => {\n                                    if (queryAllErrorMethods) {\n                                        queryAllErrorMethods(Object.assign(Object.assign({}, params), { response: rest }));\n                                    }\n                                });\n                            }\n                        }\n                    }\n                    if (mode === 'current') {\n                        handleOptions.data = treeConfig ? afterTreeFullData : afterFullData;\n                    }\n                }\n                else {\n                    handleOptions._isCustomData = true;\n                }\n                return handleExport(handleOptions);\n            },\n            importByFile(file, options) {\n                const opts = Object.assign({}, options);\n                const { beforeImportMethod } = opts;\n                if (beforeImportMethod) {\n                    beforeImportMethod({ options: opts, $table: $xeTable });\n                }\n                return handleFileImport(file, opts);\n            },\n            importData(options) {\n                const importOpts = computeImportOpts.value;\n                const opts = Object.assign({\n                    types: XEUtils.keys(importOpts._typeMaps)\n                    // beforeImportMethod: null,\n                    // afterImportMethod: null\n                }, importOpts, options);\n                const { beforeImportMethod, afterImportMethod } = opts;\n                if (beforeImportMethod) {\n                    beforeImportMethod({ options: opts, $table: $xeTable });\n                }\n                return VxeUI.readFile(opts).catch(e => {\n                    if (afterImportMethod) {\n                        afterImportMethod({ status: false, options: opts, $table: $xeTable });\n                    }\n                    return Promise.reject(e);\n                }).then((params) => {\n                    const { file } = params;\n                    return handleFileImport(file, opts);\n                });\n            },\n            saveFile(options) {\n                return VxeUI.saveFile(options);\n            },\n            readFile(options) {\n                return VxeUI.readFile(options);\n            },\n            print(options) {\n                const printOpts = computePrintOpts.value;\n                const opts = Object.assign({\n                    original: false\n                    // beforePrintMethod\n                }, printOpts, options, {\n                    type: 'html',\n                    download: false,\n                    remote: false,\n                    print: true\n                });\n                const { sheetName } = opts;\n                let printTitle = '';\n                if (sheetName) {\n                    if (XEUtils.isFunction(sheetName)) {\n                        printTitle = sheetName({\n                            options: opts,\n                            $table: $xeTable,\n                            $grid: $xeGrid\n                        });\n                    }\n                    else {\n                        printTitle = `${sheetName}`;\n                    }\n                }\n                if (!printTitle) {\n                    printTitle = document.title || '';\n                }\n                const beforePrintMethod = opts.beforePrintMethod;\n                const tableHtml = opts.html || opts.content;\n                return new Promise((resolve, reject) => {\n                    if (VxeUI.print) {\n                        if (tableHtml) {\n                            resolve(VxeUI.print({\n                                title: printTitle,\n                                html: tableHtml,\n                                customStyle: opts.style,\n                                beforeMethod: beforePrintMethod\n                                    ? ({ html }) => {\n                                        return beforePrintMethod({\n                                            html,\n                                            content: html,\n                                            options: opts,\n                                            $table: $xeTable\n                                        });\n                                    }\n                                    : undefined\n                            }));\n                        }\n                        else {\n                            resolve(exportMethods.exportData(opts).then(({ content }) => {\n                                return VxeUI.print({\n                                    title: printTitle,\n                                    html: content,\n                                    customStyle: opts.style,\n                                    beforeMethod: beforePrintMethod\n                                        ? ({ html }) => {\n                                            return beforePrintMethod({\n                                                html,\n                                                content: html,\n                                                options: opts,\n                                                $table: $xeTable\n                                            });\n                                        }\n                                        : undefined\n                                });\n                            }));\n                        }\n                    }\n                    else {\n                        const e = { status: false };\n                        reject(e);\n                    }\n                });\n            },\n            getPrintHtml(options) {\n                const printOpts = computePrintOpts.value;\n                const opts = Object.assign({\n                    original: false\n                    // beforePrintMethod\n                }, printOpts, options, {\n                    type: 'html',\n                    download: false,\n                    remote: false,\n                    print: true\n                });\n                return $xeTable.exportData(opts).then(({ content }) => {\n                    return {\n                        html: content\n                    };\n                });\n            },\n            closeImport() {\n                if (VxeUI.modal) {\n                    return VxeUI.modal.close('VXE_IMPORT_MODAL');\n                }\n                return Promise.resolve();\n            },\n            openImport(options) {\n                const { treeConfig, importConfig } = props;\n                const { initStore, importStore, importParams } = reactData;\n                const importOpts = computeImportOpts.value;\n                const defOpts = Object.assign({\n                    mode: 'insertTop',\n                    message: true,\n                    types: XEUtils.keys(importOpts._typeMaps),\n                    modes: ['insertTop', 'covering']\n                }, importOpts, options);\n                const types = defOpts.types || [];\n                const modes = defOpts.modes || [];\n                const isTree = !!treeConfig;\n                if (isTree) {\n                    if (defOpts.message) {\n                        if (VxeUI.modal) {\n                            VxeUI.modal.message({ content: getI18n('vxe.error.treeNotImp'), status: 'error' });\n                        }\n                    }\n                    return;\n                }\n                if (!importConfig) {\n                    errLog('vxe.error.reqProp', ['import-config']);\n                }\n                // 处理类型\n                const typeList = types.map((value) => {\n                    return {\n                        value,\n                        label: getI18n(`vxe.export.types.${value}`)\n                    };\n                });\n                const modeList = modes.map((item) => {\n                    if (item && item.value) {\n                        return {\n                            value: item.value,\n                            label: item.label || item.value\n                        };\n                    }\n                    return {\n                        value: item,\n                        label: getI18n(`vxe.import.modes.${item}`)\n                    };\n                });\n                Object.assign(importStore, {\n                    file: null,\n                    type: '',\n                    filename: '',\n                    modeList,\n                    typeList,\n                    visible: true\n                });\n                Object.assign(importParams, defOpts);\n                if (!modeList.some(item => item.value === importParams.mode)) {\n                    importParams.mode = modeList[0].value;\n                }\n                initStore.import = true;\n            },\n            closeExport: handleCloseExport,\n            openExport(options) {\n                const exportOpts = computeExportOpts.value;\n                const defOpts = Object.assign({\n                    message: true,\n                    types: XEUtils.keys(exportOpts._typeMaps)\n                }, exportOpts, options);\n                if (!props.exportConfig) {\n                    errLog('vxe.error.reqProp', ['export-config']);\n                }\n                return handleExportAndPrint(defOpts);\n            },\n            closePrint: handleCloseExport,\n            openPrint(options) {\n                const printOpts = computePrintOpts.value;\n                const defOpts = Object.assign({\n                    message: true\n                }, printOpts, options);\n                if (!props.printConfig) {\n                    errLog('vxe.error.reqProp', ['print-config']);\n                }\n                return handleExportAndPrint(defOpts, true);\n            }\n        };\n        return exportMethods;\n    },\n    setupGrid($xeGrid) {\n        return $xeGrid.extendTableMethods(tableExportMethodKeys);\n    }\n});\n", "// 默认导出或打印的 HTML 样式\nconst defaultHtmlStyle = 'body{margin:0;padding: 0 1px;color:#333333;font-size:14px;font-family:\"Microsoft YaHei\",微软雅黑,\"MicrosoftJhengHei\",华文细黑,ST<PERSON>eit<PERSON>,Ming<PERSON><PERSON>}body *{-webkit-box-sizing:border-box;box-sizing:border-box}.vxe-table{border-collapse:collapse;text-align:left;border-spacing:0}.vxe-table:not(.is--print){table-layout:fixed}.vxe-table,.vxe-table th,.vxe-table td,.vxe-table td{border-color:#D0D0D0;border-style:solid;border-width:0}.vxe-table.is--print{width:100%}.border--default,.border--full,.border--outer{border-top-width:1px}.border--default,.border--full,.border--outer{border-left-width:1px}.border--outer,.border--default th,.border--default td,.border--full th,.border--full td,.border--outer th,.border--inner th,.border--inner td{border-bottom-width:1px}.border--default,.border--outer,.border--full th,.border--full td{border-right-width:1px}.border--default th,.border--full th,.border--outer th{background-color:#f8f8f9}.vxe-table td>div,.vxe-table th>div{padding:.5em .4em}.col--center{text-align:center}.col--right{text-align:right}.vxe-table:not(.is--print) .col--ellipsis>div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all}.vxe-table--tree-node{text-align:left}.vxe-table--tree-node-wrapper{position:relative}.vxe-table--tree-icon-wrapper{position:absolute;top:50%;width:1em;height:1em;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vxe-table--tree-unfold-icon,.vxe-table--tree-fold-icon{position:absolute;width:0;height:0;border-style:solid;border-width:.5em;border-right-color:transparent;border-bottom-color:transparent}.vxe-table--tree-unfold-icon{left:.3em;top:0;border-left-color:#939599;border-top-color:transparent}.vxe-table--tree-fold-icon{left:0;top:.3em;border-left-color:transparent;border-top-color:#939599}.vxe-table--tree-cell{display:block;padding-left:1.5em}.vxe-table input[type=\"checkbox\"]{margin:0}.vxe-table input[type=\"checkbox\"],.vxe-table input[type=\"radio\"],.vxe-table input[type=\"checkbox\"]+span,.vxe-table input[type=\"radio\"]+span{vertical-align:middle;padding-left:0.4em}';\nexport function getExportBlobByContent(content, options) {\n    return new Blob([content], { type: `text/${options.type};charset=utf-8;` });\n}\nexport function createHtmlPage(opts, content) {\n    const { style } = opts;\n    return [\n        '<!DOCTYPE html><html>',\n        '<head>',\n        '<meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui\">',\n        `<title>${opts.sheetName}</title>`,\n        '<style media=\"print\">.vxe-page-break-before{page-break-before:always;}.vxe-page-break-after{page-break-after:always;}</style>',\n        `<style>${defaultHtmlStyle}</style>`,\n        style ? `<style>${style}</style>` : '',\n        '</head>',\n        `<body>${content}</body>`,\n        '</html>'\n    ].join('');\n}\n", "import XEUtils from 'xe-utils';\nimport { VxeUI } from '../../../ui';\nimport { getRefElem } from '../../src/util';\nimport { hasClass, getAbsolutePos, addClass, removeClass, hasControlKey } from '../../../ui/src/dom';\nconst { hooks } = VxeUI;\nconst browseObj = XEUtils.browse();\nfunction getTargetOffset(target, container) {\n    let offsetTop = 0;\n    let offsetLeft = 0;\n    const triggerCheckboxLabel = !browseObj.firefox && hasClass(target, 'vxe-checkbox--label');\n    if (triggerCheckboxLabel) {\n        const checkboxLabelStyle = getComputedStyle(target);\n        offsetTop -= XEUtils.toNumber(checkboxLabelStyle.paddingTop);\n        offsetLeft -= XEUtils.toNumber(checkboxLabelStyle.paddingLeft);\n    }\n    while (target && target !== container) {\n        offsetTop += target.offsetTop;\n        offsetLeft += target.offsetLeft;\n        target = target.offsetParent;\n        if (triggerCheckboxLabel) {\n            const checkboxStyle = getComputedStyle(target);\n            offsetTop -= XEUtils.toNumber(checkboxStyle.paddingTop);\n            offsetLeft -= XEUtils.toNumber(checkboxStyle.paddingLeft);\n        }\n    }\n    return { offsetTop, offsetLeft };\n}\nhooks.add('tableKeyboardModule', {\n    setupTable($xeTable) {\n        const { props, reactData, internalData } = $xeTable;\n        const { refElem } = $xeTable.getRefMaps();\n        const { computeEditOpts, computeCheckboxOpts, computeMouseOpts, computeTreeOpts, computeRowOpts, computeColumnOpts, computeCellOpts, computeDefaultRowHeight, computeCurrentRowOpts, computeCurrentColumnOpts } = $xeTable.getComputeMaps();\n        function getCheckboxRangeRows(evnt, params, targetTrElem, trRect, offsetClientTop, moveRange) {\n            const { showOverflow } = props;\n            const { fullAllDataRowIdData, isResizeCellHeight } = internalData;\n            const rowOpts = computeRowOpts.value;\n            const cellOpts = computeCellOpts.value;\n            const defaultRowHeight = computeDefaultRowHeight.value;\n            const { row } = params;\n            let countHeight = 0;\n            let rangeRows = [];\n            let moveSize = 0;\n            const isDown = moveRange > 0;\n            const { scrollYLoad } = reactData;\n            const { afterFullData } = internalData;\n            if (isDown) {\n                moveSize = offsetClientTop + moveRange;\n            }\n            else {\n                moveSize = (trRect.height - offsetClientTop) + Math.abs(moveRange);\n            }\n            if (scrollYLoad) {\n                const _rowIndex = $xeTable.getVTRowIndex(row);\n                const isCustomCellHeight = isResizeCellHeight || cellOpts.height || rowOpts.height;\n                if (!isCustomCellHeight && showOverflow) {\n                    if (isDown) {\n                        rangeRows = afterFullData.slice(_rowIndex, _rowIndex + Math.ceil(moveSize / defaultRowHeight));\n                    }\n                    else {\n                        rangeRows = afterFullData.slice(_rowIndex - Math.floor(moveSize / defaultRowHeight), _rowIndex + 1);\n                    }\n                }\n                else {\n                    if (isDown) {\n                        for (let i = _rowIndex; i < afterFullData.length; i++) {\n                            const item = afterFullData[i];\n                            const rowid = $xeTable.getRowid(item);\n                            const rowRest = fullAllDataRowIdData[rowid] || {};\n                            countHeight += rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;\n                            rangeRows.push(item);\n                            if (countHeight > moveSize) {\n                                return rangeRows;\n                            }\n                        }\n                    }\n                    else {\n                        for (let len = _rowIndex; len >= 0; len--) {\n                            const item = afterFullData[len];\n                            const rowid = $xeTable.getRowid(item);\n                            const rowRest = fullAllDataRowIdData[rowid] || {};\n                            countHeight += rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;\n                            rangeRows.push(item);\n                            if (countHeight > moveSize) {\n                                return rangeRows;\n                            }\n                        }\n                    }\n                }\n            }\n            else {\n                const siblingProp = isDown ? 'next' : 'previous';\n                while (targetTrElem && countHeight < moveSize) {\n                    const rowNodeRest = $xeTable.getRowNode(targetTrElem);\n                    if (rowNodeRest) {\n                        rangeRows.push(rowNodeRest.item);\n                        countHeight += targetTrElem.offsetHeight;\n                        targetTrElem = targetTrElem[`${siblingProp}ElementSibling`];\n                    }\n                }\n            }\n            return rangeRows;\n        }\n        const handleCheckboxRangeEvent = (evnt, params) => {\n            const { elemStore } = internalData;\n            const bodyScrollElem = getRefElem(elemStore['main-body-scroll']);\n            const leftScrollElem = getRefElem(elemStore['left-body-scroll']);\n            const rightScrollElem = getRefElem(elemStore['right-body-scroll']);\n            const { column, cell } = params;\n            if (column.type === 'checkbox') {\n                let bodyWrapperElem = bodyScrollElem;\n                if (leftScrollElem && column.fixed === 'left') {\n                    bodyWrapperElem = leftScrollElem;\n                }\n                else if (rightScrollElem && column.fixed === 'right') {\n                    bodyWrapperElem = rightScrollElem;\n                }\n                if (!bodyWrapperElem) {\n                    return;\n                }\n                const el = refElem.value;\n                const disX = evnt.clientX;\n                const disY = evnt.clientY;\n                const checkboxRangeElem = bodyWrapperElem.querySelector('.vxe-table--checkbox-range');\n                const trElem = cell.parentElement;\n                const selectRecords = $xeTable.getCheckboxRecords();\n                let lastRangeRows = [];\n                const marginSize = 1;\n                const offsetRest = getTargetOffset(evnt.target, bodyWrapperElem);\n                const startTop = offsetRest.offsetTop + evnt.offsetY;\n                const startLeft = offsetRest.offsetLeft + evnt.offsetX;\n                const startScrollTop = bodyWrapperElem.scrollTop;\n                const rowHeight = trElem.offsetHeight;\n                const trRect = trElem.getBoundingClientRect();\n                const offsetClientTop = disY - trRect.y;\n                let mouseScrollTimeout = null;\n                let isMouseScrollDown = false;\n                let mouseScrollSpaceSize = 1;\n                const triggerEvent = (type, evnt) => {\n                    $xeTable.dispatchEvent(`checkbox-range-${type}`, {\n                        records: () => $xeTable.getCheckboxRecords(),\n                        reserves: () => $xeTable.getCheckboxReserveRecords()\n                    }, evnt);\n                };\n                const handleChecked = (evnt) => {\n                    const { clientX, clientY } = evnt;\n                    const offsetLeft = clientX - disX;\n                    const offsetTop = clientY - disY + (bodyWrapperElem.scrollTop - startScrollTop);\n                    let rangeHeight = Math.abs(offsetTop);\n                    let rangeWidth = Math.abs(offsetLeft);\n                    let rangeTop = startTop;\n                    let rangeLeft = startLeft;\n                    if (offsetTop < marginSize) {\n                        // 向上\n                        rangeTop += offsetTop;\n                        if (rangeTop < marginSize) {\n                            rangeTop = marginSize;\n                            rangeHeight = startTop;\n                        }\n                    }\n                    else {\n                        // 向下\n                        rangeHeight = Math.min(rangeHeight, bodyWrapperElem.scrollHeight - startTop - marginSize);\n                    }\n                    if (offsetLeft < marginSize) {\n                        // 向左\n                        rangeLeft += offsetLeft;\n                        if (rangeWidth > startLeft) {\n                            rangeLeft = marginSize;\n                            rangeWidth = startLeft;\n                        }\n                    }\n                    else {\n                        // 向右\n                        rangeWidth = Math.min(rangeWidth, bodyWrapperElem.clientWidth - startLeft - marginSize);\n                    }\n                    checkboxRangeElem.style.height = `${rangeHeight}px`;\n                    checkboxRangeElem.style.width = `${rangeWidth}px`;\n                    checkboxRangeElem.style.left = `${rangeLeft}px`;\n                    checkboxRangeElem.style.top = `${rangeTop}px`;\n                    checkboxRangeElem.style.display = 'block';\n                    const rangeRows = getCheckboxRangeRows(evnt, params, trElem, trRect, offsetClientTop, offsetTop < marginSize ? -rangeHeight : rangeHeight);\n                    // 至少滑动 10px 才能有效匹配\n                    if (rangeHeight > 10 && rangeRows.length !== lastRangeRows.length) {\n                        const isControlKey = hasControlKey(evnt);\n                        lastRangeRows = rangeRows;\n                        if (isControlKey) {\n                            rangeRows.forEach((row) => {\n                                $xeTable.handleBatchSelectRows([row], selectRecords.indexOf(row) === -1);\n                            });\n                        }\n                        else {\n                            $xeTable.setAllCheckboxRow(false);\n                            $xeTable.handleCheckedCheckboxRow(rangeRows, true, false);\n                        }\n                        triggerEvent('change', evnt);\n                    }\n                };\n                // 停止鼠标滚动\n                const stopMouseScroll = () => {\n                    clearTimeout(mouseScrollTimeout);\n                    mouseScrollTimeout = null;\n                };\n                // 开始鼠标滚动\n                const startMouseScroll = (evnt) => {\n                    stopMouseScroll();\n                    mouseScrollTimeout = setTimeout(() => {\n                        if (mouseScrollTimeout) {\n                            const { scrollLeft, scrollTop, clientHeight, scrollHeight } = bodyWrapperElem;\n                            const topSize = Math.ceil(mouseScrollSpaceSize * 50 / rowHeight);\n                            if (isMouseScrollDown) {\n                                if (scrollTop + clientHeight < scrollHeight) {\n                                    $xeTable.scrollTo(scrollLeft, scrollTop + topSize);\n                                    startMouseScroll(evnt);\n                                    handleChecked(evnt);\n                                }\n                                else {\n                                    stopMouseScroll();\n                                }\n                            }\n                            else {\n                                if (scrollTop) {\n                                    $xeTable.scrollTo(scrollLeft, scrollTop - topSize);\n                                    startMouseScroll(evnt);\n                                    handleChecked(evnt);\n                                }\n                                else {\n                                    stopMouseScroll();\n                                }\n                            }\n                        }\n                    }, 50);\n                };\n                addClass(el, 'drag--range');\n                document.onmousemove = evnt => {\n                    evnt.preventDefault();\n                    evnt.stopPropagation();\n                    const { clientY } = evnt;\n                    const { boundingTop } = getAbsolutePos(bodyWrapperElem);\n                    // 如果超过可视区，触发滚动\n                    if (clientY < boundingTop) {\n                        isMouseScrollDown = false;\n                        mouseScrollSpaceSize = boundingTop - clientY;\n                        if (!mouseScrollTimeout) {\n                            startMouseScroll(evnt);\n                        }\n                    }\n                    else if (clientY > boundingTop + bodyWrapperElem.clientHeight) {\n                        isMouseScrollDown = true;\n                        mouseScrollSpaceSize = clientY - boundingTop - bodyWrapperElem.clientHeight;\n                        if (!mouseScrollTimeout) {\n                            startMouseScroll(evnt);\n                        }\n                    }\n                    else if (mouseScrollTimeout) {\n                        stopMouseScroll();\n                    }\n                    handleChecked(evnt);\n                };\n                document.onmouseup = (evnt) => {\n                    stopMouseScroll();\n                    removeClass(el, 'drag--range');\n                    checkboxRangeElem.removeAttribute('style');\n                    document.onmousemove = null;\n                    document.onmouseup = null;\n                    triggerEvent('end', evnt);\n                };\n                triggerEvent('start', evnt);\n            }\n        };\n        const handleCellMousedownEvent = (evnt, params) => {\n            const { editConfig, checkboxConfig, mouseConfig } = props;\n            const checkboxOpts = computeCheckboxOpts.value;\n            const mouseOpts = computeMouseOpts.value;\n            const editOpts = computeEditOpts.value;\n            if (mouseConfig && mouseOpts.area && $xeTable.triggerCellAreaMousednEvent) {\n                return $xeTable.triggerCellAreaMousednEvent(evnt, params);\n            }\n            else {\n                if (checkboxConfig && checkboxOpts.range) {\n                    handleCheckboxRangeEvent(evnt, params);\n                }\n                if (mouseConfig && mouseOpts.selected) {\n                    if (!editConfig || editOpts.mode === 'cell') {\n                        $xeTable.handleSelected(params, evnt);\n                    }\n                }\n            }\n        };\n        const handleMoveSelected = (evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow) => {\n            const { afterFullData, visibleColumn } = internalData;\n            const params = Object.assign({}, args);\n            const _rowIndex = $xeTable.getVTRowIndex(params.row);\n            const _columnIndex = $xeTable.getVTColumnIndex(params.column);\n            evnt.preventDefault();\n            if (isUpArrow && _rowIndex > 0) {\n                // 移动到上一行\n                params.rowIndex = _rowIndex - 1;\n                params.row = afterFullData[params.rowIndex];\n            }\n            else if (isDwArrow && _rowIndex < afterFullData.length - 1) {\n                // 移动到下一行\n                params.rowIndex = _rowIndex + 1;\n                params.row = afterFullData[params.rowIndex];\n            }\n            else if (isLeftArrow && _columnIndex) {\n                // 移动到左侧单元格\n                params.columnIndex = _columnIndex - 1;\n                params.column = visibleColumn[params.columnIndex];\n            }\n            else if (isRightArrow && _columnIndex < visibleColumn.length - 1) {\n                // 移动到右侧单元格\n                params.columnIndex = _columnIndex + 1;\n                params.column = visibleColumn[params.columnIndex];\n            }\n            $xeTable.scrollToRow(params.row, params.column).then(() => {\n                params.cell = $xeTable.getCellElement(params.row, params.column);\n                $xeTable.handleSelected(params, evnt);\n            });\n            return params;\n        };\n        const keyboardMethods = {\n            // 处理 Tab 键移动\n            moveTabSelected(args, isLeft, evnt) {\n                const { editConfig } = props;\n                const { afterFullData, visibleColumn } = internalData;\n                const editOpts = computeEditOpts.value;\n                const rowOpts = computeRowOpts.value;\n                const currentRowOpts = computeCurrentRowOpts.value;\n                const columnOpts = computeColumnOpts.value;\n                const currentColumnOpts = computeCurrentColumnOpts.value;\n                let targetRow;\n                let targetRowIndex;\n                let targetColumnIndex;\n                const params = Object.assign({}, args);\n                const _rowIndex = $xeTable.getVTRowIndex(params.row);\n                const _columnIndex = $xeTable.getVTColumnIndex(params.column);\n                evnt.preventDefault();\n                if (isLeft) {\n                    // 向左\n                    if (_columnIndex <= 0) {\n                        // 如果已经是第一列，则移动到上一行\n                        if (_rowIndex > 0) {\n                            targetRowIndex = _rowIndex - 1;\n                            targetRow = afterFullData[targetRowIndex];\n                            targetColumnIndex = visibleColumn.length - 1;\n                        }\n                    }\n                    else {\n                        targetColumnIndex = _columnIndex - 1;\n                    }\n                }\n                else {\n                    if (_columnIndex >= visibleColumn.length - 1) {\n                        // 如果已经是第一列，则移动到上一行\n                        if (_rowIndex < afterFullData.length - 1) {\n                            targetRowIndex = _rowIndex + 1;\n                            targetRow = afterFullData[targetRowIndex];\n                            targetColumnIndex = 0;\n                        }\n                    }\n                    else {\n                        targetColumnIndex = _columnIndex + 1;\n                    }\n                }\n                const targetColumn = visibleColumn[targetColumnIndex];\n                if (targetColumn) {\n                    if (targetRow) {\n                        params.rowIndex = targetRowIndex;\n                        params.row = targetRow;\n                    }\n                    else {\n                        params.rowIndex = _rowIndex;\n                    }\n                    params.columnIndex = targetColumnIndex;\n                    params.column = targetColumn;\n                    params.cell = $xeTable.getCellElement(params.row, params.column);\n                    if (rowOpts.isCurrent && currentRowOpts.isFollowSelected) {\n                        $xeTable.triggerCurrentRowEvent(evnt, params);\n                    }\n                    if (columnOpts.isCurrent && currentColumnOpts.isFollowSelected) {\n                        $xeTable.triggerCurrentColumnEvent(evnt, params);\n                    }\n                    if (editConfig) {\n                        if (editOpts.trigger === 'click' || editOpts.trigger === 'dblclick') {\n                            if (editOpts.mode === 'row') {\n                                $xeTable.handleEdit(params, evnt);\n                            }\n                            else {\n                                $xeTable.scrollToRow(params.row, params.column)\n                                    .then(() => {\n                                    $xeTable.handleSelected(params, evnt);\n                                });\n                            }\n                        }\n                    }\n                    else {\n                        $xeTable.scrollToRow(params.row, params.column)\n                            .then(() => {\n                            $xeTable.handleSelected(params, evnt);\n                        });\n                    }\n                }\n            },\n            // 处理当前行方向键移动\n            moveCurrentRow(isUpArrow, isDwArrow, evnt) {\n                const { treeConfig } = props;\n                const { currentRow } = reactData;\n                const { afterFullData } = internalData;\n                const treeOpts = computeTreeOpts.value;\n                const childrenField = treeOpts.children || treeOpts.childrenField;\n                let targetRow;\n                if (currentRow) {\n                    if (treeConfig) {\n                        const { index, items } = XEUtils.findTree(afterFullData, item => item === currentRow, { children: childrenField });\n                        if (isUpArrow && index > 0) {\n                            targetRow = items[index - 1];\n                        }\n                        else if (isDwArrow && index < items.length - 1) {\n                            targetRow = items[index + 1];\n                        }\n                    }\n                    else {\n                        const _rowIndex = $xeTable.getVTRowIndex(currentRow);\n                        if (isUpArrow && _rowIndex > 0) {\n                            targetRow = afterFullData[_rowIndex - 1];\n                        }\n                        else if (isDwArrow && _rowIndex < afterFullData.length - 1) {\n                            targetRow = afterFullData[_rowIndex + 1];\n                        }\n                    }\n                }\n                else {\n                    targetRow = afterFullData[0];\n                }\n                if (targetRow) {\n                    evnt.preventDefault();\n                    const params = {\n                        $table: $xeTable,\n                        row: targetRow,\n                        rowIndex: $xeTable.getRowIndex(targetRow),\n                        $rowIndex: $xeTable.getVMRowIndex(targetRow)\n                    };\n                    $xeTable.scrollToRow(targetRow)\n                        .then(() => $xeTable.triggerCurrentRowEvent(evnt, params));\n                }\n            },\n            // 处理当前列方向键移动\n            moveCurrentColumn(isLeftArrow, isRightArrow, evnt) {\n                const { currentColumn } = reactData;\n                const { visibleColumn } = internalData;\n                let targetCol = null;\n                if (currentColumn) {\n                    const _columnIndex = $xeTable.getVTColumnIndex(currentColumn);\n                    if (isLeftArrow && _columnIndex > 0) {\n                        targetCol = visibleColumn[_columnIndex - 1];\n                    }\n                    else if (isRightArrow && _columnIndex < visibleColumn.length - 1) {\n                        targetCol = visibleColumn[_columnIndex + 1];\n                    }\n                }\n                else {\n                    targetCol = visibleColumn[0];\n                }\n                if (targetCol) {\n                    evnt.preventDefault();\n                    const params = {\n                        $table: $xeTable,\n                        column: targetCol,\n                        columnIndex: $xeTable.getColumnIndex(targetCol),\n                        $columnIndex: $xeTable.getVMColumnIndex(targetCol)\n                    };\n                    $xeTable.scrollToColumn(targetCol)\n                        .then(() => $xeTable.triggerCurrentColumnEvent(evnt, params));\n                }\n            },\n            // 处理可编辑方向键移动\n            moveArrowSelected(args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow, evnt) {\n                const { highlightCurrentRow, highlightCurrentColumn } = props;\n                const rowOpts = computeRowOpts.value;\n                const currentRowOpts = computeCurrentRowOpts.value;\n                const columnOpts = computeColumnOpts.value;\n                const currentColumnOpts = computeCurrentColumnOpts.value;\n                const params = handleMoveSelected(evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow);\n                if (rowOpts.isCurrent || highlightCurrentRow) {\n                    if (currentRowOpts.isFollowSelected) {\n                        $xeTable.triggerCurrentRowEvent(evnt, params);\n                    }\n                    else {\n                        // 当前行按键上下移动\n                        if ((isUpArrow || isDwArrow) && (rowOpts.isCurrent || highlightCurrentRow)) {\n                            $xeTable.moveCurrentRow(isUpArrow, isDwArrow, evnt);\n                        }\n                    }\n                }\n                if (columnOpts.isCurrent || highlightCurrentColumn) {\n                    if (currentColumnOpts.isFollowSelected) {\n                        $xeTable.triggerCurrentColumnEvent(evnt, params);\n                    }\n                    else {\n                        // 当前行按键左右移动\n                        if ((isLeftArrow || isRightArrow) && (columnOpts.isCurrent || highlightCurrentColumn)) {\n                            $xeTable.moveCurrentColumn(isLeftArrow, isRightArrow, evnt);\n                        }\n                    }\n                }\n            },\n            moveEnterSelected(args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow, evnt) {\n                const { highlightCurrentRow, highlightCurrentColumn } = props;\n                const rowOpts = computeRowOpts.value;\n                const currentRowOpts = computeCurrentRowOpts.value;\n                const columnOpts = computeColumnOpts.value;\n                const currentColumnOpts = computeCurrentColumnOpts.value;\n                const params = handleMoveSelected(evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow);\n                if (((rowOpts.isCurrent || highlightCurrentRow) && currentRowOpts.isFollowSelected)) {\n                    $xeTable.triggerCurrentRowEvent(evnt, params);\n                }\n                if ((columnOpts.isCurrent || highlightCurrentColumn) && currentColumnOpts.isFollowSelected) {\n                    $xeTable.triggerCurrentColumnEvent(evnt, params);\n                }\n            },\n            // 已废弃，待删除\n            moveSelected(args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow, evnt) {\n                handleMoveSelected(evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow);\n            },\n            handleCellMousedownEvent\n        };\n        return keyboardMethods;\n    }\n});\n", "import { nextTick } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../../ui';\nimport { eqEmptyValue, getFuncText } from '../../../ui/src/utils';\nimport { scrollToView } from '../../../ui/src/dom';\nimport { handleFieldOrColumn, getRowid } from '../../src/util';\nimport { warnLog, errLog } from '../../../ui/src/log';\nconst { getConfig, validators, hooks } = VxeUI;\n/**\n * 校验规则\n */\nclass Rule {\n    constructor(rule) {\n        Object.assign(this, {\n            $options: rule,\n            required: rule.required,\n            min: rule.min,\n            max: rule.max,\n            type: rule.type,\n            pattern: rule.pattern,\n            validator: rule.validator,\n            trigger: rule.trigger,\n            maxWidth: rule.maxWidth\n        });\n    }\n    /**\n     * 获取校验不通过的消息\n     * 支持国际化翻译\n     */\n    get content() {\n        return getFuncText(this.$options.content || this.$options.message);\n    }\n    get message() {\n        return this.content;\n    }\n}\n// 如果存在 pattern，判断正则\nfunction validREValue(pattern, val) {\n    if (pattern && !(XEUtils.isRegExp(pattern) ? pattern : new RegExp(pattern)).test(val)) {\n        return false;\n    }\n    return true;\n}\n// 如果存在 max，判断最大值\nfunction validMaxValue(max, num) {\n    if (!XEUtils.eqNull(max) && num > XEUtils.toNumber(max)) {\n        return false;\n    }\n    return true;\n}\n// 如果存在 min，判断最小值\nfunction validMinValue(min, num) {\n    if (!XEUtils.eqNull(min) && num < XEUtils.toNumber(min)) {\n        return false;\n    }\n    return true;\n}\nfunction validRuleValue(rule, val, required) {\n    const { type, min, max, pattern } = rule;\n    const isArrType = type === 'array';\n    const isNumType = type === 'number';\n    const isStrType = type === 'string';\n    const strVal = `${val}`;\n    if (!validREValue(pattern, strVal)) {\n        return false;\n    }\n    if (isArrType) {\n        if (!XEUtils.isArray(val)) {\n            return false;\n        }\n        if (required) {\n            if (!val.length) {\n                return false;\n            }\n        }\n        if (!validMinValue(min, val.length)) {\n            return false;\n        }\n        if (!validMaxValue(max, val.length)) {\n            return false;\n        }\n    }\n    else if (isNumType) {\n        const numVal = Number(val);\n        if (isNaN(numVal)) {\n            return false;\n        }\n        if (!validMinValue(min, numVal)) {\n            return false;\n        }\n        if (!validMaxValue(max, numVal)) {\n            return false;\n        }\n    }\n    else {\n        if (isStrType) {\n            if (!XEUtils.isString(val)) {\n                return false;\n            }\n        }\n        if (required) {\n            if (!strVal) {\n                return false;\n            }\n        }\n        if (!validMinValue(min, strVal.length)) {\n            return false;\n        }\n        if (!validMaxValue(max, strVal.length)) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction checkRuleStatus(rule, val) {\n    const { required } = rule;\n    const isEmptyVal = XEUtils.isArray(val) ? !val.length : eqEmptyValue(val);\n    if (required) {\n        if (isEmptyVal) {\n            return false;\n        }\n        if (!validRuleValue(rule, val, required)) {\n            return false;\n        }\n    }\n    else {\n        if (!isEmptyVal) {\n            if (!validRuleValue(rule, val, required)) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nconst tableValidatorMethodKeys = ['fullValidate', 'validate', 'fullValidateField', 'validateField', 'clearValidate'];\nhooks.add('tableValidatorModule', {\n    setupTable($xeTable) {\n        const { props, reactData, internalData } = $xeTable;\n        const { refValidTooltip } = $xeTable.getRefMaps();\n        const { computeValidOpts, computeTreeOpts, computeEditOpts, computeAggregateOpts } = $xeTable.getComputeMaps();\n        let validatorMethods = {};\n        let validatorPrivateMethods = {};\n        let validRuleErr;\n        /**\n         * 聚焦到校验通过的单元格并弹出校验错误提示\n         */\n        const handleValidError = (params) => {\n            return new Promise(resolve => {\n                const validOpts = computeValidOpts.value;\n                if (validOpts.autoPos === false) {\n                    $xeTable.dispatchEvent('valid-error', params, null);\n                    resolve();\n                }\n                else {\n                    $xeTable.handleEdit(params, { type: 'valid-error', trigger: 'call' }).then(() => {\n                        resolve(validatorPrivateMethods.showValidTooltip(params));\n                    });\n                }\n            });\n        };\n        const handleErrMsgMode = (validErrMaps) => {\n            const validOpts = computeValidOpts.value;\n            if (validOpts.msgMode === 'single') {\n                const keys = Object.keys(validErrMaps);\n                const resMaps = {};\n                if (keys.length) {\n                    const firstKey = keys[0];\n                    resMaps[firstKey] = validErrMaps[firstKey];\n                }\n                return resMaps;\n            }\n            return validErrMaps;\n        };\n        /**\n         * 对表格数据进行校验\n         * 如果不指定数据，则默认只校验临时变动的数据，例如新增或修改\n         * 如果传 true 则校验当前表格数据\n         * 如果传 row 指定行记录，则只验证传入的行\n         * 如果传 rows 为多行记录，则只验证传入的行\n         * 如果只传 callback 否则默认验证整个表格数据\n         * 返回 Promise 对象，或者使用回调方式\n         */\n        const beginValidate = (rows, cols, cb, isFull) => {\n            const validRest = {};\n            const { editRules, treeConfig } = props;\n            const { isRowGroupStatus } = reactData;\n            const { afterFullData, pendingRowMaps, removeRowMaps } = internalData;\n            const treeOpts = computeTreeOpts.value;\n            const aggregateOpts = computeAggregateOpts.value;\n            const validOpts = computeValidOpts.value;\n            let validList;\n            if (rows === true) {\n                validList = afterFullData;\n            }\n            else if (rows) {\n                if (XEUtils.isFunction(rows)) {\n                    cb = rows;\n                }\n                else {\n                    validList = XEUtils.isArray(rows) ? rows : [rows];\n                }\n            }\n            if (!validList) {\n                if ($xeTable.getInsertRecords) {\n                    validList = $xeTable.getInsertRecords().concat($xeTable.getUpdateRecords());\n                }\n                else {\n                    validList = [];\n                }\n            }\n            const rowValidErrs = [];\n            internalData._lastCallTime = Date.now();\n            validRuleErr = false; // 如果为快速校验，当存在某列校验不通过时将终止执行\n            validatorMethods.clearValidate();\n            const validErrMaps = {};\n            if (editRules) {\n                const columns = cols && cols.length ? cols : $xeTable.getColumns();\n                const handleVaild = (row) => {\n                    const rowid = getRowid($xeTable, row);\n                    // 是否删除\n                    if (removeRowMaps[rowid]) {\n                        return;\n                    }\n                    // 是否标记删除\n                    if (pendingRowMaps[rowid]) {\n                        return;\n                    }\n                    if ($xeTable.isAggregateRecord(row)) {\n                        return;\n                    }\n                    if (isFull || !validRuleErr) {\n                        const colVailds = [];\n                        columns.forEach((column) => {\n                            const field = XEUtils.isString(column) ? column : column.field;\n                            if ((isFull || !validRuleErr) && XEUtils.has(editRules, field)) {\n                                colVailds.push(validatorPrivateMethods.validCellRules('all', row, column)\n                                    .catch(({ rule, rules }) => {\n                                    const rest = {\n                                        rule,\n                                        rules,\n                                        rowIndex: $xeTable.getRowIndex(row),\n                                        row,\n                                        columnIndex: $xeTable.getColumnIndex(column),\n                                        column,\n                                        field,\n                                        $table: $xeTable\n                                    };\n                                    if (!validRest[field]) {\n                                        validRest[field] = [];\n                                    }\n                                    validErrMaps[`${getRowid($xeTable, row)}:${column.id}`] = {\n                                        column,\n                                        row,\n                                        rule,\n                                        content: rule.content\n                                    };\n                                    validRest[field].push(rest);\n                                    if (!isFull) {\n                                        validRuleErr = true;\n                                        return Promise.reject(rest);\n                                    }\n                                }));\n                            }\n                        });\n                        rowValidErrs.push(Promise.all(colVailds));\n                    }\n                };\n                if (isRowGroupStatus) {\n                    XEUtils.eachTree(validList, handleVaild, { children: aggregateOpts.mapChildrenField });\n                }\n                else if (treeConfig) {\n                    const childrenField = treeOpts.children || treeOpts.childrenField;\n                    XEUtils.eachTree(validList, handleVaild, { children: childrenField });\n                }\n                else {\n                    validList.forEach(handleVaild);\n                }\n                return Promise.all(rowValidErrs).then(() => {\n                    const ruleProps = Object.keys(validRest);\n                    reactData.validErrorMaps = handleErrMsgMode(validErrMaps);\n                    return nextTick().then(() => {\n                        if (ruleProps.length) {\n                            return Promise.reject(validRest[ruleProps[0]][0]);\n                        }\n                        if (cb) {\n                            cb();\n                        }\n                    });\n                }).catch(firstErrParams => {\n                    return new Promise((resolve, reject) => {\n                        const finish = () => {\n                            nextTick(() => {\n                                if (cb) {\n                                    cb(validRest);\n                                    resolve();\n                                }\n                                else {\n                                    if (getConfig().validToReject === 'obsolete') {\n                                        // 已废弃，校验失败将不会执行catch\n                                        reject(validRest);\n                                    }\n                                    else {\n                                        resolve(validRest);\n                                    }\n                                }\n                            });\n                        };\n                        const posAndFinish = () => {\n                            firstErrParams.cell = $xeTable.getCellElement(firstErrParams.row, firstErrParams.column);\n                            scrollToView(firstErrParams.cell);\n                            handleValidError(firstErrParams).then(finish);\n                        };\n                        /**\n                         * 当校验不通过时\n                         * 将表格滚动到可视区\n                         * 由于提示信息至少需要占一行，定位向上偏移一行\n                         */\n                        if (validOpts.autoPos === false) {\n                            finish();\n                        }\n                        else {\n                            const row = firstErrParams.row;\n                            const column = firstErrParams.column;\n                            $xeTable.scrollToRow(row, column).then(posAndFinish);\n                        }\n                    });\n                });\n            }\n            else {\n                reactData.validErrorMaps = {};\n            }\n            return nextTick().then(() => {\n                if (cb) {\n                    cb();\n                }\n            });\n        };\n        validatorMethods = {\n            /**\n             * 完整校验行，和 validate 的区别就是会给有效数据中的每一行进行校验\n             */\n            fullValidate(rows, cb) {\n                if (XEUtils.isFunction(cb)) {\n                    warnLog('vxe.error.notValidators', ['fullValidate(rows, callback)', 'fullValidate(rows)']);\n                }\n                return beginValidate(rows, null, cb, true);\n            },\n            /**\n             * 快速校验行，如果存在记录不通过的记录，则返回不再继续校验（异步校验除外）\n             */\n            validate(rows, cb) {\n                return beginValidate(rows, null, cb);\n            },\n            /**\n             * 完整校验单元格，和 validateField 的区别就是会给有效数据中的每一行进行校验\n             */\n            fullValidateField(rows, fieldOrColumn) {\n                const colList = (XEUtils.isArray(fieldOrColumn) ? fieldOrColumn : (fieldOrColumn ? [fieldOrColumn] : [])).map(column => handleFieldOrColumn($xeTable, column));\n                if (colList.length) {\n                    return beginValidate(rows, colList, null, true);\n                }\n                return nextTick();\n            },\n            /**\n             * 快速校验单元格，如果存在记录不通过的记录，则返回不再继续校验（异步校验除外）\n             */\n            validateField(rows, fieldOrColumn) {\n                const colList = (XEUtils.isArray(fieldOrColumn) ? fieldOrColumn : (fieldOrColumn ? [fieldOrColumn] : [])).map(column => handleFieldOrColumn($xeTable, column));\n                if (colList.length) {\n                    return beginValidate(rows, colList, null);\n                }\n                return nextTick();\n            },\n            clearValidate(rows, fieldOrColumn) {\n                const { validErrorMaps } = reactData;\n                const validTip = refValidTooltip.value;\n                const validOpts = computeValidOpts.value;\n                const rowList = XEUtils.isArray(rows) ? rows : (rows ? [rows] : []);\n                const colList = (XEUtils.isArray(fieldOrColumn) ? fieldOrColumn : (fieldOrColumn ? [fieldOrColumn] : [])).map(column => handleFieldOrColumn($xeTable, column));\n                let validErrMaps = {};\n                if (validTip && validTip.reactData.visible) {\n                    validTip.close();\n                }\n                // 如果是单个提示模式\n                if (validOpts.msgMode === 'single') {\n                    reactData.validErrorMaps = {};\n                    return nextTick();\n                }\n                if (rowList.length && colList.length) {\n                    validErrMaps = Object.assign({}, validErrorMaps);\n                    rowList.forEach(row => {\n                        colList.forEach((column) => {\n                            const validKey = `${getRowid($xeTable, row)}:${column.id}`;\n                            if (validErrMaps[validKey]) {\n                                delete validErrMaps[validKey];\n                            }\n                        });\n                    });\n                }\n                else if (rowList.length) {\n                    const rowIdList = rowList.map(row => `${getRowid($xeTable, row)}`);\n                    XEUtils.each(validErrorMaps, (item, key) => {\n                        if (rowIdList.indexOf(key.split(':')[0]) > -1) {\n                            validErrMaps[key] = item;\n                        }\n                    });\n                }\n                else if (colList.length) {\n                    const colidList = colList.map(column => `${column.id}`);\n                    XEUtils.each(validErrorMaps, (item, key) => {\n                        if (colidList.indexOf(key.split(':')[1]) > -1) {\n                            validErrMaps[key] = item;\n                        }\n                    });\n                }\n                reactData.validErrorMaps = validErrMaps;\n                return nextTick();\n            }\n        };\n        validatorPrivateMethods = {\n            /**\n             * 校验数据\n             * 按表格行、列顺序依次校验（同步或异步）\n             * 校验规则根据索引顺序依次校验，如果是异步则会等待校验完成才会继续校验下一列\n             * 如果校验失败则，触发回调或者Promise<不通过列的错误消息>\n             * 如果是传回调方式这返回一个校验不通过列的错误消息\n             *\n             * rule 配置：\n             *  required=Boolean 是否必填\n             *  min=Number 最小长度\n             *  max=Number 最大长度\n             *  validator=Function({ cellValue, rule, rules, row, column, rowIndex, columnIndex }) 自定义校验，接收一个 Promise\n             *  trigger=blur|change 触发方式（除非特殊场景，否则默认为空就行）\n             */\n            validCellRules(validType, row, column, val) {\n                const $xeGrid = $xeTable.xeGrid;\n                const { editRules } = props;\n                const { field } = column;\n                const errorRules = [];\n                const syncValidList = [];\n                if (field && editRules) {\n                    const rules = XEUtils.get(editRules, field);\n                    if (rules) {\n                        const cellValue = XEUtils.isUndefined(val) ? XEUtils.get(row, field) : val;\n                        rules.forEach((rule) => {\n                            const { trigger, validator } = rule;\n                            if (validType === 'all' || !trigger || validType === trigger) {\n                                if (validator) {\n                                    const validParams = {\n                                        cellValue,\n                                        rule,\n                                        rules,\n                                        row,\n                                        rowIndex: $xeTable.getRowIndex(row),\n                                        column,\n                                        columnIndex: $xeTable.getColumnIndex(column),\n                                        field: column.field,\n                                        $table: $xeTable,\n                                        $grid: $xeGrid\n                                    };\n                                    let customValid;\n                                    if (XEUtils.isString(validator)) {\n                                        const gvItem = validators.get(validator);\n                                        if (gvItem) {\n                                            const tcvMethod = gvItem.tableCellValidatorMethod || gvItem.cellValidatorMethod;\n                                            if (tcvMethod) {\n                                                customValid = tcvMethod(validParams);\n                                            }\n                                            else {\n                                                errLog('vxe.error.notValidators', [validator]);\n                                            }\n                                        }\n                                        else {\n                                            errLog('vxe.error.notValidators', [validator]);\n                                        }\n                                    }\n                                    else {\n                                        customValid = validator(validParams);\n                                    }\n                                    if (customValid) {\n                                        if (XEUtils.isError(customValid)) {\n                                            validRuleErr = true;\n                                            errorRules.push(new Rule({ type: 'custom', trigger, content: customValid.message, rule: new Rule(rule) }));\n                                        }\n                                        else if (customValid.catch) {\n                                            // 如果为异步校验（注：异步校验是并发无序的）\n                                            syncValidList.push(customValid.catch((e) => {\n                                                validRuleErr = true;\n                                                errorRules.push(new Rule({ type: 'custom', trigger, content: e && e.message ? e.message : (rule.content || rule.message), rule: new Rule(rule) }));\n                                            }));\n                                        }\n                                    }\n                                }\n                                else {\n                                    if (!checkRuleStatus(rule, cellValue)) {\n                                        validRuleErr = true;\n                                        errorRules.push(new Rule(rule));\n                                    }\n                                }\n                            }\n                        });\n                    }\n                }\n                return Promise.all(syncValidList).then(() => {\n                    if (errorRules.length) {\n                        const rest = { rules: errorRules, rule: errorRules[0] };\n                        return Promise.reject(rest);\n                    }\n                });\n            },\n            hasCellRules(type, row, column) {\n                const { editRules } = props;\n                const { field } = column;\n                if (field && editRules) {\n                    const rules = XEUtils.get(editRules, field);\n                    return rules && !!XEUtils.find(rules, rule => type === 'all' || !rule.trigger || type === rule.trigger);\n                }\n                return false;\n            },\n            /**\n             * 触发校验\n             */\n            triggerValidate(type) {\n                const { editConfig, editRules } = props;\n                const { editStore } = reactData;\n                const { actived } = editStore;\n                const editOpts = computeEditOpts.value;\n                const validOpts = computeValidOpts.value;\n                // 检查清除校验消息\n                if (editRules && validOpts.msgMode === 'single') {\n                    reactData.validErrorMaps = {};\n                }\n                // 校验单元格\n                if (editConfig && editRules && actived.row) {\n                    const { row, column, cell } = actived.args;\n                    if (validatorPrivateMethods.hasCellRules(type, row, column)) {\n                        return validatorPrivateMethods.validCellRules(type, row, column).then(() => {\n                            if (editOpts.mode === 'row') {\n                                validatorMethods.clearValidate(row, column);\n                            }\n                        }).catch(({ rule }) => {\n                            // 如果校验不通过与触发方式一致，则聚焦提示错误，否则跳过并不作任何处理\n                            if (!rule.trigger || type === rule.trigger) {\n                                const rest = { rule, row, column, cell };\n                                validatorPrivateMethods.showValidTooltip(rest);\n                                return Promise.reject(rest);\n                            }\n                            return Promise.resolve();\n                        });\n                    }\n                }\n                return Promise.resolve();\n            },\n            /**\n             * 弹出校验错误提示\n             */\n            showValidTooltip(params) {\n                const { height } = props;\n                const { tableData, validStore, validErrorMaps } = reactData;\n                const { rule, row, column, cell } = params;\n                const validOpts = computeValidOpts.value;\n                const validTip = refValidTooltip.value;\n                const content = rule.content;\n                validStore.visible = true;\n                if (validOpts.msgMode === 'single') {\n                    reactData.validErrorMaps = {\n                        [`${getRowid($xeTable, row)}:${column.id}`]: {\n                            column,\n                            row,\n                            rule,\n                            content\n                        }\n                    };\n                }\n                else {\n                    reactData.validErrorMaps = Object.assign({}, validErrorMaps, {\n                        [`${getRowid($xeTable, row)}:${column.id}`]: {\n                            column,\n                            row,\n                            rule,\n                            content\n                        }\n                    });\n                }\n                $xeTable.dispatchEvent('valid-error', params, null);\n                if (validTip) {\n                    if (validTip && (validOpts.message === 'tooltip' || (validOpts.message === 'default' && !height && tableData.length < 2))) {\n                        return validTip.open(cell, content);\n                    }\n                }\n                return nextTick();\n            }\n        };\n        return Object.assign(Object.assign({}, validatorMethods), validatorPrivateMethods);\n    },\n    setupGrid($xeGrid) {\n        return $xeGrid.extendTableMethods(tableValidatorMethodKeys);\n    }\n});\n", "import { nextTick } from 'vue';\nimport { VxeUI } from '../../../ui';\nimport XEUtils from 'xe-utils';\nconst tableCustomMethodKeys = ['openCustom', 'closeCustom', 'toggleCustom', 'saveCustom', 'cancelCustom', 'resetCustom', 'toggleCustomAllCheckbox', 'setCustomAllCheckbox'];\nVxeUI.hooks.add('tableCustomModule', {\n    setupTable($xeTable) {\n        const { reactData, internalData } = $xeTable;\n        const { computeCustomOpts, computeRowGroupFields } = $xeTable.getComputeMaps();\n        const { refElem } = $xeTable.getRefMaps();\n        const $xeGrid = $xeTable.xeGrid;\n        const calcMaxHeight = () => {\n            const { customStore } = reactData;\n            const el = refElem.value;\n            // 判断面板不能大于表格高度\n            let tableHeight = 0;\n            if (el) {\n                tableHeight = el.clientHeight - 28;\n            }\n            customStore.maxHeight = Math.max(88, tableHeight);\n        };\n        const openCustom = () => {\n            const { initStore, customStore } = reactData;\n            customStore.visible = true;\n            initStore.custom = true;\n            handleUpdateCustomColumn();\n            checkCustomStatus();\n            calcMaxHeight();\n            return nextTick().then(() => calcMaxHeight());\n        };\n        const handleUpdateCustomColumn = () => {\n            const { customStore } = reactData;\n            const { collectColumn } = internalData;\n            if (customStore.visible) {\n                const sortMaps = {};\n                const fixedMaps = {};\n                const visibleMaps = {};\n                XEUtils.eachTree(collectColumn, column => {\n                    const colid = column.getKey();\n                    column.renderFixed = column.fixed;\n                    column.renderVisible = column.visible;\n                    column.renderResizeWidth = column.renderWidth;\n                    sortMaps[colid] = column.renderSortNumber;\n                    fixedMaps[colid] = column.fixed;\n                    visibleMaps[colid] = column.visible;\n                });\n                customStore.oldSortMaps = sortMaps;\n                customStore.oldFixedMaps = fixedMaps;\n                customStore.oldVisibleMaps = visibleMaps;\n                reactData.customColumnList = collectColumn.slice(0);\n            }\n        };\n        const closeCustom = () => {\n            const { customStore } = reactData;\n            const customOpts = computeCustomOpts.value;\n            if (customStore.visible) {\n                customStore.visible = false;\n                if (!customOpts.immediate) {\n                    $xeTable.handleCustom();\n                }\n            }\n            return nextTick();\n        };\n        const toggleCustom = () => {\n            const { customStore } = reactData;\n            if (customStore.visible) {\n                return closeCustom();\n            }\n            return openCustom();\n        };\n        const saveCustom = () => {\n            const { customColumnList, aggHandleFields, rowGroupList } = reactData;\n            const customOpts = computeCustomOpts.value;\n            const { allowVisible, allowSort, allowFixed, allowResizable, allowGroup, allowValues } = customOpts;\n            XEUtils.eachTree(customColumnList, (column, index, items, path, parentColumn) => {\n                if (parentColumn) {\n                    // 更新子列信息\n                    column.fixed = parentColumn.fixed;\n                }\n                else {\n                    if (allowSort) {\n                        const sortIndex = index + 1;\n                        column.renderSortNumber = sortIndex;\n                    }\n                    if (allowFixed) {\n                        column.fixed = column.renderFixed;\n                    }\n                }\n                if (allowResizable) {\n                    if (column.renderVisible && (!column.children || column.children.length)) {\n                        if (column.renderResizeWidth !== column.renderWidth) {\n                            column.resizeWidth = column.renderResizeWidth;\n                            column.renderWidth = column.renderResizeWidth;\n                        }\n                    }\n                }\n                if (allowVisible) {\n                    column.visible = column.renderVisible;\n                }\n                if (allowGroup && allowValues) {\n                    column.aggFunc = column.renderAggFn;\n                }\n            });\n            reactData.isCustomStatus = true;\n            return $xeTable.saveCustomStore('confirm').then(() => {\n                if (allowGroup && allowValues && $xeTable.handlePivotTableAggregateData) {\n                    if (rowGroupList.length !== aggHandleFields.length || rowGroupList.some((conf, i) => conf.field !== aggHandleFields[i])) {\n                        // 更新数据分组\n                        if (aggHandleFields.length) {\n                            $xeTable.setRowGroups(aggHandleFields);\n                        }\n                        else {\n                            $xeTable.clearRowGroups();\n                        }\n                    }\n                    else {\n                        // 更新聚合函数\n                        $xeTable.handleUpdateAggData();\n                    }\n                }\n            });\n        };\n        const cancelCustom = () => {\n            const { customColumnList, customStore } = reactData;\n            const { oldSortMaps, oldFixedMaps, oldVisibleMaps } = customStore;\n            const customOpts = computeCustomOpts.value;\n            const { allowVisible, allowSort, allowFixed, allowResizable } = customOpts;\n            XEUtils.eachTree(customColumnList, column => {\n                const colid = column.getKey();\n                const visible = !!oldVisibleMaps[colid];\n                const fixed = oldFixedMaps[colid] || '';\n                if (allowVisible) {\n                    column.renderVisible = visible;\n                    column.visible = visible;\n                }\n                if (allowFixed) {\n                    column.renderFixed = fixed;\n                    column.fixed = fixed;\n                }\n                if (allowSort) {\n                    column.renderSortNumber = oldSortMaps[colid] || 0;\n                }\n                if (allowResizable) {\n                    column.renderResizeWidth = column.renderWidth;\n                }\n            }, { children: 'children' });\n            return nextTick();\n        };\n        const setCustomAllCheckbox = (checked) => {\n            const { customStore } = reactData;\n            const { customColumnList } = reactData;\n            const customOpts = computeCustomOpts.value;\n            const { checkMethod, visibleMethod } = customOpts;\n            const isAll = !!checked;\n            if (customOpts.immediate) {\n                XEUtils.eachTree(customColumnList, (column) => {\n                    if (visibleMethod && !visibleMethod({ $table: $xeTable, column })) {\n                        return;\n                    }\n                    if (checkMethod && !checkMethod({ $table: $xeTable, column })) {\n                        return;\n                    }\n                    column.visible = isAll;\n                    column.renderVisible = isAll;\n                    column.halfVisible = false;\n                });\n                customStore.isAll = isAll;\n                reactData.isCustomStatus = true;\n                $xeTable.handleCustom();\n                $xeTable.saveCustomStore('update:visible');\n            }\n            else {\n                XEUtils.eachTree(customColumnList, (column) => {\n                    if (visibleMethod && !visibleMethod({ $table: $xeTable, column })) {\n                        return;\n                    }\n                    if (checkMethod && !checkMethod({ $table: $xeTable, column })) {\n                        return;\n                    }\n                    column.renderVisible = isAll;\n                    column.halfVisible = false;\n                });\n                customStore.isAll = isAll;\n            }\n            $xeTable.checkCustomStatus();\n            return nextTick();\n        };\n        const customMethods = {\n            openCustom,\n            closeCustom,\n            toggleCustom,\n            saveCustom,\n            cancelCustom,\n            resetCustom(options) {\n                const { rowGroupList } = reactData;\n                const { collectColumn } = internalData;\n                const customOpts = computeCustomOpts.value;\n                const { checkMethod } = customOpts;\n                const opts = Object.assign({\n                    visible: true,\n                    resizable: options === true,\n                    fixed: options === true,\n                    sort: options === true,\n                    aggFunc: options === true\n                }, options);\n                XEUtils.eachTree(collectColumn, (column) => {\n                    if (opts.resizable) {\n                        column.resizeWidth = 0;\n                    }\n                    if (opts.fixed) {\n                        column.fixed = column.defaultFixed;\n                    }\n                    if (opts.sort) {\n                        column.renderSortNumber = column.sortNumber;\n                    }\n                    if (!checkMethod || checkMethod({ $table: $xeTable, column })) {\n                        column.visible = column.defaultVisible;\n                    }\n                    if (opts.aggFunc) {\n                        column.aggFunc = column.defaultAggFunc;\n                        column.renderAggFn = column.defaultAggFunc;\n                    }\n                    column.renderResizeWidth = column.renderWidth;\n                });\n                reactData.isCustomStatus = false;\n                $xeTable.saveCustomStore('reset');\n                return $xeTable.handleCustom().then(() => {\n                    if (opts.aggFunc && $xeTable.handlePivotTableAggregateData) {\n                        const rowGroupFields = computeRowGroupFields.value;\n                        if (rowGroupFields ? rowGroupFields.length : rowGroupList.length) {\n                            if (rowGroupFields && rowGroupFields.length) {\n                                $xeTable.setRowGroups(rowGroupFields);\n                            }\n                            else {\n                                $xeTable.clearRowGroups();\n                            }\n                        }\n                        else {\n                            $xeTable.handleUpdateAggData();\n                        }\n                    }\n                });\n            },\n            toggleCustomAllCheckbox() {\n                const { customStore } = reactData;\n                const isAll = !customStore.isAll;\n                return setCustomAllCheckbox(isAll);\n            },\n            setCustomAllCheckbox\n        };\n        const checkCustomStatus = () => {\n            const { customStore } = reactData;\n            const { collectColumn } = internalData;\n            const customOpts = computeCustomOpts.value;\n            const { checkMethod } = customOpts;\n            customStore.isAll = collectColumn.every((column) => (checkMethod ? !checkMethod({ $table: $xeTable, column }) : false) || column.renderVisible);\n            customStore.isIndeterminate = !customStore.isAll && collectColumn.some((column) => (!checkMethod || checkMethod({ $table: $xeTable, column })) && (column.renderVisible || column.halfVisible));\n        };\n        const emitCustomEvent = (type, evnt) => {\n            const comp = $xeGrid || $xeTable;\n            comp.dispatchEvent('custom', { type }, evnt);\n        };\n        const customPrivateMethods = {\n            checkCustomStatus,\n            emitCustomEvent,\n            triggerCustomEvent(evnt) {\n                const reactData = $xeTable.reactData;\n                const { customStore } = reactData;\n                if (customStore.visible) {\n                    closeCustom();\n                    emitCustomEvent('close', evnt);\n                }\n                else {\n                    customStore.btnEl = evnt.target;\n                    openCustom();\n                    emitCustomEvent('open', evnt);\n                }\n            },\n            customOpenEvent(evnt) {\n                const reactData = $xeTable.reactData;\n                const { customStore } = reactData;\n                if (!customStore.visible) {\n                    customStore.activeBtn = true;\n                    customStore.btnEl = evnt.target;\n                    $xeTable.openCustom();\n                    $xeTable.emitCustomEvent('open', evnt);\n                }\n            },\n            customCloseEvent(evnt) {\n                const reactData = $xeTable.reactData;\n                const { customStore } = reactData;\n                if (customStore.visible) {\n                    customStore.activeBtn = false;\n                    $xeTable.closeCustom();\n                    $xeTable.emitCustomEvent('close', evnt);\n                }\n            },\n            handleUpdateCustomColumn\n        };\n        return Object.assign(Object.assign({}, customMethods), customPrivateMethods);\n    },\n    setupGrid($xeGrid) {\n        return $xeGrid.extendTableMethods(tableCustomMethodKeys);\n    }\n});\n", "import { h, resolveComponent } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../ui';\nimport { getCellValue, setCellValue } from '../../table/src/util';\nimport { getFuncText, formatText, isEmptyValue } from '../../ui/src/utils';\nimport { getOnName, getModelEvent, getChangeEvent } from '../../ui/src/vn';\nimport { errLog } from '../../ui/src/log';\nconst { getConfig, renderer, getI18n, getComponent } = VxeUI;\nconst componentDefaultModelProp = 'modelValue';\nconst defaultCompProps = {};\nfunction handleDefaultValue(value, defaultVal, initVal) {\n    return XEUtils.eqNull(value) ? (XEUtils.eqNull(defaultVal) ? initVal : defaultVal) : value;\n}\nfunction parseDate(value, props) {\n    return value && props.valueFormat ? XEUtils.toStringDate(value, props.valueFormat) : value;\n}\nfunction getFormatDate(value, props, defaultFormat) {\n    const { dateConfig = {} } = props;\n    return XEUtils.toDateString(parseDate(value, props), dateConfig.labelFormat || defaultFormat);\n}\nfunction getLabelFormatDate(value, props) {\n    return getFormatDate(value, props, getI18n(`vxe.input.date.labelFormat.${props.type || 'date'}`));\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction getOldComponentName(name) {\n    return `vxe-${name.replace('$', '')}`;\n}\nfunction getDefaultComponent({ name }) {\n    return getComponent(name);\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction getOldComponent({ name }) {\n    return resolveComponent(getOldComponentName(name));\n}\nfunction handleConfirmFilter(params, checked, option) {\n    const { $panel } = params;\n    $panel.changeOption({}, checked, option);\n}\nfunction getNativeAttrs(renderOpts) {\n    let { name, attrs } = renderOpts;\n    if (name === 'input') {\n        attrs = Object.assign({ type: 'text' }, attrs);\n    }\n    return attrs;\n}\nfunction getInputImmediateModel(renderOpts) {\n    const { name, immediate, props } = renderOpts;\n    if (!immediate) {\n        if (name === 'VxeInput' || name === '$input') {\n            const { type } = props || {};\n            return !(!type || type === 'text' || type === 'number' || type === 'integer' || type === 'float');\n        }\n        if (name === 'input' || name === 'textarea' || name === '$textarea') {\n            return false;\n        }\n        return true;\n    }\n    return immediate;\n}\nfunction getCellEditProps(renderOpts, params, value, defaultProps) {\n    return XEUtils.assign({ immediate: getInputImmediateModel(renderOpts) }, defaultCompProps, defaultProps, renderOpts.props, { [componentDefaultModelProp]: value });\n}\nfunction getCellEditFilterProps(renderOpts, params, value, defaultProps) {\n    return XEUtils.assign({}, defaultCompProps, defaultProps, renderOpts.props, { [componentDefaultModelProp]: value });\n}\nfunction isImmediateCell(renderOpts, params) {\n    return params.$type === 'cell' || getInputImmediateModel(renderOpts);\n}\nfunction getCellLabelVNs(renderOpts, params, cellLabel, opts) {\n    const { placeholder } = renderOpts;\n    return [\n        h('span', {\n            class: ['vxe-cell--label', opts ? opts.class : '']\n        }, placeholder && isEmptyValue(cellLabel)\n            ? [\n                h('span', {\n                    class: 'vxe-cell--placeholder'\n                }, formatText(getFuncText(placeholder), 1))\n            ]\n            : formatText(cellLabel, 1))\n    ];\n}\n/**\n * 原生事件处理\n * @param renderOpts\n * @param params\n * @param modelFunc\n * @param changeFunc\n */\nfunction getNativeElementOns(renderOpts, params, eFns) {\n    const { events } = renderOpts;\n    const modelEvent = getModelEvent(renderOpts);\n    const changeEvent = getChangeEvent(renderOpts);\n    const { model: modelFunc, change: changeFunc, blur: blurFunc } = eFns || {};\n    const isSameEvent = changeEvent === modelEvent;\n    const ons = {};\n    if (events) {\n        XEUtils.objectEach(events, (func, key) => {\n            ons[getOnName(key)] = function (...args) {\n                func(params, ...args);\n            };\n        });\n    }\n    if (modelFunc) {\n        ons[getOnName(modelEvent)] = function (targetEvnt) {\n            modelFunc(targetEvnt);\n            if (isSameEvent && changeFunc) {\n                changeFunc(targetEvnt);\n            }\n            if (events && events[modelEvent]) {\n                events[modelEvent](params, targetEvnt);\n            }\n        };\n    }\n    if (!isSameEvent && changeFunc) {\n        ons[getOnName(changeEvent)] = function (evnt) {\n            changeFunc(evnt);\n            if (events && events[changeEvent]) {\n                events[changeEvent](params, evnt);\n            }\n        };\n    }\n    if (blurFunc) {\n        ons[getOnName(blurEvent)] = function (evnt) {\n            blurFunc(evnt);\n            if (events && events[blurEvent]) {\n                events[blurEvent](params, evnt);\n            }\n        };\n    }\n    return ons;\n}\nconst blurEvent = 'blur';\n/**\n * 组件事件处理\n * @param renderOpts\n * @param params\n * @param modelFunc\n * @param changeFunc\n */\nfunction getComponentOns(renderOpts, params, eFns, eventOns) {\n    const { events } = renderOpts;\n    const modelEvent = getModelEvent(renderOpts);\n    const changeEvent = getChangeEvent(renderOpts);\n    const { model: modelFunc, change: changeFunc, blur: blurFunc } = eFns || {};\n    const ons = {};\n    XEUtils.objectEach(events, (func, key) => {\n        ons[getOnName(key)] = function (...args) {\n            if (!XEUtils.isFunction(func)) {\n                errLog('vxe.error.errFunc', [func]);\n            }\n            func(params, ...args);\n        };\n    });\n    if (modelFunc) {\n        ons[getOnName(modelEvent)] = function (targetEvnt) {\n            modelFunc(targetEvnt);\n            if (events && events[modelEvent]) {\n                events[modelEvent](params, targetEvnt);\n            }\n        };\n    }\n    if (changeFunc) {\n        ons[getOnName(changeEvent)] = function (...args) {\n            changeFunc(...args);\n            if (events && events[changeEvent]) {\n                events[changeEvent](params, ...args);\n            }\n        };\n    }\n    if (blurFunc) {\n        ons[getOnName(blurEvent)] = function (...args) {\n            blurFunc(...args);\n            if (events && events[blurEvent]) {\n                events[blurEvent](params, ...args);\n            }\n        };\n    }\n    return eventOns ? Object.assign(ons, eventOns) : ons;\n}\nfunction getEditOns(renderOpts, params) {\n    const { $table, row, column } = params;\n    const { name } = renderOpts;\n    const { model } = column;\n    const isImmediate = isImmediateCell(renderOpts, params);\n    return getComponentOns(renderOpts, params, {\n        model(cellValue) {\n            // 处理 model 值双向绑定\n            model.update = true;\n            model.value = cellValue;\n            if (isImmediate) {\n                setCellValue(row, column, cellValue);\n            }\n        },\n        change(eventParams) {\n            // 处理 change 事件相关逻辑\n            if (!isImmediate && name && (['VxeInput', 'VxeNumberInput', 'VxeTextarea', '$input', '$textarea'].includes(name))) {\n                const cellValue = eventParams.value;\n                model.update = true;\n                model.value = cellValue;\n                $table.updateStatus(params, cellValue);\n            }\n            else {\n                $table.updateStatus(params);\n            }\n        },\n        blur() {\n            if (isImmediate) {\n                $table.handleCellRuleUpdateStatus('blur', params);\n            }\n            else {\n                $table.handleCellRuleUpdateStatus('blur', params, model.value);\n            }\n        }\n    });\n}\nfunction getFilterOns(renderOpts, params, option) {\n    return getComponentOns(renderOpts, params, {\n        model(value) {\n            // 处理 model 值双向绑定\n            option.data = value;\n        },\n        change() {\n            handleConfirmFilter(params, !XEUtils.eqNull(option.data), option);\n        },\n        blur() {\n            handleConfirmFilter(params, !XEUtils.eqNull(option.data), option);\n        }\n    });\n}\nfunction getNativeEditOns(renderOpts, params) {\n    const { $table, row, column } = params;\n    const { model } = column;\n    return getNativeElementOns(renderOpts, params, {\n        model(evnt) {\n            // 处理 model 值双向绑定\n            const targetEl = evnt.target;\n            if (targetEl) {\n                const cellValue = targetEl.value;\n                if (isImmediateCell(renderOpts, params)) {\n                    setCellValue(row, column, cellValue);\n                }\n                else {\n                    model.update = true;\n                    model.value = cellValue;\n                }\n            }\n        },\n        change(evnt) {\n            // 处理 change 事件相关逻辑\n            const targetEl = evnt.target;\n            if (targetEl) {\n                const cellValue = targetEl.value;\n                $table.updateStatus(params, cellValue);\n            }\n        },\n        blur(evnt) {\n            const targetEl = evnt.target;\n            if (targetEl) {\n                const cellValue = targetEl.value;\n                $table.updateStatus(params, cellValue);\n            }\n        }\n    });\n}\nfunction getNativeFilterOns(renderOpts, params, option) {\n    return getNativeElementOns(renderOpts, params, {\n        model(evnt) {\n            // 处理 model 值双向绑定\n            const targetEl = evnt.target;\n            if (targetEl) {\n                option.data = targetEl.value;\n            }\n        },\n        change() {\n            handleConfirmFilter(params, !XEUtils.eqNull(option.data), option);\n        },\n        blur() {\n            handleConfirmFilter(params, !XEUtils.eqNull(option.data), option);\n        }\n    });\n}\n/**\n * 单元格可编辑渲染-原生的标签\n * input、textarea、select\n */\nfunction nativeEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const { name } = renderOpts;\n    const cellValue = isImmediateCell(renderOpts, params) ? getCellValue(row, column) : column.model.value;\n    return [\n        h(name, Object.assign(Object.assign(Object.assign({ class: `vxe-default-${name}` }, getNativeAttrs(renderOpts)), { value: cellValue }), getNativeEditOns(renderOpts, params)))\n    ];\n}\nfunction buttonCellRender(renderOpts, params) {\n    return [\n        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, null)), getComponentOns(renderOpts, params)))\n    ];\n}\nfunction defaultEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))\n    ];\n}\nfunction checkboxEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))\n    ];\n}\nfunction radioAndCheckboxGroupEditRender(renderOpts, params) {\n    const { options } = renderOpts;\n    const { row, column } = params;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ options }, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))\n    ];\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction oldEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getOldComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))\n    ];\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction oldButtonEditRender(renderOpts, params) {\n    return [\n        h(getComponent('vxe-button'), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, null)), getComponentOns(renderOpts, params)))\n    ];\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction oldButtonsEditRender(renderOpts, params) {\n    return renderOpts.children.map((childRenderOpts) => oldButtonEditRender(childRenderOpts, params)[0]);\n}\nfunction renderNativeOptgroups(renderOpts, params, renderOptionsMethods) {\n    const { optionGroups, optionGroupProps = {} } = renderOpts;\n    const groupOptions = optionGroupProps.options || 'options';\n    const groupLabel = optionGroupProps.label || 'label';\n    if (optionGroups) {\n        return optionGroups.map((group, gIndex) => {\n            return h('optgroup', {\n                key: gIndex,\n                label: group[groupLabel]\n            }, renderOptionsMethods(group[groupOptions], renderOpts, params));\n        });\n    }\n    return [];\n}\n/**\n * 渲染原生的 option 标签\n */\nfunction renderNativeOptions(options, renderOpts, params) {\n    const { optionProps = {} } = renderOpts;\n    const { row, column } = params;\n    const labelProp = optionProps.label || 'label';\n    const valueProp = optionProps.value || 'value';\n    const disabledProp = optionProps.disabled || 'disabled';\n    const cellValue = isImmediateCell(renderOpts, params) ? getCellValue(row, column) : column.model.value;\n    if (options) {\n        return options.map((option, oIndex) => {\n            return h('option', {\n                key: oIndex,\n                value: option[valueProp],\n                disabled: option[disabledProp],\n                /* eslint-disable eqeqeq */\n                selected: option[valueProp] == cellValue\n            }, option[labelProp]);\n        });\n    }\n    return [];\n}\nfunction nativeFilterRender(renderOpts, params) {\n    const { column } = params;\n    const { name } = renderOpts;\n    const attrs = getNativeAttrs(renderOpts);\n    return column.filters.map((option, oIndex) => {\n        return h(name, Object.assign(Object.assign(Object.assign({ key: oIndex, class: `vxe-default-${name}` }, attrs), { value: option.data }), getNativeFilterOns(renderOpts, params, option)));\n    });\n}\nfunction defaultFilterRender(renderOpts, params) {\n    const { column } = params;\n    return column.filters.map((option, oIndex) => {\n        const optionValue = option.data;\n        return h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, renderOpts, optionValue)), getFilterOns(renderOpts, params, option)));\n    });\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction oldFilterRender(renderOpts, params) {\n    const { column } = params;\n    return column.filters.map((option, oIndex) => {\n        const optionValue = option.data;\n        return h(getOldComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, renderOpts, optionValue)), getFilterOns(renderOpts, params, option)));\n    });\n}\nfunction handleFilterMethod({ option, row, column }) {\n    const { data } = option;\n    const cellValue = XEUtils.get(row, column.field);\n    /* eslint-disable eqeqeq */\n    return cellValue == data;\n}\nfunction handleInputFilterMethod({ option, row, column }) {\n    const { data } = option;\n    const cellValue = XEUtils.get(row, column.field);\n    /* eslint-disable eqeqeq */\n    return XEUtils.toValueString(cellValue).indexOf(data) > -1;\n}\nfunction nativeSelectEditRender(renderOpts, params) {\n    return [\n        h('select', Object.assign(Object.assign({ class: 'vxe-default-select' }, getNativeAttrs(renderOpts)), getNativeEditOns(renderOpts, params)), renderOpts.optionGroups ? renderNativeOptgroups(renderOpts, params, renderNativeOptions) : renderNativeOptions(renderOpts.options, renderOpts, params))\n    ];\n}\nfunction defaultSelectEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { options, optionProps, optionGroups, optionGroupProps })), getEditOns(renderOpts, params)))\n    ];\n}\nfunction defaultTableOrTreeSelectEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const { options, optionProps } = renderOpts;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { options, optionProps })), getEditOns(renderOpts, params)))\n    ];\n}\n/**\n * 已废弃\n * @deprecated\n */\nfunction oldSelectEditRender(renderOpts, params) {\n    const { row, column } = params;\n    const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;\n    const cellValue = getCellValue(row, column);\n    return [\n        h(getOldComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { options, optionProps, optionGroups, optionGroupProps })), getEditOns(renderOpts, params)))\n    ];\n}\nfunction getSelectCellValue(renderOpts, { row, column }) {\n    const { options, optionGroups, optionProps = {}, optionGroupProps = {} } = renderOpts;\n    const cellValue = XEUtils.get(row, column.field);\n    let selectItem;\n    const labelProp = optionProps.label || 'label';\n    const valueProp = optionProps.value || 'value';\n    if (!(cellValue === null || cellValue === undefined)) {\n        return XEUtils.map(XEUtils.isArray(cellValue) ? cellValue : [cellValue], optionGroups\n            ? (value) => {\n                const groupOptions = optionGroupProps.options || 'options';\n                for (let index = 0; index < optionGroups.length; index++) {\n                    /* eslint-disable eqeqeq */\n                    selectItem = XEUtils.find(optionGroups[index][groupOptions], item => item[valueProp] == value);\n                    if (selectItem) {\n                        break;\n                    }\n                }\n                return selectItem ? selectItem[labelProp] : value;\n            }\n            : (value) => {\n                /* eslint-disable eqeqeq */\n                selectItem = XEUtils.find(options, item => item[valueProp] == value);\n                return selectItem ? selectItem[labelProp] : value;\n            }).join(', ');\n    }\n    return '';\n}\nfunction handleExportSelectMethod(params) {\n    const { row, column, options } = params;\n    return options.original ? getCellValue(row, column) : getSelectCellValue(column.editRender || column.cellRender, params);\n}\nfunction getTreeSelectCellValue(renderOpts, { row, column }) {\n    const { options, optionProps = {} } = renderOpts;\n    const cellValue = XEUtils.get(row, column.field);\n    const labelProp = optionProps.label || 'label';\n    const valueProp = optionProps.value || 'value';\n    const childrenProp = optionProps.children || 'children';\n    if (!(cellValue === null || cellValue === undefined)) {\n        const keyMaps = {};\n        XEUtils.eachTree(options, item => {\n            keyMaps[XEUtils.get(item, valueProp)] = item;\n        }, { children: childrenProp });\n        return XEUtils.map(XEUtils.isArray(cellValue) ? cellValue : [cellValue], (value) => {\n            const item = keyMaps[value];\n            return item ? XEUtils.get(item, labelProp) : item;\n        }).join(', ');\n    }\n    return '';\n}\nfunction handleExportTreeSelectMethod(params) {\n    const { row, column, options } = params;\n    return options.original ? getCellValue(row, column) : getTreeSelectCellValue(column.editRender || column.cellRender, params);\n}\nfunction handleNumberCell(renderOpts, params) {\n    const { props = {}, showNegativeStatus } = renderOpts;\n    const { row, column } = params;\n    const { type } = props;\n    let cellValue = XEUtils.get(row, column.field);\n    let isNegative = false;\n    if (!isEmptyValue(cellValue)) {\n        const numberInputConfig = getConfig().numberInput || {};\n        if (type === 'float') {\n            const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);\n            const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 1);\n            cellValue = XEUtils.toFixed(XEUtils.floor(cellValue, digits), digits);\n            if (!autoFill) {\n                cellValue = XEUtils.toNumber(cellValue);\n            }\n            if (showNegativeStatus) {\n                if (cellValue < 0) {\n                    isNegative = true;\n                }\n            }\n        }\n        else if (type === 'amount') {\n            const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);\n            const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 2);\n            const showCurrency = handleDefaultValue(props.showCurrency, numberInputConfig.showCurrency, false);\n            cellValue = XEUtils.toNumber(cellValue);\n            if (showNegativeStatus) {\n                if (cellValue < 0) {\n                    isNegative = true;\n                }\n            }\n            cellValue = XEUtils.commafy(cellValue, { digits });\n            if (!autoFill) {\n                const [iStr, dStr] = cellValue.split('.');\n                if (dStr) {\n                    const dRest = dStr.replace(/0+$/, '');\n                    cellValue = dRest ? [iStr, '.', dRest].join('') : iStr;\n                }\n            }\n            if (showCurrency) {\n                cellValue = `${props.currencySymbol || numberInputConfig.currencySymbol || getI18n('vxe.numberInput.currencySymbol') || ''}${cellValue}`;\n            }\n        }\n        else {\n            if (showNegativeStatus) {\n                if (XEUtils.toNumber(cellValue) < 0) {\n                    isNegative = true;\n                }\n            }\n        }\n    }\n    return getCellLabelVNs(renderOpts, params, cellValue, isNegative\n        ? {\n            class: 'is--negative'\n        }\n        : {});\n}\n/**\n * 表格 - 渲染器\n */\nrenderer.mixin({\n    input: {\n        tableAutoFocus: 'input',\n        renderTableEdit: nativeEditRender,\n        renderTableDefault: nativeEditRender,\n        renderTableFilter: nativeFilterRender,\n        tableFilterDefaultMethod: handleInputFilterMethod\n    },\n    textarea: {\n        tableAutoFocus: 'textarea',\n        renderTableEdit: nativeEditRender\n    },\n    select: {\n        renderTableEdit: nativeSelectEditRender,\n        renderTableDefault: nativeSelectEditRender,\n        renderTableCell(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));\n        },\n        renderTableFilter(renderOpts, params) {\n            const { column } = params;\n            return column.filters.map((option, oIndex) => {\n                return h('select', Object.assign(Object.assign({ key: oIndex, class: 'vxe-default-select' }, getNativeAttrs(renderOpts)), getNativeFilterOns(renderOpts, params, option)), renderOpts.optionGroups ? renderNativeOptgroups(renderOpts, params, renderNativeOptions) : renderNativeOptions(renderOpts.options, renderOpts, params));\n            });\n        },\n        tableFilterDefaultMethod: handleFilterMethod,\n        tableExportMethod: handleExportSelectMethod\n    },\n    VxeInput: {\n        tableAutoFocus: 'input',\n        renderTableEdit: defaultEditRender,\n        renderTableCell(renderOpts, params) {\n            const { props = {} } = renderOpts;\n            const { row, column } = params;\n            const inputConfig = getConfig().input || {};\n            const digits = props.digits || inputConfig.digits || 2;\n            let cellValue = XEUtils.get(row, column.field);\n            if (cellValue) {\n                switch (props.type) {\n                    case 'date':\n                    case 'week':\n                    case 'month':\n                    case 'quarter':\n                    case 'year':\n                        cellValue = getLabelFormatDate(cellValue, props);\n                        break;\n                    case 'float':\n                        cellValue = XEUtils.toFixed(XEUtils.floor(cellValue, digits), digits);\n                        break;\n                }\n            }\n            return getCellLabelVNs(renderOpts, params, cellValue);\n        },\n        renderTableDefault: defaultEditRender,\n        renderTableFilter: defaultFilterRender,\n        tableFilterDefaultMethod: handleInputFilterMethod\n    },\n    FormatNumberInput: {\n        renderTableDefault: handleNumberCell,\n        tableFilterDefaultMethod: handleInputFilterMethod,\n        tableExportMethod(params) {\n            const { row, column } = params;\n            const cellValue = XEUtils.get(row, column.field);\n            return cellValue;\n        }\n    },\n    VxeNumberInput: {\n        tableAutoFocus: 'input',\n        renderTableEdit: defaultEditRender,\n        renderTableCell: handleNumberCell,\n        renderTableFooter(renderOpts, params) {\n            const { props = {} } = renderOpts;\n            const { row, column, _columnIndex } = params;\n            const { type } = props;\n            // 兼容老模式\n            const itemValue = XEUtils.isArray(row) ? row[_columnIndex] : XEUtils.get(row, column.field);\n            if (XEUtils.isNumber(itemValue)) {\n                const numberInputConfig = getConfig().numberInput || {};\n                if (type === 'float') {\n                    const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);\n                    const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 1);\n                    let amountLabel = XEUtils.toFixed(XEUtils.floor(itemValue, digits), digits);\n                    if (!autoFill) {\n                        amountLabel = XEUtils.toNumber(amountLabel);\n                    }\n                    return amountLabel;\n                }\n                else if (type === 'amount') {\n                    const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);\n                    const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 2);\n                    const showCurrency = handleDefaultValue(props.showCurrency, numberInputConfig.showCurrency, false);\n                    let amountLabel = XEUtils.commafy(XEUtils.toNumber(itemValue), { digits });\n                    if (!autoFill) {\n                        const [iStr, dStr] = amountLabel.split('.');\n                        if (dStr) {\n                            const dRest = dStr.replace(/0+$/, '');\n                            amountLabel = dRest ? [iStr, '.', dRest].join('') : iStr;\n                        }\n                    }\n                    if (showCurrency) {\n                        amountLabel = `${props.currencySymbol || numberInputConfig.currencySymbol || getI18n('vxe.numberInput.currencySymbol') || ''}${amountLabel}`;\n                    }\n                    return amountLabel;\n                }\n            }\n            return getFuncText(itemValue, 1);\n        },\n        renderTableDefault: defaultEditRender,\n        renderTableFilter: defaultFilterRender,\n        tableFilterDefaultMethod: handleInputFilterMethod,\n        tableExportMethod(params) {\n            const { row, column } = params;\n            const cellValue = XEUtils.get(row, column.field);\n            return cellValue;\n        }\n    },\n    VxeDatePicker: {\n        tableAutoFocus: 'input',\n        renderTableEdit: defaultEditRender,\n        renderTableCell(renderOpts, params) {\n            const { props = {} } = renderOpts;\n            const { row, column } = params;\n            let cellValue = XEUtils.get(row, column.field);\n            if (cellValue) {\n                if (props.type !== 'time') {\n                    cellValue = getLabelFormatDate(cellValue, props);\n                }\n            }\n            return getCellLabelVNs(renderOpts, params, cellValue);\n        },\n        renderTableDefault: defaultEditRender,\n        renderTableFilter: defaultFilterRender,\n        tableFilterDefaultMethod: handleFilterMethod\n    },\n    VxeDateRangePicker: {\n        tableAutoFocus: 'input',\n        renderTableEdit(renderOpts, params) {\n            const { startField, endField } = renderOpts;\n            const { $table, row, column } = params;\n            const { model } = column;\n            const cellValue = getCellValue(row, column);\n            const seProps = {};\n            const seOs = {};\n            if (startField && endField) {\n                seProps.startValue = XEUtils.get(row, startField);\n                seProps.endValue = XEUtils.get(row, endField);\n                seOs['onUpdate:startValue'] = (value) => {\n                    if (startField) {\n                        XEUtils.set(row, startField, value);\n                    }\n                };\n                seOs['onUpdate:endValue'] = (value) => {\n                    if (endField) {\n                        XEUtils.set(row, endField, value);\n                    }\n                };\n            }\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, seProps)), getComponentOns(renderOpts, params, {\n                    model(cellValue) {\n                        model.update = true;\n                        model.value = cellValue;\n                        setCellValue(row, column, cellValue);\n                    },\n                    change() {\n                        $table.updateStatus(params);\n                    },\n                    blur() {\n                        $table.handleCellRuleUpdateStatus('blur', params);\n                    }\n                }, seOs)))\n            ];\n        },\n        renderTableCell(renderOpts, params) {\n            const { startField, endField } = renderOpts;\n            const { row, column } = params;\n            let startValue = '';\n            let endValue = '';\n            if (startField && endField) {\n                startValue = XEUtils.get(row, startField);\n                endValue = XEUtils.get(row, endField);\n            }\n            else {\n                const cellValue = XEUtils.get(row, column.field);\n                if (cellValue) {\n                    if (XEUtils.isArray(cellValue)) {\n                        startValue = cellValue[0];\n                        endValue = cellValue[1];\n                    }\n                    else {\n                        const strs = `${cellValue}`.split(',');\n                        startValue = strs[0];\n                        endValue = strs[1];\n                    }\n                }\n            }\n            let cellLabel = '';\n            if (startValue && endValue) {\n                cellLabel = `${startValue} ~ ${endValue}`;\n            }\n            return getCellLabelVNs(renderOpts, params, cellLabel);\n        }\n    },\n    VxeTextarea: {\n        tableAutoFocus: 'textarea',\n        renderTableEdit: defaultEditRender,\n        renderTableCell(renderOpts, params) {\n            const { row, column } = params;\n            const cellValue = XEUtils.get(row, column.field);\n            return getCellLabelVNs(renderOpts, params, cellValue);\n        }\n    },\n    VxeButton: {\n        renderTableDefault: buttonCellRender\n    },\n    VxeButtonGroup: {\n        renderTableDefault(renderOpts, params) {\n            const { options } = renderOpts;\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ options }, getCellEditProps(renderOpts, params, null)), getComponentOns(renderOpts, params)))\n            ];\n        }\n    },\n    VxeSelect: {\n        tableAutoFocus: 'input',\n        renderTableEdit: defaultSelectEditRender,\n        renderTableDefault: defaultSelectEditRender,\n        renderTableCell(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));\n        },\n        renderTableFilter(renderOpts, params) {\n            const { column } = params;\n            const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;\n            return column.filters.map((option, oIndex) => {\n                const optionValue = option.data;\n                return h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, params, optionValue, { options, optionProps, optionGroups, optionGroupProps })), getFilterOns(renderOpts, params, option)));\n            });\n        },\n        tableFilterDefaultMethod: handleFilterMethod,\n        tableExportMethod: handleExportSelectMethod\n    },\n    /**\n     * 已废弃，被 FormatSelect 替换\n     * @deprecated\n     */\n    formatOption: {\n        renderTableDefault(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));\n        }\n    },\n    FormatSelect: {\n        renderTableDefault(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));\n        },\n        tableFilterDefaultMethod: handleFilterMethod,\n        tableExportMethod: handleExportSelectMethod\n    },\n    VxeTreeSelect: {\n        tableAutoFocus: 'input',\n        renderTableEdit: defaultTableOrTreeSelectEditRender,\n        renderTableCell(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));\n        },\n        tableExportMethod: handleExportTreeSelectMethod\n    },\n    /**\n     * 已废弃，被 FormatTreeSelect 替换\n     * @deprecated\n     */\n    formatTree: {\n        renderTableDefault(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));\n        }\n    },\n    FormatTreeSelect: {\n        renderTableDefault(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));\n        },\n        tableExportMethod: handleExportTreeSelectMethod\n    },\n    VxeTableSelect: {\n        tableAutoFocus: 'input',\n        renderTableEdit: defaultTableOrTreeSelectEditRender,\n        renderTableCell(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));\n        },\n        tableExportMethod: handleExportTreeSelectMethod\n    },\n    VxeColorPicker: {\n        tableAutoFocus: 'input',\n        renderTableEdit(renderOpts, params) {\n            const { row, column } = params;\n            const { options } = renderOpts;\n            const cellValue = getCellValue(row, column);\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { colors: options })), getEditOns(renderOpts, params)))\n            ];\n        },\n        renderTableCell(renderOpts, params) {\n            const { row, column } = params;\n            const cellValue = XEUtils.get(row, column.field);\n            return h('span', {\n                class: 'vxe-color-picker--readonly'\n            }, [\n                h('div', {\n                    class: 'vxe-color-picker--readonly-color',\n                    style: {\n                        backgroundColor: cellValue\n                    }\n                })\n            ]);\n        }\n    },\n    VxeIconPicker: {\n        tableAutoFocus: 'input',\n        renderTableEdit(renderOpts, params) {\n            const { row, column } = params;\n            const { options } = renderOpts;\n            const cellValue = getCellValue(row, column);\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { icons: options })), getEditOns(renderOpts, params)))\n            ];\n        },\n        renderTableCell(renderOpts, params) {\n            const { row, column } = params;\n            const cellValue = XEUtils.get(row, column.field);\n            return h('i', {\n                class: cellValue\n            });\n        }\n    },\n    VxeRadioGroup: {\n        renderTableDefault: radioAndCheckboxGroupEditRender\n    },\n    VxeCheckbox: {\n        renderTableDefault: checkboxEditRender\n    },\n    VxeCheckboxGroup: {\n        renderTableDefault: radioAndCheckboxGroupEditRender\n    },\n    VxeSwitch: {\n        tableAutoFocus: 'button',\n        renderTableEdit: defaultEditRender,\n        renderTableDefault: defaultEditRender\n    },\n    VxeUpload: {\n        renderTableEdit: defaultEditRender,\n        renderTableCell: defaultEditRender,\n        renderTableDefault: defaultEditRender\n    },\n    VxeImage: {\n        renderTableDefault(renderOpts, params) {\n            const { row, column } = params;\n            const { props } = renderOpts;\n            const cellValue = getCellValue(row, column);\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign(Object.assign({}, props), { src: cellValue }), getEditOns(renderOpts, params)))\n            ];\n        }\n    },\n    VxeImageGroup: {\n        renderTableDefault(renderOpts, params) {\n            const { row, column } = params;\n            const { props } = renderOpts;\n            const cellValue = getCellValue(row, column);\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign(Object.assign({}, props), { urlList: cellValue }), getEditOns(renderOpts, params)))\n            ];\n        }\n    },\n    VxeTextEllipsis: {\n        renderTableDefault(renderOpts, params) {\n            const { row, column } = params;\n            const { props } = renderOpts;\n            const cellValue = getCellValue(row, column);\n            return [\n                h(getDefaultComponent(renderOpts), Object.assign(Object.assign(Object.assign({}, props), { content: cellValue }), getEditOns(renderOpts, params)))\n            ];\n        }\n    },\n    VxeRate: {\n        renderTableDefault: defaultEditRender\n    },\n    VxeSlider: {\n        renderTableDefault: defaultEditRender\n    },\n    // 以下已废弃\n    $input: {\n        tableAutoFocus: '.vxe-input--inner',\n        renderTableEdit: oldEditRender,\n        renderTableCell(renderOpts, params) {\n            var _a;\n            const { props = {} } = renderOpts;\n            const { row, column } = params;\n            const digits = props.digits || ((_a = getConfig().input) === null || _a === void 0 ? void 0 : _a.digits) || 2;\n            let cellValue = XEUtils.get(row, column.field);\n            if (cellValue) {\n                switch (props.type) {\n                    case 'date':\n                    case 'week':\n                    case 'month':\n                    case 'year':\n                        cellValue = getLabelFormatDate(cellValue, props);\n                        break;\n                    case 'float':\n                        cellValue = XEUtils.toFixed(XEUtils.floor(cellValue, digits), digits);\n                        break;\n                }\n            }\n            return getCellLabelVNs(renderOpts, params, cellValue);\n        },\n        renderTableDefault: oldEditRender,\n        renderTableFilter: oldFilterRender,\n        tableFilterDefaultMethod: handleInputFilterMethod\n    },\n    $textarea: {\n        tableAutoFocus: '.vxe-textarea--inner'\n    },\n    $button: {\n        renderTableDefault: oldButtonEditRender\n    },\n    $buttons: {\n        renderTableDefault: oldButtonsEditRender\n    },\n    $select: {\n        tableAutoFocus: '.vxe-input--inner',\n        renderTableEdit: oldSelectEditRender,\n        renderTableDefault: oldSelectEditRender,\n        renderTableCell(renderOpts, params) {\n            return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));\n        },\n        renderTableFilter(renderOpts, params) {\n            const { column } = params;\n            const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;\n            return column.filters.map((option, oIndex) => {\n                const optionValue = option.data;\n                return h(getOldComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, params, optionValue, { options, optionProps, optionGroups, optionGroupProps })), getFilterOns(renderOpts, params, option)));\n            });\n        },\n        tableFilterDefaultMethod: handleFilterMethod,\n        tableExportMethod: handleExportSelectMethod\n    },\n    $radio: {\n        tableAutoFocus: '.vxe-radio--input'\n    },\n    $checkbox: {\n        tableAutoFocus: '.vxe-checkbox--input'\n    },\n    $switch: {\n        tableAutoFocus: '.vxe-switch--button',\n        renderTableEdit: oldEditRender,\n        renderTableDefault: oldEditRender\n    }\n    // 以上已废弃\n});\n", "import { VxeUI } from '../ui';\nimport VxeTableComponent from './src/table';\nimport { useCellView } from './src/use';\nimport './module/filter/hook';\nimport './module/menu/hook';\nimport './module/edit/hook';\nimport './module/export/hook';\nimport './module/keyboard/hook';\nimport './module/validator/hook';\nimport './module/custom/hook';\nimport './render';\nexport const VxeTable = Object.assign({}, VxeTableComponent, {\n    install(app) {\n        app.component(VxeTableComponent.name, VxeTableComponent);\n    }\n});\nconst tableHandle = {\n    useCellView\n};\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeTableComponent.name, VxeTableComponent);\n}\nVxeUI.component(VxeTableComponent);\nVxeUI.tableHandle = tableHandle;\nexport const Table = VxeTable;\nexport default VxeTable;\n", "import VxeTable from '../table';\nexport * from '../table';\nexport default VxeTable;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,sBAAoB;AACb,SAAS,YAAY,OAAO;AAC/B,QAAM,aAAa,SAAS,MAAM;AAC9B,UAAM,EAAE,aAAa,IAAI;AACzB,WAAO,aAAa;AAAA,EACxB,CAAC;AACD,QAAM,UAAU,SAAS,MAAM;AAC3B,UAAM,EAAE,aAAa,IAAI;AACzB,WAAO,aAAa;AAAA,EACxB,CAAC;AACD,QAAM,cAAc,SAAS,MAAM;AAC/B,UAAM,EAAE,WAAW,IAAI;AACvB,WAAO,WAAW,SAAS,CAAC;AAAA,EAChC,CAAC;AACD,QAAM,YAAY,SAAS;AAAA,IACvB,MAAM;AACF,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,aAAO,gBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAAA,IACxC;AAAA,IACA,IAAI,OAAO;AACP,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,aAAO,gBAAAA,QAAQ,IAAI,KAAK,OAAO,OAAO,KAAK;AAAA,IAC/C;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AChCA,IAAAC,mBAAoB;AAKpB,IAAM,EAAE,UAAU,MAAM,IAAI;AAC5B,IAAM,wBAAwB,CAAC,cAAc,aAAa,eAAe,mBAAmB,0BAA0B,oBAAoB,2BAA2B,qBAAqB,0BAA0B;AACpN,MAAM,IAAI,qBAAqB;AAAA,EAC3B,WAAW,UAAU;AACjB,UAAM,EAAE,OAAO,WAAW,aAAa,IAAI;AAC3C,UAAM,EAAE,SAAS,eAAe,IAAI,SAAS,WAAW;AACxD,UAAM,EAAE,mBAAmB,iBAAiB,IAAI,SAAS,eAAe;AAExE,UAAM,4BAA4B,CAAC,SAAS;AACxC,YAAM,EAAE,YAAY,IAAI;AACxB,kBAAY,QAAQ,QAAQ,CAAC,WAAW;AACpC,eAAO,UAAU,OAAO;AAAA,MAC5B,CAAC;AACD,eAAS,mBAAmB,IAAI;AAAA,IACpC;AAEA,UAAM,oBAAoB,CAAC,MAAM,SAAS,SAAS;AAC/C,YAAM,EAAE,YAAY,IAAI;AACxB,kBAAY,QAAQ,QAAQ,CAAC,WAAW;AACpC,eAAO,WAAW;AAAA,MACtB,CAAC;AACD,WAAK,WAAW;AAChB,eAAS,mBAAmB;AAC5B,gCAA0B,IAAI;AAAA,IAClC;AAEA,UAAM,uBAAuB,CAAC,MAAM,SAAS,SAAS;AAClD,WAAK,WAAW;AAChB,eAAS,mBAAmB;AAAA,IAChC;AAMA,UAAM,0BAA0B,CAAC,SAAS;AACtC,YAAM,EAAE,YAAY,IAAI;AACxB,eAAS,kBAAkB,YAAY,MAAM;AAC7C,eAAS,mBAAmB,IAAI;AAChC,UAAI,MAAM;AACN,iBAAS,cAAc,gBAAgB,EAAE,YAAY,CAAC,EAAE,GAAG,IAAI;AAAA,MACnE;AAAA,IACJ;AACA,UAAM,uBAAuB;AAAA,MACzB,qBAAqB;AACjB,cAAM,EAAE,YAAY,IAAI;AACxB,oBAAY,gBAAgB,YAAY,QAAQ,MAAM,CAAC,SAAS,KAAK,QAAQ;AAC7E,oBAAY,kBAAkB,CAAC,YAAY,iBAAiB,YAAY,QAAQ,KAAK,CAAC,SAAS,KAAK,QAAQ;AAAA,MAChH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,mBAAmB,MAAM,QAAQ,QAAQ;AACrC,cAAM,EAAE,WAAW,YAAY,IAAI;AACnC,cAAM,EAAE,UAAU,IAAI;AACtB,YAAI,YAAY,WAAW,UAAU,YAAY,SAAS;AACtD,sBAAY,UAAU;AAAA,QAC1B,OACK;AACD,gBAAM,KAAK,QAAQ;AACnB,gBAAM,EAAE,WAAW,YAAY,eAAe,aAAa,IAAI,WAAW;AAC1E,gBAAM,aAAa,kBAAkB;AACrC,gBAAM,EAAE,SAAS,IAAI;AACrB,gBAAM,YAAY,GAAG,sBAAsB;AAC3C,gBAAM,UAAU,KAAK;AACrB,gBAAM,EAAE,SAAS,gBAAgB,aAAa,IAAI;AAClD,gBAAM,WAAW,aAAa,YAAY,IAAI,SAAS,IAAI,aAAa,IAAI,IAAI;AAChF,gBAAM,WAAW,OAAO,wBAAwB,WAAY,SAAS,4BAA4B,SAAS,sBAAuB;AACjI,uBAAa,oBAAoB;AACjC,iBAAO,OAAO,aAAa;AAAA,YACvB,UAAU;AAAA,YACV,SAAS;AAAA,YACT;AAAA,YACA,OAAO;AAAA,UACX,CAAC;AAED,sBAAY,QAAQ,QAAQ,CAAC,WAAW;AACpC,kBAAM,EAAE,UAAU,QAAQ,IAAI;AAC9B,mBAAO,WAAW;AAClB,gBAAI,CAAC,WAAW,aAAa,SAAS;AAClC,kBAAI,UAAU;AACV,yBAAS,EAAE,QAAQ,QAAQ,QAAQ,SAAS,CAAC;AAAA,cACjD;AAAA,YACJ;AAAA,UACJ,CAAC;AACD,eAAK,mBAAmB;AACxB,sBAAY,UAAU;AACtB,oBAAU,SAAS;AACnB,mBAAS,MAAM;AACX,kBAAM,mBAAmB,WAAW,UAAU,oBAAoB,CAAC;AACnE,gBAAI,CAAC,kBAAkB;AACnB;AAAA,YACJ;AACA,kBAAM,cAAc,eAAe;AACnC,kBAAM,oBAAoB,cAAc,YAAY,WAAW,EAAE,QAAQ,QAAQ;AACjF,gBAAI,CAAC,mBAAmB;AACpB;AAAA,YACJ;AACA,kBAAM,UAAU,QAAQ,sBAAsB;AAC9C,kBAAM,iBAAiB,kBAAkB,cAAc,2BAA2B;AAClF,kBAAM,iBAAiB,kBAAkB,cAAc,2BAA2B;AAClF,kBAAM,cAAc,kBAAkB;AACtC,kBAAM,cAAc,cAAc;AAClC,gBAAI,OAAO;AACX,gBAAI,MAAM;AACV,gBAAI,YAAY;AAChB,gBAAI,UAAU;AACV,qBAAO,QAAQ,OAAO,cAAc;AACpC,oBAAM,QAAQ,MAAM,QAAQ,eAAe;AAC3C,0BAAY,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ,KAAK,MAAM,gBAAgB,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,gBAAgB,OAAO,iBAAiB,eAAe,eAAe,MAAM,iBAAiB,eAAe,eAAe,KAAK,EAAE,CAAC;AAChO,kBAAI,OAAO,IAAI;AACX,uBAAO;AAAA,cACX,WACS,OAAQ,eAAe,cAAc,IAAK;AAC/C,uBAAO,eAAe,cAAc;AAAA,cACxC;AAAA,YACJ,OACK;AACD,qBAAO,QAAQ,OAAO,UAAU,OAAO;AACvC,oBAAM,QAAQ,MAAM,UAAU,MAAM,QAAQ;AAC5C,0BAAY,KAAK,IAAI,IAAI,GAAG,eAAe,OAAO,iBAAiB,eAAe,eAAe,MAAM,iBAAiB,eAAe,eAAe,KAAK,EAAE;AAC7J,kBAAI,OAAO,GAAG;AACV,uBAAO;AAAA,cACX,WACS,OAAQ,GAAG,cAAc,cAAc,GAAI;AAChD,uBAAO,GAAG,cAAc,cAAc;AAAA,cAC1C;AAAA,YACJ;AACA,wBAAY,QAAQ;AAAA,cAChB,KAAK,UAAU,GAAG;AAAA,cAClB,MAAM,UAAU,IAAI;AAAA,YACxB;AAEA,wBAAY,YAAY;AAAA,UAC5B,CAAC;AAAA,QACL;AACA,iBAAS,cAAc,kBAAkB,EAAE,QAAQ,OAAO,OAAO,OAAO,UAAU,OAAO,OAAO,YAAY,SAAS,kBAAkB,GAAG,SAAS,YAAY,QAAQ,GAAG,IAAI;AAAA,MAClL;AAAA,MACA,kBAAkB,QAAQ;AACtB,YAAI,QAAQ;AACR,gBAAM,EAAE,SAAS,aAAa,IAAI;AAClC,cAAI,SAAS;AACT,kBAAM,WAAW,aAAa,YAAY,IAAI,SAAS,IAAI,aAAa,IAAI,IAAI;AAChF,kBAAM,WAAW,OAAO,sBAAsB,WAAY,SAAS,0BAA0B,SAAS,oBAAqB;AAC3H,oBAAQ,QAAQ,CAAC,SAAS;AACtB,mBAAK,WAAW;AAChB,mBAAK,UAAU;AACf,kBAAI,CAAC,UAAU;AACX,qBAAK,OAAO,iBAAAC,QAAQ,MAAM,KAAK,YAAY,IAAI;AAAA,cACnD;AAAA,YACJ,CAAC;AACD,gBAAI,UAAU;AACV,uBAAS,EAAE,SAAS,SAAS,QAAQ,QAAQ,SAAS,CAAC;AAAA,YAC3D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,0BAA0B,QAAQ,MAAM;AACpC,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,EAAE,aAAa,gBAAgB,aAAa,eAAe,IAAI;AACrE,cAAM,aAAa,kBAAkB;AACrC,cAAM,YAAY,iBAAiB;AACnC,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,SAAS,CAAC;AAChB,cAAM,QAAQ,CAAC;AACf,eAAO,QAAQ,QAAQ,CAAC,SAAS;AAC7B,cAAI,KAAK,SAAS;AACd,mBAAO,KAAK,KAAK,KAAK;AACtB,kBAAM,KAAK,KAAK,IAAI;AAAA,UACxB;AAAA,QACJ,CAAC;AACD,cAAM,aAAa,SAAS,kBAAkB;AAC9C,cAAM,SAAS,EAAE,QAAQ,UAAU,QAAQ,MAAM,QAAQ,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,YAAY,WAAW;AAEhI,YAAI,CAAC,WAAW,QAAQ;AACpB,mBAAS,gBAAgB,IAAI;AAC7B,mBAAS,qBAAqB;AAAA,QAClC;AACA,YAAI,eAAe,UAAU,QAAQ,SAAS,mBAAmB;AAC7D,mBAAS,kBAAkB,MAAM,MAAM;AAAA,QAC3C;AACA,YAAI,MAAM;AACN,mBAAS,cAAc,iBAAiB,QAAQ,IAAI;AAAA,QACxD;AACA,iBAAS,YAAY;AACrB,eAAO,SAAS,aAAa,EAAE,KAAK,MAAM;AACtC,gBAAM,EAAE,aAAa,YAAY,IAAI;AACrC,cAAK,kBAAkB,gBAAiB,kBAAkB,cAAc;AACpE,gBAAI,kBAAkB,aAAa;AAC/B,uBAAS,mBAAmB;AAAA,YAChC;AACA,gBAAI,kBAAkB,aAAa;AAC/B,uBAAS,mBAAmB;AAAA,YAChC;AACA,mBAAO,SAAS,cAAc;AAAA,UAClC;AAAA,QACJ,CAAC,EAAE,KAAK,MAAM;AACV,mBAAS,gBAAgB;AACzB,iBAAO,SAAS,YAAY,IAAI;AAAA,QACpC,CAAC,EAAE,KAAK,MAAM;AAEV,qBAAW,MAAM,SAAS,YAAY,GAAG,EAAE;AAAA,QAC/C,CAAC;AAAA,MACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,mBAAmB,MAAM;AACrB,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,EAAE,OAAO,IAAI;AACnB,iBAAS,0BAA0B,QAAQ,IAAI;AAAA,MACnD;AAAA,MACA,+BAA+B;AAAA,MAC/B,kCAAkC;AAAA;AAAA,MAElC,yBAAyB,MAAM,SAAS,MAAM;AAC1C,cAAM,EAAE,YAAY,IAAI;AACxB,YAAI,YAAY,UAAU;AACtB,+BAAqB,MAAM,SAAS,IAAI;AAAA,QAC5C,OACK;AACD,4BAAkB,MAAM,SAAS,IAAI;AAAA,QACzC;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKlB,WAAW,eAAe;AACtB,cAAM,SAAS,oBAAoB,UAAU,aAAa;AAC1D,YAAI,UAAU,OAAO,SAAS;AAC1B,gBAAM,EAAE,UAAU,IAAI;AACtB,gBAAM,EAAE,MAAM,IAAI;AAClB,iBAAO,SAAS,eAAe,MAAM,EAAE,KAAK,MAAM;AAC9C,kBAAM,oBAAoB,WAAW,UAAU,GAAG,SAAS,MAAM,iBAAiB,KAAK,UAAU,qBAAqB,CAAC;AACvH,gBAAI,mBAAmB;AACnB,oBAAM,gBAAgB,kBAAkB,cAAc,uBAAuB,OAAO,EAAE,oBAAoB;AAC1G,2BAAa,eAAe,OAAO;AAAA,YACvC;AAAA,UACJ,CAAC;AAAA,QACL;AACA,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,eAAe,SAAS,UAAU;AACxC,cAAM,SAAS,oBAAoB,UAAU,aAAa;AAC1D,YAAI,UAAU,OAAO,SAAS;AAC1B,iBAAO,UAAU,UAAU,WAAW,CAAC,CAAC;AACxC,cAAI,UAAU;AAEV,mBAAO,SAAS,0BAA0B,QAAQ,IAAI,MAAM,OAAO,CAAC;AAAA,UACxE;AAAA,QACJ;AACA,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY,eAAe;AACvB,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,EAAE,gBAAgB,IAAI;AAC5B,cAAM,aAAa,kBAAkB;AACrC,YAAI;AACJ,YAAI,eAAe;AACf,mBAAS,oBAAoB,UAAU,aAAa;AACpD,cAAI,QAAQ;AACR,qBAAS,kBAAkB,MAAM;AAAA,UACrC;AAAA,QACJ,OACK;AACD,0BAAgB,QAAQ,SAAS,iBAAiB;AAAA,QACtD;AACA,YAAI,CAAC,iBAAiB,WAAW,YAAY,QAAQ;AACjD,iBAAO,OAAO,aAAa;AAAA,YACvB,eAAe;AAAA,YACf,iBAAiB;AAAA,YACjB,OAAO;AAAA,YACP,SAAS,CAAC;AAAA,YACV,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,SAAS;AAAA,UACb,CAAC;AAAA,QACL;AACA,YAAI,CAAC,WAAW,QAAQ;AACpB,iBAAO,SAAS,WAAW;AAAA,QAC/B;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,kBAAkB;AACd,kCAA0B,IAAI;AAC9B,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,uBAAuB,MAAM;AACzB,kCAA0B,IAAI;AAC9B,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,mBAAmB;AACf,gCAAwB,IAAI;AAC5B,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,wBAAwB,MAAM;AAC1B,gCAAwB,IAAI;AAC5B,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,oBAAoB;AAChB,cAAM,EAAE,gBAAgB,IAAI;AAC5B,cAAM,aAAa,CAAC;AACpB,wBAAgB,QAAQ,CAAC,WAAW;AAChC,gBAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,gBAAM,YAAY,CAAC;AACnB,gBAAM,WAAW,CAAC;AAClB,cAAI,WAAW,QAAQ,QAAQ;AAC3B,oBAAQ,QAAQ,CAAC,SAAS;AACtB,kBAAI,KAAK,SAAS;AACd,0BAAU,KAAK,KAAK,KAAK;AACzB,yBAAS,KAAK,KAAK,IAAI;AAAA,cAC3B;AAAA,YACJ,CAAC;AACD,gBAAI,UAAU,QAAQ;AAClB,yBAAW,KAAK,EAAE,QAAQ,OAAO,UAAU,OAAO,QAAQ,WAAW,OAAO,SAAS,CAAC;AAAA,YAC1F;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AAAA,MACA,yBAAyB,MAAM,SAAS;AACpC,aAAK,WAAW;AAChB,aAAK,UAAU;AACf,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,oBAAoB;AAAA,EAC/E;AAAA,EACA,UAAU,SAAS;AACf,WAAO,QAAQ,mBAAmB,qBAAqB;AAAA,EAC3D;AACJ,CAAC;;;ACvWD,IAAAC,mBAAoB;AAIpB,IAAM,EAAE,OAAO,OAAAC,QAAO,cAAc,kBAAkB,IAAI;AAC1D,IAAM,sBAAsB,CAAC,WAAW;AACxCA,OAAM,IAAI,mBAAmB;AAAA,EACzB,WAAW,UAAU;AACjB,UAAM,EAAE,KAAK,OAAO,WAAW,aAAa,IAAI;AAChD,UAAM,EAAE,SAAS,gBAAgB,aAAa,IAAI,SAAS,WAAW;AACtE,UAAM,EAAE,kBAAkB,eAAe,gBAAgB,IAAI,SAAS,eAAe;AACrF,QAAI,cAAc,CAAC;AACnB,QAAI,qBAAqB,CAAC;AAI1B,UAAM,sBAAsB,CAAC,MAAM,MAAM,WAAW;AAChD,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,SAAS,cAAc;AAC7B,YAAM,WAAW,gBAAgB;AACjC,YAAM,SAAS,SAAS,IAAI;AAC5B,YAAM,gBAAgB,SAAS;AAC/B,UAAI,QAAQ;AACR,cAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,YAAI,UAAU;AACV,eAAK,eAAe;AAAA,QACxB,WACS,UAAU,WAAW,QAAQ,QAAQ;AAC1C,iBAAO,UAAU;AACjB,mBAAS,aAAa,MAAM,kBAAkB,QAAQ,MAAM;AACxD,gBAAI,CAAC,iBAAiB,cAAc,MAAM,GAAG;AACzC,mBAAK,eAAe;AACpB,uBAAS,aAAa;AACtB,oBAAM,EAAE,WAAW,YAAY,eAAe,aAAa,IAAI,WAAW;AAC1E,kBAAI,MAAM,KAAK,UAAU;AACzB,kBAAI,OAAO,KAAK,UAAU;AAC1B,oBAAM,gBAAgB,MAAM;AACxB,6BAAa,kBAAkB;AAC/B,uBAAO,OAAO,cAAc;AAAA,kBACxB,SAAS;AAAA,kBACT,MAAM;AAAA,kBACN,UAAU;AAAA,kBACV,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,OAAO;AAAA,oBACH,QAAQ,aAAa;AAAA,oBACrB,KAAK,GAAG,GAAG;AAAA,oBACX,MAAM,GAAG,IAAI;AAAA,kBACjB;AAAA,gBACJ,CAAC;AACD,yBAAS,MAAM;AACX,wBAAM,YAAY,aAAa;AAC/B,wBAAM,UAAU,UAAU,WAAW,EAAE,QAAQ;AAC/C,wBAAM,eAAe,QAAQ;AAC7B,wBAAM,cAAc,QAAQ;AAC5B,wBAAM,EAAE,aAAa,aAAa,IAAI,eAAe,OAAO;AAC5D,wBAAM,YAAY,cAAc,eAAe;AAC/C,wBAAM,aAAa,eAAe,cAAc;AAChD,sBAAI,YAAY,KAAK;AACjB,iCAAa,MAAM,MAAM,GAAG,KAAK,IAAI,YAAY,GAAG,MAAM,eAAe,CAAC,CAAC;AAAA,kBAC/E;AACA,sBAAI,aAAa,KAAK;AAClB,iCAAa,MAAM,OAAO,GAAG,KAAK,IAAI,aAAa,GAAG,OAAO,cAAc,CAAC,CAAC;AAAA,kBACjF;AAAA,gBACJ,CAAC;AAAA,cACL;AACA,oBAAM,EAAE,UAAU,KAAK,OAAO,IAAI;AAClC,kBAAI,YAAY,OAAO,QAAQ;AAC3B,yBAAS,YAAY,KAAK,MAAM,EAAE,KAAK,MAAM;AACzC,wBAAM,OAAO,SAAS,eAAe,KAAK,MAAM;AAChD,sBAAI,MAAM;AACN,0BAAM,EAAE,aAAa,aAAa,IAAI,eAAe,IAAI;AACzD,0BAAM,cAAc,YAAY,KAAK,MAAM,KAAK,eAAe,CAAC;AAChE,2BAAO,eAAe,aAAa,KAAK,MAAM,KAAK,cAAc,CAAC;AAAA,kBACtE;AACA,gCAAc;AAAA,gBAClB,CAAC;AAAA,cACL,OACK;AACD,8BAAc;AAAA,cAClB;AAAA,YACJ,OACK;AACD,0BAAY,UAAU;AAAA,YAC1B;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,eAAS,YAAY;AAAA,IACzB;AACA,kBAAc;AAAA;AAAA;AAAA;AAAA,MAIV,YAAY;AACR,eAAO,OAAO,UAAU,cAAc;AAAA,UAClC,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,UACb,WAAW;AAAA,QACf,CAAC;AACD,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ;AACA,yBAAqB;AAAA;AAAA;AAAA;AAAA,MAIjB,YAAY,MAAM,cAAc,UAAU,SAAS,UAAU,UAAU;AACnE,YAAI;AACJ,cAAM,cAAc,iBAAAC,QAAQ,YAAY,UAAU,UAAQ,aAAa,QAAQ,MAAM,IAAI;AACzF,YAAI,SAAS;AACT,cAAI,YAAY,gBAAgB,aAAa,QAAQ,GAAG;AACpD,yBAAa,YAAY;AAAA,UAC7B,OACK;AACD,yBAAa,YAAY;AACzB,yBAAa,cAAc;AAAA,UAC/B;AAAA,QACJ,WACS,aAAa,OAAO,MAAM,kBAAkB,QAAQ,GAAG;AAC5D,mBAAS,MAAM,cAAc,GAAG,OAAO,GAAG,OAAO;AAC7C,gBAAI,SAAS,GAAG,EAAE,YAAY,OAAO;AACjC,2BAAa,SAAS,GAAG;AACzB;AAAA,YACJ;AAAA,UACJ;AACA,uBAAa,QAAQ,IAAI,cAAc,SAAS,SAAS,SAAS,CAAC;AAAA,QACvE,WACS,aAAa,OAAO,MAAM,kBAAkB,UAAU,GAAG;AAC9D,mBAAS,QAAQ,cAAc,GAAG,QAAQ,SAAS,QAAQ,SAAS;AAChE,gBAAI,SAAS,KAAK,EAAE,YAAY,OAAO;AACnC,2BAAa,SAAS,KAAK;AAC3B;AAAA,YACJ;AAAA,UACJ;AACA,uBAAa,QAAQ,IAAI,cAAc,SAAS,CAAC;AAAA,QACrD,WACS,aAAa,QAAQ,MAAM,aAAa,OAAO,MAAM,kBAAkB,KAAK,KAAK,aAAa,OAAO,MAAM,kBAAkB,QAAQ,IAAI;AAC9I,mBAAS,iBAAiB,MAAM,aAAa,QAAQ,CAAC;AAAA,QAC1D;AAAA,MACJ;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA,6BAA6B,MAAM;AAC/B,cAAM,EAAE,aAAa,WAAW,IAAI;AACpC,cAAM,EAAE,WAAW,aAAa,IAAI;AACpC,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,cAAc,eAAe;AACnC,cAAM,YAAY,aAAa;AAC/B,cAAM,YAAY,iBAAiB;AACnC,cAAM,WAAW,gBAAgB;AACjC,cAAM,KAAK,QAAQ;AACnB,cAAM,EAAE,SAAS,IAAI;AACrB,cAAM,aAAa,CAAC,UAAU,QAAQ,QAAQ;AAC9C,YAAI,aAAa,UAAU,GAAG;AAC1B,cAAI,aAAa,WAAW,aAAa,mBAAmB,MAAM,UAAU,WAAW,EAAE,QAAQ,KAAK,EAAE,MAAM;AAC1G,iBAAK,eAAe;AACpB;AAAA,UACJ;AACA,cAAI,aAAa,SAAS;AACtB,kBAAM,OAAO;AACb,kBAAM,SAAS,EAAE,MAAM,QAAQ,UAAU,UAAU,MAAM,SAAS,cAAc,MAAM,CAAC,GAAG,QAAQ,KAAK;AAEvG,gBAAI,eAAe,UAAU,MAAM;AAC/B,oBAAM,aAAa,SAAS,kBAAkB;AAC9C,kBAAI,cAAc,WAAW,OAAO,WAAW,QAAQ;AACnD,uBAAO,MAAM,WAAW;AACxB,uBAAO,SAAS,WAAW;AAC3B,oCAAoB,MAAM,MAAM,MAAM;AACtC;AAAA,cACJ;AAAA,YACJ,WACS,eAAe,UAAU,UAAU;AAExC,kBAAI,SAAS,OAAO,SAAS,QAAQ;AACjC,uBAAO,MAAM,SAAS;AACtB,uBAAO,SAAS,SAAS;AACzB,oCAAoB,MAAM,MAAM,MAAM;AACtC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAEA,mBAAS,QAAQ,GAAG,QAAQ,WAAW,QAAQ,SAAS;AACpD,kBAAM,SAAS,WAAW,KAAK;AAC/B,kBAAM,mBAAmB,mBAAmB,MAAM,IAAI,OAAO,MAAM,YAAY,CAAC,WAAW;AAEvF,qBAAO,OAAO,WAAW,WAAW,WAAW,aAAa,KAAK,MAAM;AAAA,YAC3E,CAAC;AACD,kBAAM,SAAS,EAAE,MAAM,QAAQ,QAAQ,UAAU,SAAS,cAAc,MAAM,CAAC,GAAG,QAAQ,KAAK;AAC/F,gBAAI,iBAAiB,MAAM;AACvB,oBAAM,OAAO,iBAAiB;AAC9B,oBAAM,iBAAiB,SAAS,cAAc,IAAI;AAClD,oBAAM,SAAS,iBAAiB,eAAe,OAAO;AACtD,kBAAI,aAAa,GAAG,MAAM;AAC1B,kBAAI,QAAQ;AACR,uBAAO,OAAO,QAAQ,EAAE,QAAQ,aAAa,SAAS,eAAe,MAAM,GAAG,KAAK,CAAC;AAAA,cACxF;AACA,kBAAI,WAAW,QAAQ;AACnB,sBAAM,cAAc,SAAS,WAAW,KAAK,UAAU;AACvD,sBAAM,MAAM,cAAc,YAAY,OAAO;AAC7C,6BAAa;AACb,oBAAI,KAAK;AACL,yBAAO,MAAM;AACb,yBAAO,WAAW,SAAS,YAAY,GAAG;AAAA,gBAC9C;AAAA,cACJ;AACA,oBAAM,YAAY,GAAG,UAAU;AAC/B,kCAAoB,MAAM,QAAQ,MAAM;AACxC,uBAAS,cAAc,WAAW,QAAQ,IAAI;AAC9C;AAAA,YACJ,WACS,mBAAmB,MAAM,IAAI,cAAc,MAAM,YAAY,YAAU,OAAO,aAAa,KAAK,MAAM,GAAG,EAAE,MAAM;AACtH,kBAAI,SAAS,YAAY,QAAQ;AAC7B,qBAAK,eAAe;AAAA,cACxB,OACK;AACD,oCAAoB,MAAM,QAAQ,MAAM;AAAA,cAC5C;AACA;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,eAAe,CAAC,mBAAmB,MAAM,YAAY,WAAW,EAAE,QAAQ,KAAK,EAAE,MAAM;AACvF,mBAAS,YAAY;AAAA,QACzB;AACA,oBAAY,UAAU;AAAA,MAC1B;AAAA,MACA,sBAAsB,MAAM,MAAM,OAAO;AACrC,cAAM,WAAW,KAAK;AACtB,cAAM,EAAE,aAAa,IAAI;AACzB,aAAK,eAAe;AACpB,aAAK,gBAAgB;AACrB,qBAAa,WAAW;AACxB,qBAAa,cAAc;AAC3B,YAAI,CAAC,OAAO;AACR,uBAAa,YAAY,gBAAgB,IAAI;AAC7C,cAAI,aAAa,WAAW;AACxB,qBAAS,MAAM;AACX,oBAAM,mBAAmB,SAAS;AAClC,kBAAI,kBAAkB;AAClB,sBAAM,EAAE,aAAa,cAAc,eAAe,aAAa,IAAI,eAAe,QAAQ;AAC1F,sBAAM,SAAS,cAAc,SAAS;AACtC,sBAAM,UAAU,eAAe,SAAS;AACxC,oBAAI,OAAO;AACX,oBAAI,QAAQ;AAEZ,oBAAI,UAAU,iBAAiB,cAAc,eAAe,IAAI;AAC5D,yBAAO;AACP,0BAAQ,GAAG,SAAS,WAAW;AAAA,gBACnC;AAEA,oBAAI,MAAM;AACV,oBAAI,SAAS;AACb,oBAAI,SAAS,iBAAiB,eAAe,gBAAgB,IAAI;AAC7D,wBAAM;AACN,2BAAS;AAAA,gBACb;AACA,iCAAiB,MAAM,OAAO;AAC9B,iCAAiB,MAAM,QAAQ;AAC/B,iCAAiB,MAAM,MAAM;AAC7B,iCAAiB,MAAM,SAAS;AAAA,cACpC;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,qBAAqB,MAAM,MAAM;AAC7B,cAAM,EAAE,aAAa,IAAI;AACzB,YAAI,CAAC,KAAK,UAAU;AAChB,uBAAa,WAAW;AAAA,QAC5B;AACA,qBAAa,cAAc;AAAA,MAC/B;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB,MAAM,MAAM;AACzB,cAAM,UAAU,SAAS;AAEzB,YAAI,CAAC,KAAK,aAAa,KAAK,QAAQ,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS,SAAS;AAC1E,gBAAM,YAAY,MAAM,IAAI,KAAK,IAAI;AACrC,gBAAM,SAAS,OAAO,OAAO,CAAC,GAAG,aAAa,iBAAiB,EAAE,MAAM,QAAQ,UAAU,OAAO,SAAS,QAAQ,KAAK,CAAC;AACvH,gBAAM,WAAW,YAAa,UAAU,mBAAmB,UAAU,aAAc;AACnF,cAAI,UAAU;AACV,qBAAS,QAAQ,IAAI;AAAA,UACzB;AACA,mBAAS,cAAc,cAAc,QAAQ,IAAI;AACjD,sBAAY,UAAU;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,kBAAkB;AAAA,EAC3E;AAAA,EACA,UAAU,SAAS;AACf,WAAO,QAAQ,mBAAmB,mBAAmB;AAAA,EACzD;AACJ,CAAC;;;AC5SD,IAAAC,mBAAoB;AAMpB,IAAM,EAAE,WAAW,UAAAC,WAAU,OAAAC,QAAO,QAAQ,IAAI;AAChD,IAAM,sBAAsB,CAAC,UAAU,YAAY,gBAAgB,eAAe,iBAAiB,qBAAqB,UAAU,qBAAqB,kBAAkB,oBAAoB,gBAAgB,oBAAoB,oBAAoB,oBAAoB,iBAAiB,mBAAmB,eAAe,mBAAmB,aAAa,gBAAgB,iBAAiB,eAAe,iBAAiB,cAAc,gBAAgB,eAAe,iBAAiB,eAAe;AAC1eA,OAAM,IAAI,mBAAmB;AAAA,EACzB,WAAW,UAAU;AACjB,UAAM,EAAE,OAAO,WAAW,aAAa,IAAI;AAC3C,UAAM,EAAE,QAAQ,IAAI,SAAS,WAAW;AACxC,UAAM,EAAE,kBAAkB,iBAAiB,qBAAqB,iBAAiB,iBAAiB,IAAI,SAAS,eAAe;AAC9H,UAAMC,aAAY,iBAAAC,QAAQ,OAAO;AACjC,QAAI,cAAc,CAAC;AACnB,QAAI,qBAAqB,CAAC;AAC1B,UAAM,qBAAqB,CAAC,KAAK,WAAW;AACxC,YAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,UAAI,YAAY;AACZ,cAAM,QAAQ,aAAa,KAAK,MAAM;AACtC,cAAM,SAAS;AAAA,MACnB;AAAA,IACJ;AACA,UAAM,qBAAqB,CAAC,KAAK,WAAW;AACxC,YAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,UAAI,cAAc,MAAM,QAAQ;AAC5B,qBAAa,KAAK,QAAQ,MAAM,KAAK;AACrC,cAAM,SAAS;AACf,cAAM,QAAQ;AAAA,MAClB;AAAA,IACJ;AACA,UAAM,0BAA0B,MAAM;AAClC,YAAM,KAAK,QAAQ;AACnB,UAAI,IAAI;AACJ,cAAM,OAAO,GAAG,cAAc,gBAAgB;AAC9C,YAAI,MAAM;AACN,sBAAY,MAAM,eAAe;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,EAAE,WAAW,YAAY,IAAI;AACnC,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,UAAI,OAAO,QAAQ;AACf,YAAI,SAAS,SAAS,OAAO;AACzB,sBAAY,QAAQ,CAACC,YAAW,mBAAmB,KAAKA,OAAM,CAAC;AAAA,QACnE,OACK;AACD,6BAAmB,KAAK,MAAM;AAAA,QAClC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,YAAY,aAAa;AAC5C,YAAM,EAAE,mBAAmB,eAAe,mBAAmB,qBAAqB,IAAI;AACtF,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,UAAU,aAAa,iBAAiB,IAAI;AACpD,YAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,YAAM,WAAW,WAAW,SAAS;AACrC,iBAAW,QAAQ,UAAQ;AACvB,cAAM,cAAc,KAAK,WAAW;AACpC,cAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,cAAM,WAAW,cAAc,iBAAAD,QAAQ,SAAS,mBAAmB,CAAAE,UAAQ,gBAAgBA,MAAK,QAAQ,GAAG,EAAE,UAAU,iBAAiB,CAAC,IAAI;AAC7I,YAAI,UAAU;AACV,gBAAM,EAAE,MAAM,UAAU,IAAI;AAC5B,gBAAM,aAAa,qBAAqB,SAAS,UAAU,SAAS,CAAC;AACrE,gBAAM,cAAc,aAAa,WAAW,QAAQ;AACpD,cAAI,eAAe,UAAU,aAAa;AAC1C,cAAI,YAAY,UAAU,gBAAgB;AAC1C,cAAI,CAAC,iBAAAF,QAAQ,QAAQ,YAAY,GAAG;AAChC,2BAAe,UAAU,aAAa,IAAI,CAAC;AAAA,UAC/C;AACA,cAAI,CAAC,iBAAAA,QAAQ,QAAQ,SAAS,GAAG;AAC7B,wBAAY,UAAU,aAAa,IAAI,CAAC;AAAA,UAC5C;AACA,uBAAa,QAAQ,EAAE,IAAI;AAC3B,oBAAU,QAAQ,EAAE,IAAI;AACxB,gBAAM,OAAO,EAAE,KAAK,MAAM,OAAO,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,IAAI,OAAO,cAAc,QAAQ,WAAW,OAAO,cAAc,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM,GAAG,cAAc,EAAE;AACjN,4BAAkB,KAAK,IAAI;AAC3B,+BAAqB,KAAK,IAAI;AAAA,QAClC,OACK;AACD,cAAI,aAAa;AACb,oBAAQ,wBAAwB;AAAA,UACpC;AACA,wBAAc,QAAQ,EAAE,IAAI;AAC5B,4BAAkB,QAAQ,EAAE,IAAI;AAChC,gBAAM,OAAO,EAAE,KAAK,MAAM,OAAO,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,IAAI,OAAO,mBAAmB,QAAQ,MAAM,OAAO,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM,GAAG,cAAc,EAAE;AACnM,4BAAkB,KAAK,IAAI;AAC3B,+BAAqB,KAAK,IAAI;AAAA,QAClC;AAAA,MACJ,CAAC;AAAA,IACL;AAGA,UAAM,oBAAoB,CAAC,SAAS,WAAW,oBAAoB;AAC/D,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,EAAE,iBAAiB,IAAI;AAC7B,YAAM,EAAE,mBAAmB,eAAe,eAAe,eAAe,mBAAmB,sBAAsB,cAAc,IAAI;AACnI,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,WAAW,UAAU,iBAAiB,IAAI;AAClD,YAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,UAAI,CAAC,iBAAAA,QAAQ,QAAQ,OAAO,GAAG;AAC3B,kBAAU,CAAC,OAAO;AAAA,MACtB;AACA,YAAM,aAAa,SAAS,SAAS,YAAY,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,cAAc,YAAY,EAAE,CAAC,gBAAgB,GAAG,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAChL,UAAI,iBAAAA,QAAQ,OAAO,SAAS,GAAG;AAE3B,YAAI,cAAc,WAAW;AACzB,wBAAc,YAAY,KAAK;AAAA,QACnC,WACS,kBAAkB;AAEvB,cAAI,YAAY;AACZ,kBAAM,IAAI,MAAM,QAAQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAAA,UAC3D;AACA,kBAAQ,QAAQ,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAAA,QAEpD,OACK;AACD,qBAAW,QAAQ,UAAQ;AACvB,kBAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,kBAAM,OAAO,EAAE,KAAK,MAAM,OAAO,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,IAAI,OAAO,eAAe,QAAQ,MAAM,OAAO,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM,GAAG,cAAc,EAAE;AAC/L,8BAAkB,KAAK,IAAI;AAC3B,iCAAqB,KAAK,IAAI;AAC9B,0BAAc,QAAQ,IAAI;AAC1B,0BAAc,QAAQ,IAAI;AAAA,UAC9B,CAAC;AAED,wBAAc,QAAQ,CAAC,cAAc;AACjC,kBAAM,EAAE,KAAK,cAAc,IAAI;AAC/B,gBAAI,iBAAiB,GAAG;AACpB,wBAAU,MAAM,gBAAgB,WAAW;AAAA,YAC/C;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,OACK;AACD,YAAI,cAAc,IAAI;AAElB,cAAI,cAAc,WAAW;AACzB,0BAAc,YAAY,IAAI;AAAA,UAClC,WACS,kBAAkB;AAEvB,gBAAI,YAAY;AACZ,oBAAM,IAAI,MAAM,QAAQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAAA,YAC3D;AACA,oBAAQ,QAAQ,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAAA,UAEpD,OACK;AACD,uBAAW,QAAQ,UAAQ;AACvB,oBAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,oBAAM,OAAO,EAAE,KAAK,MAAM,OAAO,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,WAAW,IAAI,QAAQ,IAAI,OAAO,eAAe,QAAQ,MAAM,OAAO,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM,GAAG,cAAc,EAAE;AAC/L,gCAAkB,KAAK,IAAI;AAC3B,mCAAqB,KAAK,IAAI;AAC9B,4BAAc,KAAK,IAAI;AACvB,4BAAc,KAAK,IAAI;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ,OACK;AAED,cAAI,cAAc,WAAW;AACzB,kBAAM,cAAc,iBAAAA,QAAQ,SAAS,mBAAmB,UAAQ,UAAU,QAAQ,MAAM,KAAK,QAAQ,GAAG,EAAE,UAAU,iBAAiB,CAAC;AACtI,gBAAI,aAAa;AACb,oBAAM,EAAE,QAAQ,UAAU,IAAI;AAC9B,oBAAM,kBAAkB,YAAY,UAAU,gBAAgB,IAAI;AAClE,oBAAM,aAAa,qBAAqB,SAAS,UAAU,SAAS,CAAC;AACrE,oBAAM,cAAc,aAAa,WAAW,QAAQ;AACpD,yBAAW,QAAQ,CAAC,MAAM,MAAM;AAC5B,sBAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,oBAAI,KAAK,SAAS,WAAW,GAAG;AAC5B,sBAAI,aAAa,KAAK,SAAS,WAAW,MAAM,UAAU,QAAQ,GAAG;AACjE,2BAAO,qBAAqB,CAAC,GAAG,SAAS,WAAW,IAAI,KAAK,SAAS,WAAW,CAAC,IAAI,GAAG,SAAS,WAAW,IAAI,UAAU,QAAQ,CAAC,EAAE,CAAC;AAAA,kBAC3I;AAAA,gBACJ;AACA,oBAAI,WAAW;AACX,uBAAK,SAAS,WAAW,IAAI,UAAU,QAAQ;AAAA,gBACnD;AACA,oBAAI,cAAc,YAAY,QAAQ;AACtC,oBAAI,iBAAiB;AACjB,gCAAc,cAAc;AAAA,gBAChC;AACA,gCAAgB,OAAO,aAAa,GAAG,IAAI;AAC3C,sBAAM,OAAO,EAAE,KAAK,MAAM,OAAO,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,IAAI,OAAO,iBAAiB,QAAQ,WAAW,OAAO,cAAc,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM,GAAG,cAAc,EAAE;AACpN,kCAAkB,KAAK,IAAI;AAC3B,qCAAqB,KAAK,IAAI;AAAA,cAClC,CAAC;AAED,kBAAI,WAAW;AACX,sBAAM,WAAW,iBAAAA,QAAQ,SAAS,mBAAmB,UAAQ,UAAU,QAAQ,MAAM,KAAK,QAAQ,GAAG,EAAE,UAAU,cAAc,CAAC;AAChI,oBAAI,UAAU;AACV,wBAAM,eAAe,SAAS;AAC9B,sBAAI,cAAc,SAAS;AAC3B,sBAAI,iBAAiB;AACjB,kCAAc,cAAc;AAAA,kBAChC;AACA,+BAAa,OAAO,aAAa,GAAG,GAAG,UAAU;AAAA,gBACrD;AAAA,cACJ;AAAA,YACJ,OACK;AACD,sBAAQ,wBAAwB;AAChC,4BAAc,YAAY,IAAI;AAAA,YAClC;AAAA,UACJ,WACS,kBAAkB;AAEvB,gBAAI,YAAY;AACZ,oBAAM,IAAI,MAAM,QAAQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAAA,YAC3D;AACA,oBAAQ,QAAQ,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAAA,UACpD,OACK;AACD,gBAAI,YAAY;AACZ,oBAAM,IAAI,MAAM,QAAQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAAA,YAC3D;AACA,gBAAI,UAAU;AAEd,gBAAI,iBAAAA,QAAQ,SAAS,SAAS,GAAG;AAC7B,kBAAI,YAAY,cAAc,QAAQ;AAClC,0BAAU;AAAA,cACd;AAAA,YACJ,OACK;AACD,wBAAU,SAAS,eAAe,eAAe,SAAS;AAAA,YAC9D;AAEA,gBAAI,iBAAiB;AACjB,wBAAU,KAAK,IAAI,cAAc,QAAQ,UAAU,CAAC;AAAA,YACxD;AACA,gBAAI,YAAY,IAAI;AAChB,oBAAM,IAAI,MAAM,QAAQ,wBAAwB,CAAC;AAAA,YACrD;AACA,0BAAc,OAAO,SAAS,GAAG,GAAG,UAAU;AAC9C,kBAAM,UAAU,SAAS,eAAe,eAAe,SAAS;AAChE,gBAAI,UAAU,IAAI;AACd,4BAAc,OAAO,WAAW,kBAAkB,IAAI,IAAI,GAAG,GAAG,UAAU;AAAA,YAC9E,OACK;AACD,4BAAc,KAAK,GAAG,UAAU;AAAA,YACpC;AAEA,0BAAc,QAAQ,CAAC,cAAc;AACjC,oBAAM,EAAE,KAAK,eAAe,SAAS,aAAa,IAAI;AACtD,kBAAI,iBAAiB,SAAS;AAC1B,0BAAU,MAAM,gBAAgB,WAAW;AAAA,cAC/C,WACS,kBAAmB,gBAAgB,gBAAgB,UAAY,gBAAgB,eAAe,SAAU;AAC7G,0BAAU,UAAU,eAAe,WAAW;AAAA,cAClD;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW,QAAQ,YAAU;AACzB,cAAM,QAAQ,SAAS,UAAU,MAAM;AACvC,sBAAc,KAAK,IAAI;AAAA,MAC3B,CAAC;AACD,gBAAU;AACV,eAAS,YAAY,KAAK;AAC1B,eAAS,oBAAoB;AAC7B,eAAS,gBAAgB,cAAc,SAAS;AAChD,UAAI,EAAE,cAAc,YAAY;AAC5B,iBAAS,qBAAqB;AAAA,MAClC;AACA,eAAS,aAAa;AACtB,eAAS,sBAAsB;AAC/B,eAAS,qBAAqB;AAC9B,UAAI,UAAU,aAAa;AACvB,iBAAS,mBAAmB;AAAA,MAChC;AACA,aAAO,SAAS,EAAE,KAAK,MAAM;AACzB,iBAAS,gBAAgB;AACzB,eAAO,SAAS,YAAY,IAAI;AAAA,MACpC,CAAC,EAAE,KAAK,MAAM;AACV,eAAO;AAAA,UACH,KAAK,WAAW,SAAS,WAAW,WAAW,SAAS,CAAC,IAAI;AAAA,UAC7D,MAAM;AAAA,QACV;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,yBAAyB,CAAC,SAAS,WAAW,WAAW,oBAAoB;AAC/E,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,WAAW,UAAU,YAAY,IAAI;AAC7C,UAAI,cAAc,WAAW;AACzB,YAAI,CAAC,iBAAAA,QAAQ,QAAQ,OAAO,GAAG;AAC3B,oBAAU,CAAC,OAAO;AAAA,QACtB;AACA,eAAO,kBAAkB,QAAQ,IAAI,CAAC,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE,CAAC,CAAC,GAAG,WAAW,eAAe;AAAA,MAC/I,OACK;AACD,eAAO,qBAAqB,CAAC,+BAA+B,4BAA4B,CAAC;AAAA,MAC7F;AACA,aAAO,QAAQ,QAAQ,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,CAAC;AAAA,IAClD;AACA,UAAM,kBAAkB,CAAC,MAAM,cAAc;AACzC,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,UAAI,OAAO,QAAQ;AACf,YAAI,aAAa,SAAS,UAAU,SAAS,MAAM,SAAS,UAAU,GAAG,GAAG;AACxE,iBAAO,SAAS;AAAA,QACpB;AACA,wBAAgB;AAChB,gBAAQ,OAAO;AACf,gBAAQ,MAAM;AACd,gBAAQ,SAAS;AACjB,iBAAS,aAAa;AACtB,iBAAS,cAAc,eAAe;AAAA,UAClC;AAAA,UACA,UAAU,SAAS,YAAY,GAAG;AAAA,UAClC,WAAW,SAAS,cAAc,GAAG;AAAA,UACrC;AAAA,UACA,aAAa,SAAS,eAAe,MAAM;AAAA,UAC3C,cAAc,SAAS,iBAAiB,MAAM;AAAA,QAClD,GAAG,QAAQ,IAAI;AAAA,MACnB;AACA,cAAQ,MAAM;AACd,cAAQ,SAAS;AACjB,UAAI,UAAU,WAAW;AACrB,YAAI,UAAU,YAAY,UAAU,UAAU,EAAE,kBAAkB,YAAY;AAC1E,cAAI,SAAS,eAAe;AACxB,mBAAO,SAAS,cAAc;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,SAAS,EAAE,KAAK,MAAM,SAAS,gBAAgB,CAAC;AAAA,IAC3D;AACA,UAAM,mBAAmB,CAAC,QAAQ,MAAM,SAAS,UAAU;AACvD,YAAM,UAAU,SAAS;AACzB,YAAM,EAAE,YAAY,YAAY,IAAI;AACpC,YAAM,EAAE,WAAW,YAAY,IAAI;AACnC,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,KAAK,IAAI;AACjB,YAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,OAAQ,OAAO,QAAQ,SAAS,eAAe,KAAK,MAAM;AAChE,YAAM,mBAAmB,SAAS,oBAAoB,SAAS;AAC/D,aAAO,OAAO;AACd,UAAI,QAAQ,aAAa,UAAU,KAAK,aAAa,UAAU,GAAG;AAE9D,YAAI,CAAC,SAAS,eAAe,GAAG,KAAK,CAAC,SAAS,kBAAkB,GAAG,GAAG;AACnE,cAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,QAAQ,WAAW,SAAS,QAAQ;AAE9E,gBAAI,OAAO;AACX,gBAAI,CAAC,oBAAoB,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,QAAQ,UAAU,OAAO,QAAQ,CAAC,CAAC,GAAG;AACvH,kBAAI,aAAa;AACb,yBAAS,cAAc;AACvB,oBAAI,SAAS,gBAAgB;AACzB,2BAAS,eAAe;AACxB,2BAAS,kBAAkB;AAAA,gBAC/B;AAAA,cACJ;AACA,uBAAS,aAAa;AACtB,kBAAI,QAAQ,QAAQ;AAChB,gCAAgB,IAAI;AAAA,cACxB;AACA,qBAAO;AACP,qBAAO,eAAe,KAAK;AAC3B,sBAAQ,OAAO;AACf,sBAAQ,MAAM;AACd,sBAAQ,SAAS;AACjB,kBAAI,SAAS,OAAO;AAChB,4BAAY,QAAQ,CAACC,YAAW,mBAAmB,KAAKA,OAAM,CAAC;AAAA,cACnE,OACK;AACD,mCAAmB,KAAK,MAAM;AAAA,cAClC;AACA,oBAAM,kBAAkB,SAAS;AACjC,uBAAS,MAAM;AACX,oBAAI,SAAS;AACT,2BAAS,YAAY,QAAQ,IAAI;AAAA,gBACrC;AACA,oBAAI,iBAAiB;AACjB,kCAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,QAAQ,UAAU,OAAO,QAAQ,CAAC,CAAC;AAAA,gBAClG;AAAA,cACJ,CAAC;AAAA,YACL;AACA,qBAAS,cAAc,MAAM;AAAA,cACzB;AAAA,cACA,UAAU,SAAS,YAAY,GAAG;AAAA,cAClC,WAAW,SAAS,cAAc,GAAG;AAAA,cACrC;AAAA,cACA,aAAa,SAAS,eAAe,MAAM;AAAA,cAC3C,cAAc,SAAS,iBAAiB,MAAM;AAAA,YAClD,GAAG,IAAI;AAEP,gBAAI,SAAS,kBAAkB;AAC3B,uBAAS,cAAc,gBAAgB;AAAA,gBACnC;AAAA,gBACA,UAAU,SAAS,YAAY,GAAG;AAAA,gBAClC,WAAW,SAAS,cAAc,GAAG;AAAA,gBACrC;AAAA,gBACA,aAAa,SAAS,eAAe,MAAM;AAAA,gBAC3C,cAAc,SAAS,iBAAiB,MAAM;AAAA,cAClD,GAAG,IAAI;AAAA,YACX;AAAA,UACJ,OACK;AACD,kBAAM,EAAE,QAAQ,UAAU,IAAI;AAC9B,gBAAI,aAAa;AACb,uBAAS,cAAc;AACvB,kBAAI,SAAS,gBAAgB;AACzB,yBAAS,eAAe;AACxB,yBAAS,kBAAkB;AAAA,cAC/B;AAAA,YACJ;AACA,gBAAI,cAAc,QAAQ;AACtB,oBAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,kBAAI,SAAS,QAAQ;AACjB,6BAAa,KAAK,WAAW,SAAS,KAAK;AAAA,cAC/C;AACA,kBAAI,SAAS,eAAe;AACxB,yBAAS,cAAc,KAAK,MAAM;AAAA,cACtC;AAAA,YACJ;AACA,mBAAO,eAAe,KAAK;AAC3B,oBAAQ,OAAO;AACf,oBAAQ,SAAS;AACjB,gBAAI,OAAO;AACP,yBAAW,MAAM;AACb,yBAAS,YAAY,QAAQ,IAAI;AAAA,cACrC,CAAC;AAAA,YACL;AAAA,UACJ;AACA,kBAAQ,SAAS;AACjB,kBAAQ,MAAM;AACd,mBAAS,MAAM;AAAA,QACnB;AAAA,MACJ;AACA,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,iBAAiB,CAAC,KAAK,eAAe,UAAU;AAClD,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,SAAS,iBAAAD,QAAQ,SAAS,aAAa,IAAI,SAAS,iBAAiB,aAAa,IAAI;AAC5F,UAAI,OAAO,UAAU,aAAa,UAAU,KAAK,aAAa,OAAO,UAAU,KAAK,CAAC,SAAS,kBAAkB,GAAG,GAAG;AAClH,eAAO,QAAQ,QAAQ,QAAQ,SAAS,YAAY,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,MAAM;AAChF,gBAAM,OAAO,SAAS,eAAe,KAAK,MAAM;AAChD,cAAI,MAAM;AACN,6BAAiB;AAAA,cACb;AAAA,cACA,UAAU,SAAS,YAAY,GAAG;AAAA,cAClC;AAAA,cACA,aAAa,SAAS,eAAe,MAAM;AAAA,cAC3C;AAAA,cACA,QAAQ;AAAA,YACZ,GAAG,MAAM,OAAO,KAAK;AACrB,yBAAa,gBAAgB,KAAK,IAAI;AAAA,UAC1C;AACA,iBAAO,SAAS;AAAA,QACpB,CAAC;AAAA,MACL;AACA,aAAO,SAAS;AAAA,IACpB;AACA,kBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,OAAO,SAAS;AACZ,eAAO,kBAAkB,SAAS,IAAI;AAAA,MAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAS,SAAS,WAAW;AACzB,eAAO,kBAAkB,SAAS,SAAS;AAAA,MAC/C;AAAA,MACA,aAAa,SAAS,WAAW;AAC7B,eAAO,kBAAkB,SAAS,WAAW,IAAI;AAAA,MACrD;AAAA,MACA,YAAY,SAAS,WAAW;AAC5B,eAAO,uBAAuB,SAAS,WAAW,IAAI;AAAA,MAC1D;AAAA,MACA,cAAc,SAAS,WAAW,WAAW;AACzC,eAAO,uBAAuB,SAAS,WAAW,SAAS;AAAA,MAC/D;AAAA,MACA,kBAAkB,SAAS,WAAW,WAAW;AAC7C,eAAO,uBAAuB,SAAS,WAAW,WAAW,IAAI;AAAA,MACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,OAAO,MAAM;AACT,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,WAAW,iBAAiB,IAAI;AACxC,cAAM,EAAE,mBAAmB,oBAAoB,eAAe,eAAe,eAAe,gBAAgB,eAAe,cAAc,IAAI;AAC7I,cAAM,eAAe,oBAAoB;AACzC,cAAM,WAAW,gBAAgB;AACjC,cAAM,EAAE,WAAW,iBAAiB,IAAI;AACxC,cAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,EAAE,WAAW,IAAI;AACvB,YAAI,UAAU,CAAC;AACf,YAAI,CAAC,MAAM;AACP,iBAAO;AAAA,QACX,WACS,CAAC,iBAAAA,QAAQ,QAAQ,IAAI,GAAG;AAC7B,iBAAO,CAAC,IAAI;AAAA,QAChB;AAEA,aAAK,QAAQ,CAAC,QAAQ;AAClB,cAAI,CAAC,SAAS,cAAc,GAAG,GAAG;AAC9B,kBAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,0BAAc,KAAK,IAAI;AAAA,UAC3B;AAAA,QACJ,CAAC;AAED,YAAI,CAAC,YAAY;AACb,eAAK,QAAQ,CAAC,QAAQ;AAClB,kBAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,gBAAI,mBAAmB,KAAK,GAAG;AAC3B,qBAAO,mBAAmB,KAAK;AAAA,YACnC;AAAA,UACJ,CAAC;AACD,oBAAU;AAAA,QACd;AAEA,YAAI,kBAAkB,MAAM;AACxB,iBAAO,UAAU,cAAc,MAAM,CAAC;AACtC,uBAAa,gBAAgB,CAAC;AAC9B,uBAAa,gBAAgB,CAAC;AAC9B,mBAAS,gBAAgB;AAAA,QAC7B,OACK;AAED,cAAI,cAAc,WAAW;AACzB,iBAAK,QAAQ,CAAC,QAAQ;AAClB,oBAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,oBAAM,cAAc,iBAAAA,QAAQ,SAAS,mBAAmB,UAAQ,UAAU,SAAS,UAAU,IAAI,GAAG,EAAE,UAAU,iBAAiB,CAAC;AAClI,kBAAI,aAAa;AACb,sBAAM,SAAS,YAAY,MAAM,OAAO,YAAY,OAAO,CAAC;AAC5D,wBAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,cAC1B;AACA,oBAAM,WAAW,iBAAAA,QAAQ,SAAS,mBAAmB,UAAQ,UAAU,SAAS,UAAU,IAAI,GAAG,EAAE,UAAU,cAAc,CAAC;AAC5H,kBAAI,UAAU;AACV,yBAAS,MAAM,OAAO,SAAS,OAAO,CAAC;AAAA,cAC3C;AACA,oBAAM,UAAU,SAAS,eAAe,eAAe,GAAG;AAC1D,kBAAI,UAAU,IAAI;AACd,8BAAc,OAAO,SAAS,CAAC;AAAA,cACnC;AAAA,YACJ,CAAC;AAAA,UACL,WACS,kBAAkB;AAEvB,oBAAQ,QAAQ,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAAA,UACpD,OACK;AACD,iBAAK,QAAQ,CAAC,QAAQ;AAClB,oBAAM,UAAU,SAAS,eAAe,eAAe,GAAG;AAC1D,kBAAI,UAAU,IAAI;AACd,sBAAM,SAAS,cAAc,OAAO,SAAS,CAAC;AAC9C,wBAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,cAC1B;AACA,oBAAM,UAAU,SAAS,eAAe,eAAe,GAAG;AAC1D,kBAAI,UAAU,IAAI;AAEd,8BAAc,QAAQ,CAAC,cAAc;AACjC,wBAAM,EAAE,KAAK,eAAe,SAAS,aAAa,IAAI;AACtD,sBAAI,gBAAgB,SAAS;AACzB,8BAAU,MAAM,gBAAgB;AAAA,kBACpC,WACS,gBAAgB,eAAe,SAAS;AAC7C,8BAAU,UAAU,eAAe;AAAA,kBACvC;AAAA,gBACJ,CAAC;AACD,8BAAc,OAAO,SAAS,CAAC;AAAA,cACnC;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,YAAI,QAAQ,OAAO,SAAS,eAAe,MAAM,QAAQ,GAAG,IAAI,IAAI;AAChE,sBAAY,UAAU;AAAA,QAC1B;AAEA,aAAK,QAAQ,CAAC,QAAQ;AAClB,gBAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,cAAI,cAAc,KAAK,GAAG;AACtB,mBAAO,cAAc,KAAK;AAAA,UAC9B;AACA,cAAI,eAAe,KAAK,GAAG;AACvB,mBAAO,eAAe,KAAK;AAAA,UAC/B;AAAA,QACJ,CAAC;AACD,kBAAU;AACV,kBAAU;AACV,kBAAU;AACV,iBAAS,YAAY,KAAK;AAC1B,iBAAS,gBAAgB,cAAc,SAAS;AAChD,iBAAS,aAAa;AACtB,iBAAS,sBAAsB;AAC/B,YAAI,EAAE,cAAc,YAAY;AAC5B,mBAAS,qBAAqB;AAAA,QAClC;AACA,iBAAS,qBAAqB;AAC9B,YAAI,UAAU,aAAa;AACvB,mBAAS,mBAAmB;AAAA,QAChC;AACA,eAAO,SAAS,EAAE,KAAK,MAAM;AACzB,mBAAS,gBAAgB;AACzB,iBAAO,SAAS,YAAY,IAAI;AAAA,QACpC,CAAC,EAAE,KAAK,MAAM;AACV,iBAAO,EAAE,KAAK,QAAQ,SAAS,QAAQ,QAAQ,SAAS,CAAC,IAAI,MAAM,MAAM,QAAQ;AAAA,QACrF,CAAC;AAAA,MACL;AAAA;AAAA;AAAA;AAAA,MAIA,oBAAoB;AAChB,eAAO,YAAY,OAAO,SAAS,mBAAmB,CAAC,EAAE,KAAK,CAAC,WAAW;AACtE,mBAAS,iBAAiB;AAC1B,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB;AACb,cAAM,cAAc,SAAS,eAAe;AAC5C,eAAO,YAAY,OAAO,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW;AAC1D,mBAAS,cAAc;AACvB,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA;AAAA;AAAA;AAAA,MAIA,mBAAmB;AACf,cAAM,gBAAgB,SAAS,iBAAiB;AAChD,eAAO,YAAY,OAAO,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW;AAC5D,mBAAS,gBAAgB;AACzB,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA;AAAA;AAAA;AAAA,MAIA,eAAe;AACX,cAAM,gBAAgB,YAAY,iBAAiB;AACnD,cAAM,iBAAiB,SAAS,kBAAkB;AAClD,cAAM,aAAa,cAAc,OAAO,cAAc;AAEtD,cAAM,gBAAgB,YAAY,iBAAiB,EAAE,OAAO,SAAO;AAC/D,iBAAO,CAAC,WAAW,KAAK,UAAQ,SAAS,MAAM,MAAM,GAAG,CAAC;AAAA,QAC7D,CAAC;AACD,eAAO;AAAA,UACH,eAAe,YAAY,iBAAiB;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA;AAAA;AAAA,MAIA,mBAAmB;AACf,cAAM,EAAE,sBAAsB,cAAc,IAAI;AAChD,cAAM,gBAAgB,CAAC;AACvB,yBAAAA,QAAQ,KAAK,eAAe,CAAC,KAAK,UAAU;AACxC,cAAI,qBAAqB,KAAK,GAAG;AAC7B,0BAAc,KAAK,GAAG;AAAA,UAC1B;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA,MAIA,mBAAmB;AACf,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,gBAAgB,CAAC;AACvB,yBAAAA,QAAQ,KAAK,eAAe,CAAC,QAAQ;AACjC,wBAAc,KAAK,GAAG;AAAA,QAC1B,CAAC;AACD,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,mBAAmB;AACf,cAAM,EAAE,YAAY,WAAW,IAAI;AACnC,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,WAAW,gBAAgB;AACjC,YAAI,YAAY;AACZ,0BAAgB;AAChB,cAAI,YAAY;AACZ,mBAAO,iBAAAA,QAAQ,WAAW,eAAe,SAAO,SAAS,cAAc,GAAG,GAAG,QAAQ;AAAA,UACzF;AACA,iBAAO,cAAc,OAAO,CAAC,QAAQ,SAAS,cAAc,GAAG,CAAC;AAAA,QACpE;AACA,eAAO,CAAC;AAAA,MACZ;AAAA,MACA,kBAAkB;AACd,gBAAQ,qBAAqB,CAAC,mBAAmB,aAAa,CAAC;AAC/D,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,qBAAqB,IAAI;AACjC,cAAM,EAAE,MAAM,IAAI,IAAI,UAAU;AAChC,YAAI,QAAQ,OAAO,qBAAqB,SAAS,UAAU,GAAG,CAAC,GAAG;AAC9D,iBAAO,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,QAC1C;AACA,eAAO;AAAA,MACX;AAAA,MACA,gBAAgB;AACZ,gBAAQ,qBAAqB,CAAC,iBAAiB,aAAa,CAAC;AAC7D,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,qBAAqB,IAAI;AACjC,cAAM,EAAE,MAAM,IAAI,IAAI,UAAU;AAChC,YAAI,QAAQ,OAAO,qBAAqB,SAAS,UAAU,GAAG,CAAC,GAAG;AAC9D,iBAAO,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,QAC1C;AACA,eAAO;AAAA,MACX;AAAA,MACA,cAAc;AACV,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,KAAK,OAAO,IAAI,UAAU;AAClC,YAAI,UAAU,KAAK;AACf,iBAAO,EAAE,KAAK,OAAO;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA,MAIA,kBAAkB;AACd,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,KAAK,OAAO,IAAI,UAAU;AAClC,YAAI,OAAO,QAAQ;AACf,iBAAO,EAAE,KAAK,OAAO;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AAAA,MACA,aAAa,KAAK;AAEd,gBAAQ,qBAAqB,CAAC,gBAAgB,WAAW,CAAC;AAC1D,eAAO,SAAS,UAAU,GAAG;AAAA,MACjC;AAAA;AAAA;AAAA;AAAA,MAIA,UAAU,KAAK;AACX,eAAO,gBAAgB,MAAM,GAAG;AAAA,MACpC;AAAA;AAAA;AAAA;AAAA,MAIA,gBAAgB;AACZ,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,SAAS,IAAI;AACrB,iBAAS,MAAM;AACf,iBAAS,SAAS;AAClB,gCAAwB;AACxB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,cAAc,KAAK;AACf,gBAAQ,qBAAqB,CAAC,iBAAiB,aAAa,CAAC;AAE7D,eAAO,SAAS,YAAY,GAAG;AAAA,MACnC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,YAAY,KAAK;AACb,cAAM,EAAE,UAAU,IAAI;AACtB,eAAO,UAAU,QAAQ,QAAQ;AAAA,MACrC;AAAA,MACA,aAAa,KAAK;AACd,gBAAQ,qBAAqB,CAAC,gBAAgB,YAAY,CAAC;AAE3D,eAAO,YAAY,WAAW,GAAG;AAAA,MACrC;AAAA;AAAA;AAAA;AAAA,MAIA,WAAW,KAAK,eAAe;AAC3B,cAAM,EAAE,cAAc,IAAI;AAC1B,YAAI,SAAS,iBAAAA,QAAQ,KAAK,eAAe,CAAAC,YAAU,aAAaA,QAAO,UAAU,CAAC;AAClF,YAAI,QAAQ;AACZ,YAAI,eAAe;AACf,kBAAQ;AACR,cAAI,kBAAkB,MAAM;AACxB,qBAAS,iBAAAD,QAAQ,SAAS,aAAa,IAAI,SAAS,iBAAiB,aAAa,IAAI;AAAA,UAC1F;AAAA,QACJ;AACA,eAAO,eAAe,KAAK,QAAQ,KAAK;AAAA,MAC5C;AAAA,MACA,cAAc,KAAK,eAAe;AAC9B,gBAAQ,qBAAqB,CAAC,iBAAiB,aAAa,CAAC;AAE7D,eAAO,YAAY,YAAY,KAAK,aAAa;AAAA,MACrD;AAAA;AAAA;AAAA;AAAA,MAIA,YAAY,KAAK,eAAe;AAC5B,eAAO,eAAe,KAAK,eAAe,IAAI;AAAA,MAClD;AAAA;AAAA;AAAA;AAAA,MAIA,cAAc,KAAK,eAAe;AAC9B,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,WAAW,gBAAgB;AACjC,cAAM,SAAS,iBAAAA,QAAQ,SAAS,aAAa,IAAI,SAAS,iBAAiB,aAAa,IAAI;AAC5F,YAAI,OAAO,UAAU,SAAS,YAAY,UAAU;AAChD,gBAAM,WAAW,SAAS,eAAe,WAAW,GAAG;AACvD,cAAI,WAAW,MAAM,QAAQ;AACzB,kBAAM,OAAO,SAAS,eAAe,KAAK,MAAM;AAChD,kBAAM,SAAS;AAAA,cACX;AAAA,cACA;AAAA,cACA;AAAA,cACA,aAAa,SAAS,eAAe,MAAM;AAAA,cAC3C;AAAA,YACJ;AACA,qBAAS,eAAe,QAAQ,CAAC,CAAC;AAAA,UACtC;AAAA,QACJ;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ;AACA,yBAAqB;AAAA;AAAA;AAAA;AAAA,MAIjB,WAAW,QAAQ,MAAM;AACrB,eAAO,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA,MAIA,cAAc,QAAQ,MAAM;AACxB,eAAO,mBAAmB,WAAW,QAAQ,IAAI;AAAA,MACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA;AAAA;AAAA;AAAA,MAIA,YAAY,QAAQ;AAChB,cAAM,EAAE,KAAK,QAAQ,KAAK,IAAI;AAC9B,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,WAAW,gBAAgB;AACjC,YAAI,aAAa,UAAU,GAAG;AAC1B,gBAAM,aAAaH,UAAS,IAAI,WAAW,IAAI;AAC/C,cAAI,YAAY,WAAW,aAAa,WAAW;AACnD,cAAI,aAAa,WAAW,cAAc,WAAW;AACrD,cAAI;AAEJ,cAAI,SAAS,WAAW;AACpB,gBAAI,CAAC,aAAa,YAAY;AAC1B,0BAAY,WAAW,kBAAkB,WAAW,kBAAkB,WAAW;AAAA,YACrF;AACA,gBAAI,CAAC,cAAc,YAAY;AAC3B,2BAAa,WAAW,mBAAmB,WAAW;AAAA,YAC1D;AAEA,gBAAI,iBAAAG,QAAQ,WAAW,SAAS,GAAG;AAC/B,0BAAY,UAAU,MAAM;AAAA,YAChC,WACS,WAAW;AAChB,kBAAI,cAAc,MAAM;AAEpB,4BAAY,KAAK,cAAc,gBAAgB;AAAA,cACnD,OACK;AACD,4BAAY,KAAK,cAAc,SAAS;AAAA,cAC5C;AACA,kBAAI,WAAW;AACX,0BAAU,MAAM;AAAA,cACpB;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,WAAW;AACX,gBAAI,YAAY;AACZ,wBAAU,OAAO;AAAA,YACrB,OACK;AAED,kBAAID,WAAU,MAAM;AAChB,sBAAM,YAAY,UAAU,gBAAgB;AAC5C,0BAAU,SAAS,KAAK;AACxB,0BAAU,OAAO;AAAA,cACrB;AAAA,YACJ;AAAA,UACJ,OACK;AAED,gBAAI,SAAS,SAAS;AAClB,kBAAI,CAAC,OAAO,OAAO;AAEf,yBAAS,YAAY,KAAK,MAAM;AAAA,cACpC;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA;AAAA;AAAA,MAIA,eAAe,QAAQ,MAAM;AACzB,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,YAAY,iBAAiB;AACnC,cAAM,WAAW,gBAAgB;AACjC,cAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,cAAM,EAAE,KAAK,OAAO,IAAI;AACxB,cAAM,kBAAkB,eAAe,UAAU;AACjD,cAAM,eAAe,MAAM;AACvB,cAAI,oBAAoB,SAAS,QAAQ,OAAO,SAAS,WAAW,SAAS;AACzE,gBAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,QAAQ,WAAW,SAAS,QAAQ;AACvF,8BAAgB,IAAI;AACpB,uBAAS,cAAc;AACvB,kBAAI,SAAS,gBAAgB;AACzB,yBAAS,eAAe;AACxB,yBAAS,kBAAkB;AAAA,cAC/B;AACA,uBAAS,OAAO;AAChB,uBAAS,MAAM;AACf,uBAAS,SAAS;AAClB,kBAAI,iBAAiB;AACjB,mCAAmB,qBAAqB;AAAA,cAC5C;AACA,uBAAS,MAAM;AACf,kBAAI,MAAM;AACN,yBAAS,cAAc,iBAAiB,QAAQ,IAAI;AAAA,cACxD;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO,SAAS;AAAA,QACpB;AACA,eAAO,aAAa;AAAA,MACxB;AAAA,MACA,uBAAuB;AACnB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,SAAS,IAAI;AACrB,cAAM,EAAE,KAAK,OAAO,IAAI;AACxB,gCAAwB;AACxB,YAAI,OAAO,QAAQ;AACf,gBAAM,OAAO,SAAS,eAAe,KAAK,MAAM;AAChD,cAAI,MAAM;AACN,qBAAS,MAAM,eAAe;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,kBAAkB;AAAA,EAC3E;AAAA,EACA,UAAU,SAAS;AACf,WAAO,QAAQ,mBAAmB,mBAAmB;AAAA,EACzD;AACJ,CAAC;;;AC/8BD,IAAAI,mBAAoB;;;ACApB,IAAM,mBAAmB;AAClB,SAAS,uBAAuB,SAAS,SAAS;AACrD,SAAO,IAAI,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,QAAQ,QAAQ,IAAI,kBAAkB,CAAC;AAC9E;AACO,SAAS,eAAe,MAAM,SAAS;AAC1C,QAAM,EAAE,MAAM,IAAI;AAClB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,KAAK,SAAS;AAAA,IACxB;AAAA,IACA,UAAU,gBAAgB;AAAA,IAC1B,QAAQ,UAAU,KAAK,aAAa;AAAA,IACpC;AAAA,IACA,SAAS,OAAO;AAAA,IAChB;AAAA,EACJ,EAAE,KAAK,EAAE;AACb;;;ADXA,IAAM,EAAE,SAAAC,UAAS,OAAAC,QAAO,UAAAC,UAAS,IAAI;AACrC,IAAI;AACJ,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,SAAS,0BAA0B,QAAQ;AACvC,SAAO,CAAC,CAAC,OAAO,SAAS,CAAC,OAAO,YAAY,OAAO,EAAE,QAAQ,OAAO,QAAQ,EAAE,MAAM;AACzF;AACA,IAAM,oBAAoB,CAAC,YAAY;AACnC,QAAM,SAAS,CAAC;AAChB,UAAQ,QAAQ,CAAC,WAAW;AACxB,QAAI,OAAO,cAAc,OAAO,WAAW,QAAQ;AAC/C,aAAO,KAAK,MAAM;AAClB,aAAO,KAAK,GAAG,kBAAkB,OAAO,UAAU,CAAC;AAAA,IACvD,OACK;AACD,aAAO,KAAK,MAAM;AAAA,IACtB;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAM,gBAAgB,CAAC,kBAAkB;AACrC,MAAI,WAAW;AACf,QAAM,WAAW,CAAC,QAAQ,WAAW;AACjC,QAAI,QAAQ;AACR,aAAO,SAAS,OAAO,SAAS;AAChC,UAAI,WAAW,OAAO,QAAQ;AAC1B,mBAAW,OAAO;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,OAAO,cAAc,OAAO,WAAW,QAAQ;AAC/C,UAAI,UAAU;AACd,aAAO,WAAW,QAAQ,CAAC,cAAc;AACrC,iBAAS,WAAW,MAAM;AAC1B,mBAAW,UAAU;AAAA,MACzB,CAAC;AACD,aAAO,WAAW;AAAA,IACtB,OACK;AACD,aAAO,WAAW;AAAA,IACtB;AAAA,EACJ;AACA,gBAAc,QAAQ,CAAC,WAAW;AAC9B,WAAO,SAAS;AAChB,aAAS,MAAM;AAAA,EACnB,CAAC;AACD,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,SAAK,KAAK,CAAC,CAAC;AAAA,EAChB;AACA,QAAM,aAAa,kBAAkB,aAAa;AAClD,aAAW,QAAQ,CAAC,WAAW;AAC3B,QAAI,OAAO,cAAc,OAAO,WAAW,QAAQ;AAC/C,aAAO,WAAW;AAAA,IACtB,OACK;AACD,aAAO,WAAW,WAAW,OAAO,SAAS;AAAA,IACjD;AACA,SAAK,OAAO,SAAS,CAAC,EAAE,KAAK,MAAM;AAAA,EACvC,CAAC;AACD,SAAO;AACX;AACA,SAAS,cAAc,QAAQ;AAC3B,MAAI,WAAW,MAAM;AACjB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACR,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,WAAW;AAChC,SAAO,cAAc,UAAU,cAAc,UAAU,cAAc;AACzE;AACA,SAAS,cAAc,UAAU,MAAM,iBAAiB;AACpD,QAAM,EAAE,mBAAmB,IAAI;AAC/B,SAAO,qBAAqB,gBAAgB,OAAO,CAAC,OAAO,UAAU,mBAAmB,EAAE,QAAQ,UAAU,OAAO,WAAW,MAAM,CAAC,CAAC,IAAI;AAC9I;AACA,SAAS,oBAAoB,QAAQ,WAAW;AAC5C,MAAI,WAAW;AACX,QAAI,OAAO,SAAS,OAAO;AACvB,aAAO,IAAK,SAAS;AAAA,IACzB;AACA,YAAQ,OAAO,UAAU;AAAA,MACrB,KAAK;AACD,YAAI,CAAC,MAAM,SAAS,GAAG;AACnB,iBAAO,IAAK,SAAS;AAAA,QACzB;AACA;AAAA,MACJ,KAAK;AACD;AAAA,MACJ;AACI,YAAI,UAAU,UAAU,MAAM,CAAC,MAAM,SAAS,GAAG;AAC7C,iBAAO,IAAK,SAAS;AAAA,QACzB;AACA;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,WAAW,KAAK,GAAG,GAAG;AACtB,WAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;AAAA,EACtC;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM,eAAe;AAC/C,SAAO,KAAK,qBAAqB,aAAa;AAClD;AACA,SAAS,cAAc,KAAK;AACxB,SAAO,IAAI,GAAG,IAAI,iBAAAC,QAAQ,SAAS,CAAC;AACxC;AACA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAO,KAAK,QAAQ,aAAa,CAAC,QAAQ,iBAAAA,QAAQ,WAAW,OAAO,GAAG,IAAI,MAAM,GAAG,IAAI,GAAG;AAC/F;AACA,SAAS,gBAAgB,KAAK,OAAO;AACjC,QAAM,OAAO,eAAe,KAAK,KAAK;AACtC,SAAO,KAAK,QAAQ,SAAS,CAAC,SAAS,IAAI,OAAO,KAAK,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC;AACjF;AACA,SAAS,cAAc,WAAW,OAAO;AACrC,QAAM,EAAE,WAAW,UAAU,IAAI;AAEjC,MAAI,CAAC,UAAU,KAAK,GAAG;AACnB,UAAM,QAAQ,UAAU,KAAK;AAC7B,QAAI,SAAS,MAAM,OAAO;AACtB,cAAQ,MAAM;AAAA,IAClB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,WAAW,SAAS,eAAe;AACvD,QAAM,OAAO,QAAQ,MAAM,WAAW;AACtC,QAAM,OAAO,CAAC;AACd,MAAI,SAAS,CAAC;AACd,MAAI,KAAK,QAAQ;AACb,UAAM,QAAQ,CAAC;AACf,UAAM,MAAM,KAAK,IAAI;AACrB,SAAK,QAAQ,CAAC,SAAS;AACnB,UAAI,MAAM;AACN,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,QAAQ,cAAc,CAAC,MAAM,SAAS;AAC9C,gBAAM,MAAM,cAAc,GAAG;AAC7B,gBAAM,GAAG,IAAI,OAAO,MAAM;AAC1B,iBAAO;AAAA,QACX,CAAC,EAAE,QAAQ,YAAY,CAAC,MAAM,SAAS;AACnC,gBAAM,MAAM,cAAc,GAAG;AAC7B,gBAAM,GAAG,IAAI,eAAe,MAAM,KAAK;AACvC,iBAAO;AAAA,QACX,CAAC;AACD,cAAM,QAAQ,KAAK,MAAM,aAAa;AACtC,YAAI,CAAC,OAAO,QAAQ;AAChB,mBAAS,MAAM,IAAI,CAAC,QAAQ,cAAc,WAAW,gBAAgB,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC;AAAA,QAC5F,OACK;AACD,gBAAM,QAAQ,CAAC,KAAK,aAAa;AAC7B,gBAAI,WAAW,OAAO,QAAQ;AAC1B,mBAAK,OAAO,QAAQ,CAAC,IAAI,gBAAgB,IAAI,KAAK,GAAG,KAAK;AAAA,YAC9D;AAAA,UACJ,CAAC;AACD,eAAK,KAAK,IAAI;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO,EAAE,QAAQ,KAAK;AAC1B;AACA,SAAS,SAAS,WAAW,SAAS;AAClC,SAAO,eAAe,WAAW,SAAS,GAAG;AACjD;AACA,SAAS,SAAS,WAAW,SAAS;AAClC,SAAO,eAAe,WAAW,SAAS,GAAI;AAClD;AACA,SAAS,UAAU,WAAW,SAAS;AACnC,QAAM,YAAY,IAAI,UAAU;AAChC,QAAM,SAAS,UAAU,gBAAgB,SAAS,WAAW;AAC7D,QAAM,YAAY,qBAAqB,QAAQ,MAAM;AACrD,QAAM,OAAO,CAAC;AACd,QAAM,SAAS,CAAC;AAChB,MAAI,UAAU,QAAQ;AAClB,UAAM,aAAa,qBAAqB,UAAU,CAAC,GAAG,OAAO;AAC7D,QAAI,WAAW,QAAQ;AACnB,YAAM,aAAa,qBAAqB,WAAW,CAAC,GAAG,OAAO;AAC9D,UAAI,WAAW,QAAQ;AACnB,yBAAAA,QAAQ,UAAU,qBAAqB,WAAW,CAAC,GAAG,IAAI,GAAG,aAAW;AACpE,2BAAAA,QAAQ,UAAU,qBAAqB,SAAS,IAAI,GAAG,cAAY;AAC/D,mBAAO,KAAK,cAAc,WAAW,SAAS,eAAe,EAAE,CAAC;AAAA,UACpE,CAAC;AAAA,QACL,CAAC;AACD,cAAM,aAAa,qBAAqB,WAAW,CAAC,GAAG,OAAO;AAC9D,YAAI,WAAW,QAAQ;AACnB,2BAAAA,QAAQ,UAAU,qBAAqB,WAAW,CAAC,GAAG,IAAI,GAAG,aAAW;AACpE,kBAAM,OAAO,CAAC;AACd,6BAAAA,QAAQ,UAAU,qBAAqB,SAAS,IAAI,GAAG,CAAC,UAAU,aAAa;AAC3E,kBAAI,OAAO,QAAQ,GAAG;AAClB,qBAAK,OAAO,QAAQ,CAAC,IAAI,SAAS,eAAe;AAAA,cACrD;AAAA,YACJ,CAAC;AACD,iBAAK,KAAK,IAAI;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,KAAK;AAC1B;AACA,SAAS,SAAS,WAAW,SAAS;AAClC,QAAM,YAAY,IAAI,UAAU;AAChC,QAAM,SAAS,UAAU,gBAAgB,SAAS,iBAAiB;AACnE,QAAM,aAAa,qBAAqB,QAAQ,WAAW;AAC3D,QAAM,OAAO,CAAC;AACd,QAAM,SAAS,CAAC;AAChB,MAAI,WAAW,QAAQ;AACnB,UAAM,aAAa,qBAAqB,WAAW,CAAC,GAAG,OAAO;AAC9D,QAAI,WAAW,QAAQ;AACnB,YAAM,WAAW,qBAAqB,WAAW,CAAC,GAAG,KAAK;AAC1D,UAAI,SAAS,QAAQ;AACjB,yBAAAA,QAAQ,UAAU,qBAAqB,SAAS,CAAC,GAAG,MAAM,GAAG,cAAY;AACrE,iBAAO,KAAK,cAAc,WAAW,SAAS,eAAe,EAAE,CAAC;AAAA,QACpE,CAAC;AACD,yBAAAA,QAAQ,UAAU,UAAU,CAAC,SAAS,UAAU;AAC5C,cAAI,OAAO;AACP,kBAAM,OAAO,CAAC;AACd,kBAAM,YAAY,qBAAqB,SAAS,MAAM;AACtD,6BAAAA,QAAQ,UAAU,WAAW,CAAC,UAAU,aAAa;AACjD,kBAAI,OAAO,QAAQ,GAAG;AAClB,qBAAK,OAAO,QAAQ,CAAC,IAAI,SAAS;AAAA,cACtC;AAAA,YACJ,CAAC;AACD,iBAAK,KAAK,IAAI;AAAA,UAClB;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,KAAK;AAC1B;AACA,SAAS,mBAAmB,SAAS;AACjC,mBAAAA,QAAQ,SAAS,SAAS,CAAC,WAAW;AAClC,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,OAAO;AAAA,EAClB,GAAG,EAAE,UAAU,WAAW,CAAC;AAC/B;AACA,IAAM,wBAAwB,CAAC,cAAc,gBAAgB,cAAc,YAAY,YAAY,SAAS,gBAAgB,cAAc,eAAe,cAAc,eAAe,aAAa,YAAY;AAC/MF,OAAM,IAAI,qBAAqB;AAAA,EAC3B,WAAW,UAAU;AACjB,UAAM,EAAE,OAAO,WAAW,aAAa,IAAI;AAC3C,UAAM,EAAE,iBAAiB,kBAAkB,mBAAmB,mBAAmB,mBAAmB,gBAAgB,kBAAkB,qBAAqB,kBAAkB,IAAI,SAAS,eAAe;AACzM,UAAM,UAAU,OAAO,WAAW,IAAI;AACtC,UAAM,kBAAkB,CAAC,QAAQ;AAC7B,YAAM,WAAW,gBAAgB;AACjC,YAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,aAAO,IAAI,aAAa,KAAK,IAAI,aAAa,EAAE;AAAA,IACpD;AACA,UAAM,SAAS,CAAC,WAAW,KAAK,WAAW,QAAQ,iBAAiB;AAChE,YAAM,UAAU,eAAe;AAC/B,YAAM,YAAY,QAAQ,aAAa,OAAO;AAC9C,UAAI,WAAW;AACX,eAAO,UAAU;AAAA,UACb,QAAQ;AAAA,UACR;AAAA,UACA,UAAU,SAAS,YAAY,GAAG;AAAA,UAClC;AAAA,UACA;AAAA,UACA,aAAa,SAAS,eAAe,MAAM;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AACA,aAAS,eAAe,MAAM,QAAQ;AAClC,YAAM,aAAa,kBAAkB;AACrC,YAAM,mBAAmB,OAAO,sBAAsB,WAAW;AACjE,aAAO,mBAAmB,iBAAiB,EAAE,QAAQ,SAAS,MAAM,QAAQ,SAAS,CAAC,KAAM,KAAK,UAAU,OAAO,SAAS,IAAI,OAAO,UAAU;AAAA,IACpJ;AACA,UAAM,iBAAiB,CAAC,cAAc;AAClC,aAAO,iBAAAE,QAAQ,UAAU,SAAS,IAAK,YAAY,SAAS,UAAW;AAAA,IAC3E;AACA,UAAM,gBAAgB,CAAC,cAAc;AACjC,aAAO,aAAa,SAAS,IAAI,KAAK,GAAG,SAAS;AAAA,IACtD;AACA,UAAM,mBAAmB,CAAC,MAAM,SAAS,UAAU;AAC/C,YAAM,EAAE,aAAa,KAAK,IAAI;AAC9B,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,YAAY,iBAAiB;AACnC,YAAM,eAAe,oBAAoB;AACzC,YAAM,WAAW,gBAAgB;AACjC,YAAM,aAAa,kBAAkB;AACrC,UAAI,CAAC,cAAc;AACf,uBAAe,SAAS,cAAc,KAAK;AAAA,MAC/C;AACA,UAAI,YAAY;AACZ,cAAM,gBAAgB,SAAS,YAAY,SAAS;AAEpD,cAAM,OAAO,CAAC;AACd,cAAM,aAAa,CAAC;AACpB,cAAM,UAAU,CAAC;AACjB,cAAM,EAAE,eAAe,IAAI,qBAAqB,QAAQ;AACxD,yBAAAA,QAAQ,SAAS,OAAO,CAAC,MAAM,WAAW,OAAO,MAAM,QAAQ,UAAU;AACrE,gBAAM,MAAM,KAAK,QAAQ;AACzB,gBAAM,QAAQ,eAAe,GAAG;AAChC,cAAI,QAAQ,KAAK,GAAG;AAChB;AAAA,UACJ;AACA,gBAAM,YAAY,UAAU,OAAO,OAAO,OAAO,OAAO;AACxD,gBAAM,SAAS,YAAY,eAAe,SAAS,IAAI;AACvD,cAAK,eAAe,CAAC,aAAc,WAAW,MAAM,KAAK,SAAS,kBAAkB,SAAS,GAAK;AAC9F,kBAAM,cAAc,gBAAgB,GAAG;AACvC,kBAAMC,QAAO;AAAA,cACT,MAAM;AAAA,cACN,QAAQ,MAAM,SAAS;AAAA,cACvB,WAAW;AAAA,cACX,SAAS,eAAe,SAAS,kBAAkB,GAAG;AAAA,YAC1D;AACA,oBAAQ,QAAQ,CAAC,QAAQ,iBAAiB;AACtC,kBAAI,YAAY;AAChB,oBAAM,aAAa,OAAO,cAAc,OAAO;AAC/C,kBAAI,mBAAmB,OAAO,gBAAgB,WAAW;AACzD,kBAAI,CAAC,oBAAoB,cAAc,WAAW,MAAM;AACpD,sBAAM,WAAWF,UAAS,IAAI,WAAW,IAAI;AAC7C,oBAAI,UAAU;AACV,qCAAmB,SAAS,qBAAqB,SAAS;AAAA,gBAC9D;AAAA,cACJ;AACA,kBAAI,CAAC,kBAAkB;AACnB,mCAAmB,WAAW;AAAA,cAClC;AACA,kBAAI,kBAAkB;AAClB,4BAAY,iBAAiB,EAAE,QAAQ,UAAU,KAAK,QAAQ,SAAS,KAAK,CAAC;AAAA,cACjF,OACK;AACD,wBAAQ,OAAO,MAAM;AAAA,kBACjB,KAAK,OAAO;AACR,0BAAM,SAAS,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,IAAK,OAAO,GAAG,IAAI,IAAK,GAAG,EAAE,KAAK,EAAE;AAClF,gCAAY,SAAS,QAAQ,SAAS,OAAO,QAAQ,KAAK,WAAW,QAAQ,YAAY;AACzF;AAAA,kBACJ;AAAA,kBACA,KAAK;AACD,gCAAY,eAAe,SAAS,uBAAuB,GAAG,CAAC;AAC/D,oBAAAE,MAAK,iBAAiB,aAAa,aAAa,iBAAAD,QAAQ,IAAI,KAAK,aAAa,UAAU,IAAI;AAC5F,oBAAAC,MAAK,oBAAoB,aAAa,eAAe,CAAC,aAAa,YAAY,EAAE,QAAQ,UAAU,IAAI,CAAC;AACxG;AAAA,kBACJ,KAAK;AACD,gCAAY,eAAe,SAAS,oBAAoB,GAAG,CAAC;AAC5D,oBAAAA,MAAK,cAAc,UAAU,aAAa,iBAAAD,QAAQ,IAAI,KAAK,UAAU,UAAU,IAAI;AACnF,oBAAAC,MAAK,iBAAiB,UAAU,eAAe,CAAC,UAAU,YAAY,EAAE,QAAQ,UAAU,IAAI,CAAC;AAC/F;AAAA,kBACJ;AACI,wBAAI,KAAK,UAAU;AACf,kCAAY,aAAa,KAAK,MAAM;AAAA,oBACxC,OACK;AACD,kCAAY,SAAS,aAAa,KAAK,MAAM;AAC7C,0BAAI,OAAO,SAAS,QAAQ;AACxB,qCAAa,YAAY;AACzB,oCAAY,aAAa,UAAU,KAAK;AAAA,sBAC5C,OACK;AACD,8BAAM,OAAO,SAAS,eAAe,KAAK,MAAM;AAChD,4BAAI,QAAQ,CAAC,SAAS,MAAM,cAAc,GAAG;AACzC,sCAAY,KAAK,UAAU,KAAK;AAAA,wBACpC;AAAA,sBACJ;AAAA,oBACJ;AAAA,gBACR;AAAA,cACJ;AACA,cAAAA,MAAK,OAAO,EAAE,IAAI,cAAc,SAAS;AAAA,YAC7C,CAAC;AACD,oBAAQ,KAAK,IAAI;AACjB,gBAAI,QAAQ;AACR,yBAAW,MAAM,IAAI;AAAA,YACzB;AACA,iBAAK,KAAK,OAAO,OAAOA,OAAM,GAAG,CAAC;AAAA,UACtC;AAAA,QACJ,GAAG,EAAE,UAAU,cAAc,CAAC;AAC9B,eAAO;AAAA,MACX;AACA,aAAO,MAAM,IAAI,CAAC,KAAK,cAAc;AACjC,cAAM,OAAO;AAAA,UACT,MAAM;AAAA,QACV;AACA,gBAAQ,QAAQ,CAAC,QAAQ,iBAAiB;AACtC,cAAI,YAAY;AAChB,gBAAM,aAAa,OAAO,cAAc,OAAO;AAC/C,cAAI,mBAAmB,OAAO,gBAAgB,WAAW;AACzD,cAAI,CAAC,oBAAoB,cAAc,WAAW,MAAM;AACpD,kBAAM,WAAWF,UAAS,IAAI,WAAW,IAAI;AAC7C,gBAAI,UAAU;AACV,iCAAmB,SAAS,qBAAqB,SAAS;AAAA,YAC9D;AAAA,UACJ;AACA,cAAI,kBAAkB;AAClB,wBAAY,iBAAiB,EAAE,QAAQ,UAAU,KAAK,QAAQ,SAAS,KAAK,CAAC;AAAA,UACjF,OACK;AACD,oBAAQ,OAAO,MAAM;AAAA,cACjB,KAAK,OAAO;AACR,sBAAM,WAAW,YAAY;AAC7B,4BAAY,SAAS,QAAQ,WAAW,OAAO,UAAU,KAAK,WAAW,QAAQ,YAAY;AAC7F;AAAA,cACJ;AAAA,cACA,KAAK;AACD,4BAAY,eAAe,SAAS,uBAAuB,GAAG,CAAC;AAC/D,qBAAK,iBAAiB,aAAa,aAAa,iBAAAC,QAAQ,IAAI,KAAK,aAAa,UAAU,IAAI;AAC5F,qBAAK,oBAAoB,aAAa,eAAe,CAAC,aAAa,YAAY,EAAE,QAAQ,UAAU,IAAI,CAAC;AACxG;AAAA,cACJ,KAAK;AACD,4BAAY,eAAe,SAAS,oBAAoB,GAAG,CAAC;AAC5D,qBAAK,cAAc,UAAU,aAAa,iBAAAA,QAAQ,IAAI,KAAK,UAAU,UAAU,IAAI;AACnF,qBAAK,iBAAiB,UAAU,eAAe,CAAC,UAAU,YAAY,EAAE,QAAQ,UAAU,IAAI,CAAC;AAC/F;AAAA,cACJ;AACI,oBAAI,KAAK,UAAU;AACf,8BAAY,aAAa,KAAK,MAAM;AAAA,gBACxC,OACK;AACD,8BAAY,SAAS,aAAa,KAAK,MAAM;AAC7C,sBAAI,OAAO,SAAS,QAAQ;AACxB,iCAAa,YAAY;AACzB,gCAAY,aAAa,UAAU,KAAK;AAAA,kBAC5C,OACK;AACD,0BAAM,OAAO,SAAS,eAAe,KAAK,MAAM;AAChD,wBAAI,QAAQ,CAAC,SAAS,MAAM,cAAc,GAAG;AACzC,kCAAY,KAAK,UAAU,KAAK;AAAA,oBACpC;AAAA,kBACJ;AAAA,gBACJ;AAAA,YACR;AAAA,UACJ;AACA,eAAK,OAAO,EAAE,IAAI,cAAc,SAAS;AAAA,QAC7C,CAAC;AACD,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,UAAM,gBAAgB,CAAC,SAAS;AAC5B,YAAM,EAAE,SAAS,iBAAiB,IAAI;AACtC,UAAI,QAAQ,KAAK;AACjB,UAAI,kBAAkB;AAClB,gBAAQ,MAAM,OAAO,CAAC,KAAK,UAAU,iBAAiB,EAAE,QAAQ,UAAU,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,MACtG;AACA,aAAO,iBAAiB,MAAM,SAAS,KAAK;AAAA,IAChD;AACA,UAAM,qBAAqB,CAAC,MAAM,KAAK,WAAW;AAC9C,YAAM,aAAa,kBAAkB;AACrC,YAAM,aAAa,OAAO,cAAc,OAAO;AAC/C,UAAI,kBAAkB,OAAO;AAC7B,UAAI,CAAC,mBAAmB,cAAc,WAAW,MAAM;AACnD,cAAM,WAAWD,UAAS,IAAI,WAAW,IAAI;AAC7C,YAAI,UAAU;AACV,4BAAkB,SAAS,2BAA2B,SAAS;AAAA,QACnE;AAAA,MACJ;AACA,UAAI,CAAC,iBAAiB;AAClB,0BAAkB,WAAW;AAAA,MACjC;AACA,YAAM,eAAe,SAAS,iBAAiB,MAAM;AACrD,UAAI,iBAAiB;AACjB,eAAO,gBAAgB,EAAE,QAAQ,UAAU,OAAO,KAAK,WAAW,cAAc,KAAK,cAAc,QAAQ,SAAS,KAAK,CAAC;AAAA,MAC9H;AAEA,UAAI,iBAAAC,QAAQ,QAAQ,GAAG,GAAG;AACtB,eAAO,iBAAAA,QAAQ,cAAc,IAAI,YAAY,CAAC;AAAA,MAClD;AACA,aAAO,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAAA,IACxC;AACA,UAAM,QAAQ,CAACE,WAAU,MAAM,SAAS,UAAU;AAC9C,UAAI,UAAU;AACd,UAAI,KAAK,UAAU;AACf,mBAAW,QAAQ,IAAI,CAAC,WAAW,eAAe,eAAe,MAAM,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAAA,MACjG;AACA,YAAM,QAAQ,CAAC,QAAQ;AACnB,mBAAW,QAAQ,IAAI,CAAC,WAAW,eAAe,oBAAoB,QAAQ,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAAA,MAChH,CAAC;AACD,UAAI,KAAK,UAAU;AACf,cAAM,EAAE,gBAAgB,IAAI;AAC5B,cAAM,UAAU,cAAcA,WAAU,MAAM,eAAe;AAC7D,gBAAQ,QAAQ,CAAC,QAAQ;AACrB,qBAAW,QAAQ,IAAI,CAAC,WAAW,eAAe,mBAAmB,MAAM,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAAA,QAC1G,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,CAACA,WAAU,MAAM,SAAS,UAAU;AAC9C,UAAI,UAAU;AACd,UAAI,KAAK,UAAU;AACf,mBAAW,QAAQ,IAAI,CAAC,WAAW,eAAe,eAAe,MAAM,MAAM,CAAC,CAAC,EAAE,KAAK,GAAI,IAAI;AAAA,MAClG;AACA,YAAM,QAAQ,CAAC,QAAQ;AACnB,mBAAW,QAAQ,IAAI,CAAC,WAAW,eAAe,IAAI,OAAO,EAAE,CAAC,CAAC,EAAE,KAAK,GAAI,IAAI;AAAA,MACpF,CAAC;AACD,UAAI,KAAK,UAAU;AACf,cAAM,EAAE,gBAAgB,IAAI;AAC5B,cAAM,UAAU,cAAcA,WAAU,MAAM,eAAe;AAC7D,gBAAQ,QAAQ,CAAC,QAAQ;AACrB,qBAAW,QAAQ,IAAI,CAAC,WAAW,eAAe,mBAAmB,MAAM,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAI,IAAI;AAAA,QAC3G,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AACA,UAAM,cAAc,CAAC,QAAQ,UAAU,sBAAsB;AACzD,YAAM,iBAAiB,OAAO,QAAQ;AACtC,YAAM,eAAe,iBAAAF,QAAQ,YAAY,cAAc,KAAK,iBAAAA,QAAQ,OAAO,cAAc,IAAI,oBAAoB;AACjH,YAAM,eAAe,iBAAiB;AACtC,YAAM,YAAY,iBAAiB;AACnC,YAAM,cAAc,iBAAiB,QAAQ,iBAAiB;AAC9D,UAAI,aAAa,aAAa,eAAe;AAE7C,YAAM,EAAE,aAAa,YAAY,IAAI;AACrC,WAAK,eAAe,gBAAgB,CAAC,YAAY;AAC7C,qBAAa;AAAA,MACjB;AACA,aAAO;AAAA,IACX;AACA,UAAM,SAAS,CAAC,MAAM,SAAS,UAAU;AACrC,YAAM,EAAE,IAAI,QAAQ,YAAY,aAAa,gBAAgB,OAAO,UAAU,aAAa,gBAAgB,cAAc,mBAAmB,oBAAoB,wBAAwB,IAAI;AAC5L,YAAM,EAAE,eAAe,gBAAgB,IAAI;AAC3C,YAAM,EAAE,kBAAkB,IAAI;AAC9B,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,OAAO,SAAS,UAAU,UAAU,YAAY,SAAS,WAAW,SAAS,IAAI;AACzF,YAAM,SAAS;AACf,YAAM,OAAO;AAAA,QACT;AAAA,QACA,WAAW,cAAc,MAAM,CAAC;AAAA,QAChC,UAAU,cAAc;AAAA,QACxB,WAAW,eAAe;AAAA,MAC9B,EAAE,OAAO,SAAO,GAAG;AACnB,YAAM,SAAS;AAAA,QACX,iBAAiB,KAAK,KAAK,GAAG,CAAC;AAAA,QAC/B,aAAa,QAAQ,IAAI,CAAC,WAAW,qBAAqB,OAAO,WAAW,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,MAChG;AACA,UAAI,UAAU;AACV,eAAO,KAAK,SAAS;AACrB,YAAI,cAAc,CAAC,UAAU;AACzB,oBAAU,QAAQ,CAAC,SAAS;AACxB,mBAAO,KAAK,OAAO,KAAK,IAAI,CAAC,WAAW;AACpC,oBAAM,YAAY,OAAO,eAAe,OAAO,SAAS,kBAAkB;AAC1E,oBAAM,aAAa,YAAY,QAAQ,sBAAsB,uBAAuB,IAAI,CAAC,eAAe,IAAI,CAAC;AAC7G,oBAAM,YAAY,eAAe,MAAM,MAAM;AAC7C,kBAAI,aAAa;AACjB,kBAAI,aAAa;AACjB,+BAAAA,QAAQ,SAAS,CAAC,MAAM,GAAG,UAAQ;AAC/B,oBAAI,CAAC,KAAK,cAAc,CAAC,OAAO,WAAW,QAAQ;AAC/C;AAAA,gBACJ;AACA,8BAAc,KAAK;AAAA,cACvB,GAAG,EAAE,UAAU,aAAa,CAAC;AAC7B,oBAAM,YAAY,aAAa;AAC/B,kBAAI,WAAW;AACX,2BAAW,KAAK,QAAQ,SAAS,EAAE;AAAA,cACvC;AACA,kBAAI,OAAO,SAAS,YAAY;AAC5B,uBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,cAAc,OAAO,QAAQ,cAAc,OAAO,QAAQ,UAAU,UAAU,KAAK,iBAAiB,SAAS,KAAK,kCAAkC,MAAM,KAAK,gBAAgB,YAAY,EAAE,UAAU,SAAS;AAAA,cAC7P;AACA,qBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,cAAc,OAAO,QAAQ,cAAc,OAAO,QAAQ,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,SAAS,KAAK,UAAU,WAAW,WAAW,IAAI,CAAC;AAAA,YACzN,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO;AAAA,UACtB,CAAC;AAAA,QACL,OACK;AACD,iBAAO,KAAK,OAAO,QAAQ,IAAI,CAAC,WAAW;AACvC,kBAAM,YAAY,OAAO,eAAe,OAAO,SAAS,kBAAkB;AAC1E,kBAAM,aAAa,YAAY,QAAQ,sBAAsB,uBAAuB,IAAI,CAAC,eAAe,IAAI,CAAC;AAC7G,kBAAM,YAAY,eAAe,MAAM,MAAM;AAC7C,gBAAI,WAAW;AACX,yBAAW,KAAK,QAAQ,SAAS,EAAE;AAAA,YACvC;AACA,gBAAI,OAAO,SAAS,YAAY;AAC5B,qBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,kCAAkC,MAAM,KAAK,gBAAgB,YAAY,EAAE,UAAU,SAAS;AAAA,YAC5M;AACA,mBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,UAAU,WAAW,WAAW,IAAI,CAAC;AAAA,UACxK,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO;AAAA,QACtB;AACA,eAAO,KAAK,UAAU;AAAA,MAC1B;AACA,UAAI,MAAM,QAAQ;AACd,eAAO,KAAK,SAAS;AACrB,YAAI,YAAY;AACZ,gBAAM,QAAQ,CAAC,SAAS;AACpB,mBAAO,KAAK,SAAS,QAAQ,IAAI,CAAC,WAAW;AACzC,oBAAM,QAAQ,OAAO;AACrB,oBAAM,YAAY,OAAO,SAAS;AAClC,oBAAM,aAAa,YAAY,QAAQ,gBAAgB,iBAAiB,IAAI,CAAC,eAAe,IAAI,CAAC;AACjG,oBAAM,YAAY,KAAK,KAAK;AAC5B,kBAAI,WAAW;AACX,2BAAW,KAAK,QAAQ,SAAS,EAAE;AAAA,cACvC;AACA,kBAAI,OAAO,UAAU;AACjB,oBAAI,WAAW;AACf,oBAAI,KAAK,WAAW;AAChB,6BAAW,aAAa,KAAK,UAAU,8BAA8B,6BAA6B;AAAA,gBACtG;AACA,2BAAW,KAAK,sBAAsB;AACtC,oBAAI,OAAO,SAAS,SAAS;AACzB,yBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,mEAAmE,KAAK,SAAS,SAAS,MAAM,iDAAiD,QAAQ,2EAA2E,EAAE,KAAK,KAAK,iBAAiB,cAAc,EAAE,GAAG,gBAAgB,SAAS,IAAI,YAAY,EAAE,UAAU,KAAK,WAAW;AAAA,gBAC5d,WACS,OAAO,SAAS,YAAY;AACjC,yBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,mEAAmE,KAAK,SAAS,SAAS,MAAM,iDAAiD,QAAQ,kEAAkE,KAAK,oBAAoB,cAAc,EAAE,GAAG,gBAAgB,SAAS,IAAI,YAAY,EAAE,UAAU,KAAK,cAAc;AAAA,gBACld;AACA,uBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,mEAAmE,KAAK,SAAS,SAAS,MAAM,iDAAiD,QAAQ,2CAA2C,SAAS;AAAA,cAChV;AACA,kBAAI,OAAO,SAAS,SAAS;AACzB,uBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,oCAAoC,EAAE,KAAK,KAAK,iBAAiB,cAAc,EAAE,GAAG,gBAAgB,SAAS,IAAI,YAAY,EAAE,UAAU,KAAK,WAAW;AAAA,cACvQ,WACS,OAAO,SAAS,YAAY;AACjC,uBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,2BAA2B,KAAK,oBAAoB,cAAc,EAAE,GAAG,gBAAgB,SAAS,IAAI,YAAY,EAAE,UAAU,KAAK,cAAc;AAAA,cAC7P;AACA,qBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,IAAI,WAAW,WAAW,IAAI,CAAC;AAAA,YAClK,CAAC,EAAE,KAAK,EAAE,IAAI,OAAO;AAAA,UACzB,CAAC;AAAA,QACL,OACK;AACD,gBAAM,QAAQ,CAAC,SAAS;AACpB,mBAAO,KAAK,SAAS,QAAQ,IAAI,CAAC,WAAW;AACzC,oBAAM,YAAY,OAAO,SAAS;AAClC,oBAAM,aAAa,YAAY,QAAQ,gBAAgB,iBAAiB,IAAI,CAAC,eAAe,IAAI,CAAC;AACjG,oBAAM,YAAY,KAAK,OAAO,EAAE;AAChC,kBAAI,UAAU;AACd,kBAAI,UAAU;AACd,kBAAI,SAAS;AACT,sBAAM,YAAY,SAAS,cAAc,KAAK,IAAI;AAClD,sBAAM,eAAe,SAAS,iBAAiB,MAAM;AACrD,sBAAM,WAAW,kBAAkB,GAAG,SAAS,IAAI,YAAY,EAAE;AACjE,oBAAI,UAAU;AACV,wBAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,sBAAI,CAAC,WAAW,CAAC,SAAS;AACtB,2BAAO;AAAA,kBACX;AACA,sBAAI,UAAU,GAAG;AACb,8BAAU;AAAA,kBACd;AACA,sBAAI,UAAU,GAAG;AACb,8BAAU;AAAA,kBACd;AAAA,gBACJ;AAAA,cACJ;AACA,kBAAI,WAAW;AACX,2BAAW,KAAK,QAAQ,SAAS,EAAE;AAAA,cACvC;AACA,kBAAI,OAAO,SAAS,SAAS;AACzB,uBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,cAAc,OAAO,cAAc,OAAO,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,oCAAoC,EAAE,KAAK,KAAK,iBAAiB,cAAc,EAAE,GAAG,gBAAgB,SAAS,IAAI,YAAY,EAAE,UAAU,KAAK,WAAW;AAAA,cACjT,WACS,OAAO,SAAS,YAAY;AACjC,uBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,cAAc,OAAO,cAAc,OAAO,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,2BAA2B,KAAK,oBAAoB,cAAc,EAAE,GAAG,gBAAgB,SAAS,IAAI,YAAY,EAAE,UAAU,KAAK,cAAc;AAAA,cACvS;AACA,qBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,cAAc,OAAO,cAAc,OAAO,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,IAAI,WAAW,WAAW,IAAI,CAAC;AAAA,YAC5M,CAAC,EAAE,KAAK,EAAE,IAAI,OAAO;AAAA,UACzB,CAAC;AAAA,QACL;AACA,eAAO,KAAK,UAAU;AAAA,MAC1B;AACA,UAAI,UAAU;AACV,cAAM,EAAE,gBAAgB,IAAI;AAC5B,cAAM,UAAU,cAAc,UAAU,MAAM,eAAe;AAC7D,YAAI,QAAQ,QAAQ;AAChB,iBAAO,KAAK,SAAS;AACrB,kBAAQ,QAAQ,CAAC,QAAQ;AACrB,mBAAO,KAAK,OAAO,QAAQ,IAAI,CAAC,WAAW;AACvC,oBAAM,YAAY,OAAO,eAAe,OAAO,SAAS,kBAAkB;AAC1E,oBAAM,aAAa,YAAY,QAAQ,gBAAgB,iBAAiB,IAAI,CAAC,eAAe,IAAI,CAAC;AACjG,oBAAM,YAAY,mBAAmB,MAAM,KAAK,MAAM;AACtD,kBAAI,WAAW;AACX,2BAAW,KAAK,QAAQ,SAAS,EAAE;AAAA,cACvC;AACA,qBAAO,cAAc,WAAW,KAAK,GAAG,CAAC,YAAY,SAAS,UAAU,UAAU,KAAK,iBAAiB,OAAO,WAAW,KAAK,IAAI,WAAW,WAAW,IAAI,CAAC;AAAA,YAClK,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO;AAAA,UACtB,CAAC;AACD,iBAAO,KAAK,UAAU;AAAA,QAC1B;AAAA,MACJ;AAEA,YAAM,SAAS,CAAC,iBAAiB,kBAAkB,sDAAsD,MAAM,iDAAgD;AAC/J,aAAO,KAAK,YAAY,MAAM;AAC9B,aAAO,UAAU,OAAO,KAAK,EAAE,IAAI,eAAe,MAAM,OAAO,KAAK,EAAE,CAAC;AAAA,IAC3E;AACA,UAAM,QAAQ,CAAC,MAAM,SAAS,UAAU;AACpC,UAAI,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,uBAAuB,KAAK,SAAS;AAAA,QACrC;AAAA,QACA,QAAQ,IAAI,CAAC,WAAW,qBAAqB,OAAO,WAAW,KAAK,EAAE,KAAK,EAAE;AAAA,MACjF,EAAE,KAAK,EAAE;AACT,UAAI,KAAK,UAAU;AACf,eAAO,QAAQ,QAAQ,IAAI,CAAC,WAAW,gCAAgC,eAAe,MAAM,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;AAAA,MACjI;AACA,YAAM,QAAQ,CAAC,QAAQ;AACnB,eAAO,UAAU,QAAQ,IAAI,CAAC,WAAW,gCAAgC,IAAI,OAAO,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI;AAAA,MACxH,CAAC;AACD,UAAI,KAAK,UAAU;AACf,cAAM,EAAE,gBAAgB,IAAI;AAC5B,cAAM,UAAU,cAAc,UAAU,MAAM,eAAe;AAC7D,gBAAQ,QAAQ,CAAC,QAAQ;AACrB,iBAAO,QAAQ,QAAQ,IAAI,CAAC,WAAW,gCAAgC,mBAAmB,MAAM,KAAK,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;AAAA,QAC1I,CAAC;AAAA,MACL;AACA,aAAO,GAAG,GAAG;AAAA,IACjB;AACA,UAAM,aAAa,CAACE,WAAU,MAAM,SAAS,UAAU;AACnD,UAAI,QAAQ,QAAQ;AAChB,gBAAQ,KAAK,MAAM;AAAA,UACf,KAAK;AACD,mBAAO,MAAMA,WAAU,MAAM,SAAS,KAAK;AAAA,UAC/C,KAAK;AACD,mBAAO,MAAMA,WAAU,MAAM,SAAS,KAAK;AAAA,UAC/C,KAAK;AACD,mBAAO,OAAO,MAAM,SAAS,KAAK;AAAA,UACtC,KAAK;AACD,mBAAO,MAAM,MAAM,SAAS,KAAK;AAAA,QACzC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,UAAM,eAAe,CAAC,MAAM,YAAY;AACpC,YAAM,EAAE,UAAU,MAAM,SAAS,IAAI;AACrC,UAAI,CAAC,UAAU;AACX,cAAM,OAAO,uBAAuB,SAAS,IAAI;AACjD,eAAO,QAAQ,QAAQ,EAAE,MAAM,SAAS,KAAK,CAAC;AAAA,MAClD;AACA,UAAI,MAAM,UAAU;AAChB,cAAM,SAAS,EAAE,UAAU,MAAM,QAAQ,CAAC,EAAE,KAAK,MAAM;AACnD,cAAI,KAAK,YAAY,OAAO;AACxB,gBAAI,MAAM,OAAO;AACb,oBAAM,MAAM,QAAQ,EAAE,SAASL,SAAQ,sBAAsB,GAAG,QAAQ,UAAU,CAAC;AAAA,YACvF;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,eAAe,CAAC,SAAS;AAC3B,YAAM,EAAE,QAAQ,SAAS,WAAW,cAAc,kBAAkB,IAAI;AACxE,aAAO,IAAI,QAAQ,aAAW;AAC1B,YAAI,QAAQ;AACR,gBAAM,SAAS,EAAE,SAAS,MAAM,QAAQ,UAAU,OAAO,QAAQ;AACjE,kBAAQ,eAAe,aAAa,MAAM,IAAI,MAAM;AAAA,QACxD,OACK;AACD,gBAAM,QAAQ,cAAc,IAAI;AAChC,kBAAQ,SAAS,aAAa,MAAM,gBAAgB,EAAE,SAAS,MAAM,SAAS,WAAW,MAAM,GAAG,MAAM;AACpG,mBAAO,aAAa,MAAM,WAAW,UAAU,MAAM,SAAS,KAAK,CAAC;AAAA,UACxE,CAAC,CAAC;AAAA,QACN;AAAA,MACJ,CAAC,EAAE,KAAK,CAAC,WAAW;AAChB,2BAAmB,OAAO;AAC1B,YAAI,CAAC,KAAK,OAAO;AACb,cAAI,mBAAmB;AACnB,8BAAkB,EAAE,QAAQ,MAAM,SAAS,MAAM,QAAQ,UAAU,OAAO,QAAQ,CAAC;AAAA,UACvF;AAAA,QACJ;AACA,eAAO,OAAO,OAAO,EAAE,QAAQ,KAAK,GAAG,MAAM;AAAA,MACjD,CAAC,EAAE,MAAM,MAAM;AACX,2BAAmB,OAAO;AAC1B,YAAI,CAAC,KAAK,OAAO;AACb,cAAI,mBAAmB;AACnB,8BAAkB,EAAE,QAAQ,OAAO,SAAS,MAAM,QAAQ,UAAU,OAAO,QAAQ,CAAC;AAAA,UACxF;AAAA,QACJ;AACA,cAAM,SAAS,EAAE,QAAQ,MAAM;AAC/B,eAAO,QAAQ,OAAO,MAAM;AAAA,MAChC,CAAC;AAAA,IACL;AACA,UAAM,eAAe,CAAC,SAAS,SAAS;AACpC,YAAM,EAAE,iBAAiB,gBAAgB,cAAc,IAAI;AAC3D,UAAI,OAAO,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,EAAE;AAClC,YAAM,iBAAiB,CAAC;AACxB,YAAM,iBAAiB,CAAC;AACxB,sBAAgB,QAAQ,CAAC,WAAW;AAChC,cAAM,QAAQ,OAAO;AACrB,cAAM,QAAQ,OAAO,SAAS;AAC9B,YAAI,OAAO;AACP,yBAAe,KAAK,IAAI;AAAA,QAC5B;AACA,YAAI,OAAO;AACP,yBAAe,OAAO,SAAS,CAAC,IAAI;AAAA,QACxC;AAAA,MACJ,CAAC;AACD,YAAM,YAAY;AAAA,QACd,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AACA,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AACD,iBAAO,SAAS,WAAW,OAAO;AAClC;AAAA,QACJ,KAAK;AACD,iBAAO,SAAS,WAAW,OAAO;AAClC;AAAA,QACJ,KAAK;AACD,iBAAO,UAAU,WAAW,OAAO;AACnC;AAAA,QACJ,KAAK;AACD,iBAAO,SAAS,WAAW,OAAO;AAClC;AAAA,MACR;AACA,YAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,YAAM,SAAS,OAAO,KAAK,WAAS,eAAe,KAAK,KAAK,eAAe,KAAK,CAAC;AAClF,UAAI,QAAQ;AACR,iBAAS,WAAW,IAAI,EACnB,KAAK,CAAC,SAAS;AAChB,cAAI;AACJ,cAAI,KAAK,SAAS,YAAY,KAAK,SAAS,gBAAgB;AACxD,uBAAW,SAAS,SAAS,MAAM,EAAE;AAAA,UACzC;AACA,cAAI,KAAK,SAAS,aAAa;AAC3B,uBAAW,SAAS,OAAO,IAAI;AAAA,UACnC,OACK;AACD,uBAAW,SAAS,WAAW,IAAI;AAAA,UACvC;AACA,cAAI,KAAK,YAAY,OAAO;AACxB,gBAAI,MAAM,OAAO;AACb,oBAAM,MAAM,QAAQ,EAAE,SAASA,SAAQ,wBAAwB,CAAC,KAAK,MAAM,CAAC,GAAG,QAAQ,UAAU,CAAC;AAAA,YACtG;AAAA,UACJ;AACA,iBAAO,SAAS,KAAK,MAAM;AACvB,gBAAI,gBAAgB;AAChB,6BAAe,EAAE,QAAQ,KAAK,CAAC;AAAA,YACnC;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL,WACS,KAAK,YAAY,OAAO;AAC7B,YAAI,MAAM,OAAO;AACb,gBAAM,MAAM,QAAQ,EAAE,SAASA,SAAQ,qBAAqB,GAAG,QAAQ,QAAQ,CAAC;AAAA,QACpF;AACA,YAAI,eAAe;AACf,wBAAc,EAAE,QAAQ,MAAM,CAAC;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,mBAAmB,CAAC,MAAM,SAAS;AACrC,YAAM,EAAE,cAAc,kBAAkB,IAAI;AAC5C,YAAM,EAAE,MAAM,SAAS,IAAI,UAAU,IAAI;AACzC,YAAM,aAAa,kBAAkB;AAErC,UAAI,CAAC,gBAAgB,CAAC,iBAAAG,QAAQ,SAAS,iBAAAA,QAAQ,KAAK,WAAW,SAAS,GAAG,IAAI,GAAG;AAC9E,YAAI,KAAK,YAAY,OAAO;AACxB,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,QAAQ,EAAE,SAASH,SAAQ,qBAAqB,CAAC,IAAI,CAAC,GAAG,QAAQ,QAAQ,CAAC;AAAA,UAC1F;AAAA,QACJ;AACA,cAAM,SAAS,EAAE,QAAQ,MAAM;AAC/B,eAAO,QAAQ,OAAO,MAAM;AAAA,MAChC;AACA,YAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1C,cAAM,iBAAiB,CAAC,WAAW;AAC/B,kBAAQ,MAAM;AACd,uBAAa,iBAAiB;AAC9B,uBAAa,gBAAgB;AAAA,QACjC;AACA,cAAM,gBAAgB,CAAC,WAAW;AAC9B,iBAAO,MAAM;AACb,uBAAa,iBAAiB;AAC9B,uBAAa,gBAAgB;AAAA,QACjC;AACA,qBAAa,iBAAiB;AAC9B,qBAAa,gBAAgB;AAC7B,YAAI,OAAO,YAAY;AACnB,gBAAM,UAAU,OAAO,OAAO,EAAE,MAAM,YAAY,GAAG,MAAM,EAAE,MAAM,SAAS,CAAC;AAC7E,cAAI,QAAQ,QAAQ;AAChB,gBAAI,cAAc;AACd,sBAAQ,QAAQ,aAAa,EAAE,MAAM,SAAS,QAAQ,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM;AAC1E,+BAAe,EAAE,QAAQ,KAAK,CAAC;AAAA,cACnC,CAAC,EAAE,MAAM,MAAM;AACX,+BAAe,EAAE,QAAQ,KAAK,CAAC;AAAA,cACnC,CAAC;AAAA,YACL,OACK;AACD,6BAAe,EAAE,QAAQ,KAAK,CAAC;AAAA,YACnC;AAAA,UACJ,OACK;AACD,kBAAM,EAAE,gBAAgB,IAAI;AAC5B,qBAAS,aAAa,MAAM,gBAAgB,EAAE,MAAM,SAAS,SAAS,gBAAgB,GAAG,MAAM;AAC3F,oBAAM,SAAS,IAAI,WAAW;AAC9B,qBAAO,UAAU,MAAM;AACnB,uBAAO,qBAAqB,CAAC,IAAI,CAAC;AAClC,8BAAc,EAAE,QAAQ,MAAM,CAAC;AAAA,cACnC;AACA,qBAAO,SAAS,CAAC,MAAM;AACnB,6BAAa,EAAE,OAAO,QAAQ,OAAO;AAAA,cACzC;AACA,qBAAO,WAAW,MAAM,QAAQ,YAAY,OAAO;AAAA,YACvD,CAAC;AAAA,UACL;AAAA,QACJ,OACK;AAED,iBAAO,kBAAkB;AACzB,yBAAe,EAAE,QAAQ,KAAK,CAAC;AAAA,QACnC;AAAA,MACJ,CAAC;AACD,aAAO,KAAK,KAAK,MAAM;AACnB,YAAI,mBAAmB;AACnB,4BAAkB,EAAE,QAAQ,MAAM,SAAS,MAAM,QAAQ,SAAS,CAAC;AAAA,QACvE;AAAA,MACJ,CAAC,EAAE,MAAM,CAAC,MAAM;AACZ,YAAI,mBAAmB;AACnB,4BAAkB,EAAE,QAAQ,OAAO,SAAS,MAAM,QAAQ,SAAS,CAAC;AAAA,QACxE;AACA,eAAO,QAAQ,OAAO,CAAC;AAAA,MAC3B,CAAC;AAAA,IACL;AACA,UAAM,sBAAsB,CAAC,YAAY,QAAQ,YAAY;AACzD,aAAO,QAAQ,KAAK,CAAC,SAAS;AAC1B,YAAI,aAAa,IAAI,GAAG;AACpB,iBAAO,OAAO,OAAO,KAAK;AAAA,QAC9B,WACS,iBAAAG,QAAQ,SAAS,IAAI,GAAG;AAC7B,iBAAO,OAAO,UAAU;AAAA,QAC5B,OACK;AACD,gBAAM,QAAQ,KAAK,MAAM,KAAK;AAC9B,gBAAM,OAAO,KAAK;AAClB,gBAAM,QAAQ,KAAK;AACnB,cAAI,OAAO;AACP,mBAAO,OAAO,OAAO;AAAA,UACzB,WACS,SAAS,MAAM;AACpB,mBAAO,OAAO,UAAU,SAAS,OAAO,SAAS;AAAA,UACrD,WACS,OAAO;AACZ,mBAAO,OAAO,UAAU;AAAA,UAC5B,WACS,MAAM;AACX,mBAAO,OAAO,SAAS;AAAA,UAC3B;AAAA,QACJ;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,UAAM,qBAAqB,CAAC,YAAY,QAAQ,eAAe,kBAAkB;AAC7E,UAAI,eAAe;AACf,YAAI,iBAAAA,QAAQ,SAAS,eAAe,OAAO,KAAK,GAAG;AAC/C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,eAAe;AACf,YAAI,iBAAAA,QAAQ,SAAS,eAAe,OAAO,KAAK,GAAG;AAC/C,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,aAAO,WAAW,WAAW,CAAC,CAAC,OAAO,QAAQ,0BAA0B,MAAM;AAAA,IAClF;AACA,UAAM,uBAAuB,CAAC,SAAS,YAAY;AAC/C,YAAM,EAAE,YAAY,YAAY,WAAW,IAAI;AAC/C,YAAM,EAAE,WAAW,SAAS,iBAAiB,aAAa,aAAa,IAAI;AAC3E,YAAM,EAAE,eAAe,eAAe,gBAAgB,IAAI;AAC1D,YAAM,aAAa,kBAAkB;AACrC,YAAM,UAAU;AAChB,YAAM,aAAa,kBAAkB;AACrC,YAAM,gBAAgB,SAAS,mBAAmB;AAClD,YAAM,YAAY,UAAU,QAAQ,eAAe,EAAE,iBAAiB,QAAQ,CAAC;AAC/E,YAAM,YAAY,CAAC,CAAC,gBAAgB;AACpC,YAAM,WAAW,CAAC,EAAE,cAAc,UAAU,gBAAgB;AAC5D,YAAM,UAAU,OAAO,OAAO;AAAA,QAC1B,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ,UAAU,QAAQ,UAAU,KAAK,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,WAAW,YAAY,OAAO,CAAC;AAAA,MAC7G,GAAG,OAAO;AACV,YAAM,QAAQ,QAAQ,SAAS,iBAAAA,QAAQ,KAAK,WAAW,SAAS;AAChE,YAAM,QAAQ,QAAQ,SAAS,CAAC;AAChC,YAAM,cAAc,WAAW;AAC/B,YAAM,gBAAgB,cAAc,MAAM,CAAC;AAC3C,YAAM,EAAE,SAAS,eAAe,cAAc,IAAI;AAElD,YAAM,WAAW,MAAM,IAAI,CAAC,UAAU;AAClC,eAAO;AAAA,UACH;AAAA,UACA,OAAOH,SAAQ,oBAAoB,KAAK,EAAE;AAAA,QAC9C;AAAA,MACJ,CAAC;AACD,YAAM,WAAW,MAAM,IAAI,CAAC,SAAS;AACjC,YAAI,QAAQ,KAAK,OAAO;AACpB,iBAAO;AAAA,YACH,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK,SAAS,KAAK;AAAA,UAC9B;AAAA,QACJ;AACA,eAAO;AAAA,UACH,OAAO;AAAA,UACP,OAAOA,SAAQ,oBAAoB,IAAI,EAAE;AAAA,QAC7C;AAAA,MACJ,CAAC;AAED,uBAAAG,QAAQ,SAAS,eAAe,CAAC,QAAQ,OAAO,OAAO,MAAM,WAAW;AACpE,cAAM,aAAa,OAAO,YAAY,OAAO,SAAS,SAAS;AAC/D,YAAI,YAAY;AAChB,YAAI,WAAW,QAAQ,QAAQ;AAC3B,sBAAY,oBAAoB,SAAS,QAAQ,OAAO;AAAA,QAC5D,WACS,iBAAiB,eAAe;AACrC,sBAAY,mBAAmB,SAAS,QAAQ,eAAe,aAAa;AAAA,QAChF,OACK;AACD,sBAAY,OAAO,YAAY,cAAc,0BAA0B,MAAM;AAAA,QACjF;AACA,eAAO,UAAU;AACjB,eAAO,cAAc;AACrB,eAAO,WAAY,UAAU,OAAO,aAAc,cAAc,CAAC,YAAY,EAAE,QAAQ,UAAU,OAAO,CAAC,IAAI;AAAA,MACjH,CAAC;AAED,aAAO,OAAO,aAAa;AAAA,QACvB,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb,SAAS;AAAA,MACb,CAAC;AAED,aAAO,OAAO,cAAc;AAAA,QACxB,MAAM,cAAc,SAAS,aAAa;AAAA,MAC9C,GAAG,OAAO;AACV,YAAM,EAAE,UAAU,WAAW,MAAM,KAAK,IAAI;AAC5C,UAAI,UAAU;AACV,YAAI,iBAAAA,QAAQ,WAAW,QAAQ,GAAG;AAC9B,uBAAa,WAAW,SAAS;AAAA,YAC7B,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACX,CAAC;AAAA,QACL,OACK;AACD,uBAAa,WAAW,GAAG,QAAQ;AAAA,QACvC;AAAA,MACJ;AACA,UAAI,WAAW;AACX,YAAI,iBAAAA,QAAQ,WAAW,SAAS,GAAG;AAC/B,uBAAa,YAAY,UAAU;AAAA,YAC/B,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACX,CAAC;AAAA,QACL,OACK;AACD,uBAAa,YAAY,GAAG,SAAS;AAAA,QACzC;AAAA,MACJ;AACA,UAAI,CAAC,SAAS,KAAK,UAAQ,KAAK,UAAU,IAAI,GAAG;AAC7C,qBAAa,OAAO,SAAS,CAAC,EAAE;AAAA,MACpC;AACA,UAAI,CAAC,SAAS,KAAK,UAAQ,KAAK,UAAU,IAAI,GAAG;AAC7C,qBAAa,OAAO,SAAS,CAAC,EAAE;AAAA,MACpC;AACA,gBAAU,SAAS;AACnB,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,oBAAoB,MAAM;AAC5B,UAAI,MAAM,OAAO;AACb,eAAO,MAAM,MAAM,MAAM,kBAAkB;AAAA,MAC/C;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,UAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOlB,WAAW,SAAS;AAChB,cAAM,EAAE,YAAY,YAAY,WAAW,IAAI;AAC/C,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,EAAE,iBAAiB,eAAe,mBAAmB,eAAe,eAAe,gBAAgB,IAAI;AAC7G,cAAM,aAAa,kBAAkB;AACrC,cAAM,WAAW,gBAAgB;AACjC,cAAM,YAAY,UAAU,QAAQ,eAAe,EAAE,iBAAiB,QAAQ,CAAC;AAC/E,cAAM,WAAW,CAAC,EAAE,cAAc,UAAU,gBAAgB;AAC5D,cAAM,OAAO,OAAO,OAAO;AAAA,UACvB,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ,UAAU,QAAQ,UAAU,KAAK,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,WAAW,YAAY,OAAO,CAAC;AAAA,UACzG,UAAU;AAAA,UACV,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAaV,GAAG,YAAY,OAAO;AACtB,YAAI,EAAE,UAAU,WAAW,MAAM,MAAM,SAAS,UAAU,oBAAoB,oBAAoB,eAAe,cAAc,IAAI;AACnI,YAAI,SAAS,CAAC;AACd,cAAM,gBAAgB,SAAS,mBAAmB;AAClD,YAAI,CAAC,MAAM;AACP,iBAAO,cAAc,SAAS,aAAa;AAAA,QAC/C;AACA,YAAI,cAAc;AAClB,YAAI,aAAa,CAAC;AAClB,YAAI,WAAW,QAAQ,QAAQ;AAC3B,wBAAc;AACd,uBAAa;AAAA,QACjB,OACK;AACD,uBAAa,iBAAAA,QAAQ,WAAW,eAAe,YAAU;AACrD,kBAAM,aAAa,OAAO,YAAY,OAAO,SAAS,SAAS;AAC/D,gBAAI,YAAY;AAChB,gBAAI,WAAW,QAAQ,QAAQ;AAC3B,0BAAY,oBAAoB,MAAM,QAAQ,OAAO;AAAA,YACzD,WACS,iBAAiB,eAAe;AACrC,0BAAY,mBAAmB,MAAM,QAAQ,eAAe,aAAa;AAAA,YAC7E,OACK;AACD,0BAAY,OAAO,YAAY,cAAc,0BAA0B,MAAM;AAAA,YACjF;AACA,mBAAO;AAAA,UACX,GAAG,EAAE,UAAU,YAAY,aAAa,cAAc,UAAU,KAAK,CAAC;AAAA,QAC1E;AACA,cAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,UAAU,IAAI,WAAW,GAAG,CAAC;AAE7E,YAAI,CAAC,eAAe,CAAC,oBAAoB;AACrC,+BAAqB,CAAC,EAAE,OAAO,MAAM;AACjC,gBAAI,eAAe;AACf,kBAAI,iBAAAA,QAAQ,SAAS,eAAe,OAAO,KAAK,GAAG;AAC/C,uBAAO;AAAA,cACX;AAAA,YACJ;AACA,gBAAI,eAAe;AACf,kBAAI,iBAAAA,QAAQ,SAAS,eAAe,OAAO,KAAK,GAAG;AAC/C,uBAAO;AAAA,cACX;AACA,qBAAO;AAAA,YACX;AACA,mBAAO,WAAW,CAAC,CAAC,OAAO,QAAQ,0BAA0B,MAAM;AAAA,UACvE;AACA,wBAAc,qBAAqB;AAAA,QACvC;AACA,YAAI,YAAY;AACZ,wBAAc,kBAAkB;AAChC,mBAAS,iBAAAA,QAAQ,WAAW,iBAAAA,QAAQ,QAAQ,YAAY,CAAC,SAAS;AAC9D,gBAAI;AACJ,gBAAI,MAAM;AACN,kBAAI,aAAa,IAAI,GAAG;AACpB,+BAAe;AAAA,cACnB,WACS,iBAAAA,QAAQ,SAAS,IAAI,GAAG;AAC7B,+BAAe,SAAS,iBAAiB,IAAI;AAAA,cACjD,OACK;AACD,sBAAM,QAAQ,KAAK,MAAM,KAAK;AAC9B,sBAAMG,QAAO,KAAK;AAClB,sBAAM,QAAQ,KAAK;AACnB,oBAAI,OAAO;AACP,iCAAe,SAAS,cAAc,KAAK;AAAA,gBAC/C,WACS,SAASA,OAAM;AACpB,iCAAe,gBAAgB,KAAK,CAAC,WAAW,OAAO,UAAU,SAAS,OAAO,SAASA,KAAI;AAAA,gBAClG,WACS,OAAO;AACZ,iCAAe,SAAS,iBAAiB,KAAK;AAAA,gBAClD,WACSA,OAAM;AACX,iCAAe,gBAAgB,KAAK,CAAC,WAAW,OAAO,SAASA,KAAI;AAAA,gBACxE;AAAA,cACJ;AACA,qBAAO,gBAAgB,CAAC;AAAA,YAC5B;AAAA,UACJ,GAAG;AAAA,YACC,UAAU;AAAA,YACV,aAAa;AAAA,UACjB,CAAC,GAAG,CAAC,QAAQ,UAAU,aAAa,MAAM,MAAM,CAAC,sBAAsB,mBAAmB,EAAE,QAAQ,UAAU,QAAgB,cAAc,MAAM,CAAC,IAAI;AAAA,YACnJ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,UAAU;AAAA,UACd,CAAC;AAAA,QACL,OACK;AACD,mBAAS,iBAAAH,QAAQ,WAAW,UAAU,gBAAgB,iBAAiB,CAAC,QAAQ,UAAU,OAAO,YAAY,CAAC,sBAAsB,mBAAmB,EAAE,QAAQ,UAAU,QAAQ,cAAc,MAAM,CAAC,IAAI,EAAE,UAAU,YAAY,aAAa,cAAc,UAAU,KAAK,CAAC;AAAA,QACnR;AAEA,cAAM,OAAO,CAAC;AACd,yBAAAA,QAAQ,SAAS,QAAQ,YAAU;AAC/B,gBAAM,aAAa,OAAO,YAAY,OAAO,SAAS;AACtD,cAAI,CAAC,YAAY;AACb,iBAAK,KAAK,MAAM;AAAA,UACpB;AAAA,QACJ,GAAG,EAAE,UAAU,aAAa,CAAC;AAE7B,sBAAc,UAAU;AACxB,sBAAc,YAAY,cAAc,MAAM;AAC9C,YAAI,UAAU;AACV,cAAI,iBAAAA,QAAQ,WAAW,QAAQ,GAAG;AAC9B,0BAAc,WAAW,SAAS;AAAA,cAC9B,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACX,CAAC;AAAA,UACL,OACK;AACD,0BAAc,WAAW,GAAG,QAAQ;AAAA,UACxC;AAAA,QACJ;AACA,YAAI,CAAC,cAAc,UAAU;AACzB,wBAAc,WAAWH,SAAQ,cAAc,WAAW,gCAAgC,yBAAyB,CAAC,iBAAAG,QAAQ,aAAa,KAAK,IAAI,GAAG,gBAAgB,CAAC,CAAC;AAAA,QAC3K;AACA,YAAI,WAAW;AACX,cAAI,iBAAAA,QAAQ,WAAW,SAAS,GAAG;AAC/B,0BAAc,YAAY,UAAU;AAAA,cAChC,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACX,CAAC;AAAA,UACL,OACK;AACD,0BAAc,YAAY,GAAG,SAAS;AAAA,UAC1C;AAAA,QACJ;AACA,YAAI,CAAC,cAAc,WAAW;AAC1B,wBAAc,YAAY,SAAS,SAAS;AAAA,QAChD;AAEA,YAAI,CAAC,cAAc,gBAAgB,CAAC,iBAAAA,QAAQ,SAAS,iBAAAA,QAAQ,KAAK,WAAW,SAAS,GAAG,IAAI,GAAG;AAC5F,iBAAO,qBAAqB,CAAC,IAAI,CAAC;AAClC,cAAI,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,GAAG;AAChC,oBAAQ,uBAAuB,CAAC,GAAG,oBAAoB,CAAC;AAAA,UAC5D;AACA,gBAAM,SAAS,EAAE,QAAQ,MAAM;AAC/B,iBAAO,QAAQ,OAAO,MAAM;AAAA,QAChC;AACA,YAAI,CAAC,cAAc,OAAO;AACtB,cAAI,oBAAoB;AACpB,+BAAmB,EAAE,SAAS,eAAe,QAAQ,UAAU,OAAO,QAAQ,CAAC;AAAA,UACnF;AAAA,QACJ;AACA,YAAI,CAAC,cAAc,MAAM;AACrB,wBAAc,OAAO,CAAC;AACtB,cAAI,SAAS,YAAY;AACrB,gBAAI,CAAC,QAAQ,KAAK,EAAE,QAAQ,IAAI,IAAI,MAAM,YAAY;AAClD,4BAAc,OAAO,iBAAAA,QAAQ,WAAW,SAAS,aAAa,EAAE,UAAU,UAAQ,SAAS,eAAe,eAAe,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,MAAM,OAAO,CAAC,CAAC;AAAA,YACtL,OACK;AACD,4BAAc,OAAO;AAAA,YACzB;AAAA,UACJ,WACS,SAAS,OAAO;AACrB,gBAAI,CAAC,SAAS;AACV,qBAAO,qBAAqB,CAAC,OAAO,uBAAuB,CAAC;AAAA,YAChE;AACA,gBAAI,WAAW,CAAC,cAAc,QAAQ;AAClC,oBAAM,gBAAgB,QAAQ;AAC9B,oBAAM,EAAE,iBAAiB,IAAI,QAAQ,eAAe;AACpD,oBAAMI,aAAY,iBAAiB;AACnC,oBAAM,EAAE,SAAS,IAAI;AACrB,oBAAM,EAAE,gBAAgB,eAAe,OAAO,CAAC,EAAE,IAAIA;AACrD,oBAAM,aAAaA,WAAU,YAAYA,WAAU,SAAS,CAAC;AAC7D,oBAAM,cAAc,KAAK;AACzB,oBAAM,yBAAyB,KAAK;AACpC,oBAAM,uBAAuB,KAAK;AAClC,kBAAI,CAAC,aAAa;AACd,uBAAO,qBAAqB,CAAC,4BAA4B,CAAC;AAAA,cAC9D;AACA,kBAAI,aAAa;AACb,sBAAM,SAAS;AAAA,kBACX,QAAQ;AAAA,kBACR,OAAO;AAAA,kBACP,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,CAAC;AAAA,kBACvC,OAAO;AAAA,kBACP,SAAS,cAAc;AAAA,kBACvB,MAAM,cAAc;AAAA,kBACpB,SAAS;AAAA,gBACb;AACA,uBAAO,QAAQ,SAAS,kBAAkB,aAAa,MAAM,CAAC,EACzD,KAAK,UAAQ;AACd,wBAAM,WAAW,WAAW;AAC5B,gCAAc,QAAQ,WAAY,iBAAAJ,QAAQ,WAAW,QAAQ,IAAI,SAAS,EAAE,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI,iBAAAA,QAAQ,IAAI,MAAM,QAAQ,IAAK,SAAS,CAAC;AACrJ,sBAAI,eAAe;AACf,kCAAc,MAAM;AAAA,kBACxB;AACA,sBAAI,wBAAwB;AACxB,2CAAuB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,kBACvF;AACA,yBAAO,aAAa,aAAa;AAAA,gBACrC,CAAC,EACI,MAAM,CAAC,SAAS;AACjB,sBAAI,sBAAsB;AACtB,yCAAqB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,kBACrF;AAAA,gBACJ,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,SAAS,WAAW;AACpB,0BAAc,OAAO,aAAa,oBAAoB;AAAA,UAC1D;AAAA,QACJ,OACK;AACD,wBAAc,gBAAgB;AAAA,QAClC;AACA,eAAO,aAAa,aAAa;AAAA,MACrC;AAAA,MACA,aAAa,MAAM,SAAS;AACxB,cAAM,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AACtC,cAAM,EAAE,mBAAmB,IAAI;AAC/B,YAAI,oBAAoB;AACpB,6BAAmB,EAAE,SAAS,MAAM,QAAQ,SAAS,CAAC;AAAA,QAC1D;AACA,eAAO,iBAAiB,MAAM,IAAI;AAAA,MACtC;AAAA,MACA,WAAW,SAAS;AAChB,cAAM,aAAa,kBAAkB;AACrC,cAAM,OAAO,OAAO,OAAO;AAAA,UACvB,OAAO,iBAAAA,QAAQ,KAAK,WAAW,SAAS;AAAA;AAAA;AAAA,QAG5C,GAAG,YAAY,OAAO;AACtB,cAAM,EAAE,oBAAoB,kBAAkB,IAAI;AAClD,YAAI,oBAAoB;AACpB,6BAAmB,EAAE,SAAS,MAAM,QAAQ,SAAS,CAAC;AAAA,QAC1D;AACA,eAAO,MAAM,SAAS,IAAI,EAAE,MAAM,OAAK;AACnC,cAAI,mBAAmB;AACnB,8BAAkB,EAAE,QAAQ,OAAO,SAAS,MAAM,QAAQ,SAAS,CAAC;AAAA,UACxE;AACA,iBAAO,QAAQ,OAAO,CAAC;AAAA,QAC3B,CAAC,EAAE,KAAK,CAAC,WAAW;AAChB,gBAAM,EAAE,KAAK,IAAI;AACjB,iBAAO,iBAAiB,MAAM,IAAI;AAAA,QACtC,CAAC;AAAA,MACL;AAAA,MACA,SAAS,SAAS;AACd,eAAO,MAAM,SAAS,OAAO;AAAA,MACjC;AAAA,MACA,SAAS,SAAS;AACd,eAAO,MAAM,SAAS,OAAO;AAAA,MACjC;AAAA,MACA,MAAM,SAAS;AACX,cAAM,YAAY,iBAAiB;AACnC,cAAM,OAAO,OAAO,OAAO;AAAA,UACvB,UAAU;AAAA;AAAA,QAEd,GAAG,WAAW,SAAS;AAAA,UACnB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,QACX,CAAC;AACD,cAAM,EAAE,UAAU,IAAI;AACtB,YAAI,aAAa;AACjB,YAAI,WAAW;AACX,cAAI,iBAAAA,QAAQ,WAAW,SAAS,GAAG;AAC/B,yBAAa,UAAU;AAAA,cACnB,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACX,CAAC;AAAA,UACL,OACK;AACD,yBAAa,GAAG,SAAS;AAAA,UAC7B;AAAA,QACJ;AACA,YAAI,CAAC,YAAY;AACb,uBAAa,SAAS,SAAS;AAAA,QACnC;AACA,cAAM,oBAAoB,KAAK;AAC/B,cAAM,YAAY,KAAK,QAAQ,KAAK;AACpC,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,cAAI,MAAM,OAAO;AACb,gBAAI,WAAW;AACX,sBAAQ,MAAM,MAAM;AAAA,gBAChB,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,aAAa,KAAK;AAAA,gBAClB,cAAc,oBACR,CAAC,EAAE,KAAK,MAAM;AACZ,yBAAO,kBAAkB;AAAA,oBACrB;AAAA,oBACA,SAAS;AAAA,oBACT,SAAS;AAAA,oBACT,QAAQ;AAAA,kBACZ,CAAC;AAAA,gBACL,IACE;AAAA,cACV,CAAC,CAAC;AAAA,YACN,OACK;AACD,sBAAQ,cAAc,WAAW,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM;AACzD,uBAAO,MAAM,MAAM;AAAA,kBACf,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,aAAa,KAAK;AAAA,kBAClB,cAAc,oBACR,CAAC,EAAE,KAAK,MAAM;AACZ,2BAAO,kBAAkB;AAAA,sBACrB;AAAA,sBACA,SAAS;AAAA,sBACT,SAAS;AAAA,sBACT,QAAQ;AAAA,oBACZ,CAAC;AAAA,kBACL,IACE;AAAA,gBACV,CAAC;AAAA,cACL,CAAC,CAAC;AAAA,YACN;AAAA,UACJ,OACK;AACD,kBAAM,IAAI,EAAE,QAAQ,MAAM;AAC1B,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,aAAa,SAAS;AAClB,cAAM,YAAY,iBAAiB;AACnC,cAAM,OAAO,OAAO,OAAO;AAAA,UACvB,UAAU;AAAA;AAAA,QAEd,GAAG,WAAW,SAAS;AAAA,UACnB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,QACX,CAAC;AACD,eAAO,SAAS,WAAW,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM;AACnD,iBAAO;AAAA,YACH,MAAM;AAAA,UACV;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,cAAc;AACV,YAAI,MAAM,OAAO;AACb,iBAAO,MAAM,MAAM,MAAM,kBAAkB;AAAA,QAC/C;AACA,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AAAA,MACA,WAAW,SAAS;AAChB,cAAM,EAAE,YAAY,aAAa,IAAI;AACrC,cAAM,EAAE,WAAW,aAAa,aAAa,IAAI;AACjD,cAAM,aAAa,kBAAkB;AACrC,cAAM,UAAU,OAAO,OAAO;AAAA,UAC1B,MAAM;AAAA,UACN,SAAS;AAAA,UACT,OAAO,iBAAAA,QAAQ,KAAK,WAAW,SAAS;AAAA,UACxC,OAAO,CAAC,aAAa,UAAU;AAAA,QACnC,GAAG,YAAY,OAAO;AACtB,cAAM,QAAQ,QAAQ,SAAS,CAAC;AAChC,cAAM,QAAQ,QAAQ,SAAS,CAAC;AAChC,cAAM,SAAS,CAAC,CAAC;AACjB,YAAI,QAAQ;AACR,cAAI,QAAQ,SAAS;AACjB,gBAAI,MAAM,OAAO;AACb,oBAAM,MAAM,QAAQ,EAAE,SAASH,SAAQ,sBAAsB,GAAG,QAAQ,QAAQ,CAAC;AAAA,YACrF;AAAA,UACJ;AACA;AAAA,QACJ;AACA,YAAI,CAAC,cAAc;AACf,iBAAO,qBAAqB,CAAC,eAAe,CAAC;AAAA,QACjD;AAEA,cAAM,WAAW,MAAM,IAAI,CAAC,UAAU;AAClC,iBAAO;AAAA,YACH;AAAA,YACA,OAAOA,SAAQ,oBAAoB,KAAK,EAAE;AAAA,UAC9C;AAAA,QACJ,CAAC;AACD,cAAM,WAAW,MAAM,IAAI,CAAC,SAAS;AACjC,cAAI,QAAQ,KAAK,OAAO;AACpB,mBAAO;AAAA,cACH,OAAO,KAAK;AAAA,cACZ,OAAO,KAAK,SAAS,KAAK;AAAA,YAC9B;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,OAAO;AAAA,YACP,OAAOA,SAAQ,oBAAoB,IAAI,EAAE;AAAA,UAC7C;AAAA,QACJ,CAAC;AACD,eAAO,OAAO,aAAa;AAAA,UACvB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACb,CAAC;AACD,eAAO,OAAO,cAAc,OAAO;AACnC,YAAI,CAAC,SAAS,KAAK,UAAQ,KAAK,UAAU,aAAa,IAAI,GAAG;AAC1D,uBAAa,OAAO,SAAS,CAAC,EAAE;AAAA,QACpC;AACA,kBAAU,SAAS;AAAA,MACvB;AAAA,MACA,aAAa;AAAA,MACb,WAAW,SAAS;AAChB,cAAM,aAAa,kBAAkB;AACrC,cAAM,UAAU,OAAO,OAAO;AAAA,UAC1B,SAAS;AAAA,UACT,OAAO,iBAAAG,QAAQ,KAAK,WAAW,SAAS;AAAA,QAC5C,GAAG,YAAY,OAAO;AACtB,YAAI,CAAC,MAAM,cAAc;AACrB,iBAAO,qBAAqB,CAAC,eAAe,CAAC;AAAA,QACjD;AACA,eAAO,qBAAqB,OAAO;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,SAAS;AACf,cAAM,YAAY,iBAAiB;AACnC,cAAM,UAAU,OAAO,OAAO;AAAA,UAC1B,SAAS;AAAA,QACb,GAAG,WAAW,OAAO;AACrB,YAAI,CAAC,MAAM,aAAa;AACpB,iBAAO,qBAAqB,CAAC,cAAc,CAAC;AAAA,QAChD;AACA,eAAO,qBAAqB,SAAS,IAAI;AAAA,MAC7C;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,SAAS;AACf,WAAO,QAAQ,mBAAmB,qBAAqB;AAAA,EAC3D;AACJ,CAAC;;;AEjhDD,IAAAK,mBAAoB;AAIpB,IAAM,EAAE,OAAAC,OAAM,IAAI;AAClB,IAAM,YAAY,iBAAAC,QAAQ,OAAO;AACjC,SAAS,gBAAgB,QAAQ,WAAW;AACxC,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,QAAM,uBAAuB,CAAC,UAAU,WAAW,SAAS,QAAQ,qBAAqB;AACzF,MAAI,sBAAsB;AACtB,UAAM,qBAAqB,iBAAiB,MAAM;AAClD,iBAAa,iBAAAA,QAAQ,SAAS,mBAAmB,UAAU;AAC3D,kBAAc,iBAAAA,QAAQ,SAAS,mBAAmB,WAAW;AAAA,EACjE;AACA,SAAO,UAAU,WAAW,WAAW;AACnC,iBAAa,OAAO;AACpB,kBAAc,OAAO;AACrB,aAAS,OAAO;AAChB,QAAI,sBAAsB;AACtB,YAAM,gBAAgB,iBAAiB,MAAM;AAC7C,mBAAa,iBAAAA,QAAQ,SAAS,cAAc,UAAU;AACtD,oBAAc,iBAAAA,QAAQ,SAAS,cAAc,WAAW;AAAA,IAC5D;AAAA,EACJ;AACA,SAAO,EAAE,WAAW,WAAW;AACnC;AACAD,OAAM,IAAI,uBAAuB;AAAA,EAC7B,WAAW,UAAU;AACjB,UAAM,EAAE,OAAO,WAAW,aAAa,IAAI;AAC3C,UAAM,EAAE,QAAQ,IAAI,SAAS,WAAW;AACxC,UAAM,EAAE,iBAAiB,qBAAqB,kBAAkB,iBAAiB,gBAAgB,mBAAmB,iBAAiB,yBAAyB,uBAAuB,yBAAyB,IAAI,SAAS,eAAe;AAC1O,aAAS,qBAAqB,MAAM,QAAQ,cAAc,QAAQ,iBAAiB,WAAW;AAC1F,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,EAAE,sBAAsB,mBAAmB,IAAI;AACrD,YAAM,UAAU,eAAe;AAC/B,YAAM,WAAW,gBAAgB;AACjC,YAAM,mBAAmB,wBAAwB;AACjD,YAAM,EAAE,IAAI,IAAI;AAChB,UAAI,cAAc;AAClB,UAAI,YAAY,CAAC;AACjB,UAAI,WAAW;AACf,YAAM,SAAS,YAAY;AAC3B,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,cAAc,IAAI;AAC1B,UAAI,QAAQ;AACR,mBAAW,kBAAkB;AAAA,MACjC,OACK;AACD,mBAAY,OAAO,SAAS,kBAAmB,KAAK,IAAI,SAAS;AAAA,MACrE;AACA,UAAI,aAAa;AACb,cAAM,YAAY,SAAS,cAAc,GAAG;AAC5C,cAAM,qBAAqB,sBAAsB,SAAS,UAAU,QAAQ;AAC5E,YAAI,CAAC,sBAAsB,cAAc;AACrC,cAAI,QAAQ;AACR,wBAAY,cAAc,MAAM,WAAW,YAAY,KAAK,KAAK,WAAW,gBAAgB,CAAC;AAAA,UACjG,OACK;AACD,wBAAY,cAAc,MAAM,YAAY,KAAK,MAAM,WAAW,gBAAgB,GAAG,YAAY,CAAC;AAAA,UACtG;AAAA,QACJ,OACK;AACD,cAAI,QAAQ;AACR,qBAAS,IAAI,WAAW,IAAI,cAAc,QAAQ,KAAK;AACnD,oBAAM,OAAO,cAAc,CAAC;AAC5B,oBAAM,QAAQ,SAAS,SAAS,IAAI;AACpC,oBAAM,UAAU,qBAAqB,KAAK,KAAK,CAAC;AAChD,6BAAe,QAAQ,gBAAgB,SAAS,UAAU,QAAQ,UAAU,QAAQ,UAAU;AAC9F,wBAAU,KAAK,IAAI;AACnB,kBAAI,cAAc,UAAU;AACxB,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ,OACK;AACD,qBAAS,MAAM,WAAW,OAAO,GAAG,OAAO;AACvC,oBAAM,OAAO,cAAc,GAAG;AAC9B,oBAAM,QAAQ,SAAS,SAAS,IAAI;AACpC,oBAAM,UAAU,qBAAqB,KAAK,KAAK,CAAC;AAChD,6BAAe,QAAQ,gBAAgB,SAAS,UAAU,QAAQ,UAAU,QAAQ,UAAU;AAC9F,wBAAU,KAAK,IAAI;AACnB,kBAAI,cAAc,UAAU;AACxB,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,OACK;AACD,cAAM,cAAc,SAAS,SAAS;AACtC,eAAO,gBAAgB,cAAc,UAAU;AAC3C,gBAAM,cAAc,SAAS,WAAW,YAAY;AACpD,cAAI,aAAa;AACb,sBAAU,KAAK,YAAY,IAAI;AAC/B,2BAAe,aAAa;AAC5B,2BAAe,aAAa,GAAG,WAAW,gBAAgB;AAAA,UAC9D;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,UAAM,2BAA2B,CAAC,MAAM,WAAW;AAC/C,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,iBAAiB,WAAW,UAAU,kBAAkB,CAAC;AAC/D,YAAM,iBAAiB,WAAW,UAAU,kBAAkB,CAAC;AAC/D,YAAM,kBAAkB,WAAW,UAAU,mBAAmB,CAAC;AACjE,YAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,UAAI,OAAO,SAAS,YAAY;AAC5B,YAAI,kBAAkB;AACtB,YAAI,kBAAkB,OAAO,UAAU,QAAQ;AAC3C,4BAAkB;AAAA,QACtB,WACS,mBAAmB,OAAO,UAAU,SAAS;AAClD,4BAAkB;AAAA,QACtB;AACA,YAAI,CAAC,iBAAiB;AAClB;AAAA,QACJ;AACA,cAAM,KAAK,QAAQ;AACnB,cAAM,OAAO,KAAK;AAClB,cAAM,OAAO,KAAK;AAClB,cAAM,oBAAoB,gBAAgB,cAAc,4BAA4B;AACpF,cAAM,SAAS,KAAK;AACpB,cAAM,gBAAgB,SAAS,mBAAmB;AAClD,YAAI,gBAAgB,CAAC;AACrB,cAAM,aAAa;AACnB,cAAM,aAAa,gBAAgB,KAAK,QAAQ,eAAe;AAC/D,cAAM,WAAW,WAAW,YAAY,KAAK;AAC7C,cAAM,YAAY,WAAW,aAAa,KAAK;AAC/C,cAAM,iBAAiB,gBAAgB;AACvC,cAAM,YAAY,OAAO;AACzB,cAAM,SAAS,OAAO,sBAAsB;AAC5C,cAAM,kBAAkB,OAAO,OAAO;AACtC,YAAI,qBAAqB;AACzB,YAAI,oBAAoB;AACxB,YAAI,uBAAuB;AAC3B,cAAME,gBAAe,CAAC,MAAMC,UAAS;AACjC,mBAAS,cAAc,kBAAkB,IAAI,IAAI;AAAA,YAC7C,SAAS,MAAM,SAAS,mBAAmB;AAAA,YAC3C,UAAU,MAAM,SAAS,0BAA0B;AAAA,UACvD,GAAGA,KAAI;AAAA,QACX;AACA,cAAM,gBAAgB,CAACA,UAAS;AAC5B,gBAAM,EAAE,SAAS,QAAQ,IAAIA;AAC7B,gBAAM,aAAa,UAAU;AAC7B,gBAAM,YAAY,UAAU,QAAQ,gBAAgB,YAAY;AAChE,cAAI,cAAc,KAAK,IAAI,SAAS;AACpC,cAAI,aAAa,KAAK,IAAI,UAAU;AACpC,cAAI,WAAW;AACf,cAAI,YAAY;AAChB,cAAI,YAAY,YAAY;AAExB,wBAAY;AACZ,gBAAI,WAAW,YAAY;AACvB,yBAAW;AACX,4BAAc;AAAA,YAClB;AAAA,UACJ,OACK;AAED,0BAAc,KAAK,IAAI,aAAa,gBAAgB,eAAe,WAAW,UAAU;AAAA,UAC5F;AACA,cAAI,aAAa,YAAY;AAEzB,yBAAa;AACb,gBAAI,aAAa,WAAW;AACxB,0BAAY;AACZ,2BAAa;AAAA,YACjB;AAAA,UACJ,OACK;AAED,yBAAa,KAAK,IAAI,YAAY,gBAAgB,cAAc,YAAY,UAAU;AAAA,UAC1F;AACA,4BAAkB,MAAM,SAAS,GAAG,WAAW;AAC/C,4BAAkB,MAAM,QAAQ,GAAG,UAAU;AAC7C,4BAAkB,MAAM,OAAO,GAAG,SAAS;AAC3C,4BAAkB,MAAM,MAAM,GAAG,QAAQ;AACzC,4BAAkB,MAAM,UAAU;AAClC,gBAAM,YAAY,qBAAqBA,OAAM,QAAQ,QAAQ,QAAQ,iBAAiB,YAAY,aAAa,CAAC,cAAc,WAAW;AAEzI,cAAI,cAAc,MAAM,UAAU,WAAW,cAAc,QAAQ;AAC/D,kBAAM,eAAe,cAAcA,KAAI;AACvC,4BAAgB;AAChB,gBAAI,cAAc;AACd,wBAAU,QAAQ,CAAC,QAAQ;AACvB,yBAAS,sBAAsB,CAAC,GAAG,GAAG,cAAc,QAAQ,GAAG,MAAM,EAAE;AAAA,cAC3E,CAAC;AAAA,YACL,OACK;AACD,uBAAS,kBAAkB,KAAK;AAChC,uBAAS,yBAAyB,WAAW,MAAM,KAAK;AAAA,YAC5D;AACA,YAAAD,cAAa,UAAUC,KAAI;AAAA,UAC/B;AAAA,QACJ;AAEA,cAAM,kBAAkB,MAAM;AAC1B,uBAAa,kBAAkB;AAC/B,+BAAqB;AAAA,QACzB;AAEA,cAAM,mBAAmB,CAACA,UAAS;AAC/B,0BAAgB;AAChB,+BAAqB,WAAW,MAAM;AAClC,gBAAI,oBAAoB;AACpB,oBAAM,EAAE,YAAY,WAAW,cAAc,aAAa,IAAI;AAC9D,oBAAM,UAAU,KAAK,KAAK,uBAAuB,KAAK,SAAS;AAC/D,kBAAI,mBAAmB;AACnB,oBAAI,YAAY,eAAe,cAAc;AACzC,2BAAS,SAAS,YAAY,YAAY,OAAO;AACjD,mCAAiBA,KAAI;AACrB,gCAAcA,KAAI;AAAA,gBACtB,OACK;AACD,kCAAgB;AAAA,gBACpB;AAAA,cACJ,OACK;AACD,oBAAI,WAAW;AACX,2BAAS,SAAS,YAAY,YAAY,OAAO;AACjD,mCAAiBA,KAAI;AACrB,gCAAcA,KAAI;AAAA,gBACtB,OACK;AACD,kCAAgB;AAAA,gBACpB;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ,GAAG,EAAE;AAAA,QACT;AACA,iBAAS,IAAI,aAAa;AAC1B,iBAAS,cAAc,CAAAA,UAAQ;AAC3B,UAAAA,MAAK,eAAe;AACpB,UAAAA,MAAK,gBAAgB;AACrB,gBAAM,EAAE,QAAQ,IAAIA;AACpB,gBAAM,EAAE,YAAY,IAAI,eAAe,eAAe;AAEtD,cAAI,UAAU,aAAa;AACvB,gCAAoB;AACpB,mCAAuB,cAAc;AACrC,gBAAI,CAAC,oBAAoB;AACrB,+BAAiBA,KAAI;AAAA,YACzB;AAAA,UACJ,WACS,UAAU,cAAc,gBAAgB,cAAc;AAC3D,gCAAoB;AACpB,mCAAuB,UAAU,cAAc,gBAAgB;AAC/D,gBAAI,CAAC,oBAAoB;AACrB,+BAAiBA,KAAI;AAAA,YACzB;AAAA,UACJ,WACS,oBAAoB;AACzB,4BAAgB;AAAA,UACpB;AACA,wBAAcA,KAAI;AAAA,QACtB;AACA,iBAAS,YAAY,CAACA,UAAS;AAC3B,0BAAgB;AAChB,sBAAY,IAAI,aAAa;AAC7B,4BAAkB,gBAAgB,OAAO;AACzC,mBAAS,cAAc;AACvB,mBAAS,YAAY;AACrB,UAAAD,cAAa,OAAOC,KAAI;AAAA,QAC5B;AACA,QAAAD,cAAa,SAAS,IAAI;AAAA,MAC9B;AAAA,IACJ;AACA,UAAM,2BAA2B,CAAC,MAAM,WAAW;AAC/C,YAAM,EAAE,YAAY,gBAAgB,YAAY,IAAI;AACpD,YAAM,eAAe,oBAAoB;AACzC,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,UAAI,eAAe,UAAU,QAAQ,SAAS,6BAA6B;AACvE,eAAO,SAAS,4BAA4B,MAAM,MAAM;AAAA,MAC5D,OACK;AACD,YAAI,kBAAkB,aAAa,OAAO;AACtC,mCAAyB,MAAM,MAAM;AAAA,QACzC;AACA,YAAI,eAAe,UAAU,UAAU;AACnC,cAAI,CAAC,cAAc,SAAS,SAAS,QAAQ;AACzC,qBAAS,eAAe,QAAQ,IAAI;AAAA,UACxC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,qBAAqB,CAAC,MAAM,MAAM,aAAa,WAAW,cAAc,cAAc;AACxF,YAAM,EAAE,eAAe,cAAc,IAAI;AACzC,YAAM,SAAS,OAAO,OAAO,CAAC,GAAG,IAAI;AACrC,YAAM,YAAY,SAAS,cAAc,OAAO,GAAG;AACnD,YAAM,eAAe,SAAS,iBAAiB,OAAO,MAAM;AAC5D,WAAK,eAAe;AACpB,UAAI,aAAa,YAAY,GAAG;AAE5B,eAAO,WAAW,YAAY;AAC9B,eAAO,MAAM,cAAc,OAAO,QAAQ;AAAA,MAC9C,WACS,aAAa,YAAY,cAAc,SAAS,GAAG;AAExD,eAAO,WAAW,YAAY;AAC9B,eAAO,MAAM,cAAc,OAAO,QAAQ;AAAA,MAC9C,WACS,eAAe,cAAc;AAElC,eAAO,cAAc,eAAe;AACpC,eAAO,SAAS,cAAc,OAAO,WAAW;AAAA,MACpD,WACS,gBAAgB,eAAe,cAAc,SAAS,GAAG;AAE9D,eAAO,cAAc,eAAe;AACpC,eAAO,SAAS,cAAc,OAAO,WAAW;AAAA,MACpD;AACA,eAAS,YAAY,OAAO,KAAK,OAAO,MAAM,EAAE,KAAK,MAAM;AACvD,eAAO,OAAO,SAAS,eAAe,OAAO,KAAK,OAAO,MAAM;AAC/D,iBAAS,eAAe,QAAQ,IAAI;AAAA,MACxC,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,kBAAkB;AAAA;AAAA,MAEpB,gBAAgB,MAAM,QAAQ,MAAM;AAChC,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,eAAe,cAAc,IAAI;AACzC,cAAM,WAAW,gBAAgB;AACjC,cAAM,UAAU,eAAe;AAC/B,cAAM,iBAAiB,sBAAsB;AAC7C,cAAM,aAAa,kBAAkB;AACrC,cAAM,oBAAoB,yBAAyB;AACnD,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,cAAM,SAAS,OAAO,OAAO,CAAC,GAAG,IAAI;AACrC,cAAM,YAAY,SAAS,cAAc,OAAO,GAAG;AACnD,cAAM,eAAe,SAAS,iBAAiB,OAAO,MAAM;AAC5D,aAAK,eAAe;AACpB,YAAI,QAAQ;AAER,cAAI,gBAAgB,GAAG;AAEnB,gBAAI,YAAY,GAAG;AACf,+BAAiB,YAAY;AAC7B,0BAAY,cAAc,cAAc;AACxC,kCAAoB,cAAc,SAAS;AAAA,YAC/C;AAAA,UACJ,OACK;AACD,gCAAoB,eAAe;AAAA,UACvC;AAAA,QACJ,OACK;AACD,cAAI,gBAAgB,cAAc,SAAS,GAAG;AAE1C,gBAAI,YAAY,cAAc,SAAS,GAAG;AACtC,+BAAiB,YAAY;AAC7B,0BAAY,cAAc,cAAc;AACxC,kCAAoB;AAAA,YACxB;AAAA,UACJ,OACK;AACD,gCAAoB,eAAe;AAAA,UACvC;AAAA,QACJ;AACA,cAAM,eAAe,cAAc,iBAAiB;AACpD,YAAI,cAAc;AACd,cAAI,WAAW;AACX,mBAAO,WAAW;AAClB,mBAAO,MAAM;AAAA,UACjB,OACK;AACD,mBAAO,WAAW;AAAA,UACtB;AACA,iBAAO,cAAc;AACrB,iBAAO,SAAS;AAChB,iBAAO,OAAO,SAAS,eAAe,OAAO,KAAK,OAAO,MAAM;AAC/D,cAAI,QAAQ,aAAa,eAAe,kBAAkB;AACtD,qBAAS,uBAAuB,MAAM,MAAM;AAAA,UAChD;AACA,cAAI,WAAW,aAAa,kBAAkB,kBAAkB;AAC5D,qBAAS,0BAA0B,MAAM,MAAM;AAAA,UACnD;AACA,cAAI,YAAY;AACZ,gBAAI,SAAS,YAAY,WAAW,SAAS,YAAY,YAAY;AACjE,kBAAI,SAAS,SAAS,OAAO;AACzB,yBAAS,WAAW,QAAQ,IAAI;AAAA,cACpC,OACK;AACD,yBAAS,YAAY,OAAO,KAAK,OAAO,MAAM,EACzC,KAAK,MAAM;AACZ,2BAAS,eAAe,QAAQ,IAAI;AAAA,gBACxC,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ,OACK;AACD,qBAAS,YAAY,OAAO,KAAK,OAAO,MAAM,EACzC,KAAK,MAAM;AACZ,uBAAS,eAAe,QAAQ,IAAI;AAAA,YACxC,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,eAAe,WAAW,WAAW,MAAM;AACvC,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,WAAW,gBAAgB;AACjC,cAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,YAAI;AACJ,YAAI,YAAY;AACZ,cAAI,YAAY;AACZ,kBAAM,EAAE,OAAO,MAAM,IAAI,iBAAAD,QAAQ,SAAS,eAAe,UAAQ,SAAS,YAAY,EAAE,UAAU,cAAc,CAAC;AACjH,gBAAI,aAAa,QAAQ,GAAG;AACxB,0BAAY,MAAM,QAAQ,CAAC;AAAA,YAC/B,WACS,aAAa,QAAQ,MAAM,SAAS,GAAG;AAC5C,0BAAY,MAAM,QAAQ,CAAC;AAAA,YAC/B;AAAA,UACJ,OACK;AACD,kBAAM,YAAY,SAAS,cAAc,UAAU;AACnD,gBAAI,aAAa,YAAY,GAAG;AAC5B,0BAAY,cAAc,YAAY,CAAC;AAAA,YAC3C,WACS,aAAa,YAAY,cAAc,SAAS,GAAG;AACxD,0BAAY,cAAc,YAAY,CAAC;AAAA,YAC3C;AAAA,UACJ;AAAA,QACJ,OACK;AACD,sBAAY,cAAc,CAAC;AAAA,QAC/B;AACA,YAAI,WAAW;AACX,eAAK,eAAe;AACpB,gBAAM,SAAS;AAAA,YACX,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,UAAU,SAAS,YAAY,SAAS;AAAA,YACxC,WAAW,SAAS,cAAc,SAAS;AAAA,UAC/C;AACA,mBAAS,YAAY,SAAS,EACzB,KAAK,MAAM,SAAS,uBAAuB,MAAM,MAAM,CAAC;AAAA,QACjE;AAAA,MACJ;AAAA;AAAA,MAEA,kBAAkB,aAAa,cAAc,MAAM;AAC/C,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,EAAE,cAAc,IAAI;AAC1B,YAAI,YAAY;AAChB,YAAI,eAAe;AACf,gBAAM,eAAe,SAAS,iBAAiB,aAAa;AAC5D,cAAI,eAAe,eAAe,GAAG;AACjC,wBAAY,cAAc,eAAe,CAAC;AAAA,UAC9C,WACS,gBAAgB,eAAe,cAAc,SAAS,GAAG;AAC9D,wBAAY,cAAc,eAAe,CAAC;AAAA,UAC9C;AAAA,QACJ,OACK;AACD,sBAAY,cAAc,CAAC;AAAA,QAC/B;AACA,YAAI,WAAW;AACX,eAAK,eAAe;AACpB,gBAAM,SAAS;AAAA,YACX,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,aAAa,SAAS,eAAe,SAAS;AAAA,YAC9C,cAAc,SAAS,iBAAiB,SAAS;AAAA,UACrD;AACA,mBAAS,eAAe,SAAS,EAC5B,KAAK,MAAM,SAAS,0BAA0B,MAAM,MAAM,CAAC;AAAA,QACpE;AAAA,MACJ;AAAA;AAAA,MAEA,kBAAkB,MAAM,aAAa,WAAW,cAAc,WAAW,MAAM;AAC3E,cAAM,EAAE,qBAAqB,uBAAuB,IAAI;AACxD,cAAM,UAAU,eAAe;AAC/B,cAAM,iBAAiB,sBAAsB;AAC7C,cAAM,aAAa,kBAAkB;AACrC,cAAM,oBAAoB,yBAAyB;AACnD,cAAM,SAAS,mBAAmB,MAAM,MAAM,aAAa,WAAW,cAAc,SAAS;AAC7F,YAAI,QAAQ,aAAa,qBAAqB;AAC1C,cAAI,eAAe,kBAAkB;AACjC,qBAAS,uBAAuB,MAAM,MAAM;AAAA,UAChD,OACK;AAED,iBAAK,aAAa,eAAe,QAAQ,aAAa,sBAAsB;AACxE,uBAAS,eAAe,WAAW,WAAW,IAAI;AAAA,YACtD;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,WAAW,aAAa,wBAAwB;AAChD,cAAI,kBAAkB,kBAAkB;AACpC,qBAAS,0BAA0B,MAAM,MAAM;AAAA,UACnD,OACK;AAED,iBAAK,eAAe,kBAAkB,WAAW,aAAa,yBAAyB;AACnF,uBAAS,kBAAkB,aAAa,cAAc,IAAI;AAAA,YAC9D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,kBAAkB,MAAM,aAAa,WAAW,cAAc,WAAW,MAAM;AAC3E,cAAM,EAAE,qBAAqB,uBAAuB,IAAI;AACxD,cAAM,UAAU,eAAe;AAC/B,cAAM,iBAAiB,sBAAsB;AAC7C,cAAM,aAAa,kBAAkB;AACrC,cAAM,oBAAoB,yBAAyB;AACnD,cAAM,SAAS,mBAAmB,MAAM,MAAM,aAAa,WAAW,cAAc,SAAS;AAC7F,aAAM,QAAQ,aAAa,wBAAwB,eAAe,kBAAmB;AACjF,mBAAS,uBAAuB,MAAM,MAAM;AAAA,QAChD;AACA,aAAK,WAAW,aAAa,2BAA2B,kBAAkB,kBAAkB;AACxF,mBAAS,0BAA0B,MAAM,MAAM;AAAA,QACnD;AAAA,MACJ;AAAA;AAAA,MAEA,aAAa,MAAM,aAAa,WAAW,cAAc,WAAW,MAAM;AACtE,2BAAmB,MAAM,MAAM,aAAa,WAAW,cAAc,SAAS;AAAA,MAClF;AAAA,MACA;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ,CAAC;;;AC/gBD,IAAAG,mBAAoB;AAMpB,IAAM,EAAE,WAAAC,YAAW,YAAY,OAAAC,OAAM,IAAI;AAIzC,IAAM,OAAN,MAAW;AAAA,EACP,YAAY,MAAM;AACd,WAAO,OAAO,MAAM;AAAA,MAChB,UAAU;AAAA,MACV,UAAU,KAAK;AAAA,MACf,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,MAChB,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,IACnB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACV,WAAO,YAAY,KAAK,SAAS,WAAW,KAAK,SAAS,OAAO;AAAA,EACrE;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AACJ;AAEA,SAAS,aAAa,SAAS,KAAK;AAChC,MAAI,WAAW,EAAE,iBAAAC,QAAQ,SAAS,OAAO,IAAI,UAAU,IAAI,OAAO,OAAO,GAAG,KAAK,GAAG,GAAG;AACnF,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,cAAc,KAAK,KAAK;AAC7B,MAAI,CAAC,iBAAAA,QAAQ,OAAO,GAAG,KAAK,MAAM,iBAAAA,QAAQ,SAAS,GAAG,GAAG;AACrD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,cAAc,KAAK,KAAK;AAC7B,MAAI,CAAC,iBAAAA,QAAQ,OAAO,GAAG,KAAK,MAAM,iBAAAA,QAAQ,SAAS,GAAG,GAAG;AACrD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM,KAAK,UAAU;AACzC,QAAM,EAAE,MAAM,KAAK,KAAK,QAAQ,IAAI;AACpC,QAAM,YAAY,SAAS;AAC3B,QAAM,YAAY,SAAS;AAC3B,QAAM,YAAY,SAAS;AAC3B,QAAM,SAAS,GAAG,GAAG;AACrB,MAAI,CAAC,aAAa,SAAS,MAAM,GAAG;AAChC,WAAO;AAAA,EACX;AACA,MAAI,WAAW;AACX,QAAI,CAAC,iBAAAA,QAAQ,QAAQ,GAAG,GAAG;AACvB,aAAO;AAAA,IACX;AACA,QAAI,UAAU;AACV,UAAI,CAAC,IAAI,QAAQ;AACb,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,CAAC,cAAc,KAAK,IAAI,MAAM,GAAG;AACjC,aAAO;AAAA,IACX;AACA,QAAI,CAAC,cAAc,KAAK,IAAI,MAAM,GAAG;AACjC,aAAO;AAAA,IACX;AAAA,EACJ,WACS,WAAW;AAChB,UAAM,SAAS,OAAO,GAAG;AACzB,QAAI,MAAM,MAAM,GAAG;AACf,aAAO;AAAA,IACX;AACA,QAAI,CAAC,cAAc,KAAK,MAAM,GAAG;AAC7B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,cAAc,KAAK,MAAM,GAAG;AAC7B,aAAO;AAAA,IACX;AAAA,EACJ,OACK;AACD,QAAI,WAAW;AACX,UAAI,CAAC,iBAAAA,QAAQ,SAAS,GAAG,GAAG;AACxB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,UAAU;AACV,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,CAAC,cAAc,KAAK,OAAO,MAAM,GAAG;AACpC,aAAO;AAAA,IACX;AACA,QAAI,CAAC,cAAc,KAAK,OAAO,MAAM,GAAG;AACpC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,MAAM,KAAK;AAChC,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,aAAa,iBAAAA,QAAQ,QAAQ,GAAG,IAAI,CAAC,IAAI,SAAS,aAAa,GAAG;AACxE,MAAI,UAAU;AACV,QAAI,YAAY;AACZ,aAAO;AAAA,IACX;AACA,QAAI,CAAC,eAAe,MAAM,KAAK,QAAQ,GAAG;AACtC,aAAO;AAAA,IACX;AAAA,EACJ,OACK;AACD,QAAI,CAAC,YAAY;AACb,UAAI,CAAC,eAAe,MAAM,KAAK,QAAQ,GAAG;AACtC,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,2BAA2B,CAAC,gBAAgB,YAAY,qBAAqB,iBAAiB,eAAe;AACnHD,OAAM,IAAI,wBAAwB;AAAA,EAC9B,WAAW,UAAU;AACjB,UAAM,EAAE,OAAO,WAAW,aAAa,IAAI;AAC3C,UAAM,EAAE,gBAAgB,IAAI,SAAS,WAAW;AAChD,UAAM,EAAE,kBAAkB,iBAAiB,iBAAiB,qBAAqB,IAAI,SAAS,eAAe;AAC7G,QAAI,mBAAmB,CAAC;AACxB,QAAI,0BAA0B,CAAC;AAC/B,QAAI;AAIJ,UAAM,mBAAmB,CAAC,WAAW;AACjC,aAAO,IAAI,QAAQ,aAAW;AAC1B,cAAM,YAAY,iBAAiB;AACnC,YAAI,UAAU,YAAY,OAAO;AAC7B,mBAAS,cAAc,eAAe,QAAQ,IAAI;AAClD,kBAAQ;AAAA,QACZ,OACK;AACD,mBAAS,WAAW,QAAQ,EAAE,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,KAAK,MAAM;AAC7E,oBAAQ,wBAAwB,iBAAiB,MAAM,CAAC;AAAA,UAC5D,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,mBAAmB,CAAC,iBAAiB;AACvC,YAAM,YAAY,iBAAiB;AACnC,UAAI,UAAU,YAAY,UAAU;AAChC,cAAM,OAAO,OAAO,KAAK,YAAY;AACrC,cAAM,UAAU,CAAC;AACjB,YAAI,KAAK,QAAQ;AACb,gBAAM,WAAW,KAAK,CAAC;AACvB,kBAAQ,QAAQ,IAAI,aAAa,QAAQ;AAAA,QAC7C;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAUA,UAAM,gBAAgB,CAAC,MAAM,MAAM,IAAI,WAAW;AAC9C,YAAM,YAAY,CAAC;AACnB,YAAM,EAAE,WAAW,WAAW,IAAI;AAClC,YAAM,EAAE,iBAAiB,IAAI;AAC7B,YAAM,EAAE,eAAe,gBAAgB,cAAc,IAAI;AACzD,YAAM,WAAW,gBAAgB;AACjC,YAAM,gBAAgB,qBAAqB;AAC3C,YAAM,YAAY,iBAAiB;AACnC,UAAI;AACJ,UAAI,SAAS,MAAM;AACf,oBAAY;AAAA,MAChB,WACS,MAAM;AACX,YAAI,iBAAAC,QAAQ,WAAW,IAAI,GAAG;AAC1B,eAAK;AAAA,QACT,OACK;AACD,sBAAY,iBAAAA,QAAQ,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,QACpD;AAAA,MACJ;AACA,UAAI,CAAC,WAAW;AACZ,YAAI,SAAS,kBAAkB;AAC3B,sBAAY,SAAS,iBAAiB,EAAE,OAAO,SAAS,iBAAiB,CAAC;AAAA,QAC9E,OACK;AACD,sBAAY,CAAC;AAAA,QACjB;AAAA,MACJ;AACA,YAAM,eAAe,CAAC;AACtB,mBAAa,gBAAgB,KAAK,IAAI;AACtC,qBAAe;AACf,uBAAiB,cAAc;AAC/B,YAAM,eAAe,CAAC;AACtB,UAAI,WAAW;AACX,cAAM,UAAU,QAAQ,KAAK,SAAS,OAAO,SAAS,WAAW;AACjE,cAAM,cAAc,CAAC,QAAQ;AACzB,gBAAM,QAAQ,SAAS,UAAU,GAAG;AAEpC,cAAI,cAAc,KAAK,GAAG;AACtB;AAAA,UACJ;AAEA,cAAI,eAAe,KAAK,GAAG;AACvB;AAAA,UACJ;AACA,cAAI,SAAS,kBAAkB,GAAG,GAAG;AACjC;AAAA,UACJ;AACA,cAAI,UAAU,CAAC,cAAc;AACzB,kBAAM,YAAY,CAAC;AACnB,oBAAQ,QAAQ,CAAC,WAAW;AACxB,oBAAM,QAAQ,iBAAAA,QAAQ,SAAS,MAAM,IAAI,SAAS,OAAO;AACzD,mBAAK,UAAU,CAAC,iBAAiB,iBAAAA,QAAQ,IAAI,WAAW,KAAK,GAAG;AAC5D,0BAAU,KAAK,wBAAwB,eAAe,OAAO,KAAK,MAAM,EACnE,MAAM,CAAC,EAAE,MAAM,MAAM,MAAM;AAC5B,wBAAM,OAAO;AAAA,oBACT;AAAA,oBACA;AAAA,oBACA,UAAU,SAAS,YAAY,GAAG;AAAA,oBAClC;AAAA,oBACA,aAAa,SAAS,eAAe,MAAM;AAAA,oBAC3C;AAAA,oBACA;AAAA,oBACA,QAAQ;AAAA,kBACZ;AACA,sBAAI,CAAC,UAAU,KAAK,GAAG;AACnB,8BAAU,KAAK,IAAI,CAAC;AAAA,kBACxB;AACA,+BAAa,GAAG,SAAS,UAAU,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI;AAAA,oBACtD;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,SAAS,KAAK;AAAA,kBAClB;AACA,4BAAU,KAAK,EAAE,KAAK,IAAI;AAC1B,sBAAI,CAAC,QAAQ;AACT,mCAAe;AACf,2BAAO,QAAQ,OAAO,IAAI;AAAA,kBAC9B;AAAA,gBACJ,CAAC,CAAC;AAAA,cACN;AAAA,YACJ,CAAC;AACD,yBAAa,KAAK,QAAQ,IAAI,SAAS,CAAC;AAAA,UAC5C;AAAA,QACJ;AACA,YAAI,kBAAkB;AAClB,2BAAAA,QAAQ,SAAS,WAAW,aAAa,EAAE,UAAU,cAAc,iBAAiB,CAAC;AAAA,QACzF,WACS,YAAY;AACjB,gBAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,2BAAAA,QAAQ,SAAS,WAAW,aAAa,EAAE,UAAU,cAAc,CAAC;AAAA,QACxE,OACK;AACD,oBAAU,QAAQ,WAAW;AAAA,QACjC;AACA,eAAO,QAAQ,IAAI,YAAY,EAAE,KAAK,MAAM;AACxC,gBAAM,YAAY,OAAO,KAAK,SAAS;AACvC,oBAAU,iBAAiB,iBAAiB,YAAY;AACxD,iBAAO,SAAS,EAAE,KAAK,MAAM;AACzB,gBAAI,UAAU,QAAQ;AAClB,qBAAO,QAAQ,OAAO,UAAU,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AAAA,YACpD;AACA,gBAAI,IAAI;AACJ,iBAAG;AAAA,YACP;AAAA,UACJ,CAAC;AAAA,QACL,CAAC,EAAE,MAAM,oBAAkB;AACvB,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,kBAAM,SAAS,MAAM;AACjB,uBAAS,MAAM;AACX,oBAAI,IAAI;AACJ,qBAAG,SAAS;AACZ,0BAAQ;AAAA,gBACZ,OACK;AACD,sBAAIF,WAAU,EAAE,kBAAkB,YAAY;AAE1C,2BAAO,SAAS;AAAA,kBACpB,OACK;AACD,4BAAQ,SAAS;AAAA,kBACrB;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL;AACA,kBAAM,eAAe,MAAM;AACvB,6BAAe,OAAO,SAAS,eAAe,eAAe,KAAK,eAAe,MAAM;AACvF,2BAAa,eAAe,IAAI;AAChC,+BAAiB,cAAc,EAAE,KAAK,MAAM;AAAA,YAChD;AAMA,gBAAI,UAAU,YAAY,OAAO;AAC7B,qBAAO;AAAA,YACX,OACK;AACD,oBAAM,MAAM,eAAe;AAC3B,oBAAM,SAAS,eAAe;AAC9B,uBAAS,YAAY,KAAK,MAAM,EAAE,KAAK,YAAY;AAAA,YACvD;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL,OACK;AACD,kBAAU,iBAAiB,CAAC;AAAA,MAChC;AACA,aAAO,SAAS,EAAE,KAAK,MAAM;AACzB,YAAI,IAAI;AACJ,aAAG;AAAA,QACP;AAAA,MACJ,CAAC;AAAA,IACL;AACA,uBAAmB;AAAA;AAAA;AAAA;AAAA,MAIf,aAAa,MAAM,IAAI;AACnB,YAAI,iBAAAE,QAAQ,WAAW,EAAE,GAAG;AACxB,kBAAQ,2BAA2B,CAAC,gCAAgC,oBAAoB,CAAC;AAAA,QAC7F;AACA,eAAO,cAAc,MAAM,MAAM,IAAI,IAAI;AAAA,MAC7C;AAAA;AAAA;AAAA;AAAA,MAIA,SAAS,MAAM,IAAI;AACf,eAAO,cAAc,MAAM,MAAM,EAAE;AAAA,MACvC;AAAA;AAAA;AAAA;AAAA,MAIA,kBAAkB,MAAM,eAAe;AACnC,cAAM,WAAW,iBAAAA,QAAQ,QAAQ,aAAa,IAAI,gBAAiB,gBAAgB,CAAC,aAAa,IAAI,CAAC,GAAI,IAAI,YAAU,oBAAoB,UAAU,MAAM,CAAC;AAC7J,YAAI,QAAQ,QAAQ;AAChB,iBAAO,cAAc,MAAM,SAAS,MAAM,IAAI;AAAA,QAClD;AACA,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA,MAIA,cAAc,MAAM,eAAe;AAC/B,cAAM,WAAW,iBAAAA,QAAQ,QAAQ,aAAa,IAAI,gBAAiB,gBAAgB,CAAC,aAAa,IAAI,CAAC,GAAI,IAAI,YAAU,oBAAoB,UAAU,MAAM,CAAC;AAC7J,YAAI,QAAQ,QAAQ;AAChB,iBAAO,cAAc,MAAM,SAAS,IAAI;AAAA,QAC5C;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,cAAc,MAAM,eAAe;AAC/B,cAAM,EAAE,eAAe,IAAI;AAC3B,cAAM,WAAW,gBAAgB;AACjC,cAAM,YAAY,iBAAiB;AACnC,cAAM,UAAU,iBAAAA,QAAQ,QAAQ,IAAI,IAAI,OAAQ,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,cAAM,WAAW,iBAAAA,QAAQ,QAAQ,aAAa,IAAI,gBAAiB,gBAAgB,CAAC,aAAa,IAAI,CAAC,GAAI,IAAI,YAAU,oBAAoB,UAAU,MAAM,CAAC;AAC7J,YAAI,eAAe,CAAC;AACpB,YAAI,YAAY,SAAS,UAAU,SAAS;AACxC,mBAAS,MAAM;AAAA,QACnB;AAEA,YAAI,UAAU,YAAY,UAAU;AAChC,oBAAU,iBAAiB,CAAC;AAC5B,iBAAO,SAAS;AAAA,QACpB;AACA,YAAI,QAAQ,UAAU,QAAQ,QAAQ;AAClC,yBAAe,OAAO,OAAO,CAAC,GAAG,cAAc;AAC/C,kBAAQ,QAAQ,SAAO;AACnB,oBAAQ,QAAQ,CAAC,WAAW;AACxB,oBAAM,WAAW,GAAG,SAAS,UAAU,GAAG,CAAC,IAAI,OAAO,EAAE;AACxD,kBAAI,aAAa,QAAQ,GAAG;AACxB,uBAAO,aAAa,QAAQ;AAAA,cAChC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL,WACS,QAAQ,QAAQ;AACrB,gBAAM,YAAY,QAAQ,IAAI,SAAO,GAAG,SAAS,UAAU,GAAG,CAAC,EAAE;AACjE,2BAAAA,QAAQ,KAAK,gBAAgB,CAAC,MAAM,QAAQ;AACxC,gBAAI,UAAU,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI;AAC3C,2BAAa,GAAG,IAAI;AAAA,YACxB;AAAA,UACJ,CAAC;AAAA,QACL,WACS,QAAQ,QAAQ;AACrB,gBAAM,YAAY,QAAQ,IAAI,YAAU,GAAG,OAAO,EAAE,EAAE;AACtD,2BAAAA,QAAQ,KAAK,gBAAgB,CAAC,MAAM,QAAQ;AACxC,gBAAI,UAAU,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI;AAC3C,2BAAa,GAAG,IAAI;AAAA,YACxB;AAAA,UACJ,CAAC;AAAA,QACL;AACA,kBAAU,iBAAiB;AAC3B,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ;AACA,8BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAetB,eAAe,WAAW,KAAK,QAAQ,KAAK;AACxC,cAAM,UAAU,SAAS;AACzB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,aAAa,CAAC;AACpB,cAAM,gBAAgB,CAAC;AACvB,YAAI,SAAS,WAAW;AACpB,gBAAM,QAAQ,iBAAAA,QAAQ,IAAI,WAAW,KAAK;AAC1C,cAAI,OAAO;AACP,kBAAM,YAAY,iBAAAA,QAAQ,YAAY,GAAG,IAAI,iBAAAA,QAAQ,IAAI,KAAK,KAAK,IAAI;AACvE,kBAAM,QAAQ,CAAC,SAAS;AACpB,oBAAM,EAAE,SAAS,UAAU,IAAI;AAC/B,kBAAI,cAAc,SAAS,CAAC,WAAW,cAAc,SAAS;AAC1D,oBAAI,WAAW;AACX,wBAAM,cAAc;AAAA,oBAChB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,UAAU,SAAS,YAAY,GAAG;AAAA,oBAClC;AAAA,oBACA,aAAa,SAAS,eAAe,MAAM;AAAA,oBAC3C,OAAO,OAAO;AAAA,oBACd,QAAQ;AAAA,oBACR,OAAO;AAAA,kBACX;AACA,sBAAI;AACJ,sBAAI,iBAAAA,QAAQ,SAAS,SAAS,GAAG;AAC7B,0BAAM,SAAS,WAAW,IAAI,SAAS;AACvC,wBAAI,QAAQ;AACR,4BAAM,YAAY,OAAO,4BAA4B,OAAO;AAC5D,0BAAI,WAAW;AACX,sCAAc,UAAU,WAAW;AAAA,sBACvC,OACK;AACD,+BAAO,2BAA2B,CAAC,SAAS,CAAC;AAAA,sBACjD;AAAA,oBACJ,OACK;AACD,6BAAO,2BAA2B,CAAC,SAAS,CAAC;AAAA,oBACjD;AAAA,kBACJ,OACK;AACD,kCAAc,UAAU,WAAW;AAAA,kBACvC;AACA,sBAAI,aAAa;AACb,wBAAI,iBAAAA,QAAQ,QAAQ,WAAW,GAAG;AAC9B,qCAAe;AACf,iCAAW,KAAK,IAAI,KAAK,EAAE,MAAM,UAAU,SAAS,SAAS,YAAY,SAAS,MAAM,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,oBAC7G,WACS,YAAY,OAAO;AAExB,oCAAc,KAAK,YAAY,MAAM,CAAC,MAAM;AACxC,uCAAe;AACf,mCAAW,KAAK,IAAI,KAAK,EAAE,MAAM,UAAU,SAAS,SAAS,KAAK,EAAE,UAAU,EAAE,UAAW,KAAK,WAAW,KAAK,SAAU,MAAM,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,sBACrJ,CAAC,CAAC;AAAA,oBACN;AAAA,kBACJ;AAAA,gBACJ,OACK;AACD,sBAAI,CAAC,gBAAgB,MAAM,SAAS,GAAG;AACnC,mCAAe;AACf,+BAAW,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,kBAClC;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAO,QAAQ,IAAI,aAAa,EAAE,KAAK,MAAM;AACzC,cAAI,WAAW,QAAQ;AACnB,kBAAM,OAAO,EAAE,OAAO,YAAY,MAAM,WAAW,CAAC,EAAE;AACtD,mBAAO,QAAQ,OAAO,IAAI;AAAA,UAC9B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,aAAa,MAAM,KAAK,QAAQ;AAC5B,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,MAAM,IAAI;AAClB,YAAI,SAAS,WAAW;AACpB,gBAAM,QAAQ,iBAAAA,QAAQ,IAAI,WAAW,KAAK;AAC1C,iBAAO,SAAS,CAAC,CAAC,iBAAAA,QAAQ,KAAK,OAAO,UAAQ,SAAS,SAAS,CAAC,KAAK,WAAW,SAAS,KAAK,OAAO;AAAA,QAC1G;AACA,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA,MAIA,gBAAgB,MAAM;AAClB,cAAM,EAAE,YAAY,UAAU,IAAI;AAClC,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,WAAW,gBAAgB;AACjC,cAAM,YAAY,iBAAiB;AAEnC,YAAI,aAAa,UAAU,YAAY,UAAU;AAC7C,oBAAU,iBAAiB,CAAC;AAAA,QAChC;AAEA,YAAI,cAAc,aAAa,QAAQ,KAAK;AACxC,gBAAM,EAAE,KAAK,QAAQ,KAAK,IAAI,QAAQ;AACtC,cAAI,wBAAwB,aAAa,MAAM,KAAK,MAAM,GAAG;AACzD,mBAAO,wBAAwB,eAAe,MAAM,KAAK,MAAM,EAAE,KAAK,MAAM;AACxE,kBAAI,SAAS,SAAS,OAAO;AACzB,iCAAiB,cAAc,KAAK,MAAM;AAAA,cAC9C;AAAA,YACJ,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,MAAM;AAEnB,kBAAI,CAAC,KAAK,WAAW,SAAS,KAAK,SAAS;AACxC,sBAAM,OAAO,EAAE,MAAM,KAAK,QAAQ,KAAK;AACvC,wCAAwB,iBAAiB,IAAI;AAC7C,uBAAO,QAAQ,OAAO,IAAI;AAAA,cAC9B;AACA,qBAAO,QAAQ,QAAQ;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB,QAAQ;AACrB,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,EAAE,WAAW,YAAY,eAAe,IAAI;AAClD,cAAM,EAAE,MAAM,KAAK,QAAQ,KAAK,IAAI;AACpC,cAAM,YAAY,iBAAiB;AACnC,cAAM,WAAW,gBAAgB;AACjC,cAAM,UAAU,KAAK;AACrB,mBAAW,UAAU;AACrB,YAAI,UAAU,YAAY,UAAU;AAChC,oBAAU,iBAAiB;AAAA,YACvB,CAAC,GAAG,SAAS,UAAU,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,GAAG;AAAA,cACzC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,OACK;AACD,oBAAU,iBAAiB,OAAO,OAAO,CAAC,GAAG,gBAAgB;AAAA,YACzD,CAAC,GAAG,SAAS,UAAU,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,GAAG;AAAA,cACzC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACJ;AAAA,UACJ,CAAC;AAAA,QACL;AACA,iBAAS,cAAc,eAAe,QAAQ,IAAI;AAClD,YAAI,UAAU;AACV,cAAI,aAAa,UAAU,YAAY,aAAc,UAAU,YAAY,aAAa,CAAC,UAAU,UAAU,SAAS,IAAK;AACvH,mBAAO,SAAS,KAAK,MAAM,OAAO;AAAA,UACtC;AAAA,QACJ;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG,uBAAuB;AAAA,EACrF;AAAA,EACA,UAAU,SAAS;AACf,WAAO,QAAQ,mBAAmB,wBAAwB;AAAA,EAC9D;AACJ,CAAC;;;ACplBD,IAAAC,mBAAoB;AACpB,IAAM,wBAAwB,CAAC,cAAc,eAAe,gBAAgB,cAAc,gBAAgB,eAAe,2BAA2B,sBAAsB;AAC1K,MAAM,MAAM,IAAI,qBAAqB;AAAA,EACjC,WAAW,UAAU;AACjB,UAAM,EAAE,WAAW,aAAa,IAAI;AACpC,UAAM,EAAE,mBAAmB,sBAAsB,IAAI,SAAS,eAAe;AAC7E,UAAM,EAAE,QAAQ,IAAI,SAAS,WAAW;AACxC,UAAM,UAAU,SAAS;AACzB,UAAM,gBAAgB,MAAM;AACxB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,KAAK,QAAQ;AAEnB,UAAI,cAAc;AAClB,UAAI,IAAI;AACJ,sBAAc,GAAG,eAAe;AAAA,MACpC;AACA,kBAAY,YAAY,KAAK,IAAI,IAAI,WAAW;AAAA,IACpD;AACA,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,WAAW,YAAY,IAAI;AACnC,kBAAY,UAAU;AACtB,gBAAU,SAAS;AACnB,+BAAyB;AACzB,wBAAkB;AAClB,oBAAc;AACd,aAAO,SAAS,EAAE,KAAK,MAAM,cAAc,CAAC;AAAA,IAChD;AACA,UAAM,2BAA2B,MAAM;AACnC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,cAAc,IAAI;AAC1B,UAAI,YAAY,SAAS;AACrB,cAAM,WAAW,CAAC;AAClB,cAAM,YAAY,CAAC;AACnB,cAAM,cAAc,CAAC;AACrB,yBAAAC,QAAQ,SAAS,eAAe,YAAU;AACtC,gBAAM,QAAQ,OAAO,OAAO;AAC5B,iBAAO,cAAc,OAAO;AAC5B,iBAAO,gBAAgB,OAAO;AAC9B,iBAAO,oBAAoB,OAAO;AAClC,mBAAS,KAAK,IAAI,OAAO;AACzB,oBAAU,KAAK,IAAI,OAAO;AAC1B,sBAAY,KAAK,IAAI,OAAO;AAAA,QAChC,CAAC;AACD,oBAAY,cAAc;AAC1B,oBAAY,eAAe;AAC3B,oBAAY,iBAAiB;AAC7B,kBAAU,mBAAmB,cAAc,MAAM,CAAC;AAAA,MACtD;AAAA,IACJ;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,aAAa,kBAAkB;AACrC,UAAI,YAAY,SAAS;AACrB,oBAAY,UAAU;AACtB,YAAI,CAAC,WAAW,WAAW;AACvB,mBAAS,aAAa;AAAA,QAC1B;AAAA,MACJ;AACA,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,eAAe,MAAM;AACvB,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,YAAY,SAAS;AACrB,eAAO,YAAY;AAAA,MACvB;AACA,aAAO,WAAW;AAAA,IACtB;AACA,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,kBAAkB,iBAAiB,aAAa,IAAI;AAC5D,YAAM,aAAa,kBAAkB;AACrC,YAAM,EAAE,cAAc,WAAW,YAAY,gBAAgB,YAAY,YAAY,IAAI;AACzF,uBAAAA,QAAQ,SAAS,kBAAkB,CAAC,QAAQ,OAAO,OAAO,MAAM,iBAAiB;AAC7E,YAAI,cAAc;AAEd,iBAAO,QAAQ,aAAa;AAAA,QAChC,OACK;AACD,cAAI,WAAW;AACX,kBAAM,YAAY,QAAQ;AAC1B,mBAAO,mBAAmB;AAAA,UAC9B;AACA,cAAI,YAAY;AACZ,mBAAO,QAAQ,OAAO;AAAA,UAC1B;AAAA,QACJ;AACA,YAAI,gBAAgB;AAChB,cAAI,OAAO,kBAAkB,CAAC,OAAO,YAAY,OAAO,SAAS,SAAS;AACtE,gBAAI,OAAO,sBAAsB,OAAO,aAAa;AACjD,qBAAO,cAAc,OAAO;AAC5B,qBAAO,cAAc,OAAO;AAAA,YAChC;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,cAAc;AACd,iBAAO,UAAU,OAAO;AAAA,QAC5B;AACA,YAAI,cAAc,aAAa;AAC3B,iBAAO,UAAU,OAAO;AAAA,QAC5B;AAAA,MACJ,CAAC;AACD,gBAAU,iBAAiB;AAC3B,aAAO,SAAS,gBAAgB,SAAS,EAAE,KAAK,MAAM;AAClD,YAAI,cAAc,eAAe,SAAS,+BAA+B;AACrE,cAAI,aAAa,WAAW,gBAAgB,UAAU,aAAa,KAAK,CAAC,MAAM,MAAM,KAAK,UAAU,gBAAgB,CAAC,CAAC,GAAG;AAErH,gBAAI,gBAAgB,QAAQ;AACxB,uBAAS,aAAa,eAAe;AAAA,YACzC,OACK;AACD,uBAAS,eAAe;AAAA,YAC5B;AAAA,UACJ,OACK;AAED,qBAAS,oBAAoB;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,eAAe,MAAM;AACvB,YAAM,EAAE,kBAAkB,YAAY,IAAI;AAC1C,YAAM,EAAE,aAAa,cAAc,eAAe,IAAI;AACtD,YAAM,aAAa,kBAAkB;AACrC,YAAM,EAAE,cAAc,WAAW,YAAY,eAAe,IAAI;AAChE,uBAAAA,QAAQ,SAAS,kBAAkB,YAAU;AACzC,cAAM,QAAQ,OAAO,OAAO;AAC5B,cAAM,UAAU,CAAC,CAAC,eAAe,KAAK;AACtC,cAAM,QAAQ,aAAa,KAAK,KAAK;AACrC,YAAI,cAAc;AACd,iBAAO,gBAAgB;AACvB,iBAAO,UAAU;AAAA,QACrB;AACA,YAAI,YAAY;AACZ,iBAAO,cAAc;AACrB,iBAAO,QAAQ;AAAA,QACnB;AACA,YAAI,WAAW;AACX,iBAAO,mBAAmB,YAAY,KAAK,KAAK;AAAA,QACpD;AACA,YAAI,gBAAgB;AAChB,iBAAO,oBAAoB,OAAO;AAAA,QACtC;AAAA,MACJ,GAAG,EAAE,UAAU,WAAW,CAAC;AAC3B,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,uBAAuB,CAAC,YAAY;AACtC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,iBAAiB,IAAI;AAC7B,YAAM,aAAa,kBAAkB;AACrC,YAAM,EAAE,aAAa,cAAc,IAAI;AACvC,YAAM,QAAQ,CAAC,CAAC;AAChB,UAAI,WAAW,WAAW;AACtB,yBAAAA,QAAQ,SAAS,kBAAkB,CAAC,WAAW;AAC3C,cAAI,iBAAiB,CAAC,cAAc,EAAE,QAAQ,UAAU,OAAO,CAAC,GAAG;AAC/D;AAAA,UACJ;AACA,cAAI,eAAe,CAAC,YAAY,EAAE,QAAQ,UAAU,OAAO,CAAC,GAAG;AAC3D;AAAA,UACJ;AACA,iBAAO,UAAU;AACjB,iBAAO,gBAAgB;AACvB,iBAAO,cAAc;AAAA,QACzB,CAAC;AACD,oBAAY,QAAQ;AACpB,kBAAU,iBAAiB;AAC3B,iBAAS,aAAa;AACtB,iBAAS,gBAAgB,gBAAgB;AAAA,MAC7C,OACK;AACD,yBAAAA,QAAQ,SAAS,kBAAkB,CAAC,WAAW;AAC3C,cAAI,iBAAiB,CAAC,cAAc,EAAE,QAAQ,UAAU,OAAO,CAAC,GAAG;AAC/D;AAAA,UACJ;AACA,cAAI,eAAe,CAAC,YAAY,EAAE,QAAQ,UAAU,OAAO,CAAC,GAAG;AAC3D;AAAA,UACJ;AACA,iBAAO,gBAAgB;AACvB,iBAAO,cAAc;AAAA,QACzB,CAAC;AACD,oBAAY,QAAQ;AAAA,MACxB;AACA,eAAS,kBAAkB;AAC3B,aAAO,SAAS;AAAA,IACpB;AACA,UAAM,gBAAgB;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,SAAS;AACjB,cAAM,EAAE,aAAa,IAAI;AACzB,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,aAAa,kBAAkB;AACrC,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,OAAO,OAAO,OAAO;AAAA,UACvB,SAAS;AAAA,UACT,WAAW,YAAY;AAAA,UACvB,OAAO,YAAY;AAAA,UACnB,MAAM,YAAY;AAAA,UAClB,SAAS,YAAY;AAAA,QACzB,GAAG,OAAO;AACV,yBAAAA,QAAQ,SAAS,eAAe,CAAC,WAAW;AACxC,cAAI,KAAK,WAAW;AAChB,mBAAO,cAAc;AAAA,UACzB;AACA,cAAI,KAAK,OAAO;AACZ,mBAAO,QAAQ,OAAO;AAAA,UAC1B;AACA,cAAI,KAAK,MAAM;AACX,mBAAO,mBAAmB,OAAO;AAAA,UACrC;AACA,cAAI,CAAC,eAAe,YAAY,EAAE,QAAQ,UAAU,OAAO,CAAC,GAAG;AAC3D,mBAAO,UAAU,OAAO;AAAA,UAC5B;AACA,cAAI,KAAK,SAAS;AACd,mBAAO,UAAU,OAAO;AACxB,mBAAO,cAAc,OAAO;AAAA,UAChC;AACA,iBAAO,oBAAoB,OAAO;AAAA,QACtC,CAAC;AACD,kBAAU,iBAAiB;AAC3B,iBAAS,gBAAgB,OAAO;AAChC,eAAO,SAAS,aAAa,EAAE,KAAK,MAAM;AACtC,cAAI,KAAK,WAAW,SAAS,+BAA+B;AACxD,kBAAM,iBAAiB,sBAAsB;AAC7C,gBAAI,iBAAiB,eAAe,SAAS,aAAa,QAAQ;AAC9D,kBAAI,kBAAkB,eAAe,QAAQ;AACzC,yBAAS,aAAa,cAAc;AAAA,cACxC,OACK;AACD,yBAAS,eAAe;AAAA,cAC5B;AAAA,YACJ,OACK;AACD,uBAAS,oBAAoB;AAAA,YACjC;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,0BAA0B;AACtB,cAAM,EAAE,YAAY,IAAI;AACxB,cAAM,QAAQ,CAAC,YAAY;AAC3B,eAAO,qBAAqB,KAAK;AAAA,MACrC;AAAA,MACA;AAAA,IACJ;AACA,UAAM,oBAAoB,MAAM;AAC5B,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,EAAE,YAAY,IAAI;AACxB,kBAAY,QAAQ,cAAc,MAAM,CAAC,YAAY,cAAc,CAAC,YAAY,EAAE,QAAQ,UAAU,OAAO,CAAC,IAAI,UAAU,OAAO,aAAa;AAC9I,kBAAY,kBAAkB,CAAC,YAAY,SAAS,cAAc,KAAK,CAAC,YAAY,CAAC,eAAe,YAAY,EAAE,QAAQ,UAAU,OAAO,CAAC,OAAO,OAAO,iBAAiB,OAAO,YAAY;AAAA,IAClM;AACA,UAAM,kBAAkB,CAAC,MAAM,SAAS;AACpC,YAAM,OAAO,WAAW;AACxB,WAAK,cAAc,UAAU,EAAE,KAAK,GAAG,IAAI;AAAA,IAC/C;AACA,UAAM,uBAAuB;AAAA,MACzB;AAAA,MACA;AAAA,MACA,mBAAmB,MAAM;AACrB,cAAMC,aAAY,SAAS;AAC3B,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,YAAY,SAAS;AACrB,sBAAY;AACZ,0BAAgB,SAAS,IAAI;AAAA,QACjC,OACK;AACD,sBAAY,QAAQ,KAAK;AACzB,qBAAW;AACX,0BAAgB,QAAQ,IAAI;AAAA,QAChC;AAAA,MACJ;AAAA,MACA,gBAAgB,MAAM;AAClB,cAAMA,aAAY,SAAS;AAC3B,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,CAAC,YAAY,SAAS;AACtB,sBAAY,YAAY;AACxB,sBAAY,QAAQ,KAAK;AACzB,mBAAS,WAAW;AACpB,mBAAS,gBAAgB,QAAQ,IAAI;AAAA,QACzC;AAAA,MACJ;AAAA,MACA,iBAAiB,MAAM;AACnB,cAAMA,aAAY,SAAS;AAC3B,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,YAAY,SAAS;AACrB,sBAAY,YAAY;AACxB,mBAAS,YAAY;AACrB,mBAAS,gBAAgB,SAAS,IAAI;AAAA,QAC1C;AAAA,MACJ;AAAA,MACA;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,oBAAoB;AAAA,EAC/E;AAAA,EACA,UAAU,SAAS;AACf,WAAO,QAAQ,mBAAmB,qBAAqB;AAAA,EAC3D;AACJ,CAAC;;;AC9SD,IAAAC,mBAAoB;AAMpB,IAAM,EAAE,WAAAC,YAAW,UAAAC,WAAU,SAAAC,UAAS,aAAa,IAAI;AACvD,IAAM,4BAA4B;AAClC,IAAM,mBAAmB,CAAC;AAC1B,SAAS,mBAAmB,OAAO,YAAY,SAAS;AACpD,SAAO,iBAAAC,QAAQ,OAAO,KAAK,IAAK,iBAAAA,QAAQ,OAAO,UAAU,IAAI,UAAU,aAAc;AACzF;AACA,SAAS,UAAU,OAAO,OAAO;AAC7B,SAAO,SAAS,MAAM,cAAc,iBAAAA,QAAQ,aAAa,OAAO,MAAM,WAAW,IAAI;AACzF;AACA,SAAS,cAAc,OAAO,OAAO,eAAe;AAChD,QAAM,EAAE,aAAa,CAAC,EAAE,IAAI;AAC5B,SAAO,iBAAAA,QAAQ,aAAa,UAAU,OAAO,KAAK,GAAG,WAAW,eAAe,aAAa;AAChG;AACA,SAAS,mBAAmB,OAAO,OAAO;AACtC,SAAO,cAAc,OAAO,OAAOD,SAAQ,8BAA8B,MAAM,QAAQ,MAAM,EAAE,CAAC;AACpG;AAKA,SAAS,oBAAoB,MAAM;AAC/B,SAAO,OAAO,KAAK,QAAQ,KAAK,EAAE,CAAC;AACvC;AACA,SAAS,oBAAoB,EAAE,KAAK,GAAG;AACnC,SAAO,aAAa,IAAI;AAC5B;AAKA,SAAS,gBAAgB,EAAE,KAAK,GAAG;AAC/B,SAAO,iBAAiB,oBAAoB,IAAI,CAAC;AACrD;AACA,SAAS,oBAAoB,QAAQ,SAAS,QAAQ;AAClD,QAAM,EAAE,OAAO,IAAI;AACnB,SAAO,aAAa,CAAC,GAAG,SAAS,MAAM;AAC3C;AACA,SAAS,eAAe,YAAY;AAChC,MAAI,EAAE,MAAM,MAAM,IAAI;AACtB,MAAI,SAAS,SAAS;AAClB,YAAQ,OAAO,OAAO,EAAE,MAAM,OAAO,GAAG,KAAK;AAAA,EACjD;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,YAAY;AACxC,QAAM,EAAE,MAAM,WAAW,MAAM,IAAI;AACnC,MAAI,CAAC,WAAW;AACZ,QAAI,SAAS,cAAc,SAAS,UAAU;AAC1C,YAAM,EAAE,KAAK,IAAI,SAAS,CAAC;AAC3B,aAAO,EAAE,CAAC,QAAQ,SAAS,UAAU,SAAS,YAAY,SAAS,aAAa,SAAS;AAAA,IAC7F;AACA,QAAI,SAAS,WAAW,SAAS,cAAc,SAAS,aAAa;AACjE,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,YAAY,QAAQ,OAAO,cAAc;AAC/D,SAAO,iBAAAC,QAAQ,OAAO,EAAE,WAAW,uBAAuB,UAAU,EAAE,GAAG,kBAAkB,cAAc,WAAW,OAAO,EAAE,CAAC,yBAAyB,GAAG,MAAM,CAAC;AACrK;AACA,SAAS,uBAAuB,YAAY,QAAQ,OAAO,cAAc;AACrE,SAAO,iBAAAA,QAAQ,OAAO,CAAC,GAAG,kBAAkB,cAAc,WAAW,OAAO,EAAE,CAAC,yBAAyB,GAAG,MAAM,CAAC;AACtH;AACA,SAAS,gBAAgB,YAAY,QAAQ;AACzC,SAAO,OAAO,UAAU,UAAU,uBAAuB,UAAU;AACvE;AACA,SAAS,gBAAgB,YAAY,QAAQ,WAAW,MAAM;AAC1D,QAAM,EAAE,YAAY,IAAI;AACxB,SAAO;AAAA,IACH,EAAE,QAAQ;AAAA,MACN,OAAO,CAAC,mBAAmB,OAAO,KAAK,QAAQ,EAAE;AAAA,IACrD,GAAG,eAAe,aAAa,SAAS,IAClC;AAAA,MACE,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG,WAAW,YAAY,WAAW,GAAG,CAAC,CAAC;AAAA,IAC9C,IACE,WAAW,WAAW,CAAC,CAAC;AAAA,EAClC;AACJ;AAQA,SAAS,oBAAoB,YAAY,QAAQ,MAAM;AACnD,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,aAAa,cAAc,UAAU;AAC3C,QAAM,cAAc,eAAe,UAAU;AAC7C,QAAM,EAAE,OAAO,WAAW,QAAQ,YAAY,MAAM,SAAS,IAAI,QAAQ,CAAC;AAC1E,QAAM,cAAc,gBAAgB;AACpC,QAAM,MAAM,CAAC;AACb,MAAI,QAAQ;AACR,qBAAAA,QAAQ,WAAW,QAAQ,CAAC,MAAM,QAAQ;AACtC,UAAI,UAAU,GAAG,CAAC,IAAI,YAAa,MAAM;AACrC,aAAK,QAAQ,GAAG,IAAI;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,WAAW;AACX,QAAI,UAAU,UAAU,CAAC,IAAI,SAAU,YAAY;AAC/C,gBAAU,UAAU;AACpB,UAAI,eAAe,YAAY;AAC3B,mBAAW,UAAU;AAAA,MACzB;AACA,UAAI,UAAU,OAAO,UAAU,GAAG;AAC9B,eAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,CAAC,eAAe,YAAY;AAC5B,QAAI,UAAU,WAAW,CAAC,IAAI,SAAU,MAAM;AAC1C,iBAAW,IAAI;AACf,UAAI,UAAU,OAAO,WAAW,GAAG;AAC/B,eAAO,WAAW,EAAE,QAAQ,IAAI;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU;AACV,QAAI,UAAU,SAAS,CAAC,IAAI,SAAU,MAAM;AACxC,eAAS,IAAI;AACb,UAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,eAAO,SAAS,EAAE,QAAQ,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,YAAY;AAQlB,SAAS,gBAAgB,YAAY,QAAQ,MAAM,UAAU;AACzD,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,aAAa,cAAc,UAAU;AAC3C,QAAM,cAAc,eAAe,UAAU;AAC7C,QAAM,EAAE,OAAO,WAAW,QAAQ,YAAY,MAAM,SAAS,IAAI,QAAQ,CAAC;AAC1E,QAAM,MAAM,CAAC;AACb,mBAAAA,QAAQ,WAAW,QAAQ,CAAC,MAAM,QAAQ;AACtC,QAAI,UAAU,GAAG,CAAC,IAAI,YAAa,MAAM;AACrC,UAAI,CAAC,iBAAAA,QAAQ,WAAW,IAAI,GAAG;AAC3B,eAAO,qBAAqB,CAAC,IAAI,CAAC;AAAA,MACtC;AACA,WAAK,QAAQ,GAAG,IAAI;AAAA,IACxB;AAAA,EACJ,CAAC;AACD,MAAI,WAAW;AACX,QAAI,UAAU,UAAU,CAAC,IAAI,SAAU,YAAY;AAC/C,gBAAU,UAAU;AACpB,UAAI,UAAU,OAAO,UAAU,GAAG;AAC9B,eAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,YAAY;AACZ,QAAI,UAAU,WAAW,CAAC,IAAI,YAAa,MAAM;AAC7C,iBAAW,GAAG,IAAI;AAClB,UAAI,UAAU,OAAO,WAAW,GAAG;AAC/B,eAAO,WAAW,EAAE,QAAQ,GAAG,IAAI;AAAA,MACvC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU;AACV,QAAI,UAAU,SAAS,CAAC,IAAI,YAAa,MAAM;AAC3C,eAAS,GAAG,IAAI;AAChB,UAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,eAAO,SAAS,EAAE,QAAQ,GAAG,IAAI;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,WAAW,OAAO,OAAO,KAAK,QAAQ,IAAI;AACrD;AACA,SAAS,WAAW,YAAY,QAAQ;AACpC,QAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,cAAc,gBAAgB,YAAY,MAAM;AACtD,SAAO,gBAAgB,YAAY,QAAQ;AAAA,IACvC,MAAM,WAAW;AAEb,YAAM,SAAS;AACf,YAAM,QAAQ;AACd,UAAI,aAAa;AACb,qBAAa,KAAK,QAAQ,SAAS;AAAA,MACvC;AAAA,IACJ;AAAA,IACA,OAAO,aAAa;AAEhB,UAAI,CAAC,eAAe,QAAS,CAAC,YAAY,kBAAkB,eAAe,UAAU,WAAW,EAAE,SAAS,IAAI,GAAI;AAC/G,cAAM,YAAY,YAAY;AAC9B,cAAM,SAAS;AACf,cAAM,QAAQ;AACd,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC,OACK;AACD,eAAO,aAAa,MAAM;AAAA,MAC9B;AAAA,IACJ;AAAA,IACA,OAAO;AACH,UAAI,aAAa;AACb,eAAO,2BAA2B,QAAQ,MAAM;AAAA,MACpD,OACK;AACD,eAAO,2BAA2B,QAAQ,QAAQ,MAAM,KAAK;AAAA,MACjE;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,aAAa,YAAY,QAAQ,QAAQ;AAC9C,SAAO,gBAAgB,YAAY,QAAQ;AAAA,IACvC,MAAM,OAAO;AAET,aAAO,OAAO;AAAA,IAClB;AAAA,IACA,SAAS;AACL,0BAAoB,QAAQ,CAAC,iBAAAA,QAAQ,OAAO,OAAO,IAAI,GAAG,MAAM;AAAA,IACpE;AAAA,IACA,OAAO;AACH,0BAAoB,QAAQ,CAAC,iBAAAA,QAAQ,OAAO,OAAO,IAAI,GAAG,MAAM;AAAA,IACpE;AAAA,EACJ,CAAC;AACL;AACA,SAAS,iBAAiB,YAAY,QAAQ;AAC1C,QAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,QAAM,EAAE,MAAM,IAAI;AAClB,SAAO,oBAAoB,YAAY,QAAQ;AAAA,IAC3C,MAAM,MAAM;AAER,YAAM,WAAW,KAAK;AACtB,UAAI,UAAU;AACV,cAAM,YAAY,SAAS;AAC3B,YAAI,gBAAgB,YAAY,MAAM,GAAG;AACrC,uBAAa,KAAK,QAAQ,SAAS;AAAA,QACvC,OACK;AACD,gBAAM,SAAS;AACf,gBAAM,QAAQ;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,OAAO,MAAM;AAET,YAAM,WAAW,KAAK;AACtB,UAAI,UAAU;AACV,cAAM,YAAY,SAAS;AAC3B,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,KAAK,MAAM;AACP,YAAM,WAAW,KAAK;AACtB,UAAI,UAAU;AACV,cAAM,YAAY,SAAS;AAC3B,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,mBAAmB,YAAY,QAAQ,QAAQ;AACpD,SAAO,oBAAoB,YAAY,QAAQ;AAAA,IAC3C,MAAM,MAAM;AAER,YAAM,WAAW,KAAK;AACtB,UAAI,UAAU;AACV,eAAO,OAAO,SAAS;AAAA,MAC3B;AAAA,IACJ;AAAA,IACA,SAAS;AACL,0BAAoB,QAAQ,CAAC,iBAAAA,QAAQ,OAAO,OAAO,IAAI,GAAG,MAAM;AAAA,IACpE;AAAA,IACA,OAAO;AACH,0BAAoB,QAAQ,CAAC,iBAAAA,QAAQ,OAAO,OAAO,IAAI,GAAG,MAAM;AAAA,IACpE;AAAA,EACJ,CAAC;AACL;AAKA,SAAS,iBAAiB,YAAY,QAAQ;AAC1C,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,YAAY,gBAAgB,YAAY,MAAM,IAAI,aAAa,KAAK,MAAM,IAAI,OAAO,MAAM;AACjG,SAAO;AAAA,IACH,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,EAAE,OAAO,eAAe,IAAI,GAAG,GAAG,eAAe,UAAU,CAAC,GAAG,EAAE,OAAO,UAAU,CAAC,GAAG,iBAAiB,YAAY,MAAM,CAAC,CAAC;AAAA,EACjL;AACJ;AACA,SAAS,iBAAiB,YAAY,QAAQ;AAC1C,SAAO;AAAA,IACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,IAAI,CAAC,GAAG,gBAAgB,YAAY,MAAM,CAAC,CAAC;AAAA,EACxJ;AACJ;AACA,SAAS,kBAAkB,YAAY,QAAQ;AAC3C,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,SAAS,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EACxJ;AACJ;AACA,SAAS,mBAAmB,YAAY,QAAQ;AAC5C,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,SAAS,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EACxJ;AACJ;AACA,SAAS,gCAAgC,YAAY,QAAQ;AACzD,QAAM,EAAE,QAAQ,IAAI;AACpB,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,EAAE,QAAQ,GAAG,iBAAiB,YAAY,QAAQ,SAAS,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EACjK;AACJ;AAKA,SAAS,cAAc,YAAY,QAAQ;AACvC,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,gBAAgB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,SAAS,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EACpJ;AACJ;AAKA,SAAS,oBAAoB,YAAY,QAAQ;AAC7C,SAAO;AAAA,IACH,EAAE,aAAa,YAAY,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,IAAI,CAAC,GAAG,gBAAgB,YAAY,MAAM,CAAC,CAAC;AAAA,EACnJ;AACJ;AAKA,SAAS,qBAAqB,YAAY,QAAQ;AAC9C,SAAO,WAAW,SAAS,IAAI,CAAC,oBAAoB,oBAAoB,iBAAiB,MAAM,EAAE,CAAC,CAAC;AACvG;AACA,SAAS,sBAAsB,YAAY,QAAQ,sBAAsB;AACrE,QAAM,EAAE,cAAc,mBAAmB,CAAC,EAAE,IAAI;AAChD,QAAM,eAAe,iBAAiB,WAAW;AACjD,QAAM,aAAa,iBAAiB,SAAS;AAC7C,MAAI,cAAc;AACd,WAAO,aAAa,IAAI,CAAC,OAAO,WAAW;AACvC,aAAO,EAAE,YAAY;AAAA,QACjB,KAAK;AAAA,QACL,OAAO,MAAM,UAAU;AAAA,MAC3B,GAAG,qBAAqB,MAAM,YAAY,GAAG,YAAY,MAAM,CAAC;AAAA,IACpE,CAAC;AAAA,EACL;AACA,SAAO,CAAC;AACZ;AAIA,SAAS,oBAAoB,SAAS,YAAY,QAAQ;AACtD,QAAM,EAAE,cAAc,CAAC,EAAE,IAAI;AAC7B,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,YAAY,YAAY,SAAS;AACvC,QAAM,YAAY,YAAY,SAAS;AACvC,QAAM,eAAe,YAAY,YAAY;AAC7C,QAAM,YAAY,gBAAgB,YAAY,MAAM,IAAI,aAAa,KAAK,MAAM,IAAI,OAAO,MAAM;AACjG,MAAI,SAAS;AACT,WAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AACnC,aAAO,EAAE,UAAU;AAAA,QACf,KAAK;AAAA,QACL,OAAO,OAAO,SAAS;AAAA,QACvB,UAAU,OAAO,YAAY;AAAA;AAAA,QAE7B,UAAU,OAAO,SAAS,KAAK;AAAA,MACnC,GAAG,OAAO,SAAS,CAAC;AAAA,IACxB,CAAC;AAAA,EACL;AACA,SAAO,CAAC;AACZ;AACA,SAAS,mBAAmB,YAAY,QAAQ;AAC5C,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,QAAQ,eAAe,UAAU;AACvC,SAAO,OAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AAC1C,WAAO,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,QAAQ,OAAO,eAAe,IAAI,GAAG,GAAG,KAAK,GAAG,EAAE,OAAO,OAAO,KAAK,CAAC,GAAG,mBAAmB,YAAY,QAAQ,MAAM,CAAC,CAAC;AAAA,EAC5L,CAAC;AACL;AACA,SAAS,oBAAoB,YAAY,QAAQ;AAC7C,QAAM,EAAE,OAAO,IAAI;AACnB,SAAO,OAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AAC1C,UAAM,cAAc,OAAO;AAC3B,WAAO,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,OAAO,GAAG,uBAAuB,YAAY,YAAY,WAAW,CAAC,GAAG,aAAa,YAAY,QAAQ,MAAM,CAAC,CAAC;AAAA,EAClM,CAAC;AACL;AAKA,SAAS,gBAAgB,YAAY,QAAQ;AACzC,QAAM,EAAE,OAAO,IAAI;AACnB,SAAO,OAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AAC1C,UAAM,cAAc,OAAO;AAC3B,WAAO,EAAE,gBAAgB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,OAAO,GAAG,uBAAuB,YAAY,YAAY,WAAW,CAAC,GAAG,aAAa,YAAY,QAAQ,MAAM,CAAC,CAAC;AAAA,EAC9L,CAAC;AACL;AACA,SAAS,mBAAmB,EAAE,QAAQ,KAAK,OAAO,GAAG;AACjD,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAE/C,SAAO,aAAa;AACxB;AACA,SAAS,wBAAwB,EAAE,QAAQ,KAAK,OAAO,GAAG;AACtD,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAE/C,SAAO,iBAAAA,QAAQ,cAAc,SAAS,EAAE,QAAQ,IAAI,IAAI;AAC5D;AACA,SAAS,uBAAuB,YAAY,QAAQ;AAChD,SAAO;AAAA,IACH,EAAE,UAAU,OAAO,OAAO,OAAO,OAAO,EAAE,OAAO,qBAAqB,GAAG,eAAe,UAAU,CAAC,GAAG,iBAAiB,YAAY,MAAM,CAAC,GAAG,WAAW,eAAe,sBAAsB,YAAY,QAAQ,mBAAmB,IAAI,oBAAoB,WAAW,SAAS,YAAY,MAAM,CAAC;AAAA,EACvS;AACJ;AACA,SAAS,wBAAwB,YAAY,QAAQ;AACjD,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,EAAE,SAAS,aAAa,cAAc,iBAAiB,IAAI;AACjE,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,WAAW,EAAE,SAAS,aAAa,cAAc,iBAAiB,CAAC,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EAClN;AACJ;AACA,SAAS,mCAAmC,YAAY,QAAQ;AAC5D,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,EAAE,SAAS,YAAY,IAAI;AACjC,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,WAAW,EAAE,SAAS,YAAY,CAAC,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EAClL;AACJ;AAKA,SAAS,oBAAoB,YAAY,QAAQ;AAC7C,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,EAAE,SAAS,aAAa,cAAc,iBAAiB,IAAI;AACjE,QAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,SAAO;AAAA,IACH,EAAE,gBAAgB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,WAAW,EAAE,SAAS,aAAa,cAAc,iBAAiB,CAAC,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,EAC9M;AACJ;AACA,SAAS,mBAAmB,YAAY,EAAE,KAAK,OAAO,GAAG;AACrD,QAAM,EAAE,SAAS,cAAc,cAAc,CAAC,GAAG,mBAAmB,CAAC,EAAE,IAAI;AAC3E,QAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,MAAI;AACJ,QAAM,YAAY,YAAY,SAAS;AACvC,QAAM,YAAY,YAAY,SAAS;AACvC,MAAI,EAAE,cAAc,QAAQ,cAAc,SAAY;AAClD,WAAO,iBAAAA,QAAQ,IAAI,iBAAAA,QAAQ,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,eACnE,CAAC,UAAU;AACT,YAAM,eAAe,iBAAiB,WAAW;AACjD,eAAS,QAAQ,GAAG,QAAQ,aAAa,QAAQ,SAAS;AAEtD,qBAAa,iBAAAA,QAAQ,KAAK,aAAa,KAAK,EAAE,YAAY,GAAG,UAAQ,KAAK,SAAS,KAAK,KAAK;AAC7F,YAAI,YAAY;AACZ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,aAAa,WAAW,SAAS,IAAI;AAAA,IAChD,IACE,CAAC,UAAU;AAET,mBAAa,iBAAAA,QAAQ,KAAK,SAAS,UAAQ,KAAK,SAAS,KAAK,KAAK;AACnE,aAAO,aAAa,WAAW,SAAS,IAAI;AAAA,IAChD,CAAC,EAAE,KAAK,IAAI;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,QAAQ;AACtC,QAAM,EAAE,KAAK,QAAQ,QAAQ,IAAI;AACjC,SAAO,QAAQ,WAAW,aAAa,KAAK,MAAM,IAAI,mBAAmB,OAAO,cAAc,OAAO,YAAY,MAAM;AAC3H;AACA,SAAS,uBAAuB,YAAY,EAAE,KAAK,OAAO,GAAG;AACzD,QAAM,EAAE,SAAS,cAAc,CAAC,EAAE,IAAI;AACtC,QAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,QAAM,YAAY,YAAY,SAAS;AACvC,QAAM,YAAY,YAAY,SAAS;AACvC,QAAM,eAAe,YAAY,YAAY;AAC7C,MAAI,EAAE,cAAc,QAAQ,cAAc,SAAY;AAClD,UAAM,UAAU,CAAC;AACjB,qBAAAA,QAAQ,SAAS,SAAS,UAAQ;AAC9B,cAAQ,iBAAAA,QAAQ,IAAI,MAAM,SAAS,CAAC,IAAI;AAAA,IAC5C,GAAG,EAAE,UAAU,aAAa,CAAC;AAC7B,WAAO,iBAAAA,QAAQ,IAAI,iBAAAA,QAAQ,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,CAAC,UAAU;AAChF,YAAM,OAAO,QAAQ,KAAK;AAC1B,aAAO,OAAO,iBAAAA,QAAQ,IAAI,MAAM,SAAS,IAAI;AAAA,IACjD,CAAC,EAAE,KAAK,IAAI;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,6BAA6B,QAAQ;AAC1C,QAAM,EAAE,KAAK,QAAQ,QAAQ,IAAI;AACjC,SAAO,QAAQ,WAAW,aAAa,KAAK,MAAM,IAAI,uBAAuB,OAAO,cAAc,OAAO,YAAY,MAAM;AAC/H;AACA,SAAS,iBAAiB,YAAY,QAAQ;AAC1C,QAAM,EAAE,QAAQ,CAAC,GAAG,mBAAmB,IAAI;AAC3C,QAAM,EAAE,KAAK,OAAO,IAAI;AACxB,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC7C,MAAI,aAAa;AACjB,MAAI,CAAC,aAAa,SAAS,GAAG;AAC1B,UAAM,oBAAoBH,WAAU,EAAE,eAAe,CAAC;AACtD,QAAI,SAAS,SAAS;AAClB,YAAM,WAAW,mBAAmB,MAAM,UAAU,kBAAkB,UAAU,IAAI;AACpF,YAAM,SAAS,mBAAmB,MAAM,QAAQ,kBAAkB,QAAQ,CAAC;AAC3E,kBAAY,iBAAAG,QAAQ,QAAQ,iBAAAA,QAAQ,MAAM,WAAW,MAAM,GAAG,MAAM;AACpE,UAAI,CAAC,UAAU;AACX,oBAAY,iBAAAA,QAAQ,SAAS,SAAS;AAAA,MAC1C;AACA,UAAI,oBAAoB;AACpB,YAAI,YAAY,GAAG;AACf,uBAAa;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ,WACS,SAAS,UAAU;AACxB,YAAM,WAAW,mBAAmB,MAAM,UAAU,kBAAkB,UAAU,IAAI;AACpF,YAAM,SAAS,mBAAmB,MAAM,QAAQ,kBAAkB,QAAQ,CAAC;AAC3E,YAAM,eAAe,mBAAmB,MAAM,cAAc,kBAAkB,cAAc,KAAK;AACjG,kBAAY,iBAAAA,QAAQ,SAAS,SAAS;AACtC,UAAI,oBAAoB;AACpB,YAAI,YAAY,GAAG;AACf,uBAAa;AAAA,QACjB;AAAA,MACJ;AACA,kBAAY,iBAAAA,QAAQ,QAAQ,WAAW,EAAE,OAAO,CAAC;AACjD,UAAI,CAAC,UAAU;AACX,cAAM,CAAC,MAAM,IAAI,IAAI,UAAU,MAAM,GAAG;AACxC,YAAI,MAAM;AACN,gBAAM,QAAQ,KAAK,QAAQ,OAAO,EAAE;AACpC,sBAAY,QAAQ,CAAC,MAAM,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI;AAAA,QACtD;AAAA,MACJ;AACA,UAAI,cAAc;AACd,oBAAY,GAAG,MAAM,kBAAkB,kBAAkB,kBAAkBD,SAAQ,gCAAgC,KAAK,EAAE,GAAG,SAAS;AAAA,MAC1I;AAAA,IACJ,OACK;AACD,UAAI,oBAAoB;AACpB,YAAI,iBAAAC,QAAQ,SAAS,SAAS,IAAI,GAAG;AACjC,uBAAa;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,gBAAgB,YAAY,QAAQ,WAAW,aAChD;AAAA,IACE,OAAO;AAAA,EACX,IACE,CAAC,CAAC;AACZ;AAIAF,UAAS,MAAM;AAAA,EACX,OAAO;AAAA,IACH,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC9B;AAAA,EACA,UAAU;AAAA,IACN,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EACrB;AAAA,EACA,QAAQ;AAAA,IACJ,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,gBAAgB,YAAY,QAAQ;AAChC,aAAO,gBAAgB,YAAY,QAAQ,mBAAmB,YAAY,MAAM,CAAC;AAAA,IACrF;AAAA,IACA,kBAAkB,YAAY,QAAQ;AAClC,YAAM,EAAE,OAAO,IAAI;AACnB,aAAO,OAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AAC1C,eAAO,EAAE,UAAU,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,QAAQ,OAAO,qBAAqB,GAAG,eAAe,UAAU,CAAC,GAAG,mBAAmB,YAAY,QAAQ,MAAM,CAAC,GAAG,WAAW,eAAe,sBAAsB,YAAY,QAAQ,mBAAmB,IAAI,oBAAoB,WAAW,SAAS,YAAY,MAAM,CAAC;AAAA,MACrU,CAAC;AAAA,IACL;AAAA,IACA,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,IACN,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,QAAQ,CAAC,EAAE,IAAI;AACvB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,cAAcD,WAAU,EAAE,SAAS,CAAC;AAC1C,YAAM,SAAS,MAAM,UAAU,YAAY,UAAU;AACrD,UAAI,YAAY,iBAAAG,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC7C,UAAI,WAAW;AACX,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,wBAAY,mBAAmB,WAAW,KAAK;AAC/C;AAAA,UACJ,KAAK;AACD,wBAAY,iBAAAA,QAAQ,QAAQ,iBAAAA,QAAQ,MAAM,WAAW,MAAM,GAAG,MAAM;AACpE;AAAA,QACR;AAAA,MACJ;AACA,aAAO,gBAAgB,YAAY,QAAQ,SAAS;AAAA,IACxD;AAAA,IACA,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC9B;AAAA,EACA,mBAAmB;AAAA,IACf,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,kBAAkB,QAAQ;AACtB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,gBAAgB;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,kBAAkB,YAAY,QAAQ;AAClC,YAAM,EAAE,QAAQ,CAAC,EAAE,IAAI;AACvB,YAAM,EAAE,KAAK,QAAQ,aAAa,IAAI;AACtC,YAAM,EAAE,KAAK,IAAI;AAEjB,YAAM,YAAY,iBAAAA,QAAQ,QAAQ,GAAG,IAAI,IAAI,YAAY,IAAI,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC1F,UAAI,iBAAAA,QAAQ,SAAS,SAAS,GAAG;AAC7B,cAAM,oBAAoBH,WAAU,EAAE,eAAe,CAAC;AACtD,YAAI,SAAS,SAAS;AAClB,gBAAM,WAAW,mBAAmB,MAAM,UAAU,kBAAkB,UAAU,IAAI;AACpF,gBAAM,SAAS,mBAAmB,MAAM,QAAQ,kBAAkB,QAAQ,CAAC;AAC3E,cAAI,cAAc,iBAAAG,QAAQ,QAAQ,iBAAAA,QAAQ,MAAM,WAAW,MAAM,GAAG,MAAM;AAC1E,cAAI,CAAC,UAAU;AACX,0BAAc,iBAAAA,QAAQ,SAAS,WAAW;AAAA,UAC9C;AACA,iBAAO;AAAA,QACX,WACS,SAAS,UAAU;AACxB,gBAAM,WAAW,mBAAmB,MAAM,UAAU,kBAAkB,UAAU,IAAI;AACpF,gBAAM,SAAS,mBAAmB,MAAM,QAAQ,kBAAkB,QAAQ,CAAC;AAC3E,gBAAM,eAAe,mBAAmB,MAAM,cAAc,kBAAkB,cAAc,KAAK;AACjG,cAAI,cAAc,iBAAAA,QAAQ,QAAQ,iBAAAA,QAAQ,SAAS,SAAS,GAAG,EAAE,OAAO,CAAC;AACzE,cAAI,CAAC,UAAU;AACX,kBAAM,CAAC,MAAM,IAAI,IAAI,YAAY,MAAM,GAAG;AAC1C,gBAAI,MAAM;AACN,oBAAM,QAAQ,KAAK,QAAQ,OAAO,EAAE;AACpC,4BAAc,QAAQ,CAAC,MAAM,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI;AAAA,YACxD;AAAA,UACJ;AACA,cAAI,cAAc;AACd,0BAAc,GAAG,MAAM,kBAAkB,kBAAkB,kBAAkBD,SAAQ,gCAAgC,KAAK,EAAE,GAAG,WAAW;AAAA,UAC9I;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,YAAY,WAAW,CAAC;AAAA,IACnC;AAAA,IACA,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,IAC1B,kBAAkB,QAAQ;AACtB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,YAAY,iBAAAC,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,QAAQ,CAAC,EAAE,IAAI;AACvB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,UAAI,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC7C,UAAI,WAAW;AACX,YAAI,MAAM,SAAS,QAAQ;AACvB,sBAAY,mBAAmB,WAAW,KAAK;AAAA,QACnD;AAAA,MACJ;AACA,aAAO,gBAAgB,YAAY,QAAQ,SAAS;AAAA,IACxD;AAAA,IACA,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC9B;AAAA,EACA,oBAAoB;AAAA,IAChB,gBAAgB;AAAA,IAChB,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,YAAY,SAAS,IAAI;AACjC,YAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,YAAM,UAAU,CAAC;AACjB,YAAM,OAAO,CAAC;AACd,UAAI,cAAc,UAAU;AACxB,gBAAQ,aAAa,iBAAAA,QAAQ,IAAI,KAAK,UAAU;AAChD,gBAAQ,WAAW,iBAAAA,QAAQ,IAAI,KAAK,QAAQ;AAC5C,aAAK,qBAAqB,IAAI,CAAC,UAAU;AACrC,cAAI,YAAY;AACZ,6BAAAA,QAAQ,IAAI,KAAK,YAAY,KAAK;AAAA,UACtC;AAAA,QACJ;AACA,aAAK,mBAAmB,IAAI,CAAC,UAAU;AACnC,cAAI,UAAU;AACV,6BAAAA,QAAQ,IAAI,KAAK,UAAU,KAAK;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,WAAW,OAAO,CAAC,GAAG,gBAAgB,YAAY,QAAQ;AAAA,UAC9J,MAAMC,YAAW;AACb,kBAAM,SAAS;AACf,kBAAM,QAAQA;AACd,yBAAa,KAAK,QAAQA,UAAS;AAAA,UACvC;AAAA,UACA,SAAS;AACL,mBAAO,aAAa,MAAM;AAAA,UAC9B;AAAA,UACA,OAAO;AACH,mBAAO,2BAA2B,QAAQ,MAAM;AAAA,UACpD;AAAA,QACJ,GAAG,IAAI,CAAC,CAAC;AAAA,MACb;AAAA,IACJ;AAAA,IACA,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,YAAY,SAAS,IAAI;AACjC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,cAAc,UAAU;AACxB,qBAAa,iBAAAD,QAAQ,IAAI,KAAK,UAAU;AACxC,mBAAW,iBAAAA,QAAQ,IAAI,KAAK,QAAQ;AAAA,MACxC,OACK;AACD,cAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,YAAI,WAAW;AACX,cAAI,iBAAAA,QAAQ,QAAQ,SAAS,GAAG;AAC5B,yBAAa,UAAU,CAAC;AACxB,uBAAW,UAAU,CAAC;AAAA,UAC1B,OACK;AACD,kBAAM,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG;AACrC,yBAAa,KAAK,CAAC;AACnB,uBAAW,KAAK,CAAC;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,YAAY;AAChB,UAAI,cAAc,UAAU;AACxB,oBAAY,GAAG,UAAU,MAAM,QAAQ;AAAA,MAC3C;AACA,aAAO,gBAAgB,YAAY,QAAQ,SAAS;AAAA,IACxD;AAAA,EACJ;AAAA,EACA,aAAa;AAAA,IACT,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,aAAO,gBAAgB,YAAY,QAAQ,SAAS;AAAA,IACxD;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP,oBAAoB;AAAA,EACxB;AAAA,EACA,gBAAgB;AAAA,IACZ,mBAAmB,YAAY,QAAQ;AACnC,YAAM,EAAE,QAAQ,IAAI;AACpB,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,EAAE,QAAQ,GAAG,iBAAiB,YAAY,QAAQ,IAAI,CAAC,GAAG,gBAAgB,YAAY,MAAM,CAAC,CAAC;AAAA,MACjK;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,gBAAgB,YAAY,QAAQ;AAChC,aAAO,gBAAgB,YAAY,QAAQ,mBAAmB,YAAY,MAAM,CAAC;AAAA,IACrF;AAAA,IACA,kBAAkB,YAAY,QAAQ;AAClC,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,EAAE,SAAS,aAAa,cAAc,iBAAiB,IAAI;AACjE,aAAO,OAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AAC1C,cAAM,cAAc,OAAO;AAC3B,eAAO,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,OAAO,GAAG,uBAAuB,YAAY,QAAQ,aAAa,EAAE,SAAS,aAAa,cAAc,iBAAiB,CAAC,CAAC,GAAG,aAAa,YAAY,QAAQ,MAAM,CAAC,CAAC;AAAA,MACxP,CAAC;AAAA,IACL;AAAA,IACA,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA,IACV,mBAAmB,YAAY,QAAQ;AACnC,aAAO,gBAAgB,YAAY,QAAQ,mBAAmB,YAAY,MAAM,CAAC;AAAA,IACrF;AAAA,EACJ;AAAA,EACA,cAAc;AAAA,IACV,mBAAmB,YAAY,QAAQ;AACnC,aAAO,gBAAgB,YAAY,QAAQ,mBAAmB,YAAY,MAAM,CAAC;AAAA,IACrF;AAAA,IACA,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB,YAAY,QAAQ;AAChC,aAAO,gBAAgB,YAAY,QAAQ,uBAAuB,YAAY,MAAM,CAAC;AAAA,IACzF;AAAA,IACA,mBAAmB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA,IACR,mBAAmB,YAAY,QAAQ;AACnC,aAAO,gBAAgB,YAAY,QAAQ,uBAAuB,YAAY,MAAM,CAAC;AAAA,IACzF;AAAA,EACJ;AAAA,EACA,kBAAkB;AAAA,IACd,mBAAmB,YAAY,QAAQ;AACnC,aAAO,gBAAgB,YAAY,QAAQ,uBAAuB,YAAY,MAAM,CAAC;AAAA,IACzF;AAAA,IACA,mBAAmB;AAAA,EACvB;AAAA,EACA,gBAAgB;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB,YAAY,QAAQ;AAChC,aAAO,gBAAgB,YAAY,QAAQ,uBAAuB,YAAY,MAAM,CAAC;AAAA,IACzF;AAAA,IACA,mBAAmB;AAAA,EACvB;AAAA,EACA,gBAAgB;AAAA,IACZ,gBAAgB;AAAA,IAChB,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,WAAW,EAAE,QAAQ,QAAQ,CAAC,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,MAC7K;AAAA,IACJ;AAAA,IACA,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,aAAO,EAAE,QAAQ;AAAA,QACb,OAAO;AAAA,MACX,GAAG;AAAA,QACC,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,YACH,iBAAiB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX,gBAAgB;AAAA,IAChB,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,YAAY,QAAQ,WAAW,EAAE,OAAO,QAAQ,CAAC,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,MAC5K;AAAA,IACJ;AAAA,IACA,gBAAgB,YAAY,QAAQ;AAChC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,YAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC/C,aAAO,EAAE,KAAK;AAAA,QACV,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX,oBAAoB;AAAA,EACxB;AAAA,EACA,aAAa;AAAA,IACT,oBAAoB;AAAA,EACxB;AAAA,EACA,kBAAkB;AAAA,IACd,oBAAoB;AAAA,EACxB;AAAA,EACA,WAAW;AAAA,IACP,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACxB;AAAA,EACA,WAAW;AAAA,IACP,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,IACN,mBAAmB,YAAY,QAAQ;AACnC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,KAAK,UAAU,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,MACjJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX,mBAAmB,YAAY,QAAQ;AACnC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,SAAS,UAAU,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,MACrJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,iBAAiB;AAAA,IACb,mBAAmB,YAAY,QAAQ;AACnC,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,YAAY,aAAa,KAAK,MAAM;AAC1C,aAAO;AAAA,QACH,EAAE,oBAAoB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,SAAS,UAAU,CAAC,GAAG,WAAW,YAAY,MAAM,CAAC,CAAC;AAAA,MACrJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL,oBAAoB;AAAA,EACxB;AAAA,EACA,WAAW;AAAA,IACP,oBAAoB;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AAAA,IACJ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB,YAAY,QAAQ;AAChC,UAAI;AACJ,YAAM,EAAE,QAAQ,CAAC,EAAE,IAAI;AACvB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,YAAM,SAAS,MAAM,YAAY,KAAKH,WAAU,EAAE,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAC5G,UAAI,YAAY,iBAAAG,QAAQ,IAAI,KAAK,OAAO,KAAK;AAC7C,UAAI,WAAW;AACX,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,wBAAY,mBAAmB,WAAW,KAAK;AAC/C;AAAA,UACJ,KAAK;AACD,wBAAY,iBAAAA,QAAQ,QAAQ,iBAAAA,QAAQ,MAAM,WAAW,MAAM,GAAG,MAAM;AACpE;AAAA,QACR;AAAA,MACJ;AACA,aAAO,gBAAgB,YAAY,QAAQ,SAAS;AAAA,IACxD;AAAA,IACA,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC9B;AAAA,EACA,WAAW;AAAA,IACP,gBAAgB;AAAA,EACpB;AAAA,EACA,SAAS;AAAA,IACL,oBAAoB;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,IACN,oBAAoB;AAAA,EACxB;AAAA,EACA,SAAS;AAAA,IACL,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,gBAAgB,YAAY,QAAQ;AAChC,aAAO,gBAAgB,YAAY,QAAQ,mBAAmB,YAAY,MAAM,CAAC;AAAA,IACrF;AAAA,IACA,kBAAkB,YAAY,QAAQ;AAClC,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,EAAE,SAAS,aAAa,cAAc,iBAAiB,IAAI;AACjE,aAAO,OAAO,QAAQ,IAAI,CAAC,QAAQ,WAAW;AAC1C,cAAM,cAAc,OAAO;AAC3B,eAAO,EAAE,gBAAgB,UAAU,GAAG,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,OAAO,GAAG,uBAAuB,YAAY,QAAQ,aAAa,EAAE,SAAS,aAAa,cAAc,iBAAiB,CAAC,CAAC,GAAG,aAAa,YAAY,QAAQ,MAAM,CAAC,CAAC;AAAA,MACpP,CAAC;AAAA,IACL;AAAA,IACA,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,EACvB;AAAA,EACA,QAAQ;AAAA,IACJ,gBAAgB;AAAA,EACpB;AAAA,EACA,WAAW;AAAA,IACP,gBAAgB;AAAA,EACpB;AAAA,EACA,SAAS;AAAA,IACL,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACxB;AAAA;AAEJ,CAAC;;;ACz/BM,IAAM,WAAW,OAAO,OAAO,CAAC,GAAG,eAAmB;AAAA,EACzD,QAAQ,KAAK;AACT,QAAI,UAAU,cAAkB,MAAM,aAAiB;AAAA,EAC3D;AACJ,CAAC;AACD,IAAM,cAAc;AAAA,EAChB;AACJ;AACA,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,cAAkB,MAAM,aAAiB;AACxE;AACA,MAAM,UAAU,aAAiB;AACjC,MAAM,cAAc;AACb,IAAM,QAAQ;AACrB,IAAOE,iBAAQ;;;ACvBf,IAAO,oBAAQC;", "names": ["XEUtils", "import_xe_utils", "XEUtils", "import_xe_utils", "hooks", "XEUtils", "import_xe_utils", "renderer", "hooks", "browseObj", "XEUtils", "column", "item", "import_xe_utils", "getI18n", "hooks", "renderer", "XEUtils", "item", "$xeTable", "type", "proxyOpts", "import_xe_utils", "hooks", "XEUtils", "triggerEvent", "evnt", "import_xe_utils", "getConfig", "hooks", "XEUtils", "import_xe_utils", "XEUtils", "reactData", "import_xe_utils", "getConfig", "renderer", "getI18n", "XEUtils", "cellValue", "table_default", "table_default"]}