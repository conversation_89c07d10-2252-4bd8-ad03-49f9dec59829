import {
  toolbar_default
} from "./chunk-A6LP3H4R.js";
import "./chunk-ULNOKXWY.js";
import "./chunk-CHFZI6DY.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-MSIZQRL4.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/index.js
var VxeToolbar = Object.assign({}, toolbar_default, {
  install(app) {
    app.component(toolbar_default.name, toolbar_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(toolbar_default.name, toolbar_default);
}
VxeUI.component(toolbar_default);
var Toolbar = VxeToolbar;
var toolbar_default2 = VxeToolbar;

// ../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-toolbar/index.js
var vxe_toolbar_default = toolbar_default2;
export {
  Toolbar,
  VxeToolbar,
  vxe_toolbar_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-toolbar_index__js.js.map
