#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/dist/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/dist/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../circular-dependency-scanner/dist/cli.js" "$@"
else
  exec node  "$basedir/../circular-dependency-scanner/dist/cli.js" "$@"
fi
