import {
  classNames_default
} from "./chunk-KVIU2RTF.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/statusUtils.js
function getStatusClassNames(prefixCls, status, hasFeedback) {
  return classNames_default({
    [`${prefixCls}-status-success`]: status === "success",
    [`${prefixCls}-status-warning`]: status === "warning",
    [`${prefixCls}-status-error`]: status === "error",
    [`${prefixCls}-status-validating`]: status === "validating",
    [`${prefixCls}-has-feedback`]: hasFeedback
  });
}
var getMergedStatus = (contextStatus, customStatus) => customStatus || contextStatus;

export {
  getStatusClassNames,
  getMergedStatus
};
//# sourceMappingURL=chunk-ULZHSRS6.js.map
