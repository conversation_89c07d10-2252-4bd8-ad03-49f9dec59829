@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules\nitropack\dist\cli\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules\nitropack\dist\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules\nitropack\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules\nitropack\dist\cli\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules\nitropack\dist\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules\nitropack\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\nitropack@2.11.13_@netlify+blobs@8.2.0_encoding@0.1.13\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\nitropack\dist\cli\index.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\nitropack\dist\cli\index.mjs" %*
)
