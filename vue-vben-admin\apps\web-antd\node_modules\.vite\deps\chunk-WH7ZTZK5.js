import {
  onBeforeUpdate,
  ref
} from "./chunk-ZCM5A7SR.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useRefs.js
var useRefs = () => {
  const refs = ref(/* @__PURE__ */ new Map());
  const setRef = (key) => (el) => {
    refs.value.set(key, el);
  };
  onBeforeUpdate(() => {
    refs.value = /* @__PURE__ */ new Map();
  });
  return [setRef, refs];
};
var useRefs_default = useRefs;

export {
  useRefs_default
};
//# sourceMappingURL=chunk-WH7ZTZK5.js.map
