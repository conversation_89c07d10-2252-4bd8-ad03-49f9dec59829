import {
  emits_default,
  props_default,
  table_default
} from "./chunk-ECYZWHAX.js";
import {
  getDomNode,
  getOffsetHeight,
  getPaddingTopBottomSize,
  toCssUnit
} from "./chunk-VEPVTQAW.js";
import {
  toolbar_default
} from "./chunk-A6LP3H4R.js";
import {
  defineVxeComponent,
  errLog,
  getSlotVNs,
  warnLog
} from "./chunk-ULNOKXWY.js";
import {
  getLastZIndex,
  isEnableConf,
  nextZIndex
} from "./chunk-CHFZI6DY.js";
import {
  require_xe_utils
} from "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-MSIZQRL4.js";
import "./chunk-G6CDOZZI.js";
import {
  computed,
  h,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  reactive,
  ref,
  watch
} from "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/grid/src/grid.js
var import_xe_utils = __toESM(require_xe_utils());
var { getConfig, getI18n, commands, hooks, useFns, createEvent, globalEvents, GLOBAL_EVENT_KEYS, renderEmptyElement } = VxeUI;
var tableComponentPropKeys = Object.keys(props_default);
var tableComponentMethodKeys = ["clearAll", "syncData", "updateData", "loadData", "reloadData", "reloadRow", "loadColumn", "reloadColumn", "getRowNode", "getColumnNode", "getRowIndex", "getVTRowIndex", "getVMRowIndex", "getColumnIndex", "getVTColumnIndex", "getVMColumnIndex", "setRow", "createData", "createRow", "revertData", "clearData", "isRemoveByRow", "isInsertByRow", "isUpdateByRow", "getColumns", "getColumnById", "getColumnByField", "getTableColumn", "getFullColumns", "getData", "getCheckboxRecords", "getParentRow", "getTreeRowChildren", "getTreeParentRow", "getRowSeq", "getRowById", "getRowid", "getTableData", "getFullData", "setColumnFixed", "clearColumnFixed", "setColumnWidth", "getColumnWidth", "setRowHeightConf", "getRowHeightConf", "setRowHeight", "getRowHeight", "hideColumn", "showColumn", "resetColumn", "refreshColumn", "refreshScroll", "recalculate", "closeTooltip", "isAllCheckboxChecked", "isAllCheckboxIndeterminate", "getCheckboxIndeterminateRecords", "setCheckboxRow", "setCheckboxRowKey", "isCheckedByCheckboxRow", "isCheckedByCheckboxRowKey", "isIndeterminateByCheckboxRow", "isIndeterminateByCheckboxRowKey", "toggleCheckboxRow", "setAllCheckboxRow", "getRadioReserveRecord", "clearRadioReserve", "getCheckboxReserveRecords", "clearCheckboxReserve", "toggleAllCheckboxRow", "clearCheckboxRow", "setCurrentRow", "isCheckedByRadioRow", "isCheckedByRadioRowKey", "setRadioRow", "setRadioRowKey", "clearCurrentRow", "clearRadioRow", "getCurrentRecord", "getRadioRecord", "getCurrentColumn", "setCurrentColumn", "clearCurrentColumn", "setPendingRow", "togglePendingRow", "hasPendingByRow", "isPendingByRow", "getPendingRecords", "clearPendingRow", "sort", "setSort", "clearSort", "clearSortByEvent", "isSort", "getSortColumns", "closeFilter", "isFilter", "clearFilterByEvent", "isActiveFilterByColumn", "isRowExpandLoaded", "clearRowExpandLoaded", "reloadRowExpand", "reloadRowExpand", "toggleRowExpand", "setAllRowExpand", "setRowExpand", "isExpandByRow", "isRowExpandByRow", "clearRowExpand", "clearRowExpandReserve", "getRowExpandRecords", "getTreeExpandRecords", "isTreeExpandLoaded", "clearTreeExpandLoaded", "reloadTreeExpand", "reloadTreeChilds", "toggleTreeExpand", "setAllTreeExpand", "setTreeExpand", "isTreeExpandByRow", "clearTreeExpand", "clearTreeExpandReserve", "getScroll", "scrollTo", "scrollToRow", "scrollToColumn", "clearScroll", "updateFooter", "updateStatus", "setMergeCells", "removeInsertRow", "removeMergeCells", "getMergeCells", "clearMergeCells", "setMergeFooterItems", "removeMergeFooterItems", "getMergeFooterItems", "clearMergeFooterItems", "getCustomStoreData", "setRowGroupExpand", "setAllRowGroupExpand", "clearRowGroupExpand", "isRowGroupExpandByRow", "isRowGroupRecord", "isAggregateRecord", "isAggregateExpandByRow", "getAggregateContentByRow", "getAggregateRowChildren", "setRowGroups", "clearRowGroups", "openTooltip", "moveColumnTo", "moveRowTo", "getCellLabel", "getCellElement", "focus", "blur", "connect"];
var gridComponentEmits = [
  ...emits_default,
  "page-change",
  "form-submit",
  "form-submit-invalid",
  "form-reset",
  "form-collapse",
  "form-toggle-collapse",
  "proxy-query",
  "proxy-delete",
  "proxy-save",
  "toolbar-button-click",
  "toolbar-tool-click",
  "zoom"
];
var grid_default = defineVxeComponent({
  name: "VxeGrid",
  props: Object.assign(Object.assign({}, props_default), { layouts: Array, columns: Array, pagerConfig: Object, proxyConfig: Object, toolbarConfig: Object, formConfig: Object, zoomConfig: Object, size: {
    type: String,
    default: () => getConfig().grid.size || getConfig().size
  } }),
  emits: gridComponentEmits,
  setup(props, context) {
    var _a;
    const { slots, emit } = context;
    const xID = import_xe_utils.default.uniqueId();
    const VxeUIFormComponent = VxeUI.getComponent("VxeForm");
    const VxeUIPagerComponent = VxeUI.getComponent("VxePager");
    const defaultLayouts = [["Form"], ["Toolbar", "Top", "Table", "Bottom", "Pager"]];
    const { computeSize } = useFns.useSize(props);
    const reactData = reactive({
      tableLoading: false,
      proxyInited: false,
      isZMax: false,
      tableData: [],
      filterData: [],
      formData: {},
      sortData: [],
      tZindex: 0,
      tablePage: {
        total: 0,
        pageSize: ((_a = getConfig().pager) === null || _a === void 0 ? void 0 : _a.pageSize) || 10,
        currentPage: 1
      }
    });
    const refElem = ref();
    const refTable = ref();
    const refForm = ref();
    const refToolbar = ref();
    const refPager = ref();
    const refFormWrapper = ref();
    const refToolbarWrapper = ref();
    const refTopWrapper = ref();
    const refBottomWrapper = ref();
    const refPagerWrapper = ref();
    const extendTableMethods = (methodKeys) => {
      const funcs = {};
      methodKeys.forEach((name) => {
        funcs[name] = (...args) => {
          const $xeTable = refTable.value;
          if ($xeTable && $xeTable[name]) {
            return $xeTable[name](...args);
          }
        };
      });
      return funcs;
    };
    const gridExtendTableMethods = extendTableMethods(tableComponentMethodKeys);
    tableComponentMethodKeys.forEach((name) => {
      gridExtendTableMethods[name] = (...args) => {
        const $xeTable = refTable.value;
        if ($xeTable && $xeTable[name]) {
          return $xeTable && $xeTable[name](...args);
        }
      };
    });
    const computeProxyOpts = computed(() => {
      return import_xe_utils.default.merge({}, import_xe_utils.default.clone(getConfig().grid.proxyConfig, true), props.proxyConfig);
    });
    const computeIsRespMsg = computed(() => {
      const proxyOpts = computeProxyOpts.value;
      return !!(import_xe_utils.default.isBoolean(proxyOpts.message) ? proxyOpts.message : proxyOpts.showResponseMsg);
    });
    const computeIsActiveMsg = computed(() => {
      const proxyOpts = computeProxyOpts.value;
      return import_xe_utils.default.isBoolean(proxyOpts.showActionMsg) ? proxyOpts.showActionMsg : !!proxyOpts.showActiveMsg;
    });
    const computePagerOpts = computed(() => {
      return Object.assign({}, getConfig().grid.pagerConfig, props.pagerConfig);
    });
    const computeFormOpts = computed(() => {
      return Object.assign({}, getConfig().grid.formConfig, props.formConfig);
    });
    const computeToolbarOpts = computed(() => {
      return Object.assign({}, getConfig().grid.toolbarConfig, props.toolbarConfig);
    });
    const computeZoomOpts = computed(() => {
      return Object.assign({}, getConfig().grid.zoomConfig, props.zoomConfig);
    });
    const computeStyles = computed(() => {
      const { height, maxHeight } = props;
      const { isZMax, tZindex } = reactData;
      const stys = {};
      if (isZMax) {
        stys.zIndex = tZindex;
      } else {
        if (height) {
          stys.height = height === "auto" || height === "100%" ? "100%" : toCssUnit(height);
        }
        if (maxHeight) {
          stys.maxHeight = maxHeight === "auto" || maxHeight === "100%" ? "100%" : toCssUnit(maxHeight);
        }
      }
      return stys;
    });
    const computeTableExtendProps = computed(() => {
      const rest = {};
      const gridProps = props;
      tableComponentPropKeys.forEach((key) => {
        rest[key] = gridProps[key];
      });
      return rest;
    });
    const computeTableProps = computed(() => {
      const { seqConfig, pagerConfig, editConfig, proxyConfig } = props;
      const { isZMax, tablePage } = reactData;
      const tableExtendProps = computeTableExtendProps.value;
      const proxyOpts = computeProxyOpts.value;
      const pagerOpts = computePagerOpts.value;
      const isLoading = computeIsLoading.value;
      const tProps = Object.assign({}, tableExtendProps);
      if (isZMax) {
        if (tableExtendProps.maxHeight) {
          tProps.maxHeight = "100%";
        } else {
          tProps.height = "100%";
        }
      }
      if (proxyConfig && isEnableConf(proxyOpts)) {
        tProps.loading = isLoading;
        if (pagerConfig && proxyOpts.seq && isEnableConf(pagerOpts)) {
          tProps.seqConfig = Object.assign({}, seqConfig, { startIndex: (tablePage.currentPage - 1) * tablePage.pageSize });
        }
      }
      if (editConfig) {
        tProps.editConfig = Object.assign({}, editConfig);
      }
      return tProps;
    });
    const computeCurrLayoutConf = computed(() => {
      const { layouts } = props;
      let confs = [];
      if (layouts && layouts.length) {
        confs = layouts;
      } else {
        confs = getConfig().grid.layouts || defaultLayouts;
      }
      let headKeys = [];
      let bodyKeys = [];
      let footKeys = [];
      if (confs.length) {
        if (import_xe_utils.default.isArray(confs[0])) {
          headKeys = confs[0];
          bodyKeys = confs[1] || [];
          footKeys = confs[2] || [];
        } else {
          bodyKeys = confs;
        }
      }
      return {
        headKeys,
        bodyKeys,
        footKeys
      };
    });
    const computeCustomCurrentPageFlag = computed(() => {
      const pagerOpts = computePagerOpts.value;
      return pagerOpts.currentPage;
    });
    const computeCustomPageSizeFlag = computed(() => {
      const pagerOpts = computePagerOpts.value;
      return pagerOpts.pageSize;
    });
    const computeCustomTotalFlag = computed(() => {
      const pagerOpts = computePagerOpts.value;
      return pagerOpts.total;
    });
    const computeIsLoading = computed(() => {
      const { loading, proxyConfig } = props;
      const { tableLoading } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const { showLoading } = proxyOpts;
      return loading || tableLoading && showLoading && proxyConfig && isEnableConf(proxyOpts);
    });
    const refMaps = {
      refElem,
      refTable,
      refForm,
      refToolbar,
      refPager
    };
    const computeMaps = {
      computeProxyOpts,
      computePagerOpts,
      computeFormOpts,
      computeToolbarOpts,
      computeZoomOpts
    };
    const $xeGrid = {
      xID,
      props,
      context,
      reactData,
      getRefMaps: () => refMaps,
      getComputeMaps: () => computeMaps
    };
    const initToolbar = () => {
      const toolbarOpts = computeToolbarOpts.value;
      if (props.toolbarConfig && isEnableConf(toolbarOpts)) {
        nextTick(() => {
          const $xeTable = refTable.value;
          const $xeToolbar = refToolbar.value;
          if ($xeTable && $xeToolbar) {
            $xeTable.connect($xeToolbar);
          }
        });
      }
    };
    const getFormData = () => {
      const { proxyConfig } = props;
      const { formData } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const formOpts = computeFormOpts.value;
      return proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data;
    };
    const initPages = (propKey) => {
      const { tablePage } = reactData;
      const { pagerConfig } = props;
      const pagerOpts = computePagerOpts.value;
      if (pagerConfig && isEnableConf(pagerOpts)) {
        if (propKey) {
          if (pagerOpts[propKey]) {
            tablePage[propKey] = import_xe_utils.default.toNumber(pagerOpts[propKey]);
          }
        } else {
          const { currentPage, pageSize, total } = pagerOpts;
          if (currentPage) {
            tablePage.currentPage = currentPage;
          }
          if (pageSize) {
            tablePage.pageSize = pageSize;
          }
          if (total) {
            tablePage.pageSize = total;
          }
        }
      }
    };
    const triggerPendingEvent = (code) => {
      const isActiveMsg = computeIsActiveMsg.value;
      const $xeTable = refTable.value;
      const selectRecords = $xeTable.getCheckboxRecords();
      if (selectRecords.length) {
        $xeTable.togglePendingRow(selectRecords);
        gridExtendTableMethods.clearCheckboxRow();
      } else {
        if (isActiveMsg) {
          if (VxeUI.modal) {
            VxeUI.modal.message({ id: code, content: getI18n("vxe.grid.selectOneRecord"), status: "warning" });
          }
        }
      }
    };
    const getRespMsg = (rest, defaultMsg) => {
      const proxyOpts = computeProxyOpts.value;
      const resConfigs = proxyOpts.response || proxyOpts.props || {};
      const messageProp = resConfigs.message;
      let msg;
      if (rest && messageProp) {
        msg = import_xe_utils.default.isFunction(messageProp) ? messageProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, messageProp);
      }
      return msg || getI18n(defaultMsg);
    };
    const handleDeleteRow = (code, alertKey, callback) => {
      const isActiveMsg = computeIsActiveMsg.value;
      const selectRecords = gridExtendTableMethods.getCheckboxRecords();
      if (isActiveMsg) {
        if (selectRecords.length) {
          if (VxeUI.modal) {
            return VxeUI.modal.confirm({ id: `cfm_${code}`, content: getI18n(alertKey), escClosable: true }).then((type) => {
              if (type === "confirm") {
                return callback();
              }
            });
          }
        } else {
          if (VxeUI.modal) {
            VxeUI.modal.message({ id: `msg_${code}`, content: getI18n("vxe.grid.selectOneRecord"), status: "warning" });
          }
        }
      } else {
        if (selectRecords.length) {
          callback();
        }
      }
      return Promise.resolve();
    };
    const pageChangeEvent = (params) => {
      const { proxyConfig } = props;
      const { tablePage } = reactData;
      const { $event, currentPage, pageSize } = params;
      const proxyOpts = computeProxyOpts.value;
      tablePage.currentPage = currentPage;
      tablePage.pageSize = pageSize;
      $xeGrid.dispatchEvent("page-change", params, $event);
      if (proxyConfig && isEnableConf(proxyOpts)) {
        $xeGrid.commitProxy("query").then((rest) => {
          $xeGrid.dispatchEvent("proxy-query", rest, $event);
        });
      }
    };
    const sortChangeEvent = (params) => {
      const $xeTable = refTable.value;
      const { proxyConfig } = props;
      const { computeSortOpts } = $xeTable.getComputeMaps();
      const proxyOpts = computeProxyOpts.value;
      const sortOpts = computeSortOpts.value;
      if (sortOpts.remote) {
        reactData.sortData = params.sortList;
        if (proxyConfig && isEnableConf(proxyOpts)) {
          reactData.tablePage.currentPage = 1;
          $xeGrid.commitProxy("query").then((rest) => {
            $xeGrid.dispatchEvent("proxy-query", rest, params.$event);
          });
        }
      }
      $xeGrid.dispatchEvent("sort-change", params, params.$event);
    };
    const filterChangeEvent = (params) => {
      const $xeTable = refTable.value;
      const { proxyConfig } = props;
      const { computeFilterOpts } = $xeTable.getComputeMaps();
      const proxyOpts = computeProxyOpts.value;
      const filterOpts = computeFilterOpts.value;
      if (filterOpts.remote) {
        reactData.filterData = params.filterList;
        if (proxyConfig && isEnableConf(proxyOpts)) {
          reactData.tablePage.currentPage = 1;
          $xeGrid.commitProxy("query").then((rest) => {
            $xeGrid.dispatchEvent("proxy-query", rest, params.$event);
          });
        }
      }
      $xeGrid.dispatchEvent("filter-change", params, params.$event);
    };
    const submitFormEvent = (params) => {
      const { proxyConfig } = props;
      const proxyOpts = computeProxyOpts.value;
      if (reactData.tableLoading) {
        return;
      }
      if (proxyConfig && isEnableConf(proxyOpts)) {
        $xeGrid.commitProxy("reload").then((rest) => {
          $xeGrid.dispatchEvent("proxy-query", Object.assign(Object.assign({}, rest), { isReload: true }), params.$event);
        });
      }
      $xeGrid.dispatchEvent("form-submit", params, params.$event);
    };
    const resetFormEvent = (params) => {
      const $xeTable = refTable.value;
      const { proxyConfig } = props;
      const { $event } = params;
      const proxyOpts = computeProxyOpts.value;
      if (proxyConfig && isEnableConf(proxyOpts)) {
        $xeTable.clearScroll();
        $xeGrid.commitProxy("reload").then((rest) => {
          $xeGrid.dispatchEvent("proxy-query", Object.assign(Object.assign({}, rest), { isReload: true }), $event);
        });
      }
      $xeGrid.dispatchEvent("form-reset", params, $event);
    };
    const submitInvalidEvent = (params) => {
      $xeGrid.dispatchEvent("form-submit-invalid", params, params.$event);
    };
    const collapseEvent = (params) => {
      const { $event } = params;
      $xeGrid.dispatchEvent("form-toggle-collapse", params, $event);
      $xeGrid.dispatchEvent("form-collapse", params, $event);
    };
    const handleZoom = (isMax) => {
      const { isZMax } = reactData;
      if (isMax ? !isZMax : isZMax) {
        reactData.isZMax = !isZMax;
        if (reactData.tZindex < getLastZIndex()) {
          reactData.tZindex = nextZIndex();
        }
      }
      return nextTick().then(() => $xeGrid.recalculate(true)).then(() => {
        setTimeout(() => $xeGrid.recalculate(true), 15);
        return reactData.isZMax;
      });
    };
    const getFuncSlot = (optSlots, slotKey) => {
      const funcSlot = optSlots[slotKey];
      if (funcSlot) {
        if (import_xe_utils.default.isString(funcSlot)) {
          if (slots[funcSlot]) {
            return slots[funcSlot];
          } else {
            errLog("vxe.error.notSlot", [funcSlot]);
          }
        } else {
          return funcSlot;
        }
      }
      return null;
    };
    const getConfigSlot = (slotConfigs) => {
      const slotConf = {};
      import_xe_utils.default.objectMap(slotConfigs, (slotFunc, slotKey) => {
        if (slotFunc) {
          if (import_xe_utils.default.isString(slotFunc)) {
            if (slots[slotFunc]) {
              slotConf[slotKey] = slots[slotFunc];
            } else {
              errLog("vxe.error.notSlot", [slotFunc]);
            }
          } else {
            slotConf[slotKey] = slotFunc;
          }
        }
      });
      return slotConf;
    };
    const renderForm = () => {
      const { formConfig, proxyConfig } = props;
      const { formData } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const formOpts = computeFormOpts.value;
      if (formConfig && isEnableConf(formOpts) || slots.form) {
        let slotVNs = [];
        if (slots.form) {
          slotVNs = slots.form({ $grid: $xeGrid });
        } else {
          if (formOpts.items) {
            const formSlots = {};
            if (!formOpts.inited) {
              formOpts.inited = true;
              const beforeItem = proxyOpts.beforeItem;
              if (proxyOpts && beforeItem) {
                formOpts.items.forEach((item) => {
                  beforeItem({ $grid: $xeGrid, item });
                });
              }
            }
            formOpts.items.forEach((item) => {
              import_xe_utils.default.each(item.slots, (func) => {
                if (!import_xe_utils.default.isFunction(func)) {
                  if (slots[func]) {
                    formSlots[func] = slots[func];
                  }
                }
              });
            });
            if (VxeUIFormComponent) {
              slotVNs.push(h(VxeUIFormComponent, Object.assign(Object.assign({ ref: refForm }, Object.assign({}, formOpts, {
                data: proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data
              })), { onSubmit: submitFormEvent, onReset: resetFormEvent, onSubmitInvalid: submitInvalidEvent, onCollapse: collapseEvent }), formSlots));
            }
          }
        }
        return h("div", {
          ref: refFormWrapper,
          key: "form",
          class: "vxe-grid--form-wrapper"
        }, slotVNs);
      }
      return renderEmptyElement($xeGrid);
    };
    const renderToolbar = () => {
      const { toolbarConfig } = props;
      const toolbarOpts = computeToolbarOpts.value;
      if (toolbarConfig && isEnableConf(toolbarOpts) || slots.toolbar) {
        let slotVNs = [];
        if (slots.toolbar) {
          slotVNs = slots.toolbar({ $grid: $xeGrid });
        } else {
          const toolbarOptSlots = toolbarOpts.slots;
          const toolbarSlots = {};
          if (toolbarOptSlots) {
            const buttonsSlot = getFuncSlot(toolbarOptSlots, "buttons");
            const buttonPrefixSlot = getFuncSlot(toolbarOptSlots, "buttonPrefix");
            const buttonSuffixSlot = getFuncSlot(toolbarOptSlots, "buttonSuffix");
            const toolsSlot = getFuncSlot(toolbarOptSlots, "tools");
            const toolPrefixSlot = getFuncSlot(toolbarOptSlots, "toolPrefix");
            const toolSuffixSlot = getFuncSlot(toolbarOptSlots, "toolSuffix");
            if (buttonsSlot) {
              toolbarSlots.buttons = buttonsSlot;
            }
            if (buttonPrefixSlot) {
              toolbarSlots.buttonPrefix = buttonPrefixSlot;
            }
            if (buttonSuffixSlot) {
              toolbarSlots.buttonSuffix = buttonSuffixSlot;
            }
            if (toolsSlot) {
              toolbarSlots.tools = toolsSlot;
            }
            if (toolPrefixSlot) {
              toolbarSlots.toolPrefix = toolPrefixSlot;
            }
            if (toolSuffixSlot) {
              toolbarSlots.toolSuffix = toolSuffixSlot;
            }
          }
          slotVNs.push(h(toolbar_default, Object.assign(Object.assign({ ref: refToolbar }, toolbarOpts), { slots: void 0 }), toolbarSlots));
        }
        return h("div", {
          ref: refToolbarWrapper,
          key: "toolbar",
          class: "vxe-grid--toolbar-wrapper"
        }, slotVNs);
      }
      return renderEmptyElement($xeGrid);
    };
    const renderTop = () => {
      const topSlot = slots.top;
      if (topSlot) {
        return h("div", {
          ref: refTopWrapper,
          key: "top",
          class: "vxe-grid--top-wrapper"
        }, topSlot({ $grid: $xeGrid }));
      }
      return renderEmptyElement($xeGrid);
    };
    const renderTableLeft = () => {
      const leftSlot = slots.left;
      if (leftSlot) {
        return h("div", {
          class: "vxe-grid--left-wrapper"
        }, leftSlot({ $grid: $xeGrid }));
      }
      return renderEmptyElement($xeGrid);
    };
    const renderTableRight = () => {
      const rightSlot = slots.right;
      if (rightSlot) {
        return h("div", {
          class: "vxe-grid--right-wrapper"
        }, rightSlot({ $grid: $xeGrid }));
      }
      return renderEmptyElement($xeGrid);
    };
    const renderTable = () => {
      const { proxyConfig } = props;
      const tableProps = computeTableProps.value;
      const proxyOpts = computeProxyOpts.value;
      const tableOns = Object.assign({}, tableCompEvents);
      const emptySlot = slots.empty;
      const loadingSlot = slots.loading;
      const rowDragIconSlot = slots.rowDragIcon || slots["row-drag-icon"];
      const columnDragIconSlot = slots.columnDragIcon || slots["column-drag-icon"];
      if (proxyConfig && isEnableConf(proxyOpts)) {
        if (proxyOpts.sort) {
          tableOns.onSortChange = sortChangeEvent;
        }
        if (proxyOpts.filter) {
          tableOns.onFilterChange = filterChangeEvent;
        }
      }
      const slotObj = {};
      if (emptySlot) {
        slotObj.empty = emptySlot;
      }
      if (loadingSlot) {
        slotObj.loading = loadingSlot;
      }
      if (rowDragIconSlot) {
        slotObj.rowDragIcon = rowDragIconSlot;
      }
      if (columnDragIconSlot) {
        slotObj.columnDragIcon = columnDragIconSlot;
      }
      return h("div", {
        class: "vxe-grid--table-wrapper"
      }, [
        h(table_default, Object.assign(Object.assign({ ref: refTable }, tableProps), tableOns), slotObj)
      ]);
    };
    const renderBottom = () => {
      if (slots.bottom) {
        return h("div", {
          ref: refBottomWrapper,
          key: "bottom",
          class: "vxe-grid--bottom-wrapper"
        }, slots.bottom({ $grid: $xeGrid }));
      }
      return renderEmptyElement($xeGrid);
    };
    const renderPager = () => {
      const { proxyConfig, pagerConfig } = props;
      const proxyOpts = computeProxyOpts.value;
      const pagerOpts = computePagerOpts.value;
      const pagerSlot = slots.pager;
      if (pagerConfig && isEnableConf(pagerOpts) || slots.pager) {
        return h("div", {
          ref: refPagerWrapper,
          key: "pager",
          class: "vxe-grid--pager-wrapper"
        }, pagerSlot ? pagerSlot({ $grid: $xeGrid }) : [
          VxeUIPagerComponent ? h(VxeUIPagerComponent, Object.assign(Object.assign(Object.assign({ ref: refPager }, pagerOpts), proxyConfig && isEnableConf(proxyOpts) ? reactData.tablePage : {}), { onPageChange: pageChangeEvent }), getConfigSlot(pagerOpts.slots)) : renderEmptyElement($xeGrid)
        ]);
      }
      return renderEmptyElement($xeGrid);
    };
    const renderChildLayout = (layoutKeys) => {
      const childVNs = [];
      layoutKeys.forEach((key) => {
        switch (key) {
          case "Form":
            childVNs.push(renderForm());
            break;
          case "Toolbar":
            childVNs.push(renderToolbar());
            break;
          case "Top":
            childVNs.push(renderTop());
            break;
          case "Table":
            childVNs.push(h("div", {
              key: "table",
              class: "vxe-grid--table-container"
            }, [
              renderTableLeft(),
              renderTable(),
              renderTableRight()
            ]));
            break;
          case "Bottom":
            childVNs.push(renderBottom());
            break;
          case "Pager":
            childVNs.push(renderPager());
            break;
          default:
            errLog("vxe.error.notProp", [`layouts -> ${key}`]);
            break;
        }
      });
      return childVNs;
    };
    const renderLayout = () => {
      const currLayoutConf = computeCurrLayoutConf.value;
      const { headKeys, bodyKeys, footKeys } = currLayoutConf;
      const asideLeftSlot = slots.asideLeft || slots["aside-left"];
      const asideRightSlot = slots.asideRight || slots["aside-right"];
      return [
        h("div", {
          class: "vxe-grid--layout-header-wrapper"
        }, renderChildLayout(headKeys)),
        h("div", {
          class: "vxe-grid--layout-body-wrapper"
        }, [
          asideLeftSlot ? h("div", {
            class: "vxe-grid--layout-aside-left-wrapper"
          }, asideLeftSlot({})) : renderEmptyElement($xeGrid),
          h("div", {
            class: "vxe-grid--layout-body-content-wrapper"
          }, renderChildLayout(bodyKeys)),
          asideRightSlot ? h("div", {
            class: "vxe-grid--layout-aside-right-wrapper"
          }, asideRightSlot({})) : renderEmptyElement($xeGrid)
        ]),
        h("div", {
          class: "vxe-grid--layout-footer-wrapper"
        }, renderChildLayout(footKeys))
      ];
    };
    const tableCompEvents = {};
    emits_default.forEach((name) => {
      const type = import_xe_utils.default.camelCase(`on-${name}`);
      tableCompEvents[type] = (...args) => emit(name, ...args);
    });
    const getDefaultFormData = () => {
      const formOpts = computeFormOpts.value;
      if (formOpts.items) {
        const fData = {};
        formOpts.items.forEach((item) => {
          const { field, itemRender } = item;
          if (field) {
            let itemValue = null;
            if (itemRender) {
              const { defaultValue } = itemRender;
              if (import_xe_utils.default.isFunction(defaultValue)) {
                itemValue = defaultValue({ item });
              } else if (!import_xe_utils.default.isUndefined(defaultValue)) {
                itemValue = defaultValue;
              }
            }
            fData[field] = itemValue;
          }
        });
        return fData;
      }
      return {};
    };
    const initProxy = () => {
      const { proxyConfig, formConfig } = props;
      const { proxyInited } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const formOpts = computeFormOpts.value;
      if (proxyConfig && isEnableConf(proxyOpts)) {
        if (formConfig && isEnableConf(formOpts) && proxyOpts.form && formOpts.items) {
          reactData.formData = getDefaultFormData();
        }
        if (!proxyInited) {
          reactData.proxyInited = true;
          if (proxyOpts.autoLoad !== false) {
            nextTick().then(() => gridMethods.commitProxy("initial")).then((rest) => {
              gridMethods.dispatchEvent("proxy-query", Object.assign(Object.assign({}, rest), { isInited: true }), new Event("initial"));
            });
          }
        }
      }
    };
    const handleGlobalKeydownEvent = (evnt) => {
      const zoomOpts = computeZoomOpts.value;
      const isEsc = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ESCAPE);
      if (isEsc && reactData.isZMax && zoomOpts.escRestore !== false) {
        $xeGrid.triggerZoomEvent(evnt);
      }
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $grid: $xeGrid }, params));
    };
    const gridMethods = {
      dispatchEvent,
      getEl() {
        return refElem.value;
      },
      /**
       * 提交指令，支持 code 或 button
       * @param {String/Object} code 字符串或对象
       */
      commitProxy(proxyTarget, ...args) {
        const { proxyConfig, toolbarConfig, pagerConfig, editRules, validConfig } = props;
        const { tablePage } = reactData;
        const isActiveMsg = computeIsActiveMsg.value;
        const isRespMsg = computeIsRespMsg.value;
        const proxyOpts = computeProxyOpts.value;
        const pagerOpts = computePagerOpts.value;
        const toolbarOpts = computeToolbarOpts.value;
        const { beforeQuery, afterQuery, beforeDelete, afterDelete, beforeSave, afterSave, ajax = {} } = proxyOpts;
        const resConfigs = proxyOpts.response || proxyOpts.props || {};
        const $xeTable = refTable.value;
        let formData = getFormData();
        let button = null;
        let code = null;
        if (import_xe_utils.default.isString(proxyTarget)) {
          const { buttons } = toolbarOpts;
          const matchObj = toolbarConfig && isEnableConf(toolbarOpts) && buttons ? import_xe_utils.default.findTree(buttons, (item) => item.code === proxyTarget, { children: "dropdowns" }) : null;
          button = matchObj ? matchObj.item : null;
          code = proxyTarget;
        } else {
          button = proxyTarget;
          code = button.code;
        }
        const btnParams = button ? button.params : null;
        switch (code) {
          case "insert":
            return $xeTable.insert({});
          case "insert_edit":
            return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row, true));
          // 已废弃
          case "insert_actived":
            return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row, true));
          // 已废弃
          case "mark_cancel":
            triggerPendingEvent(code);
            break;
          case "remove":
            return handleDeleteRow(code, "vxe.grid.removeSelectRecord", () => $xeTable.removeCheckboxRow());
          case "import":
            $xeTable.importData(btnParams);
            break;
          case "open_import":
            $xeTable.openImport(btnParams);
            break;
          case "export":
            $xeTable.exportData(btnParams);
            break;
          case "open_export":
            $xeTable.openExport(btnParams);
            break;
          case "reset_custom":
            return $xeTable.resetCustom(true);
          case "initial":
          case "reload":
          case "query": {
            const ajaxMethods = ajax.query;
            const querySuccessMethods = ajax.querySuccess;
            const queryErrorMethods = ajax.queryError;
            if (ajaxMethods) {
              const isInited = code === "initial";
              const isReload = code === "reload";
              if (!isInited && reactData.tableLoading) {
                return nextTick();
              }
              let sortList = [];
              let filterList = [];
              let pageParams = {};
              if (pagerConfig) {
                if (isInited || isReload) {
                  tablePage.currentPage = 1;
                }
                if (isEnableConf(pagerOpts)) {
                  pageParams = Object.assign({}, tablePage);
                }
              }
              if (isInited) {
                if (proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form) {
                  formData = getDefaultFormData();
                  reactData.formData = formData;
                }
                if ($xeTable) {
                  const tableInternalData = $xeTable.internalData;
                  const { tableFullColumn, fullColumnFieldData } = tableInternalData;
                  const { computeSortOpts } = $xeTable.getComputeMaps();
                  const sortOpts = computeSortOpts.value;
                  let defaultSort = sortOpts.defaultSort;
                  tableFullColumn.forEach((column) => {
                    column.order = null;
                  });
                  if (defaultSort) {
                    if (!import_xe_utils.default.isArray(defaultSort)) {
                      defaultSort = [defaultSort];
                    }
                    sortList = defaultSort.map((item) => {
                      const { field, order } = item;
                      const colRest = fullColumnFieldData[field];
                      if (colRest) {
                        const column = colRest.column;
                        if (column) {
                          column.order = order;
                        }
                      }
                      return {
                        field,
                        property: field,
                        order
                      };
                    });
                  }
                  filterList = $xeTable.getCheckedFilters();
                }
              } else {
                if ($xeTable) {
                  if (isReload) {
                    $xeTable.clearAll();
                  } else {
                    sortList = $xeTable.getSortColumns();
                    filterList = $xeTable.getCheckedFilters();
                  }
                }
              }
              const commitParams = {
                code,
                button,
                isInited,
                isReload,
                $grid: $xeGrid,
                page: pageParams,
                sort: sortList.length ? sortList[0] : {},
                sorts: sortList,
                filters: filterList,
                form: formData,
                options: ajaxMethods
              };
              reactData.sortData = sortList;
              reactData.filterData = filterList;
              reactData.tableLoading = true;
              return Promise.resolve((beforeQuery || ajaxMethods)(commitParams, ...args)).then((rest) => {
                let tableData = [];
                reactData.tableLoading = false;
                if (rest) {
                  if (pagerConfig && isEnableConf(pagerOpts)) {
                    const totalProp = resConfigs.total;
                    const total = (import_xe_utils.default.isFunction(totalProp) ? totalProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, totalProp || "page.total")) || 0;
                    tablePage.total = import_xe_utils.default.toNumber(total);
                    const resultProp = resConfigs.result;
                    tableData = (import_xe_utils.default.isFunction(resultProp) ? resultProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, resultProp || "result")) || [];
                    const pageCount = Math.max(Math.ceil(total / tablePage.pageSize), 1);
                    if (tablePage.currentPage > pageCount) {
                      tablePage.currentPage = pageCount;
                    }
                  } else {
                    const listProp = resConfigs.list;
                    tableData = (listProp ? import_xe_utils.default.isFunction(listProp) ? listProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, listProp) : rest) || [];
                  }
                }
                if ($xeTable) {
                  $xeTable.loadData(tableData);
                } else {
                  nextTick(() => {
                    if ($xeTable) {
                      $xeTable.loadData(tableData);
                    }
                  });
                }
                if (afterQuery) {
                  afterQuery(commitParams, ...args);
                }
                if (querySuccessMethods) {
                  querySuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                }
                return { status: true };
              }).catch((rest) => {
                reactData.tableLoading = false;
                if (queryErrorMethods) {
                  queryErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                }
                return { status: false };
              });
            } else {
              errLog("vxe.error.notFunc", ["proxy-config.ajax.query"]);
            }
            break;
          }
          case "delete": {
            const ajaxMethods = ajax.delete;
            const deleteSuccessMethods = ajax.deleteSuccess;
            const deleteErrorMethods = ajax.deleteError;
            if (ajaxMethods) {
              const selectRecords = gridExtendTableMethods.getCheckboxRecords();
              const removeRecords = selectRecords.filter((row) => !$xeTable.isInsertByRow(row));
              const body = { removeRecords };
              const commitParams = { $grid: $xeGrid, code, button, body, form: formData, options: ajaxMethods };
              if (selectRecords.length) {
                return handleDeleteRow(code, "vxe.grid.deleteSelectRecord", () => {
                  if (!removeRecords.length) {
                    return $xeTable.remove(selectRecords);
                  }
                  reactData.tableLoading = true;
                  return Promise.resolve((beforeDelete || ajaxMethods)(commitParams, ...args)).then((rest) => {
                    reactData.tableLoading = false;
                    $xeTable.setPendingRow(removeRecords, false);
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ content: getRespMsg(rest, "vxe.grid.delSuccess"), status: "success" });
                      }
                    }
                    if (afterDelete) {
                      afterDelete(commitParams, ...args);
                    } else {
                      gridMethods.commitProxy("query");
                    }
                    if (deleteSuccessMethods) {
                      deleteSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: true };
                  }).catch((rest) => {
                    reactData.tableLoading = false;
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ id: code, content: getRespMsg(rest, "vxe.grid.operError"), status: "error" });
                      }
                    }
                    if (deleteErrorMethods) {
                      deleteErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: false };
                  });
                });
              } else {
                if (isActiveMsg) {
                  if (VxeUI.modal) {
                    VxeUI.modal.message({ id: code, content: getI18n("vxe.grid.selectOneRecord"), status: "warning" });
                  }
                }
              }
            } else {
              errLog("vxe.error.notFunc", ["proxy-config.ajax.delete"]);
            }
            break;
          }
          case "save": {
            const ajaxMethods = ajax.save;
            const saveSuccessMethods = ajax.saveSuccess;
            const saveErrorMethods = ajax.saveError;
            if (ajaxMethods) {
              const body = $xeTable.getRecordset();
              const { insertRecords, removeRecords, updateRecords, pendingRecords } = body;
              const commitParams = { $grid: $xeGrid, code, button, body, form: formData, options: ajaxMethods };
              if (insertRecords.length) {
                body.pendingRecords = pendingRecords.filter((row) => $xeTable.findRowIndexOf(insertRecords, row) === -1);
              }
              if (pendingRecords.length) {
                body.insertRecords = insertRecords.filter((row) => $xeTable.findRowIndexOf(pendingRecords, row) === -1);
              }
              let restPromise = Promise.resolve();
              if (editRules) {
                restPromise = $xeTable[validConfig && validConfig.msgMode === "full" ? "fullValidate" : "validate"](body.insertRecords.concat(updateRecords));
              }
              return restPromise.then((errMap) => {
                if (errMap) {
                  return;
                }
                if (body.insertRecords.length || removeRecords.length || updateRecords.length || body.pendingRecords.length) {
                  reactData.tableLoading = true;
                  return Promise.resolve((beforeSave || ajaxMethods)(commitParams, ...args)).then((rest) => {
                    reactData.tableLoading = false;
                    $xeTable.clearPendingRow();
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ content: getRespMsg(rest, "vxe.grid.saveSuccess"), status: "success" });
                      }
                    }
                    if (afterSave) {
                      afterSave(commitParams, ...args);
                    } else {
                      gridMethods.commitProxy("query");
                    }
                    if (saveSuccessMethods) {
                      saveSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: true };
                  }).catch((rest) => {
                    reactData.tableLoading = false;
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ id: code, content: getRespMsg(rest, "vxe.grid.operError"), status: "error" });
                      }
                    }
                    if (saveErrorMethods) {
                      saveErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: false };
                  });
                } else {
                  if (isActiveMsg) {
                    if (VxeUI.modal) {
                      VxeUI.modal.message({ id: code, content: getI18n("vxe.grid.dataUnchanged"), status: "info" });
                    }
                  }
                }
              });
            } else {
              errLog("vxe.error.notFunc", ["proxy-config.ajax.save"]);
            }
            break;
          }
          default: {
            const gCommandOpts = commands.get(code);
            if (gCommandOpts) {
              const tCommandMethod = gCommandOpts.tableCommandMethod || gCommandOpts.commandMethod;
              if (tCommandMethod) {
                tCommandMethod({ code, button, $grid: $xeGrid, $table: $xeTable }, ...args);
              } else {
                errLog("vxe.error.notCommands", [code]);
              }
            }
          }
        }
        return nextTick();
      },
      zoom() {
        if (reactData.isZMax) {
          return gridMethods.revert();
        }
        return gridMethods.maximize();
      },
      isMaximized() {
        return reactData.isZMax;
      },
      maximize() {
        return handleZoom(true);
      },
      revert() {
        return handleZoom();
      },
      getFormData,
      getFormItems(itemIndex) {
        const formOpts = computeFormOpts.value;
        const { formConfig } = props;
        const { items } = formOpts;
        const itemList = [];
        import_xe_utils.default.eachTree(formConfig && isEnableConf(formOpts) && items ? items : [], (item) => {
          itemList.push(item);
        }, { children: "children" });
        return import_xe_utils.default.isUndefined(itemIndex) ? itemList : itemList[itemIndex];
      },
      getProxyInfo() {
        const $xeTable = refTable.value;
        if (props.proxyConfig) {
          const { sortData } = reactData;
          return {
            data: $xeTable ? $xeTable.getFullData() : [],
            filter: reactData.filterData,
            form: getFormData(),
            sort: sortData.length ? sortData[0] : {},
            sorts: sortData,
            pager: reactData.tablePage,
            pendingRecords: $xeTable ? $xeTable.getPendingRecords() : []
          };
        }
        return null;
      }
      // setProxyInfo (options) {
      //   if (props.proxyConfig && options) {
      //     const { pager, form } = options
      //     const proxyOpts = computeProxyOpts.value
      //     if (pager) {
      //       if (pager.currentPage) {
      //         reactData.tablePage.currentPage = Number(pager.currentPage)
      //       }
      //       if (pager.pageSize) {
      //         reactData.tablePage.pageSize = Number(pager.pageSize)
      //       }
      //     }
      //     if (proxyOpts.form && form) {
      //       Object.assign(reactData.formData, form)
      //     }
      //   }
      //   return nextTick()
      // }
    };
    const gridPrivateMethods = {
      extendTableMethods,
      callSlot(slotFunc, params) {
        if (slotFunc) {
          if (import_xe_utils.default.isString(slotFunc)) {
            slotFunc = slots[slotFunc] || null;
          }
          if (import_xe_utils.default.isFunction(slotFunc)) {
            return getSlotVNs(slotFunc(params));
          }
        }
        return [];
      },
      /**
       * 获取需要排除的高度
       */
      getExcludeHeight() {
        const { isZMax } = reactData;
        const el = refElem.value;
        if (el) {
          const formWrapper = refFormWrapper.value;
          const toolbarWrapper = refToolbarWrapper.value;
          const topWrapper = refTopWrapper.value;
          const bottomWrapper = refBottomWrapper.value;
          const pagerWrapper = refPagerWrapper.value;
          const parentEl = el.parentElement;
          const parentPaddingSize = isZMax ? 0 : parentEl ? getPaddingTopBottomSize(parentEl) : 0;
          return parentPaddingSize + getPaddingTopBottomSize(el) + getOffsetHeight(formWrapper) + getOffsetHeight(toolbarWrapper) + getOffsetHeight(topWrapper) + getOffsetHeight(bottomWrapper) + getOffsetHeight(pagerWrapper);
        }
        return 0;
      },
      getParentHeight() {
        const el = refElem.value;
        if (el) {
          const parentEl = el.parentElement;
          return (reactData.isZMax ? getDomNode().visibleHeight : parentEl ? import_xe_utils.default.toNumber(getComputedStyle(parentEl).height) : 0) - gridPrivateMethods.getExcludeHeight();
        }
        return 0;
      },
      triggerToolbarCommitEvent(params, evnt) {
        const { code } = params;
        return $xeGrid.commitProxy(params, evnt).then((rest) => {
          if (code && rest && rest.status && ["query", "reload", "delete", "save"].includes(code)) {
            $xeGrid.dispatchEvent(code === "delete" || code === "save" ? `proxy-${code}` : "proxy-query", Object.assign(Object.assign({}, rest), { isReload: code === "reload" }), evnt);
          }
        });
      },
      triggerToolbarBtnEvent(button, evnt) {
        $xeGrid.triggerToolbarCommitEvent(button, evnt);
        $xeGrid.dispatchEvent("toolbar-button-click", { code: button.code, button }, evnt);
      },
      triggerToolbarTolEvent(tool, evnt) {
        $xeGrid.triggerToolbarCommitEvent(tool, evnt);
        $xeGrid.dispatchEvent("toolbar-tool-click", { code: tool.code, tool }, evnt);
      },
      triggerZoomEvent(evnt) {
        $xeGrid.zoom();
        $xeGrid.dispatchEvent("zoom", { type: reactData.isZMax ? "max" : "revert" }, evnt);
      }
    };
    Object.assign($xeGrid, gridExtendTableMethods, gridMethods, gridPrivateMethods, {
      // 检查插槽
      loadColumn(columns) {
        const $xeTable = refTable.value;
        import_xe_utils.default.eachTree(columns, (column) => {
          if (column.slots) {
            import_xe_utils.default.each(column.slots, (func) => {
              if (!import_xe_utils.default.isFunction(func)) {
                if (!slots[func]) {
                  errLog("vxe.error.notSlot", [func]);
                }
              }
            });
          }
        });
        if ($xeTable) {
          return $xeTable.loadColumn(columns);
        }
        return nextTick();
      },
      reloadColumn(columns) {
        $xeGrid.clearAll();
        return $xeGrid.loadColumn(columns);
      }
    });
    const renderVN = () => {
      const vSize = computeSize.value;
      const styles = computeStyles.value;
      const isLoading = computeIsLoading.value;
      return h("div", {
        ref: refElem,
        class: ["vxe-grid", {
          [`size--${vSize}`]: vSize,
          "is--animat": !!props.animat,
          "is--round": props.round,
          "is--maximize": reactData.isZMax,
          "is--loading": isLoading
        }],
        style: styles
      }, renderLayout());
    };
    const columnFlag = ref(0);
    watch(() => props.columns ? props.columns.length : -1, () => {
      columnFlag.value++;
    });
    watch(() => props.columns, () => {
      columnFlag.value++;
    });
    watch(columnFlag, () => {
      nextTick(() => $xeGrid.loadColumn(props.columns || []));
    });
    watch(() => props.toolbarConfig, () => {
      initToolbar();
    });
    watch(computeCustomCurrentPageFlag, () => {
      initPages("currentPage");
    });
    watch(computeCustomPageSizeFlag, () => {
      initPages("pageSize");
    });
    watch(computeCustomTotalFlag, () => {
      initPages("total");
    });
    watch(() => props.proxyConfig, () => {
      initProxy();
    });
    hooks.forEach((options) => {
      const { setupGrid } = options;
      if (setupGrid) {
        const hookRest = setupGrid($xeGrid);
        if (hookRest && import_xe_utils.default.isObject(hookRest)) {
          Object.assign($xeGrid, hookRest);
        }
      }
    });
    initPages();
    onMounted(() => {
      nextTick(() => {
        const { columns } = props;
        const proxyOpts = computeProxyOpts.value;
        if (props.formConfig) {
          if (!VxeUIFormComponent) {
            errLog("vxe.error.reqComp", ["vxe-form"]);
          }
        }
        if (props.pagerConfig) {
          if (!VxeUIPagerComponent) {
            errLog("vxe.error.reqComp", ["vxe-pager"]);
          }
        }
        if (proxyOpts.props) {
          warnLog("vxe.error.delProp", ["proxy-config.props", "proxy-config.response"]);
        }
        if (columns && columns.length) {
          $xeGrid.loadColumn(columns);
        }
        initToolbar();
        initProxy();
      });
      globalEvents.on($xeGrid, "keydown", handleGlobalKeydownEvent);
    });
    onUnmounted(() => {
      globalEvents.off($xeGrid, "keydown");
    });
    $xeGrid.renderVN = renderVN;
    provide("$xeGrid", $xeGrid);
    return $xeGrid;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/grid/index.js
var VxeGrid = Object.assign({}, grid_default, {
  install(app) {
    app.component(grid_default.name, grid_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(grid_default.name, grid_default);
}
VxeUI.component(grid_default);
var Grid = VxeGrid;
var grid_default2 = VxeGrid;

// ../../node_modules/.pnpm/vxe-table@4.14.4_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-grid/index.js
var vxe_grid_default = grid_default2;
export {
  Grid,
  VxeGrid,
  vxe_grid_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-grid_index__js.js.map
