import { createJiti } from "../../../../../../node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/lib/jiti.mjs";

const jiti = createJiti(import.meta.url, {
  "interopDefault": true,
  "alias": {
    "@vben-core/shared": "C:/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/packages/@core/base/shared"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("C:/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/packages/@core/base/shared/src/cache/index.js")} */
const _module = await jiti.import("C:/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/packages/@core/base/shared/src/cache/index.ts");

export const StorageManager = _module.StorageManager;