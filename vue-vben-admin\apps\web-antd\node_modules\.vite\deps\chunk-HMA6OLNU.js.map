{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/auto-complete/Option.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/auto-complete/OptGroup.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/auto-complete/index.js"], "sourcesContent": ["const Option = () => null;\nOption.isSelectOption = true;\nOption.displayName = 'AAutoCompleteOption';\nexport default Option;", "const OptGroup = () => null;\nOptGroup.isSelectOptGroup = true;\nOptGroup.displayName = 'AAutoCompleteOptGroup';\nexport default OptGroup;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nimport { defineComponent, ref } from 'vue';\nimport Select, { selectProps } from '../select';\nimport { isValidElement, flattenChildren } from '../_util/props-util';\nimport warning from '../_util/warning';\nimport Option from './Option';\nimport OptGroup from './OptGroup';\nimport omit from '../_util/omit';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nfunction isSelectOptionOrSelectOptGroup(child) {\n  var _a, _b;\n  return ((_a = child === null || child === void 0 ? void 0 : child.type) === null || _a === void 0 ? void 0 : _a.isSelectOption) || ((_b = child === null || child === void 0 ? void 0 : child.type) === null || _b === void 0 ? void 0 : _b.isSelectOptGroup);\n}\nexport const autoCompleteProps = () => _extends(_extends({}, omit(selectProps(), ['loading', 'mode', 'optionLabelProp', 'labelInValue'])), {\n  dataSource: Array,\n  dropdownMenuStyle: {\n    type: Object,\n    default: undefined\n  },\n  // optionLabelProp: String,\n  dropdownMatchSelectWidth: {\n    type: [Number, Boolean],\n    default: true\n  },\n  prefixCls: String,\n  showSearch: {\n    type: Boolean,\n    default: undefined\n  },\n  transitionName: String,\n  choiceTransitionName: {\n    type: String,\n    default: 'zoom'\n  },\n  autofocus: {\n    type: Boolean,\n    default: undefined\n  },\n  backfill: {\n    type: Boolean,\n    default: undefined\n  },\n  // optionLabelProp: PropTypes.string.def('children'),\n  filterOption: {\n    type: [Boolean, Function],\n    default: false\n  },\n  defaultActiveFirstOption: {\n    type: Boolean,\n    default: true\n  },\n  status: String\n});\nexport const AutoCompleteOption = Option;\nexport const AutoCompleteOptGroup = OptGroup;\nconst AutoComplete = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AAutoComplete',\n  inheritAttrs: false,\n  props: autoCompleteProps(),\n  // emits: ['change', 'select', 'focus', 'blur'],\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs,\n      expose\n    } = _ref;\n    warning(!('dataSource' in slots), 'AutoComplete', '`dataSource` slot is deprecated, please use props `options` instead.');\n    warning(!('options' in slots), 'AutoComplete', '`options` slot is deprecated, please use props `options` instead.');\n    warning(!props.dropdownClassName, 'AutoComplete', '`dropdownClassName` is deprecated, please use `popupClassName` instead.');\n    const selectRef = ref();\n    const getInputElement = () => {\n      var _a;\n      const children = flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));\n      const element = children.length ? children[0] : undefined;\n      return element;\n    };\n    const focus = () => {\n      var _a;\n      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.blur();\n    };\n    expose({\n      focus,\n      blur\n    });\n    const {\n      prefixCls\n    } = useConfigInject('select', props);\n    return () => {\n      var _a, _b, _c;\n      const {\n        size,\n        dataSource,\n        notFoundContent = (_a = slots.notFoundContent) === null || _a === void 0 ? void 0 : _a.call(slots)\n      } = props;\n      let optionChildren;\n      const {\n        class: className\n      } = attrs;\n      const cls = {\n        [className]: !!className,\n        [`${prefixCls.value}-lg`]: size === 'large',\n        [`${prefixCls.value}-sm`]: size === 'small',\n        [`${prefixCls.value}-show-search`]: true,\n        [`${prefixCls.value}-auto-complete`]: true\n      };\n      if (props.options === undefined) {\n        const childArray = ((_b = slots.dataSource) === null || _b === void 0 ? void 0 : _b.call(slots)) || ((_c = slots.options) === null || _c === void 0 ? void 0 : _c.call(slots)) || [];\n        if (childArray.length && isSelectOptionOrSelectOptGroup(childArray[0])) {\n          optionChildren = childArray;\n        } else {\n          optionChildren = dataSource ? dataSource.map(item => {\n            if (isValidElement(item)) {\n              return item;\n            }\n            switch (typeof item) {\n              case 'string':\n                return _createVNode(Option, {\n                  \"key\": item,\n                  \"value\": item\n                }, {\n                  default: () => [item]\n                });\n              case 'object':\n                return _createVNode(Option, {\n                  \"key\": item.value,\n                  \"value\": item.value\n                }, {\n                  default: () => [item.text]\n                });\n              default:\n                throw new Error('AutoComplete[dataSource] only supports type `string[] | Object[]`.');\n            }\n          }) : [];\n        }\n      }\n      const selectProps = omit(_extends(_extends(_extends({}, props), attrs), {\n        mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n        // optionLabelProp,\n        getInputElement,\n        notFoundContent,\n        // placeholder: '',\n        class: cls,\n        popupClassName: props.popupClassName || props.dropdownClassName,\n        ref: selectRef\n      }), ['dataSource', 'loading']);\n      return _createVNode(Select, selectProps, _objectSpread({\n        default: () => [optionChildren]\n      }, omit(slots, ['default', 'dataSource', 'options'])));\n    };\n  }\n});\n/* istanbul ignore next */\nexport default _extends(AutoComplete, {\n  Option,\n  OptGroup,\n  install(app) {\n    app.component(AutoComplete.name, AutoComplete);\n    app.component(Option.displayName, Option);\n    app.component(OptGroup.displayName, OptGroup);\n    return app;\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,SAAS,MAAM;AACrB,OAAO,iBAAiB;AACxB,OAAO,cAAc;AACrB,IAAO,iBAAQ;;;ACHf,IAAM,WAAW,MAAM;AACvB,SAAS,mBAAmB;AAC5B,SAAS,cAAc;AACvB,IAAO,mBAAQ;;;ACQf,SAAS,+BAA+B,OAAO;AAC7C,MAAI,IAAI;AACR,WAAS,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9O;AACO,IAAM,oBAAoB,MAAM,SAAS,SAAS,CAAC,GAAG,aAAK,YAAY,GAAG,CAAC,WAAW,QAAQ,mBAAmB,cAAc,CAAC,CAAC,GAAG;AAAA,EACzI,YAAY;AAAA,EACZ,mBAAmB;AAAA,IACjB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,0BAA0B;AAAA,IACxB,MAAM,CAAC,QAAQ,OAAO;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,IACpB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,cAAc;AAAA,IACZ,MAAM,CAAC,SAAS,QAAQ;AAAA,IACxB,SAAS;AAAA,EACX;AAAA,EACA,0BAA0B;AAAA,IACxB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AACV,CAAC;AACM,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AACpC,IAAM,eAAe,gBAAgB;AAAA,EACnC,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,kBAAkB;AAAA;AAAA,EAEzB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,oBAAQ,EAAE,gBAAgB,QAAQ,gBAAgB,sEAAsE;AACxH,oBAAQ,EAAE,aAAa,QAAQ,gBAAgB,mEAAmE;AAClH,oBAAQ,CAAC,MAAM,mBAAmB,gBAAgB,yEAAyE;AAC3H,UAAM,YAAY,IAAI;AACtB,UAAM,kBAAkB,MAAM;AAC5B,UAAI;AACJ,YAAM,WAAW,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AACzG,YAAM,UAAU,SAAS,SAAS,SAAS,CAAC,IAAI;AAChD,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,MAAM;AAClB,UAAI;AACJ,OAAC,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACvE;AACA,UAAM,OAAO,MAAM;AACjB,UAAI;AACJ,OAAC,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,IACtE;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,wBAAgB,UAAU,KAAK;AACnC,WAAO,MAAM;AACX,UAAI,IAAI,IAAI;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,mBAAmB,KAAK,MAAM,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,MACnG,IAAI;AACJ,UAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,MACT,IAAI;AACJ,YAAM,MAAM;AAAA,QACV,CAAC,SAAS,GAAG,CAAC,CAAC;AAAA,QACf,CAAC,GAAG,UAAU,KAAK,KAAK,GAAG,SAAS;AAAA,QACpC,CAAC,GAAG,UAAU,KAAK,KAAK,GAAG,SAAS;AAAA,QACpC,CAAC,GAAG,UAAU,KAAK,cAAc,GAAG;AAAA,QACpC,CAAC,GAAG,UAAU,KAAK,gBAAgB,GAAG;AAAA,MACxC;AACA,UAAI,MAAM,YAAY,QAAW;AAC/B,cAAM,eAAe,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,QAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,MAAM,CAAC;AACnL,YAAI,WAAW,UAAU,+BAA+B,WAAW,CAAC,CAAC,GAAG;AACtE,2BAAiB;AAAA,QACnB,OAAO;AACL,2BAAiB,aAAa,WAAW,IAAI,UAAQ;AACnD,gBAAI,eAAe,IAAI,GAAG;AACxB,qBAAO;AAAA,YACT;AACA,oBAAQ,OAAO,MAAM;AAAA,cACnB,KAAK;AACH,uBAAO,YAAa,gBAAQ;AAAA,kBAC1B,OAAO;AAAA,kBACP,SAAS;AAAA,gBACX,GAAG;AAAA,kBACD,SAAS,MAAM,CAAC,IAAI;AAAA,gBACtB,CAAC;AAAA,cACH,KAAK;AACH,uBAAO,YAAa,gBAAQ;AAAA,kBAC1B,OAAO,KAAK;AAAA,kBACZ,SAAS,KAAK;AAAA,gBAChB,GAAG;AAAA,kBACD,SAAS,MAAM,CAAC,KAAK,IAAI;AAAA,gBAC3B,CAAC;AAAA,cACH;AACE,sBAAM,IAAI,MAAM,oEAAoE;AAAA,YACxF;AAAA,UACF,CAAC,IAAI,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAMA,eAAc,aAAK,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG;AAAA,QACtE,MAAM,eAAO;AAAA;AAAA,QAEb;AAAA,QACA;AAAA;AAAA,QAEA,OAAO;AAAA,QACP,gBAAgB,MAAM,kBAAkB,MAAM;AAAA,QAC9C,KAAK;AAAA,MACP,CAAC,GAAG,CAAC,cAAc,SAAS,CAAC;AAC7B,aAAO,YAAa,gBAAQA,cAAa,eAAc;AAAA,QACrD,SAAS,MAAM,CAAC,cAAc;AAAA,MAChC,GAAG,aAAK,OAAO,CAAC,WAAW,cAAc,SAAS,CAAC,CAAC,CAAC;AAAA,IACvD;AAAA,EACF;AACF,CAAC;AAED,IAAO,wBAAQ,SAAS,cAAc;AAAA,EACpC;AAAA,EACA;AAAA,EACA,QAAQ,KAAK;AACX,QAAI,UAAU,aAAa,MAAM,YAAY;AAC7C,QAAI,UAAU,eAAO,aAAa,cAAM;AACxC,QAAI,UAAU,iBAAS,aAAa,gBAAQ;AAC5C,WAAO;AAAA,EACT;AACF,CAAC;", "names": ["selectProps"]}