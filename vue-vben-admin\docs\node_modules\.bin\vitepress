#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_ad674ef88d58bdb2716d55dc92f3e305/node_modules/vitepress/bin/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_ad674ef88d58bdb2716d55dc92f3e305/node_modules/vitepress/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_ad674ef88d58bdb2716d55dc92f3e305/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_ad674ef88d58bdb2716d55dc92f3e305/node_modules/vitepress/bin/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_ad674ef88d58bdb2716d55dc92f3e305/node_modules/vitepress/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/vitepress@1.6.3_@algolia+cl_ad674ef88d58bdb2716d55dc92f3e305/node_modules:/mnt/c/Users/<USER>/Downloads/Acumatica Dev/Cluade/Envent Bridge New/vue-vben-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitepress/bin/vitepress.js" "$@"
else
  exec node  "$basedir/../vitepress/bin/vitepress.js" "$@"
fi
