import {
  collapseMotion_default
} from "./chunk-4BUGUT7V.js";
import {
  BaseSelect_default,
  baseSelectPropsWithoutPrivate,
  getIcons,
  style_default,
  toReactive,
  useBaseProps,
  useId,
  useInjectLegacySelectContext,
  useProvideLegacySelectContext,
  vc_virtual_list_default
} from "./chunk-NSNPNVH5.js";
import {
  pickAttrs
} from "./chunk-Q3M5V53Y.js";
import {
  getStyle
} from "./chunk-TLDPKQMG.js";
import {
  useMemo
} from "./chunk-5JYKKRBT.js";
import {
  useMergedState
} from "./chunk-J7YJVBQ7.js";
import {
  collapse_default,
  getTransitionDirection
} from "./chunk-W3AWYJHA.js";
import {
  LoadingOutlined_default
} from "./chunk-W2PLHJK2.js";
import {
  KeyCode_default
} from "./chunk-LSZPS4UL.js";
import {
  getMergedStatus,
  getStatusClassNames
} from "./chunk-ULZHSRS6.js";
import {
  omit_default
} from "./chunk-S44RY227.js";
import {
  useCompactItemContext
} from "./chunk-Y2MDLMWE.js";
import {
  AntdIcon_default
} from "./chunk-HCTNNGFQ.js";
import {
  FormItemInputContext,
  useInjectFormItemContext
} from "./chunk-QZA3HDZG.js";
import {
  devWarning_default
} from "./chunk-TUEPXVUS.js";
import {
  vue_types_default
} from "./chunk-S333D6IW.js";
import {
  Keyframes_default,
  _objectSpread2,
  booleanType,
  camelize,
  classNames_default,
  filterEmpty,
  flattenChildren,
  functionType,
  genComponentStyleHook,
  genFocusOutline,
  initDefaultProps_default,
  isValidElement,
  merge,
  note,
  objectType,
  resetComponent,
  someType,
  stringType,
  useConfigInject_default,
  useInjectDisabled,
  warning
} from "./chunk-KVIU2RTF.js";
import {
  _extends
} from "./chunk-RVLAGWQP.js";
import {
  Fragment,
  Transition,
  cloneVNode,
  computed,
  createVNode,
  defineComponent,
  getCurrentInstance,
  inject,
  nextTick,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  onUpdated,
  provide,
  reactive,
  ref,
  shallowRef,
  toRaw,
  toRef,
  toRefs,
  vShow,
  watch,
  watchEffect,
  withDirectives
} from "./chunk-ZCM5A7SR.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/contextTypes.js
var TreeContextKey = Symbol("TreeContextKey");
var TreeContext = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "TreeContext",
  props: {
    value: {
      type: Object
    }
  },
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    provide(TreeContextKey, computed(() => props.value));
    return () => {
      var _a;
      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);
    };
  }
});
var useInjectTreeContext = () => {
  return inject(TreeContextKey, computed(() => ({})));
};
var KeysStateKey = Symbol("KeysStateKey");
var useProvideKeysState = (state) => {
  provide(KeysStateKey, state);
};
var useInjectKeysState = () => {
  return inject(KeysStateKey, {
    expandedKeys: shallowRef([]),
    selectedKeys: shallowRef([]),
    loadedKeys: shallowRef([]),
    loadingKeys: shallowRef([]),
    checkedKeys: shallowRef([]),
    halfCheckedKeys: shallowRef([]),
    expandedKeysSet: computed(() => /* @__PURE__ */ new Set()),
    selectedKeysSet: computed(() => /* @__PURE__ */ new Set()),
    loadedKeysSet: computed(() => /* @__PURE__ */ new Set()),
    loadingKeysSet: computed(() => /* @__PURE__ */ new Set()),
    checkedKeysSet: computed(() => /* @__PURE__ */ new Set()),
    halfCheckedKeysSet: computed(() => /* @__PURE__ */ new Set()),
    flattenNodes: shallowRef([])
  });
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/Indent.js
var Indent = (_ref) => {
  let {
    prefixCls,
    level,
    isStart,
    isEnd
  } = _ref;
  const baseClassName = `${prefixCls}-indent-unit`;
  const list = [];
  for (let i = 0; i < level; i += 1) {
    list.push(createVNode("span", {
      "key": i,
      "class": {
        [baseClassName]: true,
        [`${baseClassName}-start`]: isStart[i],
        [`${baseClassName}-end`]: isEnd[i]
      }
    }, null));
  }
  return createVNode("span", {
    "aria-hidden": "true",
    "class": `${prefixCls}-indent`
  }, [list]);
};
var Indent_default = Indent;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/utils/treeUtil.js
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
function getKey(key, pos) {
  if (key !== null && key !== void 0) {
    return key;
  }
  return pos;
}
function fillFieldNames(fieldNames) {
  const {
    title,
    _title,
    key,
    children
  } = fieldNames || {};
  const mergedTitle = title || "title";
  return {
    title: mergedTitle,
    _title: _title || [mergedTitle],
    key: key || "key",
    children: children || "children"
  };
}
function convertTreeToData(rootNodes) {
  function dig() {
    let node = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
    const treeNodes = filterEmpty(node);
    return treeNodes.map((treeNode) => {
      var _a, _b, _c, _d;
      if (!isTreeNode(treeNode)) {
        warning(!treeNode, "Tree/TreeNode can only accept TreeNode as children.");
        return null;
      }
      const slots = treeNode.children || {};
      const key = treeNode.key;
      const props = {};
      for (const [k, v] of Object.entries(treeNode.props)) {
        props[camelize(k)] = v;
      }
      const {
        isLeaf,
        checkable,
        selectable,
        disabled,
        disableCheckbox
      } = props;
      const newProps = {
        isLeaf: isLeaf || isLeaf === "" || void 0,
        checkable: checkable || checkable === "" || void 0,
        selectable: selectable || selectable === "" || void 0,
        disabled: disabled || disabled === "" || void 0,
        disableCheckbox: disableCheckbox || disableCheckbox === "" || void 0
      };
      const slotsProps = _extends(_extends({}, props), newProps);
      const {
        title = (_a = slots.title) === null || _a === void 0 ? void 0 : _a.call(slots, slotsProps),
        icon = (_b = slots.icon) === null || _b === void 0 ? void 0 : _b.call(slots, slotsProps),
        switcherIcon = (_c = slots.switcherIcon) === null || _c === void 0 ? void 0 : _c.call(slots, slotsProps)
      } = props, rest = __rest(props, ["title", "icon", "switcherIcon"]);
      const children = (_d = slots.default) === null || _d === void 0 ? void 0 : _d.call(slots);
      const dataNode = _extends(_extends(_extends({}, rest), {
        title,
        icon,
        switcherIcon,
        key,
        isLeaf
      }), newProps);
      const parsedChildren = dig(children);
      if (parsedChildren.length) {
        dataNode.children = parsedChildren;
      }
      return dataNode;
    });
  }
  return dig(rootNodes);
}
function flattenTreeData(treeNodeList, expandedKeys, fieldNames) {
  const {
    _title: fieldTitles,
    key: fieldKey,
    children: fieldChildren
  } = fillFieldNames(fieldNames);
  const expandedKeySet = new Set(expandedKeys === true ? [] : expandedKeys);
  const flattenList = [];
  function dig(list) {
    let parent = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;
    return list.map((treeNode, index) => {
      const pos = getPosition(parent ? parent.pos : "0", index);
      const mergedKey = getKey(treeNode[fieldKey], pos);
      let mergedTitle;
      for (let i = 0; i < fieldTitles.length; i += 1) {
        const fieldTitle = fieldTitles[i];
        if (treeNode[fieldTitle] !== void 0) {
          mergedTitle = treeNode[fieldTitle];
          break;
        }
      }
      const flattenNode = _extends(_extends({}, omit_default(treeNode, [...fieldTitles, fieldKey, fieldChildren])), {
        title: mergedTitle,
        key: mergedKey,
        parent,
        pos,
        children: null,
        data: treeNode,
        isStart: [...parent ? parent.isStart : [], index === 0],
        isEnd: [...parent ? parent.isEnd : [], index === list.length - 1]
      });
      flattenList.push(flattenNode);
      if (expandedKeys === true || expandedKeySet.has(mergedKey)) {
        flattenNode.children = dig(treeNode[fieldChildren] || [], flattenNode);
      } else {
        flattenNode.children = [];
      }
      return flattenNode;
    });
  }
  dig(treeNodeList);
  return flattenList;
}
function traverseDataNodes(dataNodes, callback, config) {
  let mergedConfig = {};
  if (typeof config === "object") {
    mergedConfig = config;
  } else {
    mergedConfig = {
      externalGetKey: config
    };
  }
  mergedConfig = mergedConfig || {};
  const {
    childrenPropName,
    externalGetKey,
    fieldNames
  } = mergedConfig;
  const {
    key: fieldKey,
    children: fieldChildren
  } = fillFieldNames(fieldNames);
  const mergeChildrenPropName = childrenPropName || fieldChildren;
  let syntheticGetKey;
  if (externalGetKey) {
    if (typeof externalGetKey === "string") {
      syntheticGetKey = (node) => node[externalGetKey];
    } else if (typeof externalGetKey === "function") {
      syntheticGetKey = (node) => externalGetKey(node);
    }
  } else {
    syntheticGetKey = (node, pos) => getKey(node[fieldKey], pos);
  }
  function processNode(node, index, parent, pathNodes) {
    const children = node ? node[mergeChildrenPropName] : dataNodes;
    const pos = node ? getPosition(parent.pos, index) : "0";
    const connectNodes = node ? [...pathNodes, node] : [];
    if (node) {
      const key = syntheticGetKey(node, pos);
      const data = {
        node,
        index,
        pos,
        key,
        parentPos: parent.node ? parent.pos : null,
        level: parent.level + 1,
        nodes: connectNodes
      };
      callback(data);
    }
    if (children) {
      children.forEach((subNode, subIndex) => {
        processNode(subNode, subIndex, {
          node,
          pos,
          level: parent ? parent.level + 1 : -1
        }, connectNodes);
      });
    }
  }
  processNode(null);
}
function convertDataToEntities(dataNodes) {
  let {
    initWrapper,
    processEntity,
    onProcessFinished,
    externalGetKey,
    childrenPropName,
    fieldNames
  } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  let legacyExternalGetKey = arguments.length > 2 ? arguments[2] : void 0;
  const mergedExternalGetKey = externalGetKey || legacyExternalGetKey;
  const posEntities = {};
  const keyEntities = {};
  let wrapper = {
    posEntities,
    keyEntities
  };
  if (initWrapper) {
    wrapper = initWrapper(wrapper) || wrapper;
  }
  traverseDataNodes(dataNodes, (item) => {
    const {
      node,
      index,
      pos,
      key,
      parentPos,
      level,
      nodes
    } = item;
    const entity = {
      node,
      nodes,
      index,
      key,
      pos,
      level
    };
    const mergedKey = getKey(key, pos);
    posEntities[pos] = entity;
    keyEntities[mergedKey] = entity;
    entity.parent = posEntities[parentPos];
    if (entity.parent) {
      entity.parent.children = entity.parent.children || [];
      entity.parent.children.push(entity);
    }
    if (processEntity) {
      processEntity(entity, wrapper);
    }
  }, {
    externalGetKey: mergedExternalGetKey,
    childrenPropName,
    fieldNames
  });
  if (onProcessFinished) {
    onProcessFinished(wrapper);
  }
  return wrapper;
}
function getTreeNodeProps(key, _ref) {
  let {
    expandedKeysSet,
    selectedKeysSet,
    loadedKeysSet,
    loadingKeysSet,
    checkedKeysSet,
    halfCheckedKeysSet,
    dragOverNodeKey,
    dropPosition,
    keyEntities
  } = _ref;
  const entity = keyEntities[key];
  const treeNodeProps2 = {
    eventKey: key,
    expanded: expandedKeysSet.has(key),
    selected: selectedKeysSet.has(key),
    loaded: loadedKeysSet.has(key),
    loading: loadingKeysSet.has(key),
    checked: checkedKeysSet.has(key),
    halfChecked: halfCheckedKeysSet.has(key),
    pos: String(entity ? entity.pos : ""),
    parent: entity.parent,
    // [Legacy] Drag props
    // Since the interaction of drag is changed, the semantic of the props are
    // not accuracy, I think it should be finally removed
    dragOver: dragOverNodeKey === key && dropPosition === 0,
    dragOverGapTop: dragOverNodeKey === key && dropPosition === -1,
    dragOverGapBottom: dragOverNodeKey === key && dropPosition === 1
  };
  return treeNodeProps2;
}
function convertNodePropsToEventData(props) {
  const {
    data,
    expanded,
    selected,
    checked,
    loaded,
    loading,
    halfChecked,
    dragOver,
    dragOverGapTop,
    dragOverGapBottom,
    pos,
    active,
    eventKey
  } = props;
  const eventData = _extends(_extends({
    dataRef: data
  }, data), {
    expanded,
    selected,
    checked,
    loaded,
    loading,
    halfChecked,
    dragOver,
    dragOverGapTop,
    dragOverGapBottom,
    pos,
    active,
    eventKey,
    key: eventKey
  });
  if (!("props" in eventData)) {
    Object.defineProperty(eventData, "props", {
      get() {
        warning(false, "Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`.");
        return props;
      }
    });
  }
  return eventData;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/props.js
var treeNodeProps = {
  eventKey: [String, Number],
  prefixCls: String,
  // By parent
  // expanded: { type: Boolean, default: undefined },
  // selected: { type: Boolean, default: undefined },
  // checked: { type: Boolean, default: undefined },
  // loaded: { type: Boolean, default: undefined },
  // loading: { type: Boolean, default: undefined },
  // halfChecked: { type: Boolean, default: undefined },
  // dragOver: { type: Boolean, default: undefined },
  // dragOverGapTop: { type: Boolean, default: undefined },
  // dragOverGapBottom: { type: Boolean, default: undefined },
  // pos: String,
  title: vue_types_default.any,
  /** New added in Tree for easy data access */
  data: {
    type: Object,
    default: void 0
  },
  parent: {
    type: Object,
    default: void 0
  },
  isStart: {
    type: Array
  },
  isEnd: {
    type: Array
  },
  active: {
    type: Boolean,
    default: void 0
  },
  onMousemove: {
    type: Function
  },
  // By user
  isLeaf: {
    type: Boolean,
    default: void 0
  },
  checkable: {
    type: Boolean,
    default: void 0
  },
  selectable: {
    type: Boolean,
    default: void 0
  },
  disabled: {
    type: Boolean,
    default: void 0
  },
  disableCheckbox: {
    type: Boolean,
    default: void 0
  },
  icon: vue_types_default.any,
  switcherIcon: vue_types_default.any,
  domRef: {
    type: Function
  }
};
var nodeListProps = {
  prefixCls: {
    type: String
  },
  // data: { type: Array as PropType<FlattenNode[]> },
  motion: {
    type: Object
  },
  focusable: {
    type: Boolean
  },
  activeItem: {
    type: Object
  },
  focused: {
    type: Boolean
  },
  tabindex: {
    type: Number
  },
  checkable: {
    type: Boolean
  },
  selectable: {
    type: Boolean
  },
  disabled: {
    type: Boolean
  },
  // expandedKeys: { type: Array as PropType<Key[]> },
  // selectedKeys: { type: Array as PropType<Key[]> },
  // checkedKeys: { type: Array as PropType<Key[]> },
  // loadedKeys: { type: Array as PropType<Key[]> },
  // loadingKeys: { type: Array as PropType<Key[]> },
  // halfCheckedKeys: { type: Array as PropType<Key[]> },
  // keyEntities: { type: Object as PropType<Record<Key, DataEntity<DataNode>>> },
  // dragging: { type: Boolean as PropType<boolean> },
  // dragOverNodeKey: { type: [String, Number] as PropType<Key> },
  // dropPosition: { type: Number as PropType<number> },
  // Virtual list
  height: {
    type: Number
  },
  itemHeight: {
    type: Number
  },
  virtual: {
    type: Boolean
  },
  onScroll: {
    type: Function
  },
  onKeydown: {
    type: Function
  },
  onFocus: {
    type: Function
  },
  onBlur: {
    type: Function
  },
  onActiveChange: {
    type: Function
  },
  onContextmenu: {
    type: Function
  },
  onListChangeStart: {
    type: Function
  },
  onListChangeEnd: {
    type: Function
  }
};
var treeProps = () => ({
  prefixCls: String,
  focusable: {
    type: Boolean,
    default: void 0
  },
  activeKey: [Number, String],
  tabindex: Number,
  children: vue_types_default.any,
  treeData: {
    type: Array
  },
  fieldNames: {
    type: Object
  },
  showLine: {
    type: [Boolean, Object],
    default: void 0
  },
  showIcon: {
    type: Boolean,
    default: void 0
  },
  icon: vue_types_default.any,
  selectable: {
    type: Boolean,
    default: void 0
  },
  expandAction: [String, Boolean],
  disabled: {
    type: Boolean,
    default: void 0
  },
  multiple: {
    type: Boolean,
    default: void 0
  },
  checkable: {
    type: Boolean,
    default: void 0
  },
  checkStrictly: {
    type: Boolean,
    default: void 0
  },
  draggable: {
    type: [Function, Boolean]
  },
  defaultExpandParent: {
    type: Boolean,
    default: void 0
  },
  autoExpandParent: {
    type: Boolean,
    default: void 0
  },
  defaultExpandAll: {
    type: Boolean,
    default: void 0
  },
  defaultExpandedKeys: {
    type: Array
  },
  expandedKeys: {
    type: Array
  },
  defaultCheckedKeys: {
    type: Array
  },
  checkedKeys: {
    type: [Object, Array]
  },
  defaultSelectedKeys: {
    type: Array
  },
  selectedKeys: {
    type: Array
  },
  allowDrop: {
    type: Function
  },
  dropIndicatorRender: {
    type: Function
  },
  onFocus: {
    type: Function
  },
  onBlur: {
    type: Function
  },
  onKeydown: {
    type: Function
  },
  onContextmenu: {
    type: Function
  },
  onClick: {
    type: Function
  },
  onDblclick: {
    type: Function
  },
  onScroll: {
    type: Function
  },
  onExpand: {
    type: Function
  },
  onCheck: {
    type: Function
  },
  onSelect: {
    type: Function
  },
  onLoad: {
    type: Function
  },
  loadData: {
    type: Function
  },
  loadedKeys: {
    type: Array
  },
  onMouseenter: {
    type: Function
  },
  onMouseleave: {
    type: Function
  },
  onRightClick: {
    type: Function
  },
  onDragstart: {
    type: Function
  },
  onDragenter: {
    type: Function
  },
  onDragover: {
    type: Function
  },
  onDragleave: {
    type: Function
  },
  onDragend: {
    type: Function
  },
  onDrop: {
    type: Function
  },
  /**
   * Used for `rc-tree-select` only.
   * Do not use in your production code directly since this will be refactor.
   */
  onActiveChange: {
    type: Function
  },
  filterTreeNode: {
    type: Function
  },
  motion: vue_types_default.any,
  switcherIcon: vue_types_default.any,
  // Virtual List
  height: Number,
  itemHeight: Number,
  virtual: {
    type: Boolean,
    default: void 0
  },
  // direction for drag logic
  direction: {
    type: String
  },
  rootClassName: String,
  rootStyle: Object
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/eagerComputed.js
function eagerComputed(fn) {
  const result = shallowRef();
  watchEffect(() => {
    result.value = fn();
  }, {
    flush: "sync"
    // needed so updates are immediate.
  });
  return result;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/TreeNode.js
var __rest2 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var ICON_OPEN = "open";
var ICON_CLOSE = "close";
var defaultTitle = "---";
var TreeNode_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ATreeNode",
  inheritAttrs: false,
  props: treeNodeProps,
  isTreeNode: 1,
  setup(props, _ref) {
    let {
      attrs,
      slots,
      expose
    } = _ref;
    warning(!("slots" in props.data), `treeData slots is deprecated, please use ${Object.keys(props.data.slots || {}).map((key) => "`v-slot:" + key + "` ")}instead`);
    const dragNodeHighlight = shallowRef(false);
    const context = useInjectTreeContext();
    const {
      expandedKeysSet,
      selectedKeysSet,
      loadedKeysSet,
      loadingKeysSet,
      checkedKeysSet,
      halfCheckedKeysSet
    } = useInjectKeysState();
    const {
      dragOverNodeKey,
      dropPosition,
      keyEntities
    } = context.value;
    const mergedTreeNodeProps = computed(() => {
      return getTreeNodeProps(props.eventKey, {
        expandedKeysSet: expandedKeysSet.value,
        selectedKeysSet: selectedKeysSet.value,
        loadedKeysSet: loadedKeysSet.value,
        loadingKeysSet: loadingKeysSet.value,
        checkedKeysSet: checkedKeysSet.value,
        halfCheckedKeysSet: halfCheckedKeysSet.value,
        dragOverNodeKey,
        dropPosition,
        keyEntities
      });
    });
    const expanded = eagerComputed(() => mergedTreeNodeProps.value.expanded);
    const selected = eagerComputed(() => mergedTreeNodeProps.value.selected);
    const checked = eagerComputed(() => mergedTreeNodeProps.value.checked);
    const loaded = eagerComputed(() => mergedTreeNodeProps.value.loaded);
    const loading = eagerComputed(() => mergedTreeNodeProps.value.loading);
    const halfChecked = eagerComputed(() => mergedTreeNodeProps.value.halfChecked);
    const dragOver = eagerComputed(() => mergedTreeNodeProps.value.dragOver);
    const dragOverGapTop = eagerComputed(() => mergedTreeNodeProps.value.dragOverGapTop);
    const dragOverGapBottom = eagerComputed(() => mergedTreeNodeProps.value.dragOverGapBottom);
    const pos = eagerComputed(() => mergedTreeNodeProps.value.pos);
    const selectHandle = shallowRef();
    const hasChildren = computed(() => {
      const {
        eventKey
      } = props;
      const {
        keyEntities: keyEntities2
      } = context.value;
      const {
        children
      } = keyEntities2[eventKey] || {};
      return !!(children || []).length;
    });
    const isLeaf = computed(() => {
      const {
        isLeaf: isLeaf2
      } = props;
      const {
        loadData
      } = context.value;
      const has = hasChildren.value;
      if (isLeaf2 === false) {
        return false;
      }
      return isLeaf2 || !loadData && !has || loadData && loaded.value && !has;
    });
    const nodeState = computed(() => {
      if (isLeaf.value) {
        return null;
      }
      return expanded.value ? ICON_OPEN : ICON_CLOSE;
    });
    const isDisabled = computed(() => {
      const {
        disabled
      } = props;
      const {
        disabled: treeDisabled
      } = context.value;
      return !!(treeDisabled || disabled);
    });
    const isCheckable = computed(() => {
      const {
        checkable
      } = props;
      const {
        checkable: treeCheckable
      } = context.value;
      if (!treeCheckable || checkable === false) return false;
      return treeCheckable;
    });
    const isSelectable = computed(() => {
      const {
        selectable
      } = props;
      const {
        selectable: treeSelectable
      } = context.value;
      if (typeof selectable === "boolean") {
        return selectable;
      }
      return treeSelectable;
    });
    const renderArgsData = computed(() => {
      const {
        data,
        active,
        checkable,
        disableCheckbox,
        disabled,
        selectable
      } = props;
      return _extends(_extends({
        active,
        checkable,
        disableCheckbox,
        disabled,
        selectable
      }, data), {
        dataRef: data,
        data,
        isLeaf: isLeaf.value,
        checked: checked.value,
        expanded: expanded.value,
        loading: loading.value,
        selected: selected.value,
        halfChecked: halfChecked.value
      });
    });
    const instance = getCurrentInstance();
    const eventData = computed(() => {
      const {
        eventKey
      } = props;
      const {
        keyEntities: keyEntities2
      } = context.value;
      const {
        parent
      } = keyEntities2[eventKey] || {};
      return _extends(_extends({}, convertNodePropsToEventData(_extends({}, props, mergedTreeNodeProps.value))), {
        parent
      });
    });
    const dragNodeEvent = reactive({
      eventData,
      eventKey: computed(() => props.eventKey),
      selectHandle,
      pos,
      key: instance.vnode.key
    });
    expose(dragNodeEvent);
    const onSelectorDoubleClick = (e) => {
      const {
        onNodeDoubleClick
      } = context.value;
      onNodeDoubleClick(e, eventData.value);
    };
    const onSelect = (e) => {
      if (isDisabled.value) return;
      const {
        onNodeSelect
      } = context.value;
      e.preventDefault();
      onNodeSelect(e, eventData.value);
    };
    const onCheck = (e) => {
      if (isDisabled.value) return;
      const {
        disableCheckbox
      } = props;
      const {
        onNodeCheck
      } = context.value;
      if (!isCheckable.value || disableCheckbox) return;
      e.preventDefault();
      const targetChecked = !checked.value;
      onNodeCheck(e, eventData.value, targetChecked);
    };
    const onSelectorClick = (e) => {
      const {
        onNodeClick
      } = context.value;
      onNodeClick(e, eventData.value);
      if (isSelectable.value) {
        onSelect(e);
      } else {
        onCheck(e);
      }
    };
    const onMouseEnter = (e) => {
      const {
        onNodeMouseEnter
      } = context.value;
      onNodeMouseEnter(e, eventData.value);
    };
    const onMouseLeave = (e) => {
      const {
        onNodeMouseLeave
      } = context.value;
      onNodeMouseLeave(e, eventData.value);
    };
    const onContextmenu = (e) => {
      const {
        onNodeContextMenu
      } = context.value;
      onNodeContextMenu(e, eventData.value);
    };
    const onDragStart = (e) => {
      const {
        onNodeDragStart
      } = context.value;
      e.stopPropagation();
      dragNodeHighlight.value = true;
      onNodeDragStart(e, dragNodeEvent);
      try {
        e.dataTransfer.setData("text/plain", "");
      } catch (error) {
      }
    };
    const onDragEnter = (e) => {
      const {
        onNodeDragEnter
      } = context.value;
      e.preventDefault();
      e.stopPropagation();
      onNodeDragEnter(e, dragNodeEvent);
    };
    const onDragOver = (e) => {
      const {
        onNodeDragOver
      } = context.value;
      e.preventDefault();
      e.stopPropagation();
      onNodeDragOver(e, dragNodeEvent);
    };
    const onDragLeave = (e) => {
      const {
        onNodeDragLeave
      } = context.value;
      e.stopPropagation();
      onNodeDragLeave(e, dragNodeEvent);
    };
    const onDragEnd = (e) => {
      const {
        onNodeDragEnd
      } = context.value;
      e.stopPropagation();
      dragNodeHighlight.value = false;
      onNodeDragEnd(e, dragNodeEvent);
    };
    const onDrop = (e) => {
      const {
        onNodeDrop
      } = context.value;
      e.preventDefault();
      e.stopPropagation();
      dragNodeHighlight.value = false;
      onNodeDrop(e, dragNodeEvent);
    };
    const onExpand = (e) => {
      const {
        onNodeExpand
      } = context.value;
      if (loading.value) return;
      onNodeExpand(e, eventData.value);
    };
    const isDraggable = () => {
      const {
        data
      } = props;
      const {
        draggable
      } = context.value;
      return !!(draggable && (!draggable.nodeDraggable || draggable.nodeDraggable(data)));
    };
    const renderDragHandler = () => {
      const {
        draggable,
        prefixCls
      } = context.value;
      return draggable && (draggable === null || draggable === void 0 ? void 0 : draggable.icon) ? createVNode("span", {
        "class": `${prefixCls}-draggable-icon`
      }, [draggable.icon]) : null;
    };
    const renderSwitcherIconDom = () => {
      var _a, _b, _c;
      const {
        switcherIcon: switcherIconFromProps = slots.switcherIcon || ((_a = context.value.slots) === null || _a === void 0 ? void 0 : _a[(_c = (_b = props.data) === null || _b === void 0 ? void 0 : _b.slots) === null || _c === void 0 ? void 0 : _c.switcherIcon])
      } = props;
      const {
        switcherIcon: switcherIconFromCtx
      } = context.value;
      const switcherIcon = switcherIconFromProps || switcherIconFromCtx;
      if (typeof switcherIcon === "function") {
        return switcherIcon(renderArgsData.value);
      }
      return switcherIcon;
    };
    const syncLoadData = () => {
      const {
        loadData,
        onNodeLoad
      } = context.value;
      if (loading.value) {
        return;
      }
      if (loadData && expanded.value && !isLeaf.value) {
        if (!hasChildren.value && !loaded.value) {
          onNodeLoad(eventData.value);
        }
      }
    };
    onMounted(() => {
      syncLoadData();
    });
    onUpdated(() => {
      syncLoadData();
    });
    const renderSwitcher = () => {
      const {
        prefixCls
      } = context.value;
      const switcherIconDom = renderSwitcherIconDom();
      if (isLeaf.value) {
        return switcherIconDom !== false ? createVNode("span", {
          "class": classNames_default(`${prefixCls}-switcher`, `${prefixCls}-switcher-noop`)
        }, [switcherIconDom]) : null;
      }
      const switcherCls = classNames_default(`${prefixCls}-switcher`, `${prefixCls}-switcher_${expanded.value ? ICON_OPEN : ICON_CLOSE}`);
      return switcherIconDom !== false ? createVNode("span", {
        "onClick": onExpand,
        "class": switcherCls
      }, [switcherIconDom]) : null;
    };
    const renderCheckbox = () => {
      var _a, _b;
      const {
        disableCheckbox
      } = props;
      const {
        prefixCls
      } = context.value;
      const disabled = isDisabled.value;
      const checkable = isCheckable.value;
      if (!checkable) return null;
      return createVNode("span", {
        "class": classNames_default(`${prefixCls}-checkbox`, checked.value && `${prefixCls}-checkbox-checked`, !checked.value && halfChecked.value && `${prefixCls}-checkbox-indeterminate`, (disabled || disableCheckbox) && `${prefixCls}-checkbox-disabled`),
        "onClick": onCheck
      }, [(_b = (_a = context.value).customCheckable) === null || _b === void 0 ? void 0 : _b.call(_a)]);
    };
    const renderIcon = () => {
      const {
        prefixCls
      } = context.value;
      return createVNode("span", {
        "class": classNames_default(`${prefixCls}-iconEle`, `${prefixCls}-icon__${nodeState.value || "docu"}`, loading.value && `${prefixCls}-icon_loading`)
      }, null);
    };
    const renderDropIndicator = () => {
      const {
        disabled,
        eventKey
      } = props;
      const {
        draggable,
        dropLevelOffset,
        dropPosition: dropPosition2,
        prefixCls,
        indent,
        dropIndicatorRender,
        dragOverNodeKey: dragOverNodeKey2,
        direction
      } = context.value;
      const rootDraggable = draggable !== false;
      const showIndicator = !disabled && rootDraggable && dragOverNodeKey2 === eventKey;
      return showIndicator ? dropIndicatorRender({
        dropPosition: dropPosition2,
        dropLevelOffset,
        indent,
        prefixCls,
        direction
      }) : null;
    };
    const renderSelector = () => {
      var _a, _b, _c, _d, _e, _f;
      const {
        // title = slots.title ||
        //   context.value.slots?.[props.data?.slots?.title] ||
        //   context.value.slots?.title,
        // selected,
        icon = slots.icon,
        // loading,
        data
      } = props;
      const title = slots.title || ((_a = context.value.slots) === null || _a === void 0 ? void 0 : _a[(_c = (_b = props.data) === null || _b === void 0 ? void 0 : _b.slots) === null || _c === void 0 ? void 0 : _c.title]) || ((_d = context.value.slots) === null || _d === void 0 ? void 0 : _d.title) || props.title;
      const {
        prefixCls,
        showIcon,
        icon: treeIcon,
        loadData
        // slots: contextSlots,
      } = context.value;
      const disabled = isDisabled.value;
      const wrapClass = `${prefixCls}-node-content-wrapper`;
      let $icon;
      if (showIcon) {
        const currentIcon = icon || ((_e = context.value.slots) === null || _e === void 0 ? void 0 : _e[(_f = data === null || data === void 0 ? void 0 : data.slots) === null || _f === void 0 ? void 0 : _f.icon]) || treeIcon;
        $icon = currentIcon ? createVNode("span", {
          "class": classNames_default(`${prefixCls}-iconEle`, `${prefixCls}-icon__customize`)
        }, [typeof currentIcon === "function" ? currentIcon(renderArgsData.value) : currentIcon]) : renderIcon();
      } else if (loadData && loading.value) {
        $icon = renderIcon();
      }
      let titleNode;
      if (typeof title === "function") {
        titleNode = title(renderArgsData.value);
      } else {
        titleNode = title;
      }
      titleNode = titleNode === void 0 ? defaultTitle : titleNode;
      const $title = createVNode("span", {
        "class": `${prefixCls}-title`
      }, [titleNode]);
      return createVNode("span", {
        "ref": selectHandle,
        "title": typeof title === "string" ? title : "",
        "class": classNames_default(`${wrapClass}`, `${wrapClass}-${nodeState.value || "normal"}`, !disabled && (selected.value || dragNodeHighlight.value) && `${prefixCls}-node-selected`),
        "onMouseenter": onMouseEnter,
        "onMouseleave": onMouseLeave,
        "onContextmenu": onContextmenu,
        "onClick": onSelectorClick,
        "onDblclick": onSelectorDoubleClick
      }, [$icon, $title, renderDropIndicator()]);
    };
    return () => {
      const _a = _extends(_extends({}, props), attrs), {
        eventKey,
        isLeaf: isLeaf2,
        isStart,
        isEnd,
        domRef,
        active,
        data,
        onMousemove,
        selectable
      } = _a, otherProps = __rest2(_a, ["eventKey", "isLeaf", "isStart", "isEnd", "domRef", "active", "data", "onMousemove", "selectable"]);
      const {
        prefixCls,
        filterTreeNode,
        keyEntities: keyEntities2,
        dropContainerKey,
        dropTargetKey,
        draggingNodeKey
      } = context.value;
      const disabled = isDisabled.value;
      const dataOrAriaAttributeProps = pickAttrs(otherProps, {
        aria: true,
        data: true
      });
      const {
        level
      } = keyEntities2[eventKey] || {};
      const isEndNode = isEnd[isEnd.length - 1];
      const mergedDraggable = isDraggable();
      const draggableWithoutDisabled = !disabled && mergedDraggable;
      const dragging = draggingNodeKey === eventKey;
      const ariaSelected = selectable !== void 0 ? {
        "aria-selected": !!selectable
      } : void 0;
      return createVNode("div", _objectSpread2(_objectSpread2({
        "ref": domRef,
        "class": classNames_default(attrs.class, `${prefixCls}-treenode`, {
          [`${prefixCls}-treenode-disabled`]: disabled,
          [`${prefixCls}-treenode-switcher-${expanded.value ? "open" : "close"}`]: !isLeaf2,
          [`${prefixCls}-treenode-checkbox-checked`]: checked.value,
          [`${prefixCls}-treenode-checkbox-indeterminate`]: halfChecked.value,
          [`${prefixCls}-treenode-selected`]: selected.value,
          [`${prefixCls}-treenode-loading`]: loading.value,
          [`${prefixCls}-treenode-active`]: active,
          [`${prefixCls}-treenode-leaf-last`]: isEndNode,
          [`${prefixCls}-treenode-draggable`]: draggableWithoutDisabled,
          dragging,
          "drop-target": dropTargetKey === eventKey,
          "drop-container": dropContainerKey === eventKey,
          "drag-over": !disabled && dragOver.value,
          "drag-over-gap-top": !disabled && dragOverGapTop.value,
          "drag-over-gap-bottom": !disabled && dragOverGapBottom.value,
          "filter-node": filterTreeNode && filterTreeNode(eventData.value)
        }),
        "style": attrs.style,
        "draggable": draggableWithoutDisabled,
        "aria-grabbed": dragging,
        "onDragstart": draggableWithoutDisabled ? onDragStart : void 0,
        "onDragenter": mergedDraggable ? onDragEnter : void 0,
        "onDragover": mergedDraggable ? onDragOver : void 0,
        "onDragleave": mergedDraggable ? onDragLeave : void 0,
        "onDrop": mergedDraggable ? onDrop : void 0,
        "onDragend": mergedDraggable ? onDragEnd : void 0,
        "onMousemove": onMousemove
      }, ariaSelected), dataOrAriaAttributeProps), [createVNode(Indent_default, {
        "prefixCls": prefixCls,
        "level": level,
        "isStart": isStart,
        "isEnd": isEnd
      }, null), renderDragHandler(), renderSwitcher(), renderCheckbox(), renderSelector()]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/util.js
function arrDel(list, value) {
  if (!list) return [];
  const clone = list.slice();
  const index = clone.indexOf(value);
  if (index >= 0) {
    clone.splice(index, 1);
  }
  return clone;
}
function arrAdd(list, value) {
  const clone = (list || []).slice();
  if (clone.indexOf(value) === -1) {
    clone.push(value);
  }
  return clone;
}
function posToArr(pos) {
  return pos.split("-");
}
function getPosition(level, index) {
  return `${level}-${index}`;
}
function isTreeNode(node) {
  return node && node.type && node.type.isTreeNode;
}
function getDragChildrenKeys(dragNodeKey, keyEntities) {
  const dragChildrenKeys = [];
  const entity = keyEntities[dragNodeKey];
  function dig() {
    let list = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
    list.forEach((_ref) => {
      let {
        key,
        children
      } = _ref;
      dragChildrenKeys.push(key);
      dig(children);
    });
  }
  dig(entity.children);
  return dragChildrenKeys;
}
function isLastChild(treeNodeEntity) {
  if (treeNodeEntity.parent) {
    const posArr = posToArr(treeNodeEntity.pos);
    return Number(posArr[posArr.length - 1]) === treeNodeEntity.parent.children.length - 1;
  }
  return false;
}
function isFirstChild(treeNodeEntity) {
  const posArr = posToArr(treeNodeEntity.pos);
  return Number(posArr[posArr.length - 1]) === 0;
}
function calcDropPosition(event, dragNode, targetNode, indent, startMousePosition, allowDrop, flattenedNodes, keyEntities, expandKeysSet, direction) {
  var _a;
  const {
    clientX,
    clientY
  } = event;
  const {
    top,
    height
  } = event.target.getBoundingClientRect();
  const horizontalMouseOffset = (direction === "rtl" ? -1 : 1) * (((startMousePosition === null || startMousePosition === void 0 ? void 0 : startMousePosition.x) || 0) - clientX);
  const rawDropLevelOffset = (horizontalMouseOffset - 12) / indent;
  let abstractDropNodeEntity = keyEntities[targetNode.eventKey];
  if (clientY < top + height / 2) {
    const nodeIndex = flattenedNodes.findIndex((flattenedNode) => flattenedNode.key === abstractDropNodeEntity.key);
    const prevNodeIndex = nodeIndex <= 0 ? 0 : nodeIndex - 1;
    const prevNodeKey = flattenedNodes[prevNodeIndex].key;
    abstractDropNodeEntity = keyEntities[prevNodeKey];
  }
  const initialAbstractDropNodeKey = abstractDropNodeEntity.key;
  const abstractDragOverEntity = abstractDropNodeEntity;
  const dragOverNodeKey = abstractDropNodeEntity.key;
  let dropPosition = 0;
  let dropLevelOffset = 0;
  if (!expandKeysSet.has(initialAbstractDropNodeKey)) {
    for (let i = 0; i < rawDropLevelOffset; i += 1) {
      if (isLastChild(abstractDropNodeEntity)) {
        abstractDropNodeEntity = abstractDropNodeEntity.parent;
        dropLevelOffset += 1;
      } else {
        break;
      }
    }
  }
  const abstractDragDataNode = dragNode.eventData;
  const abstractDropDataNode = abstractDropNodeEntity.node;
  let dropAllowed = true;
  if (isFirstChild(abstractDropNodeEntity) && abstractDropNodeEntity.level === 0 && clientY < top + height / 2 && allowDrop({
    dragNode: abstractDragDataNode,
    dropNode: abstractDropDataNode,
    dropPosition: -1
  }) && abstractDropNodeEntity.key === targetNode.eventKey) {
    dropPosition = -1;
  } else if ((abstractDragOverEntity.children || []).length && expandKeysSet.has(dragOverNodeKey)) {
    if (allowDrop({
      dragNode: abstractDragDataNode,
      dropNode: abstractDropDataNode,
      dropPosition: 0
    })) {
      dropPosition = 0;
    } else {
      dropAllowed = false;
    }
  } else if (dropLevelOffset === 0) {
    if (rawDropLevelOffset > -1.5) {
      if (allowDrop({
        dragNode: abstractDragDataNode,
        dropNode: abstractDropDataNode,
        dropPosition: 1
      })) {
        dropPosition = 1;
      } else {
        dropAllowed = false;
      }
    } else {
      if (allowDrop({
        dragNode: abstractDragDataNode,
        dropNode: abstractDropDataNode,
        dropPosition: 0
      })) {
        dropPosition = 0;
      } else if (allowDrop({
        dragNode: abstractDragDataNode,
        dropNode: abstractDropDataNode,
        dropPosition: 1
      })) {
        dropPosition = 1;
      } else {
        dropAllowed = false;
      }
    }
  } else {
    if (allowDrop({
      dragNode: abstractDragDataNode,
      dropNode: abstractDropDataNode,
      dropPosition: 1
    })) {
      dropPosition = 1;
    } else {
      dropAllowed = false;
    }
  }
  return {
    dropPosition,
    dropLevelOffset,
    dropTargetKey: abstractDropNodeEntity.key,
    dropTargetPos: abstractDropNodeEntity.pos,
    dragOverNodeKey,
    dropContainerKey: dropPosition === 0 ? null : ((_a = abstractDropNodeEntity.parent) === null || _a === void 0 ? void 0 : _a.key) || null,
    dropAllowed
  };
}
function calcSelectedKeys(selectedKeys, props) {
  if (!selectedKeys) return void 0;
  const {
    multiple
  } = props;
  if (multiple) {
    return selectedKeys.slice();
  }
  if (selectedKeys.length) {
    return [selectedKeys[0]];
  }
  return selectedKeys;
}
function parseCheckedKeys(keys) {
  if (!keys) {
    return null;
  }
  let keyProps;
  if (Array.isArray(keys)) {
    keyProps = {
      checkedKeys: keys,
      halfCheckedKeys: void 0
    };
  } else if (typeof keys === "object") {
    keyProps = {
      checkedKeys: keys.checked || void 0,
      halfCheckedKeys: keys.halfChecked || void 0
    };
  } else {
    warning(false, "`checkedKeys` is not an array or an object");
    return null;
  }
  return keyProps;
}
function conductExpandParent(keyList, keyEntities) {
  const expandedKeys = /* @__PURE__ */ new Set();
  function conductUp(key) {
    if (expandedKeys.has(key)) return;
    const entity = keyEntities[key];
    if (!entity) return;
    expandedKeys.add(key);
    const {
      parent,
      node
    } = entity;
    if (node.disabled) return;
    if (parent) {
      conductUp(parent.key);
    }
  }
  (keyList || []).forEach((key) => {
    conductUp(key);
  });
  return [...expandedKeys];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/MotionTreeNode.js
var __rest3 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var MotionTreeNode_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "MotionTreeNode",
  inheritAttrs: false,
  props: _extends(_extends({}, treeNodeProps), {
    active: Boolean,
    motion: Object,
    motionNodes: {
      type: Array
    },
    onMotionStart: Function,
    onMotionEnd: Function,
    motionType: String
  }),
  setup(props, _ref) {
    let {
      attrs,
      slots
    } = _ref;
    const visible = shallowRef(true);
    const context = useInjectTreeContext();
    const motionedRef = shallowRef(false);
    const transitionProps = computed(() => {
      if (props.motion) {
        return props.motion;
      } else {
        return collapseMotion_default();
      }
    });
    const onMotionEnd = (node, type) => {
      var _a, _b, _c, _d;
      if (type === "appear") {
        (_b = (_a = transitionProps.value) === null || _a === void 0 ? void 0 : _a.onAfterEnter) === null || _b === void 0 ? void 0 : _b.call(_a, node);
      } else if (type === "leave") {
        (_d = (_c = transitionProps.value) === null || _c === void 0 ? void 0 : _c.onAfterLeave) === null || _d === void 0 ? void 0 : _d.call(_c, node);
      }
      if (!motionedRef.value) {
        props.onMotionEnd();
      }
      motionedRef.value = true;
    };
    watch(() => props.motionNodes, () => {
      if (props.motionNodes && props.motionType === "hide" && visible.value) {
        nextTick(() => {
          visible.value = false;
        });
      }
    }, {
      immediate: true,
      flush: "post"
    });
    onMounted(() => {
      props.motionNodes && props.onMotionStart();
    });
    onBeforeUnmount(() => {
      props.motionNodes && onMotionEnd();
    });
    return () => {
      const {
        motion,
        motionNodes,
        motionType,
        active,
        eventKey
      } = props, otherProps = __rest3(props, ["motion", "motionNodes", "motionType", "active", "eventKey"]);
      if (motionNodes) {
        return createVNode(Transition, _objectSpread2(_objectSpread2({}, transitionProps.value), {}, {
          "appear": motionType === "show",
          "onAfterAppear": (node) => onMotionEnd(node, "appear"),
          "onAfterLeave": (node) => onMotionEnd(node, "leave")
        }), {
          default: () => [withDirectives(createVNode("div", {
            "class": `${context.value.prefixCls}-treenode-motion`
          }, [motionNodes.map((treeNode) => {
            const restProps = __rest3(treeNode.data, []), {
              title,
              key,
              isStart,
              isEnd
            } = treeNode;
            delete restProps.children;
            return createVNode(TreeNode_default, _objectSpread2(_objectSpread2({}, restProps), {}, {
              "title": title,
              "active": active,
              "data": treeNode.data,
              "key": key,
              "eventKey": key,
              "isStart": isStart,
              "isEnd": isEnd
            }), slots);
          })]), [[vShow, visible.value]])]
        });
      }
      return createVNode(TreeNode_default, _objectSpread2(_objectSpread2({
        "class": attrs.class,
        "style": attrs.style
      }, otherProps), {}, {
        "active": active,
        "eventKey": eventKey
      }), slots);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/utils/diffUtil.js
function findExpandedKeys() {
  let prev = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  let next = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
  const prevLen = prev.length;
  const nextLen = next.length;
  if (Math.abs(prevLen - nextLen) !== 1) {
    return {
      add: false,
      key: null
    };
  }
  function find(shorter, longer) {
    const cache = /* @__PURE__ */ new Map();
    shorter.forEach((key) => {
      cache.set(key, true);
    });
    const keys = longer.filter((key) => !cache.has(key));
    return keys.length === 1 ? keys[0] : null;
  }
  if (prevLen < nextLen) {
    return {
      add: true,
      key: find(prev, next)
    };
  }
  return {
    add: false,
    key: find(next, prev)
  };
}
function getExpandRange(shorter, longer, key) {
  const shorterStartIndex = shorter.findIndex((item) => item.key === key);
  const shorterEndNode = shorter[shorterStartIndex + 1];
  const longerStartIndex = longer.findIndex((item) => item.key === key);
  if (shorterEndNode) {
    const longerEndIndex = longer.findIndex((item) => item.key === shorterEndNode.key);
    return longer.slice(longerStartIndex + 1, longerEndIndex);
  }
  return longer.slice(longerStartIndex + 1);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/NodeList.js
var __rest4 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
var HIDDEN_STYLE = {
  width: 0,
  height: 0,
  display: "flex",
  overflow: "hidden",
  opacity: 0,
  border: 0,
  padding: 0,
  margin: 0
};
var noop = () => {
};
var MOTION_KEY = `RC_TREE_MOTION_${Math.random()}`;
var MotionNode = {
  key: MOTION_KEY
};
var MotionEntity = {
  key: MOTION_KEY,
  level: 0,
  index: 0,
  pos: "0",
  node: MotionNode,
  nodes: [MotionNode]
};
var MotionFlattenData = {
  parent: null,
  children: [],
  pos: MotionEntity.pos,
  data: MotionNode,
  title: null,
  key: MOTION_KEY,
  /** Hold empty list here since we do not use it */
  isStart: [],
  isEnd: []
};
function getMinimumRangeTransitionRange(list, virtual, height, itemHeight) {
  if (virtual === false || !height) {
    return list;
  }
  return list.slice(0, Math.ceil(height / itemHeight) + 1);
}
function itemKey(item) {
  const {
    key,
    pos
  } = item;
  return getKey(key, pos);
}
function getAccessibilityPath(item) {
  let path = String(item.key);
  let current = item;
  while (current.parent) {
    current = current.parent;
    path = `${current.key} > ${path}`;
  }
  return path;
}
var NodeList_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "NodeList",
  inheritAttrs: false,
  props: nodeListProps,
  setup(props, _ref) {
    let {
      expose,
      attrs
    } = _ref;
    const listRef = ref();
    const indentMeasurerRef = ref();
    const {
      expandedKeys,
      flattenNodes
    } = useInjectKeysState();
    expose({
      scrollTo: (scroll) => {
        listRef.value.scrollTo(scroll);
      },
      getIndentWidth: () => indentMeasurerRef.value.offsetWidth
    });
    const transitionData = shallowRef(flattenNodes.value);
    const transitionRange = shallowRef([]);
    const motionType = ref(null);
    function onMotionEnd() {
      transitionData.value = flattenNodes.value;
      transitionRange.value = [];
      motionType.value = null;
      props.onListChangeEnd();
    }
    const context = useInjectTreeContext();
    watch([() => expandedKeys.value.slice(), flattenNodes], (_ref2, _ref3) => {
      let [expandedKeys2, data] = _ref2;
      let [prevExpandedKeys, prevData] = _ref3;
      const diffExpanded = findExpandedKeys(prevExpandedKeys, expandedKeys2);
      if (diffExpanded.key !== null) {
        const {
          virtual,
          height,
          itemHeight
        } = props;
        if (diffExpanded.add) {
          const keyIndex = prevData.findIndex((_ref4) => {
            let {
              key
            } = _ref4;
            return key === diffExpanded.key;
          });
          const rangeNodes = getMinimumRangeTransitionRange(getExpandRange(prevData, data, diffExpanded.key), virtual, height, itemHeight);
          const newTransitionData = prevData.slice();
          newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);
          transitionData.value = newTransitionData;
          transitionRange.value = rangeNodes;
          motionType.value = "show";
        } else {
          const keyIndex = data.findIndex((_ref5) => {
            let {
              key
            } = _ref5;
            return key === diffExpanded.key;
          });
          const rangeNodes = getMinimumRangeTransitionRange(getExpandRange(data, prevData, diffExpanded.key), virtual, height, itemHeight);
          const newTransitionData = data.slice();
          newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);
          transitionData.value = newTransitionData;
          transitionRange.value = rangeNodes;
          motionType.value = "hide";
        }
      } else if (prevData !== data) {
        transitionData.value = data;
      }
    });
    watch(() => context.value.dragging, (dragging) => {
      if (!dragging) {
        onMotionEnd();
      }
    });
    const mergedData = computed(() => props.motion === void 0 ? transitionData.value : flattenNodes.value);
    const onActiveChange = () => {
      props.onActiveChange(null);
    };
    return () => {
      const _a = _extends(_extends({}, props), attrs), {
        prefixCls,
        selectable,
        checkable,
        disabled,
        motion,
        height,
        itemHeight,
        virtual,
        focusable,
        activeItem,
        focused,
        tabindex,
        onKeydown,
        onFocus,
        onBlur,
        onListChangeStart,
        onListChangeEnd
      } = _a, domProps = __rest4(_a, ["prefixCls", "selectable", "checkable", "disabled", "motion", "height", "itemHeight", "virtual", "focusable", "activeItem", "focused", "tabindex", "onKeydown", "onFocus", "onBlur", "onListChangeStart", "onListChangeEnd"]);
      return createVNode(Fragment, null, [focused && activeItem && createVNode("span", {
        "style": HIDDEN_STYLE,
        "aria-live": "assertive"
      }, [getAccessibilityPath(activeItem)]), createVNode("div", null, [createVNode("input", {
        "style": HIDDEN_STYLE,
        "disabled": focusable === false || disabled,
        "tabindex": focusable !== false ? tabindex : null,
        "onKeydown": onKeydown,
        "onFocus": onFocus,
        "onBlur": onBlur,
        "value": "",
        "onChange": noop,
        "aria-label": "for screen reader"
      }, null)]), createVNode("div", {
        "class": `${prefixCls}-treenode`,
        "aria-hidden": true,
        "style": {
          position: "absolute",
          pointerEvents: "none",
          visibility: "hidden",
          height: 0,
          overflow: "hidden"
        }
      }, [createVNode("div", {
        "class": `${prefixCls}-indent`
      }, [createVNode("div", {
        "ref": indentMeasurerRef,
        "class": `${prefixCls}-indent-unit`
      }, null)])]), createVNode(vc_virtual_list_default, _objectSpread2(_objectSpread2({}, omit_default(domProps, ["onActiveChange"])), {}, {
        "data": mergedData.value,
        "itemKey": itemKey,
        "height": height,
        "fullHeight": false,
        "virtual": virtual,
        "itemHeight": itemHeight,
        "prefixCls": `${prefixCls}-list`,
        "ref": listRef,
        "onVisibleChange": (originList, fullList) => {
          const originSet = new Set(originList);
          const restList = fullList.filter((item) => !originSet.has(item));
          if (restList.some((item) => itemKey(item) === MOTION_KEY)) {
            onMotionEnd();
          }
        }
      }), {
        default: (treeNode) => {
          const {
            pos
          } = treeNode, restProps = __rest4(treeNode.data, []), {
            title,
            key,
            isStart,
            isEnd
          } = treeNode;
          const mergedKey = getKey(key, pos);
          delete restProps.key;
          delete restProps.children;
          return createVNode(MotionTreeNode_default, _objectSpread2(_objectSpread2({}, restProps), {}, {
            "eventKey": mergedKey,
            "title": title,
            "active": !!activeItem && key === activeItem.key,
            "data": treeNode.data,
            "isStart": isStart,
            "isEnd": isEnd,
            "motion": motion,
            "motionNodes": key === MOTION_KEY ? transitionRange.value : null,
            "motionType": motionType.value,
            "onMotionStart": onListChangeStart,
            "onMotionEnd": onMotionEnd,
            "onMousemove": onActiveChange
          }), null);
        }
      })]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/utils/conductUtil.js
function removeFromCheckedKeys(halfCheckedKeys, checkedKeys) {
  const filteredKeys = /* @__PURE__ */ new Set();
  halfCheckedKeys.forEach((key) => {
    if (!checkedKeys.has(key)) {
      filteredKeys.add(key);
    }
  });
  return filteredKeys;
}
function isCheckDisabled(node) {
  const {
    disabled,
    disableCheckbox,
    checkable
  } = node || {};
  return !!(disabled || disableCheckbox) || checkable === false;
}
function fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled) {
  const checkedKeys = new Set(keys);
  const halfCheckedKeys = /* @__PURE__ */ new Set();
  for (let level = 0; level <= maxLevel; level += 1) {
    const entities = levelEntities.get(level) || /* @__PURE__ */ new Set();
    entities.forEach((entity) => {
      const {
        key,
        node,
        children = []
      } = entity;
      if (checkedKeys.has(key) && !syntheticGetCheckDisabled(node)) {
        children.filter((childEntity) => !syntheticGetCheckDisabled(childEntity.node)).forEach((childEntity) => {
          checkedKeys.add(childEntity.key);
        });
      }
    });
  }
  const visitedKeys = /* @__PURE__ */ new Set();
  for (let level = maxLevel; level >= 0; level -= 1) {
    const entities = levelEntities.get(level) || /* @__PURE__ */ new Set();
    entities.forEach((entity) => {
      const {
        parent,
        node
      } = entity;
      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {
        return;
      }
      if (syntheticGetCheckDisabled(entity.parent.node)) {
        visitedKeys.add(parent.key);
        return;
      }
      let allChecked = true;
      let partialChecked = false;
      (parent.children || []).filter((childEntity) => !syntheticGetCheckDisabled(childEntity.node)).forEach((_ref) => {
        let {
          key
        } = _ref;
        const checked = checkedKeys.has(key);
        if (allChecked && !checked) {
          allChecked = false;
        }
        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {
          partialChecked = true;
        }
      });
      if (allChecked) {
        checkedKeys.add(parent.key);
      }
      if (partialChecked) {
        halfCheckedKeys.add(parent.key);
      }
      visitedKeys.add(parent.key);
    });
  }
  return {
    checkedKeys: Array.from(checkedKeys),
    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))
  };
}
function cleanConductCheck(keys, halfKeys, levelEntities, maxLevel, syntheticGetCheckDisabled) {
  const checkedKeys = new Set(keys);
  let halfCheckedKeys = new Set(halfKeys);
  for (let level = 0; level <= maxLevel; level += 1) {
    const entities = levelEntities.get(level) || /* @__PURE__ */ new Set();
    entities.forEach((entity) => {
      const {
        key,
        node,
        children = []
      } = entity;
      if (!checkedKeys.has(key) && !halfCheckedKeys.has(key) && !syntheticGetCheckDisabled(node)) {
        children.filter((childEntity) => !syntheticGetCheckDisabled(childEntity.node)).forEach((childEntity) => {
          checkedKeys.delete(childEntity.key);
        });
      }
    });
  }
  halfCheckedKeys = /* @__PURE__ */ new Set();
  const visitedKeys = /* @__PURE__ */ new Set();
  for (let level = maxLevel; level >= 0; level -= 1) {
    const entities = levelEntities.get(level) || /* @__PURE__ */ new Set();
    entities.forEach((entity) => {
      const {
        parent,
        node
      } = entity;
      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {
        return;
      }
      if (syntheticGetCheckDisabled(entity.parent.node)) {
        visitedKeys.add(parent.key);
        return;
      }
      let allChecked = true;
      let partialChecked = false;
      (parent.children || []).filter((childEntity) => !syntheticGetCheckDisabled(childEntity.node)).forEach((_ref2) => {
        let {
          key
        } = _ref2;
        const checked = checkedKeys.has(key);
        if (allChecked && !checked) {
          allChecked = false;
        }
        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {
          partialChecked = true;
        }
      });
      if (!allChecked) {
        checkedKeys.delete(parent.key);
      }
      if (partialChecked) {
        halfCheckedKeys.add(parent.key);
      }
      visitedKeys.add(parent.key);
    });
  }
  return {
    checkedKeys: Array.from(checkedKeys),
    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))
  };
}
function conductCheck(keyList, checked, keyEntities, maxLevel, levelEntities, getCheckDisabled) {
  const warningMissKeys = [];
  let syntheticGetCheckDisabled;
  if (getCheckDisabled) {
    syntheticGetCheckDisabled = getCheckDisabled;
  } else {
    syntheticGetCheckDisabled = isCheckDisabled;
  }
  const keys = new Set(keyList.filter((key) => {
    const hasEntity = !!keyEntities[key];
    if (!hasEntity) {
      warningMissKeys.push(key);
    }
    return hasEntity;
  }));
  note(!warningMissKeys.length, `Tree missing follow keys: ${warningMissKeys.slice(0, 100).map((key) => `'${key}'`).join(", ")}`);
  let result;
  if (checked === true) {
    result = fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled);
  } else {
    result = cleanConductCheck(keys, checked.halfCheckedKeys, levelEntities, maxLevel, syntheticGetCheckDisabled);
  }
  return result;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/DropIndicator.js
function DropIndicator(_ref) {
  let {
    dropPosition,
    dropLevelOffset,
    indent
  } = _ref;
  const style = {
    pointerEvents: "none",
    position: "absolute",
    right: 0,
    backgroundColor: "red",
    height: `${2}px`
  };
  switch (dropPosition) {
    case -1:
      style.top = 0;
      style.left = `${-dropLevelOffset * indent}px`;
      break;
    case 1:
      style.bottom = 0;
      style.left = `${-dropLevelOffset * indent}px`;
      break;
    case 0:
      style.bottom = 0;
      style.left = `${indent}`;
      break;
  }
  return createVNode("div", {
    "style": style
  }, null);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/useMaxLevel.js
function useMaxLevel(keyEntities) {
  const maxLevel = ref(0);
  const levelEntities = shallowRef();
  watchEffect(() => {
    const newLevelEntities = /* @__PURE__ */ new Map();
    let newMaxLevel = 0;
    const keyEntitiesValue = keyEntities.value || {};
    for (const key in keyEntitiesValue) {
      if (Object.prototype.hasOwnProperty.call(keyEntitiesValue, key)) {
        const entity = keyEntitiesValue[key];
        const {
          level
        } = entity;
        let levelSet = newLevelEntities.get(level);
        if (!levelSet) {
          levelSet = /* @__PURE__ */ new Set();
          newLevelEntities.set(level, levelSet);
        }
        levelSet.add(entity);
        newMaxLevel = Math.max(newMaxLevel, level);
      }
    }
    maxLevel.value = newMaxLevel;
    levelEntities.value = newLevelEntities;
  });
  return {
    maxLevel,
    levelEntities
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree/Tree.js
var MAX_RETRY_TIMES = 10;
var Tree_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "Tree",
  inheritAttrs: false,
  props: initDefaultProps_default(treeProps(), {
    prefixCls: "vc-tree",
    showLine: false,
    showIcon: true,
    selectable: true,
    multiple: false,
    checkable: false,
    disabled: false,
    checkStrictly: false,
    draggable: false,
    expandAction: false,
    defaultExpandParent: true,
    autoExpandParent: false,
    defaultExpandAll: false,
    defaultExpandedKeys: [],
    defaultCheckedKeys: [],
    defaultSelectedKeys: [],
    dropIndicatorRender: DropIndicator,
    allowDrop: () => true
  }),
  setup(props, _ref) {
    let {
      attrs,
      slots,
      expose
    } = _ref;
    const destroyed = shallowRef(false);
    let delayedDragEnterLogic = {};
    const indent = shallowRef();
    const selectedKeys = shallowRef([]);
    const checkedKeys = shallowRef([]);
    const halfCheckedKeys = shallowRef([]);
    const loadedKeys = shallowRef([]);
    const loadingKeys = shallowRef([]);
    const expandedKeys = shallowRef([]);
    const loadingRetryTimes = {};
    const dragState = reactive({
      draggingNodeKey: null,
      dragChildrenKeys: [],
      // dropTargetKey is the key of abstract-drop-node
      // the abstract-drop-node is the real drop node when drag and drop
      // not the DOM drag over node
      dropTargetKey: null,
      dropPosition: null,
      dropContainerKey: null,
      dropLevelOffset: null,
      dropTargetPos: null,
      dropAllowed: true,
      // the abstract-drag-over-node
      // if mouse is on the bottom of top dom node or no the top of the bottom dom node
      // abstract-drag-over-node is the top node
      dragOverNodeKey: null
    });
    const treeData = shallowRef([]);
    watch([() => props.treeData, () => props.children], () => {
      treeData.value = props.treeData !== void 0 ? props.treeData.slice() : convertTreeToData(toRaw(props.children));
    }, {
      immediate: true,
      deep: true
    });
    const keyEntities = shallowRef({});
    const focused = shallowRef(false);
    const activeKey = shallowRef(null);
    const listChanging = shallowRef(false);
    const fieldNames = computed(() => fillFieldNames(props.fieldNames));
    const listRef = shallowRef();
    let dragStartMousePosition = null;
    let dragNode = null;
    let currentMouseOverDroppableNodeKey = null;
    const treeNodeRequiredProps = computed(() => {
      return {
        expandedKeysSet: expandedKeysSet.value,
        selectedKeysSet: selectedKeysSet.value,
        loadedKeysSet: loadedKeysSet.value,
        loadingKeysSet: loadingKeysSet.value,
        checkedKeysSet: checkedKeysSet.value,
        halfCheckedKeysSet: halfCheckedKeysSet.value,
        dragOverNodeKey: dragState.dragOverNodeKey,
        dropPosition: dragState.dropPosition,
        keyEntities: keyEntities.value
      };
    });
    const expandedKeysSet = computed(() => {
      return new Set(expandedKeys.value);
    });
    const selectedKeysSet = computed(() => {
      return new Set(selectedKeys.value);
    });
    const loadedKeysSet = computed(() => {
      return new Set(loadedKeys.value);
    });
    const loadingKeysSet = computed(() => {
      return new Set(loadingKeys.value);
    });
    const checkedKeysSet = computed(() => {
      return new Set(checkedKeys.value);
    });
    const halfCheckedKeysSet = computed(() => {
      return new Set(halfCheckedKeys.value);
    });
    watchEffect(() => {
      if (treeData.value) {
        const entitiesMap = convertDataToEntities(treeData.value, {
          fieldNames: fieldNames.value
        });
        keyEntities.value = _extends({
          [MOTION_KEY]: MotionEntity
        }, entitiesMap.keyEntities);
      }
    });
    let init = false;
    watch(
      [() => props.expandedKeys, () => props.autoExpandParent, keyEntities],
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      (_ref2, _ref3) => {
        let [_newKeys, newAutoExpandParent] = _ref2;
        let [_oldKeys, oldAutoExpandParent] = _ref3;
        let keys = expandedKeys.value;
        if (props.expandedKeys !== void 0 || init && newAutoExpandParent !== oldAutoExpandParent) {
          keys = props.autoExpandParent || !init && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities.value) : props.expandedKeys;
        } else if (!init && props.defaultExpandAll) {
          const cloneKeyEntities = _extends({}, keyEntities.value);
          delete cloneKeyEntities[MOTION_KEY];
          keys = Object.keys(cloneKeyEntities).map((key) => cloneKeyEntities[key].key);
        } else if (!init && props.defaultExpandedKeys) {
          keys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities.value) : props.defaultExpandedKeys;
        }
        if (keys) {
          expandedKeys.value = keys;
        }
        init = true;
      },
      {
        immediate: true
      }
    );
    const flattenNodes = shallowRef([]);
    watchEffect(() => {
      flattenNodes.value = flattenTreeData(treeData.value, expandedKeys.value, fieldNames.value);
    });
    watchEffect(() => {
      if (props.selectable) {
        if (props.selectedKeys !== void 0) {
          selectedKeys.value = calcSelectedKeys(props.selectedKeys, props);
        } else if (!init && props.defaultSelectedKeys) {
          selectedKeys.value = calcSelectedKeys(props.defaultSelectedKeys, props);
        }
      }
    });
    const {
      maxLevel,
      levelEntities
    } = useMaxLevel(keyEntities);
    watchEffect(() => {
      if (props.checkable) {
        let checkedKeyEntity;
        if (props.checkedKeys !== void 0) {
          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};
        } else if (!init && props.defaultCheckedKeys) {
          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};
        } else if (treeData.value) {
          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {
            checkedKeys: checkedKeys.value,
            halfCheckedKeys: halfCheckedKeys.value
          };
        }
        if (checkedKeyEntity) {
          let {
            checkedKeys: newCheckedKeys = [],
            halfCheckedKeys: newHalfCheckedKeys = []
          } = checkedKeyEntity;
          if (!props.checkStrictly) {
            const conductKeys = conductCheck(newCheckedKeys, true, keyEntities.value, maxLevel.value, levelEntities.value);
            ({
              checkedKeys: newCheckedKeys,
              halfCheckedKeys: newHalfCheckedKeys
            } = conductKeys);
          }
          checkedKeys.value = newCheckedKeys;
          halfCheckedKeys.value = newHalfCheckedKeys;
        }
      }
    });
    watchEffect(() => {
      if (props.loadedKeys) {
        loadedKeys.value = props.loadedKeys;
      }
    });
    const resetDragState = () => {
      _extends(dragState, {
        dragOverNodeKey: null,
        dropPosition: null,
        dropLevelOffset: null,
        dropTargetKey: null,
        dropContainerKey: null,
        dropTargetPos: null,
        dropAllowed: false
      });
    };
    const scrollTo = (scroll) => {
      listRef.value.scrollTo(scroll);
    };
    watch(() => props.activeKey, () => {
      if (props.activeKey !== void 0) {
        activeKey.value = props.activeKey;
      }
    }, {
      immediate: true
    });
    watch(activeKey, (val) => {
      nextTick(() => {
        if (val !== null) {
          scrollTo({
            key: val
          });
        }
      });
    }, {
      immediate: true,
      flush: "post"
    });
    const setExpandedKeys = (keys) => {
      if (props.expandedKeys === void 0) {
        expandedKeys.value = keys;
      }
    };
    const cleanDragState = () => {
      if (dragState.draggingNodeKey !== null) {
        _extends(dragState, {
          draggingNodeKey: null,
          dropPosition: null,
          dropContainerKey: null,
          dropTargetKey: null,
          dropLevelOffset: null,
          dropAllowed: true,
          dragOverNodeKey: null
        });
      }
      dragStartMousePosition = null;
      currentMouseOverDroppableNodeKey = null;
    };
    const onNodeDragEnd = (event, node) => {
      const {
        onDragend
      } = props;
      dragState.dragOverNodeKey = null;
      cleanDragState();
      onDragend === null || onDragend === void 0 ? void 0 : onDragend({
        event,
        node: node.eventData
      });
      dragNode = null;
    };
    const onWindowDragEnd = (event) => {
      onNodeDragEnd(event, null, true);
      window.removeEventListener("dragend", onWindowDragEnd);
    };
    const onNodeDragStart = (event, node) => {
      const {
        onDragstart
      } = props;
      const {
        eventKey,
        eventData
      } = node;
      dragNode = node;
      dragStartMousePosition = {
        x: event.clientX,
        y: event.clientY
      };
      const newExpandedKeys = arrDel(expandedKeys.value, eventKey);
      dragState.draggingNodeKey = eventKey;
      dragState.dragChildrenKeys = getDragChildrenKeys(eventKey, keyEntities.value);
      indent.value = listRef.value.getIndentWidth();
      setExpandedKeys(newExpandedKeys);
      window.addEventListener("dragend", onWindowDragEnd);
      if (onDragstart) {
        onDragstart({
          event,
          node: eventData
        });
      }
    };
    const onNodeDragEnter = (event, node) => {
      const {
        onDragenter,
        onExpand,
        allowDrop,
        direction
      } = props;
      const {
        pos,
        eventKey
      } = node;
      if (currentMouseOverDroppableNodeKey !== eventKey) {
        currentMouseOverDroppableNodeKey = eventKey;
      }
      if (!dragNode) {
        resetDragState();
        return;
      }
      const {
        dropPosition,
        dropLevelOffset,
        dropTargetKey,
        dropContainerKey,
        dropTargetPos,
        dropAllowed,
        dragOverNodeKey
      } = calcDropPosition(event, dragNode, node, indent.value, dragStartMousePosition, allowDrop, flattenNodes.value, keyEntities.value, expandedKeysSet.value, direction);
      if (
        // don't allow drop inside its children
        dragState.dragChildrenKeys.indexOf(dropTargetKey) !== -1 || // don't allow drop when drop is not allowed caculated by calcDropPosition
        !dropAllowed
      ) {
        resetDragState();
        return;
      }
      if (!delayedDragEnterLogic) {
        delayedDragEnterLogic = {};
      }
      Object.keys(delayedDragEnterLogic).forEach((key) => {
        clearTimeout(delayedDragEnterLogic[key]);
      });
      if (dragNode.eventKey !== node.eventKey) {
        delayedDragEnterLogic[pos] = window.setTimeout(() => {
          if (dragState.draggingNodeKey === null) return;
          let newExpandedKeys = expandedKeys.value.slice();
          const entity = keyEntities.value[node.eventKey];
          if (entity && (entity.children || []).length) {
            newExpandedKeys = arrAdd(expandedKeys.value, node.eventKey);
          }
          setExpandedKeys(newExpandedKeys);
          if (onExpand) {
            onExpand(newExpandedKeys, {
              node: node.eventData,
              expanded: true,
              nativeEvent: event
            });
          }
        }, 800);
      }
      if (dragNode.eventKey === dropTargetKey && dropLevelOffset === 0) {
        resetDragState();
        return;
      }
      _extends(dragState, {
        dragOverNodeKey,
        dropPosition,
        dropLevelOffset,
        dropTargetKey,
        dropContainerKey,
        dropTargetPos,
        dropAllowed
      });
      if (onDragenter) {
        onDragenter({
          event,
          node: node.eventData,
          expandedKeys: expandedKeys.value
        });
      }
    };
    const onNodeDragOver = (event, node) => {
      const {
        onDragover,
        allowDrop,
        direction
      } = props;
      if (!dragNode) {
        return;
      }
      const {
        dropPosition,
        dropLevelOffset,
        dropTargetKey,
        dropContainerKey,
        dropAllowed,
        dropTargetPos,
        dragOverNodeKey
      } = calcDropPosition(event, dragNode, node, indent.value, dragStartMousePosition, allowDrop, flattenNodes.value, keyEntities.value, expandedKeysSet.value, direction);
      if (dragState.dragChildrenKeys.indexOf(dropTargetKey) !== -1 || !dropAllowed) {
        return;
      }
      if (dragNode.eventKey === dropTargetKey && dropLevelOffset === 0) {
        if (!(dragState.dropPosition === null && dragState.dropLevelOffset === null && dragState.dropTargetKey === null && dragState.dropContainerKey === null && dragState.dropTargetPos === null && dragState.dropAllowed === false && dragState.dragOverNodeKey === null)) {
          resetDragState();
        }
      } else if (!(dropPosition === dragState.dropPosition && dropLevelOffset === dragState.dropLevelOffset && dropTargetKey === dragState.dropTargetKey && dropContainerKey === dragState.dropContainerKey && dropTargetPos === dragState.dropTargetPos && dropAllowed === dragState.dropAllowed && dragOverNodeKey === dragState.dragOverNodeKey)) {
        _extends(dragState, {
          dropPosition,
          dropLevelOffset,
          dropTargetKey,
          dropContainerKey,
          dropTargetPos,
          dropAllowed,
          dragOverNodeKey
        });
      }
      if (onDragover) {
        onDragover({
          event,
          node: node.eventData
        });
      }
    };
    const onNodeDragLeave = (event, node) => {
      if (currentMouseOverDroppableNodeKey === node.eventKey && !event.currentTarget.contains(event.relatedTarget)) {
        resetDragState();
        currentMouseOverDroppableNodeKey = null;
      }
      const {
        onDragleave
      } = props;
      if (onDragleave) {
        onDragleave({
          event,
          node: node.eventData
        });
      }
    };
    const onNodeDrop = function(event, _node) {
      let outsideTree = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
      var _a;
      const {
        dragChildrenKeys,
        dropPosition,
        dropTargetKey,
        dropTargetPos,
        dropAllowed
      } = dragState;
      if (!dropAllowed) return;
      const {
        onDrop
      } = props;
      dragState.dragOverNodeKey = null;
      cleanDragState();
      if (dropTargetKey === null) return;
      const abstractDropNodeProps = _extends(_extends({}, getTreeNodeProps(dropTargetKey, toRaw(treeNodeRequiredProps.value))), {
        active: ((_a = activeItem.value) === null || _a === void 0 ? void 0 : _a.key) === dropTargetKey,
        data: keyEntities.value[dropTargetKey].node
      });
      const dropToChild = dragChildrenKeys.indexOf(dropTargetKey) !== -1;
      warning(!dropToChild, "Can not drop to dragNode's children node. Maybe this is a bug of ant-design-vue. Please report an issue.");
      const posArr = posToArr(dropTargetPos);
      const dropResult = {
        event,
        node: convertNodePropsToEventData(abstractDropNodeProps),
        dragNode: dragNode ? dragNode.eventData : null,
        dragNodesKeys: [dragNode.eventKey].concat(dragChildrenKeys),
        dropToGap: dropPosition !== 0,
        dropPosition: dropPosition + Number(posArr[posArr.length - 1])
      };
      if (!outsideTree) {
        onDrop === null || onDrop === void 0 ? void 0 : onDrop(dropResult);
      }
      dragNode = null;
    };
    const triggerExpandActionExpand = (e, treeNode) => {
      const {
        expanded,
        key
      } = treeNode;
      const node = flattenNodes.value.filter((nodeItem) => nodeItem.key === key)[0];
      const eventNode = convertNodePropsToEventData(_extends(_extends({}, getTreeNodeProps(key, treeNodeRequiredProps.value)), {
        data: node.data
      }));
      setExpandedKeys(expanded ? arrDel(expandedKeys.value, key) : arrAdd(expandedKeys.value, key));
      onNodeExpand(e, eventNode);
    };
    const onNodeClick = (e, treeNode) => {
      const {
        onClick,
        expandAction
      } = props;
      if (expandAction === "click") {
        triggerExpandActionExpand(e, treeNode);
      }
      if (onClick) {
        onClick(e, treeNode);
      }
    };
    const onNodeDoubleClick = (e, treeNode) => {
      const {
        onDblclick,
        expandAction
      } = props;
      if (expandAction === "doubleclick" || expandAction === "dblclick") {
        triggerExpandActionExpand(e, treeNode);
      }
      if (onDblclick) {
        onDblclick(e, treeNode);
      }
    };
    const onNodeSelect = (e, treeNode) => {
      let newSelectedKeys = selectedKeys.value;
      const {
        onSelect,
        multiple
      } = props;
      const {
        selected
      } = treeNode;
      const key = treeNode[fieldNames.value.key];
      const targetSelected = !selected;
      if (!targetSelected) {
        newSelectedKeys = arrDel(newSelectedKeys, key);
      } else if (!multiple) {
        newSelectedKeys = [key];
      } else {
        newSelectedKeys = arrAdd(newSelectedKeys, key);
      }
      const keyEntitiesValue = keyEntities.value;
      const selectedNodes = newSelectedKeys.map((selectedKey) => {
        const entity = keyEntitiesValue[selectedKey];
        if (!entity) return null;
        return entity.node;
      }).filter((node) => node);
      if (props.selectedKeys === void 0) {
        selectedKeys.value = newSelectedKeys;
      }
      if (onSelect) {
        onSelect(newSelectedKeys, {
          event: "select",
          selected: targetSelected,
          node: treeNode,
          selectedNodes,
          nativeEvent: e
        });
      }
    };
    const onNodeCheck = (e, treeNode, checked) => {
      const {
        checkStrictly,
        onCheck
      } = props;
      const key = treeNode[fieldNames.value.key];
      let checkedObj;
      const eventObj = {
        event: "check",
        node: treeNode,
        checked,
        nativeEvent: e
      };
      const keyEntitiesValue = keyEntities.value;
      if (checkStrictly) {
        const newCheckedKeys = checked ? arrAdd(checkedKeys.value, key) : arrDel(checkedKeys.value, key);
        const newHalfCheckedKeys = arrDel(halfCheckedKeys.value, key);
        checkedObj = {
          checked: newCheckedKeys,
          halfChecked: newHalfCheckedKeys
        };
        eventObj.checkedNodes = newCheckedKeys.map((checkedKey) => keyEntitiesValue[checkedKey]).filter((entity) => entity).map((entity) => entity.node);
        if (props.checkedKeys === void 0) {
          checkedKeys.value = newCheckedKeys;
        }
      } else {
        let {
          checkedKeys: newCheckedKeys,
          halfCheckedKeys: newHalfCheckedKeys
        } = conductCheck([...checkedKeys.value, key], true, keyEntitiesValue, maxLevel.value, levelEntities.value);
        if (!checked) {
          const keySet = new Set(newCheckedKeys);
          keySet.delete(key);
          ({
            checkedKeys: newCheckedKeys,
            halfCheckedKeys: newHalfCheckedKeys
          } = conductCheck(Array.from(keySet), {
            checked: false,
            halfCheckedKeys: newHalfCheckedKeys
          }, keyEntitiesValue, maxLevel.value, levelEntities.value));
        }
        checkedObj = newCheckedKeys;
        eventObj.checkedNodes = [];
        eventObj.checkedNodesPositions = [];
        eventObj.halfCheckedKeys = newHalfCheckedKeys;
        newCheckedKeys.forEach((checkedKey) => {
          const entity = keyEntitiesValue[checkedKey];
          if (!entity) return;
          const {
            node,
            pos
          } = entity;
          eventObj.checkedNodes.push(node);
          eventObj.checkedNodesPositions.push({
            node,
            pos
          });
        });
        if (props.checkedKeys === void 0) {
          checkedKeys.value = newCheckedKeys;
          halfCheckedKeys.value = newHalfCheckedKeys;
        }
      }
      if (onCheck) {
        onCheck(checkedObj, eventObj);
      }
    };
    const onNodeLoad = (treeNode) => {
      const key = treeNode[fieldNames.value.key];
      const loadPromise = new Promise((resolve, reject) => {
        const {
          loadData,
          onLoad
        } = props;
        if (!loadData || loadedKeysSet.value.has(key) || loadingKeysSet.value.has(key)) {
          return null;
        }
        const promise = loadData(treeNode);
        promise.then(() => {
          const newLoadedKeys = arrAdd(loadedKeys.value, key);
          const newLoadingKeys = arrDel(loadingKeys.value, key);
          if (onLoad) {
            onLoad(newLoadedKeys, {
              event: "load",
              node: treeNode
            });
          }
          if (props.loadedKeys === void 0) {
            loadedKeys.value = newLoadedKeys;
          }
          loadingKeys.value = newLoadingKeys;
          resolve();
        }).catch((e) => {
          const newLoadingKeys = arrDel(loadingKeys.value, key);
          loadingKeys.value = newLoadingKeys;
          loadingRetryTimes[key] = (loadingRetryTimes[key] || 0) + 1;
          if (loadingRetryTimes[key] >= MAX_RETRY_TIMES) {
            warning(false, "Retry for `loadData` many times but still failed. No more retry.");
            const newLoadedKeys = arrAdd(loadedKeys.value, key);
            if (props.loadedKeys === void 0) {
              loadedKeys.value = newLoadedKeys;
            }
            resolve();
          }
          reject(e);
        });
        loadingKeys.value = arrAdd(loadingKeys.value, key);
      });
      loadPromise.catch(() => {
      });
      return loadPromise;
    };
    const onNodeMouseEnter = (event, node) => {
      const {
        onMouseenter
      } = props;
      if (onMouseenter) {
        onMouseenter({
          event,
          node
        });
      }
    };
    const onNodeMouseLeave = (event, node) => {
      const {
        onMouseleave
      } = props;
      if (onMouseleave) {
        onMouseleave({
          event,
          node
        });
      }
    };
    const onNodeContextMenu = (event, node) => {
      const {
        onRightClick
      } = props;
      if (onRightClick) {
        event.preventDefault();
        onRightClick({
          event,
          node
        });
      }
    };
    const onFocus = (e) => {
      const {
        onFocus: onFocus2
      } = props;
      focused.value = true;
      if (onFocus2) {
        onFocus2(e);
      }
    };
    const onBlur = (e) => {
      const {
        onBlur: onBlur2
      } = props;
      focused.value = false;
      onActiveChange(null);
      if (onBlur2) {
        onBlur2(e);
      }
    };
    const onNodeExpand = (e, treeNode) => {
      let newExpandedKeys = expandedKeys.value;
      const {
        onExpand,
        loadData
      } = props;
      const {
        expanded
      } = treeNode;
      const key = treeNode[fieldNames.value.key];
      if (listChanging.value) {
        return;
      }
      const index = newExpandedKeys.indexOf(key);
      const targetExpanded = !expanded;
      warning(expanded && index !== -1 || !expanded && index === -1, "Expand state not sync with index check");
      if (targetExpanded) {
        newExpandedKeys = arrAdd(newExpandedKeys, key);
      } else {
        newExpandedKeys = arrDel(newExpandedKeys, key);
      }
      setExpandedKeys(newExpandedKeys);
      if (onExpand) {
        onExpand(newExpandedKeys, {
          node: treeNode,
          expanded: targetExpanded,
          nativeEvent: e
        });
      }
      if (targetExpanded && loadData) {
        const loadPromise = onNodeLoad(treeNode);
        if (loadPromise) {
          loadPromise.then(() => {
          }).catch((e2) => {
            const expandedKeysToRestore = arrDel(expandedKeys.value, key);
            setExpandedKeys(expandedKeysToRestore);
            Promise.reject(e2);
          });
        }
      }
    };
    const onListChangeStart = () => {
      listChanging.value = true;
    };
    const onListChangeEnd = () => {
      setTimeout(() => {
        listChanging.value = false;
      });
    };
    const onActiveChange = (newActiveKey) => {
      const {
        onActiveChange: onActiveChange2
      } = props;
      if (activeKey.value === newActiveKey) {
        return;
      }
      if (props.activeKey !== void 0) {
        activeKey.value = newActiveKey;
      }
      if (newActiveKey !== null) {
        scrollTo({
          key: newActiveKey
        });
      }
      if (onActiveChange2) {
        onActiveChange2(newActiveKey);
      }
    };
    const activeItem = computed(() => {
      if (activeKey.value === null) {
        return null;
      }
      return flattenNodes.value.find((_ref4) => {
        let {
          key
        } = _ref4;
        return key === activeKey.value;
      }) || null;
    });
    const offsetActiveKey = (offset) => {
      let index = flattenNodes.value.findIndex((_ref5) => {
        let {
          key
        } = _ref5;
        return key === activeKey.value;
      });
      if (index === -1 && offset < 0) {
        index = flattenNodes.value.length;
      }
      index = (index + offset + flattenNodes.value.length) % flattenNodes.value.length;
      const item = flattenNodes.value[index];
      if (item) {
        const {
          key
        } = item;
        onActiveChange(key);
      } else {
        onActiveChange(null);
      }
    };
    const activeItemEventNode = computed(() => {
      return convertNodePropsToEventData(_extends(_extends({}, getTreeNodeProps(activeKey.value, treeNodeRequiredProps.value)), {
        data: activeItem.value.data,
        active: true
      }));
    });
    const onKeydown = (event) => {
      const {
        onKeydown: onKeydown2,
        checkable,
        selectable
      } = props;
      switch (event.which) {
        case KeyCode_default.UP: {
          offsetActiveKey(-1);
          event.preventDefault();
          break;
        }
        case KeyCode_default.DOWN: {
          offsetActiveKey(1);
          event.preventDefault();
          break;
        }
      }
      const item = activeItem.value;
      if (item && item.data) {
        const expandable = item.data.isLeaf === false || !!(item.data.children || []).length;
        const eventNode = activeItemEventNode.value;
        switch (event.which) {
          // >>> Expand
          case KeyCode_default.LEFT: {
            if (expandable && expandedKeysSet.value.has(activeKey.value)) {
              onNodeExpand({}, eventNode);
            } else if (item.parent) {
              onActiveChange(item.parent.key);
            }
            event.preventDefault();
            break;
          }
          case KeyCode_default.RIGHT: {
            if (expandable && !expandedKeysSet.value.has(activeKey.value)) {
              onNodeExpand({}, eventNode);
            } else if (item.children && item.children.length) {
              onActiveChange(item.children[0].key);
            }
            event.preventDefault();
            break;
          }
          // Selection
          case KeyCode_default.ENTER:
          case KeyCode_default.SPACE: {
            if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {
              onNodeCheck({}, eventNode, !checkedKeysSet.value.has(activeKey.value));
            } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {
              onNodeSelect({}, eventNode);
            }
            break;
          }
        }
      }
      if (onKeydown2) {
        onKeydown2(event);
      }
    };
    expose({
      onNodeExpand,
      scrollTo,
      onKeydown,
      selectedKeys: computed(() => selectedKeys.value),
      checkedKeys: computed(() => checkedKeys.value),
      halfCheckedKeys: computed(() => halfCheckedKeys.value),
      loadedKeys: computed(() => loadedKeys.value),
      loadingKeys: computed(() => loadingKeys.value),
      expandedKeys: computed(() => expandedKeys.value)
    });
    onUnmounted(() => {
      window.removeEventListener("dragend", onWindowDragEnd);
      destroyed.value = true;
    });
    useProvideKeysState({
      expandedKeys,
      selectedKeys,
      loadedKeys,
      loadingKeys,
      checkedKeys,
      halfCheckedKeys,
      expandedKeysSet,
      selectedKeysSet,
      loadedKeysSet,
      loadingKeysSet,
      checkedKeysSet,
      halfCheckedKeysSet,
      flattenNodes
    });
    return () => {
      const {
        // focused,
        // flattenNodes,
        // keyEntities,
        draggingNodeKey,
        // activeKey,
        dropLevelOffset,
        dropContainerKey,
        dropTargetKey,
        dropPosition,
        dragOverNodeKey
        // indent,
      } = dragState;
      const {
        prefixCls,
        showLine,
        focusable,
        tabindex = 0,
        selectable,
        showIcon,
        icon = slots.icon,
        switcherIcon,
        draggable,
        checkable,
        checkStrictly,
        disabled,
        motion,
        loadData,
        filterTreeNode,
        height,
        itemHeight,
        virtual,
        dropIndicatorRender,
        onContextmenu,
        onScroll,
        direction,
        rootClassName,
        rootStyle
      } = props;
      const {
        class: className,
        style
      } = attrs;
      const domProps = pickAttrs(_extends(_extends({}, props), attrs), {
        aria: true,
        data: true
      });
      let draggableConfig;
      if (draggable) {
        if (typeof draggable === "object") {
          draggableConfig = draggable;
        } else if (typeof draggable === "function") {
          draggableConfig = {
            nodeDraggable: draggable
          };
        } else {
          draggableConfig = {};
        }
      } else {
        draggableConfig = false;
      }
      return createVNode(TreeContext, {
        "value": {
          prefixCls,
          selectable,
          showIcon,
          icon,
          switcherIcon,
          draggable: draggableConfig,
          draggingNodeKey,
          checkable,
          customCheckable: slots.checkable,
          checkStrictly,
          disabled,
          keyEntities: keyEntities.value,
          dropLevelOffset,
          dropContainerKey,
          dropTargetKey,
          dropPosition,
          dragOverNodeKey,
          dragging: draggingNodeKey !== null,
          indent: indent.value,
          direction,
          dropIndicatorRender,
          loadData,
          filterTreeNode,
          onNodeClick,
          onNodeDoubleClick,
          onNodeExpand,
          onNodeSelect,
          onNodeCheck,
          onNodeLoad,
          onNodeMouseEnter,
          onNodeMouseLeave,
          onNodeContextMenu,
          onNodeDragStart,
          onNodeDragEnter,
          onNodeDragOver,
          onNodeDragLeave,
          onNodeDragEnd,
          onNodeDrop,
          slots
        }
      }, {
        default: () => [createVNode("div", {
          "role": "tree",
          "class": classNames_default(prefixCls, className, rootClassName, {
            [`${prefixCls}-show-line`]: showLine,
            [`${prefixCls}-focused`]: focused.value,
            [`${prefixCls}-active-focused`]: activeKey.value !== null
          }),
          "style": rootStyle
        }, [createVNode(NodeList_default, _objectSpread2({
          "ref": listRef,
          "prefixCls": prefixCls,
          "style": style,
          "disabled": disabled,
          "selectable": selectable,
          "checkable": !!checkable,
          "motion": motion,
          "height": height,
          "itemHeight": itemHeight,
          "virtual": virtual,
          "focusable": focusable,
          "focused": focused.value,
          "tabindex": tabindex,
          "activeItem": activeItem.value,
          "onFocus": onFocus,
          "onBlur": onBlur,
          "onKeydown": onKeydown,
          "onActiveChange": onActiveChange,
          "onListChangeStart": onListChangeStart,
          "onListChangeEnd": onListChangeEnd,
          "onContextmenu": onContextmenu,
          "onScroll": onScroll
        }, domProps), null)])]
      });
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/utils/valueUtil.js
function toArray(value) {
  if (Array.isArray(value)) {
    return value;
  }
  return value !== void 0 ? [value] : [];
}
function fillFieldNames2(fieldNames) {
  const {
    label,
    value,
    children
  } = fieldNames || {};
  const mergedValue = value || "value";
  return {
    _title: label ? [label] : ["title", "label"],
    value: mergedValue,
    key: mergedValue,
    children: children || "children"
  };
}
function isCheckDisabled2(node) {
  return node.disabled || node.disableCheckbox || node.checkable === false;
}
function getAllKeys(treeData, fieldNames) {
  const keys = [];
  function dig(list) {
    list.forEach((item) => {
      keys.push(item[fieldNames.value]);
      const children = item[fieldNames.children];
      if (children) {
        dig(children);
      }
    });
  }
  dig(treeData);
  return keys;
}
function isNil(val) {
  return val === null || val === void 0;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/TreeSelectContext.js
var TreeSelectContextPropsKey = Symbol("TreeSelectContextPropsKey");
function useProvideSelectContext(props) {
  return provide(TreeSelectContextPropsKey, props);
}
function useInjectSelectContext() {
  return inject(TreeSelectContextPropsKey, {});
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/OptionList.js
var HIDDEN_STYLE2 = {
  width: 0,
  height: 0,
  display: "flex",
  overflow: "hidden",
  opacity: 0,
  border: 0,
  padding: 0,
  margin: 0
};
var OptionList_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "OptionList",
  inheritAttrs: false,
  setup(_, _ref) {
    let {
      slots,
      expose
    } = _ref;
    const baseProps = useBaseProps();
    const legacyContext = useInjectLegacySelectContext();
    const context = useInjectSelectContext();
    const treeRef = ref();
    const memoTreeData = useMemo(() => context.treeData, [() => baseProps.open, () => context.treeData], (next) => next[0]);
    const mergedCheckedKeys = computed(() => {
      const {
        checkable,
        halfCheckedKeys,
        checkedKeys
      } = legacyContext;
      if (!checkable) {
        return null;
      }
      return {
        checked: checkedKeys,
        halfChecked: halfCheckedKeys
      };
    });
    watch(() => baseProps.open, () => {
      nextTick(() => {
        var _a;
        if (baseProps.open && !baseProps.multiple && legacyContext.checkedKeys.length) {
          (_a = treeRef.value) === null || _a === void 0 ? void 0 : _a.scrollTo({
            key: legacyContext.checkedKeys[0]
          });
        }
      });
    }, {
      immediate: true,
      flush: "post"
    });
    const lowerSearchValue = computed(() => String(baseProps.searchValue).toLowerCase());
    const filterTreeNode = (treeNode) => {
      if (!lowerSearchValue.value) {
        return false;
      }
      return String(treeNode[legacyContext.treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue.value);
    };
    const expandedKeys = shallowRef(legacyContext.treeDefaultExpandedKeys);
    const searchExpandedKeys = shallowRef(null);
    watch(() => baseProps.searchValue, () => {
      if (baseProps.searchValue) {
        searchExpandedKeys.value = getAllKeys(toRaw(context.treeData), toRaw(context.fieldNames));
      }
    }, {
      immediate: true
    });
    const mergedExpandedKeys = computed(() => {
      if (legacyContext.treeExpandedKeys) {
        return legacyContext.treeExpandedKeys.slice();
      }
      return baseProps.searchValue ? searchExpandedKeys.value : expandedKeys.value;
    });
    const onInternalExpand = (keys) => {
      var _a;
      expandedKeys.value = keys;
      searchExpandedKeys.value = keys;
      (_a = legacyContext.onTreeExpand) === null || _a === void 0 ? void 0 : _a.call(legacyContext, keys);
    };
    const onListMouseDown = (event) => {
      event.preventDefault();
    };
    const onInternalSelect = (_2, _ref2) => {
      let {
        node
      } = _ref2;
      var _a, _b;
      const {
        checkable,
        checkedKeys
      } = legacyContext;
      if (checkable && isCheckDisabled2(node)) {
        return;
      }
      (_a = context.onSelect) === null || _a === void 0 ? void 0 : _a.call(context, node.key, {
        selected: !checkedKeys.includes(node.key)
      });
      if (!baseProps.multiple) {
        (_b = baseProps.toggleOpen) === null || _b === void 0 ? void 0 : _b.call(baseProps, false);
      }
    };
    const activeKey = ref(null);
    const activeEntity = computed(() => legacyContext.keyEntities[activeKey.value]);
    const setActiveKey = (key) => {
      activeKey.value = key;
    };
    expose({
      scrollTo: function() {
        var _a, _b;
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        return (_b = (_a = treeRef.value) === null || _a === void 0 ? void 0 : _a.scrollTo) === null || _b === void 0 ? void 0 : _b.call(_a, ...args);
      },
      onKeydown: (event) => {
        var _a;
        const {
          which
        } = event;
        switch (which) {
          // >>> Arrow keys
          case KeyCode_default.UP:
          case KeyCode_default.DOWN:
          case KeyCode_default.LEFT:
          case KeyCode_default.RIGHT:
            (_a = treeRef.value) === null || _a === void 0 ? void 0 : _a.onKeydown(event);
            break;
          // >>> Select item
          case KeyCode_default.ENTER: {
            if (activeEntity.value) {
              const {
                selectable,
                value
              } = activeEntity.value.node || {};
              if (selectable !== false) {
                onInternalSelect(null, {
                  node: {
                    key: activeKey.value
                  },
                  selected: !legacyContext.checkedKeys.includes(value)
                });
              }
            }
            break;
          }
          // >>> Close
          case KeyCode_default.ESC: {
            baseProps.toggleOpen(false);
          }
        }
      },
      onKeyup: () => {
      }
    });
    return () => {
      var _a;
      const {
        prefixCls,
        multiple,
        searchValue,
        open,
        notFoundContent = (_a = slots.notFoundContent) === null || _a === void 0 ? void 0 : _a.call(slots)
      } = baseProps;
      const {
        listHeight,
        listItemHeight,
        virtual,
        dropdownMatchSelectWidth,
        treeExpandAction
      } = context;
      const {
        checkable,
        treeDefaultExpandAll,
        treeIcon,
        showTreeIcon,
        switcherIcon,
        treeLine,
        loadData,
        treeLoadedKeys,
        treeMotion,
        onTreeLoad,
        checkedKeys
      } = legacyContext;
      if (memoTreeData.value.length === 0) {
        return createVNode("div", {
          "role": "listbox",
          "class": `${prefixCls}-empty`,
          "onMousedown": onListMouseDown
        }, [notFoundContent]);
      }
      const treeProps2 = {
        fieldNames: context.fieldNames
      };
      if (treeLoadedKeys) {
        treeProps2.loadedKeys = treeLoadedKeys;
      }
      if (mergedExpandedKeys.value) {
        treeProps2.expandedKeys = mergedExpandedKeys.value;
      }
      return createVNode("div", {
        "onMousedown": onListMouseDown
      }, [activeEntity.value && open && createVNode("span", {
        "style": HIDDEN_STYLE2,
        "aria-live": "assertive"
      }, [activeEntity.value.node.value]), createVNode(Tree_default, _objectSpread2(_objectSpread2({
        "ref": treeRef,
        "focusable": false,
        "prefixCls": `${prefixCls}-tree`,
        "treeData": memoTreeData.value,
        "height": listHeight,
        "itemHeight": listItemHeight,
        "virtual": virtual !== false && dropdownMatchSelectWidth !== false,
        "multiple": multiple,
        "icon": treeIcon,
        "showIcon": showTreeIcon,
        "switcherIcon": switcherIcon,
        "showLine": treeLine,
        "loadData": searchValue ? null : loadData,
        "motion": treeMotion,
        "activeKey": activeKey.value,
        "checkable": checkable,
        "checkStrictly": true,
        "checkedKeys": mergedCheckedKeys.value,
        "selectedKeys": !checkable ? checkedKeys : [],
        "defaultExpandAll": treeDefaultExpandAll
      }, treeProps2), {}, {
        "onActiveChange": setActiveKey,
        "onSelect": onInternalSelect,
        "onCheck": onInternalSelect,
        "onExpand": onInternalExpand,
        "onLoad": onTreeLoad,
        "filterTreeNode": filterTreeNode,
        "expandAction": treeExpandAction
      }), _extends(_extends({}, slots), {
        checkable: legacyContext.customSlots.treeCheckable
      }))]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/utils/strategyUtil.js
var SHOW_ALL = "SHOW_ALL";
var SHOW_PARENT = "SHOW_PARENT";
var SHOW_CHILD = "SHOW_CHILD";
function formatStrategyValues(values, strategy, keyEntities, fieldNames) {
  const valueSet = new Set(values);
  if (strategy === SHOW_CHILD) {
    return values.filter((key) => {
      const entity = keyEntities[key];
      if (entity && entity.children && entity.children.some((_ref) => {
        let {
          node
        } = _ref;
        return valueSet.has(node[fieldNames.value]);
      }) && entity.children.every((_ref2) => {
        let {
          node
        } = _ref2;
        return isCheckDisabled2(node) || valueSet.has(node[fieldNames.value]);
      })) {
        return false;
      }
      return true;
    });
  }
  if (strategy === SHOW_PARENT) {
    return values.filter((key) => {
      const entity = keyEntities[key];
      const parent = entity ? entity.parent : null;
      if (parent && !isCheckDisabled2(parent.node) && valueSet.has(parent.key)) {
        return false;
      }
      return true;
    });
  }
  return values;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/TreeNode.js
var TreeNode = () => null;
TreeNode.inheritAttrs = false;
TreeNode.displayName = "ATreeSelectNode";
TreeNode.isTreeSelectNode = true;
var TreeNode_default2 = TreeNode;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/utils/legacyUtil.js
var __rest5 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
function isTreeSelectNode(node) {
  return node && node.type && node.type.isTreeSelectNode;
}
function convertChildrenToData(rootNodes) {
  function dig() {
    let treeNodes = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
    return filterEmpty(treeNodes).map((treeNode) => {
      var _a, _b, _c;
      if (!isTreeSelectNode(treeNode)) {
        warning(!treeNode, "TreeSelect/TreeSelectNode can only accept TreeSelectNode as children.");
        return null;
      }
      const slots = treeNode.children || {};
      const key = treeNode.key;
      const props = {};
      for (const [k, v] of Object.entries(treeNode.props)) {
        props[camelize(k)] = v;
      }
      const {
        isLeaf,
        checkable,
        selectable,
        disabled,
        disableCheckbox
      } = props;
      const newProps = {
        isLeaf: isLeaf || isLeaf === "" || void 0,
        checkable: checkable || checkable === "" || void 0,
        selectable: selectable || selectable === "" || void 0,
        disabled: disabled || disabled === "" || void 0,
        disableCheckbox: disableCheckbox || disableCheckbox === "" || void 0
      };
      const slotsProps = _extends(_extends({}, props), newProps);
      const {
        title = (_a = slots.title) === null || _a === void 0 ? void 0 : _a.call(slots, slotsProps),
        switcherIcon = (_b = slots.switcherIcon) === null || _b === void 0 ? void 0 : _b.call(slots, slotsProps)
      } = props, rest = __rest5(props, ["title", "switcherIcon"]);
      const children = (_c = slots.default) === null || _c === void 0 ? void 0 : _c.call(slots);
      const dataNode = _extends(_extends(_extends({}, rest), {
        title,
        switcherIcon,
        key,
        isLeaf
      }), newProps);
      const parsedChildren = dig(children);
      if (parsedChildren.length) {
        dataNode.children = parsedChildren;
      }
      return dataNode;
    });
  }
  return dig(rootNodes);
}
function fillLegacyProps(dataNode) {
  if (!dataNode) {
    return dataNode;
  }
  const cloneNode = _extends({}, dataNode);
  if (!("props" in cloneNode)) {
    Object.defineProperty(cloneNode, "props", {
      get() {
        warning(false, "New `vc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.");
        return cloneNode;
      }
    });
  }
  return cloneNode;
}
function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {
  let triggerNode = null;
  let nodeList = null;
  function generateMap() {
    function dig(list) {
      let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "0";
      let parentIncluded = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
      return list.map((option, index) => {
        const pos = `${level}-${index}`;
        const value = option[fieldNames.value];
        const included = checkedValues.includes(value);
        const children = dig(option[fieldNames.children] || [], pos, included);
        const node = createVNode(TreeNode_default2, option, {
          default: () => [children.map((child) => child.node)]
        });
        if (triggerValue === value) {
          triggerNode = node;
        }
        if (included) {
          const checkedNode = {
            pos,
            node,
            children
          };
          if (!parentIncluded) {
            nodeList.push(checkedNode);
          }
          return checkedNode;
        }
        return null;
      }).filter((node) => node);
    }
    if (!nodeList) {
      nodeList = [];
      dig(treeData);
      nodeList.sort((_ref, _ref2) => {
        let {
          node: {
            props: {
              value: val1
            }
          }
        } = _ref;
        let {
          node: {
            props: {
              value: val2
            }
          }
        } = _ref2;
        const index1 = checkedValues.indexOf(val1);
        const index2 = checkedValues.indexOf(val2);
        return index1 - index2;
      });
    }
  }
  Object.defineProperty(extra, "triggerNode", {
    get() {
      warning(false, "`triggerNode` is deprecated. Please consider decoupling data with node.");
      generateMap();
      return triggerNode;
    }
  });
  Object.defineProperty(extra, "allCheckedNodes", {
    get() {
      warning(false, "`allCheckedNodes` is deprecated. Please consider decoupling data with node.");
      generateMap();
      if (showPosition) {
        return nodeList;
      }
      return nodeList.map((_ref3) => {
        let {
          node
        } = _ref3;
        return node;
      });
    }
  });
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/hooks/useTreeData.js
function parseSimpleTreeData(treeData, _ref) {
  let {
    id,
    pId,
    rootPId
  } = _ref;
  const keyNodes = {};
  const rootNodeList = [];
  const nodeList = treeData.map((node) => {
    const clone = _extends({}, node);
    const key = clone[id];
    keyNodes[key] = clone;
    clone.key = clone.key || key;
    return clone;
  });
  nodeList.forEach((node) => {
    const parentKey = node[pId];
    const parent = keyNodes[parentKey];
    if (parent) {
      parent.children = parent.children || [];
      parent.children.push(node);
    }
    if (parentKey === rootPId || !parent && rootPId === null) {
      rootNodeList.push(node);
    }
  });
  return rootNodeList;
}
function useTreeData(treeData, children, simpleMode) {
  const mergedTreeData = shallowRef();
  watch([simpleMode, treeData, children], () => {
    const simpleModeValue = simpleMode.value;
    if (treeData.value) {
      mergedTreeData.value = simpleMode.value ? parseSimpleTreeData(toRaw(treeData.value), _extends({
        id: "id",
        pId: "pId",
        rootPId: null
      }, simpleModeValue !== true ? simpleModeValue : {})) : toRaw(treeData.value).slice();
    } else {
      mergedTreeData.value = convertChildrenToData(toRaw(children.value));
    }
  }, {
    immediate: true,
    deep: true
  });
  return mergedTreeData;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/hooks/useCache.js
var useCache_default = (values) => {
  const cacheRef = shallowRef({
    valueLabels: /* @__PURE__ */ new Map()
  });
  const mergedValues = shallowRef();
  watch(values, () => {
    mergedValues.value = toRaw(values.value);
  }, {
    immediate: true
  });
  const newFilledValues = computed(() => {
    const {
      valueLabels
    } = cacheRef.value;
    const valueLabelsCache = /* @__PURE__ */ new Map();
    const filledValues = mergedValues.value.map((item) => {
      var _a;
      const {
        value
      } = item;
      const mergedLabel = (_a = item.label) !== null && _a !== void 0 ? _a : valueLabels.get(value);
      valueLabelsCache.set(value, mergedLabel);
      return _extends(_extends({}, item), {
        label: mergedLabel
      });
    });
    cacheRef.value.valueLabels = valueLabelsCache;
    return filledValues;
  });
  return [newFilledValues];
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/hooks/useDataEntities.js
var useDataEntities_default = (treeData, fieldNames) => {
  const valueEntities = shallowRef(/* @__PURE__ */ new Map());
  const keyEntities = shallowRef({});
  watchEffect(() => {
    const fieldNamesValue = fieldNames.value;
    const collection = convertDataToEntities(treeData.value, {
      fieldNames: fieldNamesValue,
      initWrapper: (wrapper) => _extends(_extends({}, wrapper), {
        valueEntities: /* @__PURE__ */ new Map()
      }),
      processEntity: (entity, wrapper) => {
        const val = entity.node[fieldNamesValue.value];
        if (true) {
          const key = entity.node.key;
          warning(!isNil(val), "TreeNode `value` is invalidate: undefined");
          warning(!wrapper.valueEntities.has(val), `Same \`value\` exist in the tree: ${val}`);
          warning(!key || String(key) === String(val), `\`key\` or \`value\` with TreeNode must be the same or you can remove one of them. key: ${key}, value: ${val}.`);
        }
        wrapper.valueEntities.set(val, entity);
      }
    });
    valueEntities.value = collection.valueEntities;
    keyEntities.value = collection.keyEntities;
  });
  return {
    valueEntities,
    keyEntities
  };
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/hooks/useCheckedKeys.js
var useCheckedKeys_default = (rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities, maxLevel, levelEntities) => {
  const newRawCheckedValues = shallowRef([]);
  const newRawHalfCheckedValues = shallowRef([]);
  watchEffect(() => {
    let checkedKeys = rawLabeledValues.value.map((_ref) => {
      let {
        value
      } = _ref;
      return value;
    });
    let halfCheckedKeys = rawHalfCheckedValues.value.map((_ref2) => {
      let {
        value
      } = _ref2;
      return value;
    });
    const missingValues = checkedKeys.filter((key) => !keyEntities.value[key]);
    if (treeConduction.value) {
      ({
        checkedKeys,
        halfCheckedKeys
      } = conductCheck(checkedKeys, true, keyEntities.value, maxLevel.value, levelEntities.value));
    }
    newRawCheckedValues.value = Array.from(/* @__PURE__ */ new Set([...missingValues, ...checkedKeys]));
    newRawHalfCheckedValues.value = halfCheckedKeys;
  });
  return [newRawCheckedValues, newRawHalfCheckedValues];
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/hooks/useFilterTreeData.js
var useFilterTreeData_default = (treeData, searchValue, _ref) => {
  let {
    treeNodeFilterProp,
    filterTreeNode,
    fieldNames
  } = _ref;
  return computed(() => {
    const {
      children: fieldChildren
    } = fieldNames.value;
    const searchValueVal = searchValue.value;
    const treeNodeFilterPropValue = treeNodeFilterProp === null || treeNodeFilterProp === void 0 ? void 0 : treeNodeFilterProp.value;
    if (!searchValueVal || filterTreeNode.value === false) {
      return treeData.value;
    }
    let filterOptionFunc;
    if (typeof filterTreeNode.value === "function") {
      filterOptionFunc = filterTreeNode.value;
    } else {
      const upperStr = searchValueVal.toUpperCase();
      filterOptionFunc = (_, dataNode) => {
        const value = dataNode[treeNodeFilterPropValue];
        return String(value).toUpperCase().includes(upperStr);
      };
    }
    function dig(list) {
      let keepAll = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
      const res = [];
      for (let index = 0, len = list.length; index < len; index++) {
        const dataNode = list[index];
        const children = dataNode[fieldChildren];
        const match = keepAll || filterOptionFunc(searchValueVal, fillLegacyProps(dataNode));
        const childList = dig(children || [], match);
        if (match || childList.length) {
          res.push(_extends(_extends({}, dataNode), {
            [fieldChildren]: childList
          }));
        }
      }
      return res;
    }
    return dig(treeData.value);
  });
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/utils/warningPropsUtil.js
function warningProps(props) {
  const {
    searchPlaceholder,
    treeCheckStrictly,
    treeCheckable,
    labelInValue,
    value,
    multiple
  } = props;
  warning(!searchPlaceholder, "`searchPlaceholder` has been removed, please use `placeholder` instead");
  if (treeCheckStrictly && labelInValue === false) {
    warning(false, "`treeCheckStrictly` will force set `labelInValue` to `true`.");
  }
  if (labelInValue || treeCheckStrictly) {
    warning(toArray(value).every((val) => val && typeof val === "object" && "value" in val), "Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.");
  }
  if (treeCheckStrictly || multiple || treeCheckable) {
    warning(!value || Array.isArray(value), "`value` should be an array when `TreeSelect` is checkable or multiple.");
  } else {
    warning(!Array.isArray(value), "`value` should not be array when `TreeSelect` is single mode.");
  }
}
var warningPropsUtil_default = warningProps;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/TreeSelect.js
function treeSelectProps() {
  return _extends(_extends({}, omit_default(baseSelectPropsWithoutPrivate(), ["mode"])), {
    prefixCls: String,
    id: String,
    value: {
      type: [String, Number, Object, Array]
    },
    defaultValue: {
      type: [String, Number, Object, Array]
    },
    onChange: {
      type: Function
    },
    searchValue: String,
    /** @deprecated Use `searchValue` instead */
    inputValue: String,
    onSearch: {
      type: Function
    },
    autoClearSearchValue: {
      type: Boolean,
      default: void 0
    },
    filterTreeNode: {
      type: [Boolean, Function],
      default: void 0
    },
    treeNodeFilterProp: String,
    // >>> Select
    onSelect: Function,
    onDeselect: Function,
    showCheckedStrategy: {
      type: String
    },
    treeNodeLabelProp: String,
    fieldNames: {
      type: Object
    },
    // >>> Mode
    multiple: {
      type: Boolean,
      default: void 0
    },
    treeCheckable: {
      type: Boolean,
      default: void 0
    },
    treeCheckStrictly: {
      type: Boolean,
      default: void 0
    },
    labelInValue: {
      type: Boolean,
      default: void 0
    },
    // >>> Data
    treeData: {
      type: Array
    },
    treeDataSimpleMode: {
      type: [Boolean, Object],
      default: void 0
    },
    loadData: {
      type: Function
    },
    treeLoadedKeys: {
      type: Array
    },
    onTreeLoad: {
      type: Function
    },
    // >>> Expanded
    treeDefaultExpandAll: {
      type: Boolean,
      default: void 0
    },
    treeExpandedKeys: {
      type: Array
    },
    treeDefaultExpandedKeys: {
      type: Array
    },
    onTreeExpand: {
      type: Function
    },
    // >>> Options
    virtual: {
      type: Boolean,
      default: void 0
    },
    listHeight: Number,
    listItemHeight: Number,
    onDropdownVisibleChange: {
      type: Function
    },
    // >>> Tree
    treeLine: {
      type: [Boolean, Object],
      default: void 0
    },
    treeIcon: vue_types_default.any,
    showTreeIcon: {
      type: Boolean,
      default: void 0
    },
    switcherIcon: vue_types_default.any,
    treeMotion: vue_types_default.any,
    children: Array,
    treeExpandAction: String,
    showArrow: {
      type: Boolean,
      default: void 0
    },
    showSearch: {
      type: Boolean,
      default: void 0
    },
    open: {
      type: Boolean,
      default: void 0
    },
    defaultOpen: {
      type: Boolean,
      default: void 0
    },
    disabled: {
      type: Boolean,
      default: void 0
    },
    placeholder: vue_types_default.any,
    maxTagPlaceholder: {
      type: Function
    },
    dropdownPopupAlign: vue_types_default.any,
    customSlots: Object
  });
}
function isRawValue(value) {
  return !value || typeof value !== "object";
}
var TreeSelect_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "TreeSelect",
  inheritAttrs: false,
  props: initDefaultProps_default(treeSelectProps(), {
    treeNodeFilterProp: "value",
    autoClearSearchValue: true,
    showCheckedStrategy: SHOW_CHILD,
    listHeight: 200,
    listItemHeight: 20,
    prefixCls: "vc-tree-select"
  }),
  setup(props, _ref) {
    let {
      attrs,
      expose,
      slots
    } = _ref;
    const mergedId = useId(toRef(props, "id"));
    const treeConduction = computed(() => props.treeCheckable && !props.treeCheckStrictly);
    const mergedCheckable = computed(() => props.treeCheckable || props.treeCheckStrictly);
    const mergedLabelInValue = computed(() => props.treeCheckStrictly || props.labelInValue);
    const mergedMultiple = computed(() => mergedCheckable.value || props.multiple);
    if (true) {
      watchEffect(() => {
        warningPropsUtil_default(props);
      });
    }
    const mergedFieldNames = computed(() => fillFieldNames2(props.fieldNames));
    const [mergedSearchValue, setSearchValue] = useMergedState("", {
      value: computed(() => props.searchValue !== void 0 ? props.searchValue : props.inputValue),
      postState: (search) => search || ""
    });
    const onInternalSearch = (searchText) => {
      var _a;
      setSearchValue(searchText);
      (_a = props.onSearch) === null || _a === void 0 ? void 0 : _a.call(props, searchText);
    };
    const mergedTreeData = useTreeData(toRef(props, "treeData"), toRef(props, "children"), toRef(props, "treeDataSimpleMode"));
    const {
      keyEntities,
      valueEntities
    } = useDataEntities_default(mergedTreeData, mergedFieldNames);
    const splitRawValues = (newRawValues) => {
      const missingRawValues = [];
      const existRawValues = [];
      newRawValues.forEach((val) => {
        if (valueEntities.value.has(val)) {
          existRawValues.push(val);
        } else {
          missingRawValues.push(val);
        }
      });
      return {
        missingRawValues,
        existRawValues
      };
    };
    const filteredTreeData = useFilterTreeData_default(mergedTreeData, mergedSearchValue, {
      fieldNames: mergedFieldNames,
      treeNodeFilterProp: toRef(props, "treeNodeFilterProp"),
      filterTreeNode: toRef(props, "filterTreeNode")
    });
    const getLabel = (item) => {
      if (item) {
        if (props.treeNodeLabelProp) {
          return item[props.treeNodeLabelProp];
        }
        const {
          _title: titleList
        } = mergedFieldNames.value;
        for (let i = 0; i < titleList.length; i += 1) {
          const title = item[titleList[i]];
          if (title !== void 0) {
            return title;
          }
        }
      }
    };
    const toLabeledValues = (draftValues) => {
      const values = toArray(draftValues);
      return values.map((val) => {
        if (isRawValue(val)) {
          return {
            value: val
          };
        }
        return val;
      });
    };
    const convert2LabelValues = (draftValues) => {
      const values = toLabeledValues(draftValues);
      return values.map((item) => {
        let {
          label: rawLabel
        } = item;
        const {
          value: rawValue,
          halfChecked: rawHalfChecked
        } = item;
        let rawDisabled;
        const entity = valueEntities.value.get(rawValue);
        if (entity) {
          rawLabel = rawLabel !== null && rawLabel !== void 0 ? rawLabel : getLabel(entity.node);
          rawDisabled = entity.node.disabled;
        }
        return {
          label: rawLabel,
          value: rawValue,
          halfChecked: rawHalfChecked,
          disabled: rawDisabled
        };
      });
    };
    const [internalValue, setInternalValue] = useMergedState(props.defaultValue, {
      value: toRef(props, "value")
    });
    const rawMixedLabeledValues = computed(() => toLabeledValues(internalValue.value));
    const rawLabeledValues = shallowRef([]);
    const rawHalfLabeledValues = shallowRef([]);
    watchEffect(() => {
      const fullCheckValues = [];
      const halfCheckValues = [];
      rawMixedLabeledValues.value.forEach((item) => {
        if (item.halfChecked) {
          halfCheckValues.push(item);
        } else {
          fullCheckValues.push(item);
        }
      });
      rawLabeledValues.value = fullCheckValues;
      rawHalfLabeledValues.value = halfCheckValues;
    });
    const rawValues = computed(() => rawLabeledValues.value.map((item) => item.value));
    const {
      maxLevel,
      levelEntities
    } = useMaxLevel(keyEntities);
    const [rawCheckedValues, rawHalfCheckedValues] = useCheckedKeys_default(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities, maxLevel, levelEntities);
    const displayValues = computed(() => {
      const displayKeys = formatStrategyValues(rawCheckedValues.value, props.showCheckedStrategy, keyEntities.value, mergedFieldNames.value);
      const values = displayKeys.map((key) => {
        var _a, _b, _c;
        return (_c = (_b = (_a = keyEntities.value[key]) === null || _a === void 0 ? void 0 : _a.node) === null || _b === void 0 ? void 0 : _b[mergedFieldNames.value.value]) !== null && _c !== void 0 ? _c : key;
      });
      const labeledValues = values.map((val) => {
        const targetItem = rawLabeledValues.value.find((item) => item.value === val);
        return {
          value: val,
          label: targetItem === null || targetItem === void 0 ? void 0 : targetItem.label
        };
      });
      const rawDisplayValues = convert2LabelValues(labeledValues);
      const firstVal = rawDisplayValues[0];
      if (!mergedMultiple.value && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {
        return [];
      }
      return rawDisplayValues.map((item) => {
        var _a;
        return _extends(_extends({}, item), {
          label: (_a = item.label) !== null && _a !== void 0 ? _a : item.value
        });
      });
    });
    const [cachedDisplayValues] = useCache_default(displayValues);
    const triggerChange = (newRawValues, extra, source) => {
      const labeledValues = convert2LabelValues(newRawValues);
      setInternalValue(labeledValues);
      if (props.autoClearSearchValue) {
        setSearchValue("");
      }
      if (props.onChange) {
        let eventValues = newRawValues;
        if (treeConduction.value) {
          const formattedKeyList = formatStrategyValues(newRawValues, props.showCheckedStrategy, keyEntities.value, mergedFieldNames.value);
          eventValues = formattedKeyList.map((key) => {
            const entity = valueEntities.value.get(key);
            return entity ? entity.node[mergedFieldNames.value.value] : key;
          });
        }
        const {
          triggerValue,
          selected
        } = extra || {
          triggerValue: void 0,
          selected: void 0
        };
        let returnRawValues = eventValues;
        if (props.treeCheckStrictly) {
          const halfValues = rawHalfLabeledValues.value.filter((item) => !eventValues.includes(item.value));
          returnRawValues = [...returnRawValues, ...halfValues];
        }
        const returnLabeledValues = convert2LabelValues(returnRawValues);
        const additionalInfo = {
          // [Legacy] Always return as array contains label & value
          preValue: rawLabeledValues.value,
          triggerValue
        };
        let showPosition = true;
        if (props.treeCheckStrictly || source === "selection" && !selected) {
          showPosition = false;
        }
        fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData.value, showPosition, mergedFieldNames.value);
        if (mergedCheckable.value) {
          additionalInfo.checked = selected;
        } else {
          additionalInfo.selected = selected;
        }
        const returnValues = mergedLabelInValue.value ? returnLabeledValues : returnLabeledValues.map((item) => item.value);
        props.onChange(mergedMultiple.value ? returnValues : returnValues[0], mergedLabelInValue.value ? null : returnLabeledValues.map((item) => item.label), additionalInfo);
      }
    };
    const onOptionSelect = (selectedKey, _ref2) => {
      let {
        selected,
        source
      } = _ref2;
      var _a, _b, _c;
      const keyEntitiesValue = toRaw(keyEntities.value);
      const valueEntitiesValue = toRaw(valueEntities.value);
      const entity = keyEntitiesValue[selectedKey];
      const node = entity === null || entity === void 0 ? void 0 : entity.node;
      const selectedValue = (_a = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value.value]) !== null && _a !== void 0 ? _a : selectedKey;
      if (!mergedMultiple.value) {
        triggerChange([selectedValue], {
          selected: true,
          triggerValue: selectedValue
        }, "option");
      } else {
        let newRawValues = selected ? [...rawValues.value, selectedValue] : rawCheckedValues.value.filter((v) => v !== selectedValue);
        if (treeConduction.value) {
          const {
            missingRawValues,
            existRawValues
          } = splitRawValues(newRawValues);
          const keyList = existRawValues.map((val) => valueEntitiesValue.get(val).key);
          let checkedKeys;
          if (selected) {
            ({
              checkedKeys
            } = conductCheck(keyList, true, keyEntitiesValue, maxLevel.value, levelEntities.value));
          } else {
            ({
              checkedKeys
            } = conductCheck(keyList, {
              checked: false,
              halfCheckedKeys: rawHalfCheckedValues.value
            }, keyEntitiesValue, maxLevel.value, levelEntities.value));
          }
          newRawValues = [...missingRawValues, ...checkedKeys.map((key) => keyEntitiesValue[key].node[mergedFieldNames.value.value])];
        }
        triggerChange(newRawValues, {
          selected,
          triggerValue: selectedValue
        }, source || "option");
      }
      if (selected || !mergedMultiple.value) {
        (_b = props.onSelect) === null || _b === void 0 ? void 0 : _b.call(props, selectedValue, fillLegacyProps(node));
      } else {
        (_c = props.onDeselect) === null || _c === void 0 ? void 0 : _c.call(props, selectedValue, fillLegacyProps(node));
      }
    };
    const onInternalDropdownVisibleChange = (open) => {
      if (props.onDropdownVisibleChange) {
        const legacyParam = {};
        Object.defineProperty(legacyParam, "documentClickClose", {
          get() {
            warning(false, "Second param of `onDropdownVisibleChange` has been removed.");
            return false;
          }
        });
        props.onDropdownVisibleChange(open, legacyParam);
      }
    };
    const onDisplayValuesChange = (newValues, info) => {
      const newRawValues = newValues.map((item) => item.value);
      if (info.type === "clear") {
        triggerChange(newRawValues, {}, "selection");
        return;
      }
      if (info.values.length) {
        onOptionSelect(info.values[0].value, {
          selected: false,
          source: "selection"
        });
      }
    };
    const {
      treeNodeFilterProp,
      // Data
      loadData,
      treeLoadedKeys,
      onTreeLoad,
      // Expanded
      treeDefaultExpandAll,
      treeExpandedKeys,
      treeDefaultExpandedKeys,
      onTreeExpand,
      // Options
      virtual,
      listHeight,
      listItemHeight,
      // Tree
      treeLine,
      treeIcon,
      showTreeIcon,
      switcherIcon,
      treeMotion,
      customSlots,
      dropdownMatchSelectWidth,
      treeExpandAction
    } = toRefs(props);
    useProvideLegacySelectContext(toReactive({
      checkable: mergedCheckable,
      loadData,
      treeLoadedKeys,
      onTreeLoad,
      checkedKeys: rawCheckedValues,
      halfCheckedKeys: rawHalfCheckedValues,
      treeDefaultExpandAll,
      treeExpandedKeys,
      treeDefaultExpandedKeys,
      onTreeExpand,
      treeIcon,
      treeMotion,
      showTreeIcon,
      switcherIcon,
      treeLine,
      treeNodeFilterProp,
      keyEntities,
      customSlots
    }));
    useProvideSelectContext(toReactive({
      virtual,
      listHeight,
      listItemHeight,
      treeData: filteredTreeData,
      fieldNames: mergedFieldNames,
      onSelect: onOptionSelect,
      dropdownMatchSelectWidth,
      treeExpandAction
    }));
    const selectRef = ref();
    expose({
      focus() {
        var _a;
        (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.focus();
      },
      blur() {
        var _a;
        (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.blur();
      },
      scrollTo(arg) {
        var _a;
        (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.scrollTo(arg);
      }
    });
    return () => {
      var _a;
      const restProps = omit_default(props, [
        "id",
        "prefixCls",
        "customSlots",
        // Value
        "value",
        "defaultValue",
        "onChange",
        "onSelect",
        "onDeselect",
        // Search
        "searchValue",
        "inputValue",
        "onSearch",
        "autoClearSearchValue",
        "filterTreeNode",
        "treeNodeFilterProp",
        // Selector
        "showCheckedStrategy",
        "treeNodeLabelProp",
        //  Mode
        "multiple",
        "treeCheckable",
        "treeCheckStrictly",
        "labelInValue",
        // FieldNames
        "fieldNames",
        // Data
        "treeDataSimpleMode",
        "treeData",
        "children",
        "loadData",
        "treeLoadedKeys",
        "onTreeLoad",
        // Expanded
        "treeDefaultExpandAll",
        "treeExpandedKeys",
        "treeDefaultExpandedKeys",
        "onTreeExpand",
        // Options
        "virtual",
        "listHeight",
        "listItemHeight",
        "onDropdownVisibleChange",
        // Tree
        "treeLine",
        "treeIcon",
        "showTreeIcon",
        "switcherIcon",
        "treeMotion"
      ]);
      return createVNode(BaseSelect_default, _objectSpread2(_objectSpread2(_objectSpread2({
        "ref": selectRef
      }, attrs), restProps), {}, {
        "id": mergedId,
        "prefixCls": props.prefixCls,
        "mode": mergedMultiple.value ? "multiple" : void 0,
        "displayValues": cachedDisplayValues.value,
        "onDisplayValuesChange": onDisplayValuesChange,
        "searchValue": mergedSearchValue.value,
        "onSearch": onInternalSearch,
        "OptionList": OptionList_default,
        "emptyOptions": !mergedTreeData.value.length,
        "onDropdownVisibleChange": onInternalDropdownVisibleChange,
        "tagRender": props.tagRender || slots.tagRender,
        "dropdownMatchSelectWidth": (_a = props.dropdownMatchSelectWidth) !== null && _a !== void 0 ? _a : true
      }), slots);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-tree-select/index.js
var vc_tree_select_default = TreeSelect_default;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/FileOutlined.js
var FileOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z" } }] }, "name": "file", "theme": "outlined" };
var FileOutlined_default = FileOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/FileOutlined.js
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var FileOutlined2 = function FileOutlined3(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread({}, p, {
    "icon": FileOutlined_default
  }), null);
};
FileOutlined2.displayName = "FileOutlined";
FileOutlined2.inheritAttrs = false;
var FileOutlined_default2 = FileOutlined2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/MinusSquareOutlined.js
var MinusSquareOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z" } }, { "tag": "path", "attrs": { "d": "M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z" } }] }, "name": "minus-square", "theme": "outlined" };
var MinusSquareOutlined_default = MinusSquareOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/MinusSquareOutlined.js
function _objectSpread3(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty2(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty2(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var MinusSquareOutlined2 = function MinusSquareOutlined3(props, context) {
  var p = _objectSpread3({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread3({}, p, {
    "icon": MinusSquareOutlined_default
  }), null);
};
MinusSquareOutlined2.displayName = "MinusSquareOutlined";
MinusSquareOutlined2.inheritAttrs = false;
var MinusSquareOutlined_default2 = MinusSquareOutlined2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/PlusSquareOutlined.js
var PlusSquareOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z" } }, { "tag": "path", "attrs": { "d": "M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z" } }] }, "name": "plus-square", "theme": "outlined" };
var PlusSquareOutlined_default = PlusSquareOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/PlusSquareOutlined.js
function _objectSpread4(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty3(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty3(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var PlusSquareOutlined2 = function PlusSquareOutlined3(props, context) {
  var p = _objectSpread4({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread4({}, p, {
    "icon": PlusSquareOutlined_default
  }), null);
};
PlusSquareOutlined2.displayName = "PlusSquareOutlined";
PlusSquareOutlined2.inheritAttrs = false;
var PlusSquareOutlined_default2 = PlusSquareOutlined2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/CaretDownFilled.js
var CaretDownFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "0 0 1024 1024", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z" } }] }, "name": "caret-down", "theme": "filled" };
var CaretDownFilled_default = CaretDownFilled;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/CaretDownFilled.js
function _objectSpread5(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty4(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty4(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var CaretDownFilled2 = function CaretDownFilled3(props, context) {
  var p = _objectSpread5({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread5({}, p, {
    "icon": CaretDownFilled_default
  }), null);
};
CaretDownFilled2.displayName = "CaretDownFilled";
CaretDownFilled2.inheritAttrs = false;
var CaretDownFilled_default2 = CaretDownFilled2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tree/utils/iconUtil.js
function renderSwitcherIcon(prefixCls, switcherIcon, props, leafIcon, showLine) {
  const {
    isLeaf,
    expanded,
    loading
  } = props;
  let icon = switcherIcon;
  if (loading) {
    return createVNode(LoadingOutlined_default, {
      "class": `${prefixCls}-switcher-loading-icon`
    }, null);
  }
  let showLeafIcon;
  if (showLine && typeof showLine === "object") {
    showLeafIcon = showLine.showLeafIcon;
  }
  let defaultIcon = null;
  const switcherCls = `${prefixCls}-switcher-icon`;
  if (isLeaf) {
    if (!showLine) {
      return null;
    }
    if (showLeafIcon && leafIcon) {
      return leafIcon(props);
    }
    if (typeof showLine === "object" && !showLeafIcon) {
      defaultIcon = createVNode("span", {
        "class": `${prefixCls}-switcher-leaf-line`
      }, null);
    } else {
      defaultIcon = createVNode(FileOutlined_default2, {
        "class": `${prefixCls}-switcher-line-icon`
      }, null);
    }
    return defaultIcon;
  } else {
    defaultIcon = createVNode(CaretDownFilled_default2, {
      "class": switcherCls
    }, null);
    if (showLine) {
      defaultIcon = expanded ? createVNode(MinusSquareOutlined_default2, {
        "class": `${prefixCls}-switcher-line-icon`
      }, null) : createVNode(PlusSquareOutlined_default2, {
        "class": `${prefixCls}-switcher-line-icon`
      }, null);
    }
  }
  if (typeof switcherIcon === "function") {
    icon = switcherIcon(_extends(_extends({}, props), {
      defaultIcon,
      switcherCls
    }));
  } else if (isValidElement(icon)) {
    icon = cloneVNode(icon, {
      class: switcherCls
    });
  }
  return icon || defaultIcon;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tree/style/index.js
var treeNodeFX = new Keyframes_default("ant-tree-node-fx-do-not-use", {
  "0%": {
    opacity: 0
  },
  "100%": {
    opacity: 1
  }
});
var getSwitchStyle = (prefixCls, token) => ({
  [`.${prefixCls}-switcher-icon`]: {
    display: "inline-block",
    fontSize: 10,
    verticalAlign: "baseline",
    svg: {
      transition: `transform ${token.motionDurationSlow}`
    }
  }
});
var getDropIndicatorStyle = (prefixCls, token) => ({
  [`.${prefixCls}-drop-indicator`]: {
    position: "absolute",
    // it should displayed over the following node
    zIndex: 1,
    height: 2,
    backgroundColor: token.colorPrimary,
    borderRadius: 1,
    pointerEvents: "none",
    "&:after": {
      position: "absolute",
      top: -3,
      insetInlineStart: -6,
      width: 8,
      height: 8,
      backgroundColor: "transparent",
      border: `${token.lineWidthBold}px solid ${token.colorPrimary}`,
      borderRadius: "50%",
      content: '""'
    }
  }
});
var genBaseStyle = (prefixCls, token) => {
  const {
    treeCls,
    treeNodeCls,
    treeNodePadding,
    treeTitleHeight
  } = token;
  const treeCheckBoxMarginVertical = (treeTitleHeight - token.fontSizeLG) / 2;
  const treeCheckBoxMarginHorizontal = token.paddingXS;
  return {
    [treeCls]: _extends(_extends({}, resetComponent(token)), {
      background: token.colorBgContainer,
      borderRadius: token.borderRadius,
      transition: `background-color ${token.motionDurationSlow}`,
      [`&${treeCls}-rtl`]: {
        // >>> Switcher
        [`${treeCls}-switcher`]: {
          "&_close": {
            [`${treeCls}-switcher-icon`]: {
              svg: {
                transform: "rotate(90deg)"
              }
            }
          }
        }
      },
      [`&-focused:not(:hover):not(${treeCls}-active-focused)`]: _extends({}, genFocusOutline(token)),
      // =================== Virtual List ===================
      [`${treeCls}-list-holder-inner`]: {
        alignItems: "flex-start"
      },
      [`&${treeCls}-block-node`]: {
        [`${treeCls}-list-holder-inner`]: {
          alignItems: "stretch",
          // >>> Title
          [`${treeCls}-node-content-wrapper`]: {
            flex: "auto"
          },
          // >>> Drag
          [`${treeNodeCls}.dragging`]: {
            position: "relative",
            "&:after": {
              position: "absolute",
              top: 0,
              insetInlineEnd: 0,
              bottom: treeNodePadding,
              insetInlineStart: 0,
              border: `1px solid ${token.colorPrimary}`,
              opacity: 0,
              animationName: treeNodeFX,
              animationDuration: token.motionDurationSlow,
              animationPlayState: "running",
              animationFillMode: "forwards",
              content: '""',
              pointerEvents: "none"
            }
          }
        }
      },
      // ===================== TreeNode =====================
      [`${treeNodeCls}`]: {
        display: "flex",
        alignItems: "flex-start",
        padding: `0 0 ${treeNodePadding}px 0`,
        outline: "none",
        "&-rtl": {
          direction: "rtl"
        },
        // Disabled
        "&-disabled": {
          // >>> Title
          [`${treeCls}-node-content-wrapper`]: {
            color: token.colorTextDisabled,
            cursor: "not-allowed",
            "&:hover": {
              background: "transparent"
            }
          }
        },
        [`&-active ${treeCls}-node-content-wrapper`]: _extends({}, genFocusOutline(token)),
        [`&:not(${treeNodeCls}-disabled).filter-node ${treeCls}-title`]: {
          color: "inherit",
          fontWeight: 500
        },
        "&-draggable": {
          [`${treeCls}-draggable-icon`]: {
            width: treeTitleHeight,
            lineHeight: `${treeTitleHeight}px`,
            textAlign: "center",
            visibility: "visible",
            opacity: 0.2,
            transition: `opacity ${token.motionDurationSlow}`,
            [`${treeNodeCls}:hover &`]: {
              opacity: 0.45
            }
          },
          [`&${treeNodeCls}-disabled`]: {
            [`${treeCls}-draggable-icon`]: {
              visibility: "hidden"
            }
          }
        }
      },
      // >>> Indent
      [`${treeCls}-indent`]: {
        alignSelf: "stretch",
        whiteSpace: "nowrap",
        userSelect: "none",
        "&-unit": {
          display: "inline-block",
          width: treeTitleHeight
        }
      },
      // >>> Drag Handler
      [`${treeCls}-draggable-icon`]: {
        visibility: "hidden"
      },
      // >>> Switcher
      [`${treeCls}-switcher`]: _extends(_extends({}, getSwitchStyle(prefixCls, token)), {
        position: "relative",
        flex: "none",
        alignSelf: "stretch",
        width: treeTitleHeight,
        margin: 0,
        lineHeight: `${treeTitleHeight}px`,
        textAlign: "center",
        cursor: "pointer",
        userSelect: "none",
        "&-noop": {
          cursor: "default"
        },
        "&_close": {
          [`${treeCls}-switcher-icon`]: {
            svg: {
              transform: "rotate(-90deg)"
            }
          }
        },
        "&-loading-icon": {
          color: token.colorPrimary
        },
        "&-leaf-line": {
          position: "relative",
          zIndex: 1,
          display: "inline-block",
          width: "100%",
          height: "100%",
          // https://github.com/ant-design/ant-design/issues/31884
          "&:before": {
            position: "absolute",
            top: 0,
            insetInlineEnd: treeTitleHeight / 2,
            bottom: -treeNodePadding,
            marginInlineStart: -1,
            borderInlineEnd: `1px solid ${token.colorBorder}`,
            content: '""'
          },
          "&:after": {
            position: "absolute",
            width: treeTitleHeight / 2 * 0.8,
            height: treeTitleHeight / 2,
            borderBottom: `1px solid ${token.colorBorder}`,
            content: '""'
          }
        }
      }),
      // >>> Checkbox
      [`${treeCls}-checkbox`]: {
        top: "initial",
        marginInlineEnd: treeCheckBoxMarginHorizontal,
        marginBlockStart: treeCheckBoxMarginVertical
      },
      // >>> Title
      // add `${treeCls}-checkbox + span` to cover checkbox `${checkboxCls} + span`
      [`${treeCls}-node-content-wrapper, ${treeCls}-checkbox + span`]: {
        position: "relative",
        zIndex: "auto",
        minHeight: treeTitleHeight,
        margin: 0,
        padding: `0 ${token.paddingXS / 2}px`,
        color: "inherit",
        lineHeight: `${treeTitleHeight}px`,
        background: "transparent",
        borderRadius: token.borderRadius,
        cursor: "pointer",
        transition: `all ${token.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,
        "&:hover": {
          backgroundColor: token.controlItemBgHover
        },
        [`&${treeCls}-node-selected`]: {
          backgroundColor: token.controlItemBgActive
        },
        // Icon
        [`${treeCls}-iconEle`]: {
          display: "inline-block",
          width: treeTitleHeight,
          height: treeTitleHeight,
          lineHeight: `${treeTitleHeight}px`,
          textAlign: "center",
          verticalAlign: "top",
          "&:empty": {
            display: "none"
          }
        }
      },
      // https://github.com/ant-design/ant-design/issues/28217
      [`${treeCls}-unselectable ${treeCls}-node-content-wrapper:hover`]: {
        backgroundColor: "transparent"
      },
      // ==================== Draggable =====================
      [`${treeCls}-node-content-wrapper`]: _extends({
        lineHeight: `${treeTitleHeight}px`,
        userSelect: "none"
      }, getDropIndicatorStyle(prefixCls, token)),
      [`${treeNodeCls}.drop-container`]: {
        "> [draggable]": {
          boxShadow: `0 0 0 2px ${token.colorPrimary}`
        }
      },
      // ==================== Show Line =====================
      "&-show-line": {
        // ================ Indent lines ================
        [`${treeCls}-indent`]: {
          "&-unit": {
            position: "relative",
            height: "100%",
            "&:before": {
              position: "absolute",
              top: 0,
              insetInlineEnd: treeTitleHeight / 2,
              bottom: -treeNodePadding,
              borderInlineEnd: `1px solid ${token.colorBorder}`,
              content: '""'
            },
            "&-end": {
              "&:before": {
                display: "none"
              }
            }
          }
        },
        // ============== Cover Background ==============
        [`${treeCls}-switcher`]: {
          background: "transparent",
          "&-line-icon": {
            // https://github.com/ant-design/ant-design/issues/32813
            verticalAlign: "-0.15em"
          }
        }
      },
      [`${treeNodeCls}-leaf-last`]: {
        [`${treeCls}-switcher`]: {
          "&-leaf-line": {
            "&:before": {
              top: "auto !important",
              bottom: "auto !important",
              height: `${treeTitleHeight / 2}px !important`
            }
          }
        }
      }
    })
  };
};
var genDirectoryStyle = (token) => {
  const {
    treeCls,
    treeNodeCls,
    treeNodePadding
  } = token;
  return {
    [`${treeCls}${treeCls}-directory`]: {
      // ================== TreeNode ==================
      [treeNodeCls]: {
        position: "relative",
        // Hover color
        "&:before": {
          position: "absolute",
          top: 0,
          insetInlineEnd: 0,
          bottom: treeNodePadding,
          insetInlineStart: 0,
          transition: `background-color ${token.motionDurationMid}`,
          content: '""',
          pointerEvents: "none"
        },
        "&:hover": {
          "&:before": {
            background: token.controlItemBgHover
          }
        },
        // Elements
        "> *": {
          zIndex: 1
        },
        // >>> Switcher
        [`${treeCls}-switcher`]: {
          transition: `color ${token.motionDurationMid}`
        },
        // >>> Title
        [`${treeCls}-node-content-wrapper`]: {
          borderRadius: 0,
          userSelect: "none",
          "&:hover": {
            background: "transparent"
          },
          [`&${treeCls}-node-selected`]: {
            color: token.colorTextLightSolid,
            background: "transparent"
          }
        },
        // ============= Selected =============
        "&-selected": {
          [`
            &:hover::before,
            &::before
          `]: {
            background: token.colorPrimary
          },
          // >>> Switcher
          [`${treeCls}-switcher`]: {
            color: token.colorTextLightSolid
          },
          // >>> Title
          [`${treeCls}-node-content-wrapper`]: {
            color: token.colorTextLightSolid,
            background: "transparent"
          }
        }
      }
    }
  };
};
var genTreeStyle = (prefixCls, token) => {
  const treeCls = `.${prefixCls}`;
  const treeNodeCls = `${treeCls}-treenode`;
  const treeNodePadding = token.paddingXS / 2;
  const treeTitleHeight = token.controlHeightSM;
  const treeToken = merge(token, {
    treeCls,
    treeNodeCls,
    treeNodePadding,
    treeTitleHeight
  });
  return [
    // Basic
    genBaseStyle(prefixCls, treeToken),
    // Directory
    genDirectoryStyle(treeToken)
  ];
};
var style_default2 = genComponentStyleHook("Tree", (token, _ref) => {
  let {
    prefixCls
  } = _ref;
  return [{
    [token.componentCls]: getStyle(`${prefixCls}-checkbox`, token)
  }, genTreeStyle(prefixCls, token), collapse_default(token)];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tree-select/style/index.js
var genBaseStyle2 = (token) => {
  const {
    componentCls,
    treePrefixCls,
    colorBgElevated
  } = token;
  const treeCls = `.${treePrefixCls}`;
  return [
    // ======================================================
    // ==                     Dropdown                     ==
    // ======================================================
    {
      [`${componentCls}-dropdown`]: [
        {
          padding: `${token.paddingXS}px ${token.paddingXS / 2}px`
        },
        // ====================== Tree ======================
        genTreeStyle(treePrefixCls, merge(token, {
          colorBgContainer: colorBgElevated
        })),
        {
          [treeCls]: {
            borderRadius: 0,
            "&-list-holder-inner": {
              alignItems: "stretch",
              [`${treeCls}-treenode`]: {
                [`${treeCls}-node-content-wrapper`]: {
                  flex: "auto"
                }
              }
            }
          }
        },
        // ==================== Checkbox ====================
        getStyle(`${treePrefixCls}-checkbox`, token),
        // ====================== RTL =======================
        {
          "&-rtl": {
            direction: "rtl",
            [`${treeCls}-switcher${treeCls}-switcher_close`]: {
              [`${treeCls}-switcher-icon svg`]: {
                transform: "rotate(90deg)"
              }
            }
          }
        }
      ]
    }
  ];
};
function useTreeSelectStyle(prefixCls, treePrefixCls) {
  return genComponentStyleHook("TreeSelect", (token) => {
    const treeSelectToken = merge(token, {
      treePrefixCls: treePrefixCls.value
    });
    return [genBaseStyle2(treeSelectToken)];
  })(prefixCls);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tree-select/index.js
var getTransitionName = (rootPrefixCls, motion, transitionName) => {
  if (transitionName !== void 0) {
    return transitionName;
  }
  return `${rootPrefixCls}-${motion}`;
};
function treeSelectProps2() {
  return _extends(_extends({}, omit_default(treeSelectProps(), ["showTreeIcon", "treeMotion", "inputIcon", "getInputElement", "treeLine", "customSlots"])), {
    suffixIcon: vue_types_default.any,
    size: stringType(),
    bordered: booleanType(),
    treeLine: someType([Boolean, Object]),
    replaceFields: objectType(),
    placement: stringType(),
    status: stringType(),
    popupClassName: String,
    /** @deprecated Please use `popupClassName` instead */
    dropdownClassName: String,
    "onUpdate:value": functionType(),
    "onUpdate:treeExpandedKeys": functionType(),
    "onUpdate:searchValue": functionType()
  });
}
var TreeSelect = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ATreeSelect",
  inheritAttrs: false,
  props: initDefaultProps_default(treeSelectProps2(), {
    choiceTransitionName: "",
    listHeight: 256,
    treeIcon: false,
    listItemHeight: 26,
    bordered: true
  }),
  slots: Object,
  setup(props, _ref) {
    let {
      attrs,
      slots,
      expose,
      emit
    } = _ref;
    warning(!(props.treeData === void 0 && slots.default), "`children` of TreeSelect is deprecated. Please use `treeData` instead.");
    devWarning_default(props.multiple !== false || !props.treeCheckable, "TreeSelect", "`multiple` will always be `true` when `treeCheckable` is true");
    devWarning_default(props.replaceFields === void 0, "TreeSelect", "`replaceFields` is deprecated, please use fieldNames instead");
    devWarning_default(!props.dropdownClassName, "TreeSelect", "`dropdownClassName` is deprecated. Please use `popupClassName` instead.");
    const formItemContext = useInjectFormItemContext();
    const formItemInputContext = FormItemInputContext.useInject();
    const mergedStatus = computed(() => getMergedStatus(formItemInputContext.status, props.status));
    const {
      prefixCls,
      renderEmpty,
      direction,
      virtual,
      dropdownMatchSelectWidth,
      size: contextSize,
      getPopupContainer,
      getPrefixCls,
      disabled
    } = useConfigInject_default("select", props);
    const {
      compactSize,
      compactItemClassnames
    } = useCompactItemContext(prefixCls, direction);
    const mergedSize = computed(() => compactSize.value || contextSize.value);
    const contextDisabled = useInjectDisabled();
    const mergedDisabled = computed(() => {
      var _a;
      return (_a = disabled.value) !== null && _a !== void 0 ? _a : contextDisabled.value;
    });
    const rootPrefixCls = computed(() => getPrefixCls());
    const placement = computed(() => {
      if (props.placement !== void 0) {
        return props.placement;
      }
      return direction.value === "rtl" ? "bottomRight" : "bottomLeft";
    });
    const transitionName = computed(() => getTransitionName(rootPrefixCls.value, getTransitionDirection(placement.value), props.transitionName));
    const choiceTransitionName = computed(() => getTransitionName(rootPrefixCls.value, "", props.choiceTransitionName));
    const treePrefixCls = computed(() => getPrefixCls("select-tree", props.prefixCls));
    const treeSelectPrefixCls = computed(() => getPrefixCls("tree-select", props.prefixCls));
    const [wrapSelectSSR, hashId] = style_default(prefixCls);
    const [wrapTreeSelectSSR] = useTreeSelectStyle(treeSelectPrefixCls, treePrefixCls);
    const mergedDropdownClassName = computed(() => classNames_default(props.popupClassName || props.dropdownClassName, `${treeSelectPrefixCls.value}-dropdown`, {
      [`${treeSelectPrefixCls.value}-dropdown-rtl`]: direction.value === "rtl"
    }, hashId.value));
    const isMultiple = computed(() => !!(props.treeCheckable || props.multiple));
    const mergedShowArrow = computed(() => props.showArrow !== void 0 ? props.showArrow : props.loading || !isMultiple.value);
    const treeSelectRef = ref();
    expose({
      focus() {
        var _a, _b;
        (_b = (_a = treeSelectRef.value).focus) === null || _b === void 0 ? void 0 : _b.call(_a);
      },
      blur() {
        var _a, _b;
        (_b = (_a = treeSelectRef.value).blur) === null || _b === void 0 ? void 0 : _b.call(_a);
      }
    });
    const handleChange = function() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      emit("update:value", args[0]);
      emit("change", ...args);
      formItemContext.onFieldChange();
    };
    const handleTreeExpand = (keys) => {
      emit("update:treeExpandedKeys", keys);
      emit("treeExpand", keys);
    };
    const handleSearch = (value) => {
      emit("update:searchValue", value);
      emit("search", value);
    };
    const handleBlur = (e) => {
      emit("blur", e);
      formItemContext.onFieldBlur();
    };
    return () => {
      var _a, _b, _c;
      const {
        notFoundContent = (_a = slots.notFoundContent) === null || _a === void 0 ? void 0 : _a.call(slots),
        prefixCls: customizePrefixCls,
        bordered,
        listHeight,
        listItemHeight,
        multiple,
        treeIcon,
        treeLine,
        showArrow,
        switcherIcon = (_b = slots.switcherIcon) === null || _b === void 0 ? void 0 : _b.call(slots),
        fieldNames = props.replaceFields,
        id = formItemContext.id.value,
        placeholder = (_c = slots.placeholder) === null || _c === void 0 ? void 0 : _c.call(slots)
      } = props;
      const {
        isFormItemInput,
        hasFeedback,
        feedbackIcon
      } = formItemInputContext;
      const {
        suffixIcon,
        removeIcon,
        clearIcon
      } = getIcons(_extends(_extends({}, props), {
        multiple: isMultiple.value,
        showArrow: mergedShowArrow.value,
        hasFeedback,
        feedbackIcon,
        prefixCls: prefixCls.value
      }), slots);
      let mergedNotFound;
      if (notFoundContent !== void 0) {
        mergedNotFound = notFoundContent;
      } else {
        mergedNotFound = renderEmpty("Select");
      }
      const selectProps = omit_default(props, ["suffixIcon", "itemIcon", "removeIcon", "clearIcon", "switcherIcon", "bordered", "status", "onUpdate:value", "onUpdate:treeExpandedKeys", "onUpdate:searchValue"]);
      const mergedClassName = classNames_default(!customizePrefixCls && treeSelectPrefixCls.value, {
        [`${prefixCls.value}-lg`]: mergedSize.value === "large",
        [`${prefixCls.value}-sm`]: mergedSize.value === "small",
        [`${prefixCls.value}-rtl`]: direction.value === "rtl",
        [`${prefixCls.value}-borderless`]: !bordered,
        [`${prefixCls.value}-in-form-item`]: isFormItemInput
      }, getStatusClassNames(prefixCls.value, mergedStatus.value, hasFeedback), compactItemClassnames.value, attrs.class, hashId.value);
      const otherProps = {};
      if (props.treeData === void 0 && slots.default) {
        otherProps.children = flattenChildren(slots.default());
      }
      return wrapSelectSSR(wrapTreeSelectSSR(createVNode(vc_tree_select_default, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, attrs), selectProps), {}, {
        "disabled": mergedDisabled.value,
        "virtual": virtual.value,
        "dropdownMatchSelectWidth": dropdownMatchSelectWidth.value,
        "id": id,
        "fieldNames": fieldNames,
        "ref": treeSelectRef,
        "prefixCls": prefixCls.value,
        "class": mergedClassName,
        "listHeight": listHeight,
        "listItemHeight": listItemHeight,
        "treeLine": !!treeLine,
        "inputIcon": suffixIcon,
        "multiple": multiple,
        "removeIcon": removeIcon,
        "clearIcon": clearIcon,
        "switcherIcon": (nodeProps) => renderSwitcherIcon(treePrefixCls.value, switcherIcon, nodeProps, slots.leafIcon, treeLine),
        "showTreeIcon": treeIcon,
        "notFoundContent": mergedNotFound,
        "getPopupContainer": getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.value,
        "treeMotion": null,
        "dropdownClassName": mergedDropdownClassName.value,
        "choiceTransitionName": choiceTransitionName.value,
        "onChange": handleChange,
        "onBlur": handleBlur,
        "onSearch": handleSearch,
        "onTreeExpand": handleTreeExpand
      }, otherProps), {}, {
        "transitionName": transitionName.value,
        "customSlots": _extends(_extends({}, slots), {
          treeCheckable: () => createVNode("span", {
            "class": `${prefixCls.value}-tree-checkbox-inner`
          }, null)
        }),
        "maxTagPlaceholder": props.maxTagPlaceholder || slots.maxTagPlaceholder,
        "placement": placement.value,
        "showArrow": hasFeedback || showArrow,
        "placeholder": placeholder
      }), _extends(_extends({}, slots), {
        treeCheckable: () => createVNode("span", {
          "class": `${prefixCls.value}-tree-checkbox-inner`
        }, null)
      }))));
    };
  }
});
var TreeSelectNode = TreeNode_default2;
var tree_select_default = _extends(TreeSelect, {
  TreeNode: TreeNode_default2,
  SHOW_ALL,
  SHOW_PARENT,
  SHOW_CHILD,
  install: (app) => {
    app.component(TreeSelect.name, TreeSelect);
    app.component(TreeSelectNode.displayName, TreeSelectNode);
    return app;
  }
});

export {
  eagerComputed,
  treeProps,
  TreeNode_default,
  arrDel,
  arrAdd,
  conductExpandParent,
  fillFieldNames,
  convertTreeToData,
  convertDataToEntities,
  conductCheck,
  useMaxLevel,
  Tree_default,
  FileOutlined_default2 as FileOutlined_default,
  renderSwitcherIcon,
  style_default2 as style_default,
  treeSelectProps2 as treeSelectProps,
  TreeSelectNode,
  tree_select_default
};
//# sourceMappingURL=chunk-IJBILFQK.js.map
