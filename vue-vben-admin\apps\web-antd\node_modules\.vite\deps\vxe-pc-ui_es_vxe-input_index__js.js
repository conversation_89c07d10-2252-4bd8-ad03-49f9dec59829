import {
  input_default
} from "./chunk-A6FEOLH4.js";
import "./chunk-PFX5QMW6.js";
import "./chunk-H6EAC26D.js";
import "./chunk-A3JYOOKK.js";
import "./chunk-EYZRMTBP.js";
import {
  dynamicApp
} from "./chunk-BUOIGA6Q.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-MSIZQRL4.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/input/index.js
var VxeInput = Object.assign(input_default, {
  install(app) {
    app.component(input_default.name, input_default);
  }
});
dynamicApp.use(VxeInput);
VxeUI.component(input_default);
var Input = VxeInput;
var input_default2 = VxeInput;

// ../../node_modules/.pnpm/vxe-pc-ui@4.7.16_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-input/index.js
var vxe_input_default = input_default2;
export {
  Input,
  VxeInput,
  vxe_input_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-input_index__js.js.map
