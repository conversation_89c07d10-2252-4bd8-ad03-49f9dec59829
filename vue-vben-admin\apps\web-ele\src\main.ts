import { initPreferences } from '@vben/preferences';
import { unmountGlobalLoading } from '@vben/utils';

import { overridesPreferences } from './preferences';

/**
 * Perform page loading and rendering after application initialization is complete
 */
async function initApplication() {
  // name is used to specify the unique identifier of the project
  // Used to distinguish preference settings of different projects and key prefixes for storing data and other data that needs to be isolated
  const env = import.meta.env.PROD ? 'prod' : 'dev';
  const appVersion = import.meta.env.VITE_APP_VERSION;
  const namespace = `${import.meta.env.VITE_APP_NAMESPACE}-${appVersion}-${env}`;

  // App preference settings initialization
  await initPreferences({
    namespace,
    overrides: overridesPreferences,
  });

  // Start application and mount
  // Vue application main logic and views
  const { bootstrap } = await import('./bootstrap');
  await bootstrap(namespace);

  // Remove and destroy loading
  unmountGlobalLoading();
}

initApplication();
