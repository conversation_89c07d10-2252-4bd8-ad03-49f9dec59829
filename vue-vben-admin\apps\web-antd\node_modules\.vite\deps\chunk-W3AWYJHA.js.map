{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/transition.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/supportsPassive.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-util/Dom/addEventListener.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/utils/motionUtil.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/BaseMixin.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/interface.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/interface.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/Mask.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/MobilePopupInner.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/useVisibleStatus.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/useStretchStyle.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/propertyUtils.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/utils.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/getOffsetParent.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/isAncestorFixed.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/getVisibleRectForElement.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/adjustForViewport.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/getRegion.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/getAlignOffset.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/getElFuturePos.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/align/align.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/align/alignElement.js", "../../../../../node_modules/.pnpm/dom-align@1.12.4/node_modules/src/align/alignPoint.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-align/util.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-align/hooks/useBuffer.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-align/Align.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/PopupInner.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Popup/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/utils/alignUtil.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/context.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/Portal.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/getScrollBarSize.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useScrollLocker.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/PortalWrapper.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/Trigger.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-trigger/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/style/motion/motion.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/style/motion/fade.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/style/motion/move.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/style/motion/slide.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/style/motion/zoom.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/style/motion/collapse.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { nextTick } from 'vue';\nimport { tuple } from './type';\nconst SelectPlacements = tuple('bottomLeft', 'bottomRight', 'topLeft', 'topRight');\nconst getTransitionDirection = placement => {\n  if (placement !== undefined && (placement === 'topLeft' || placement === 'topRight')) {\n    return `slide-down`;\n  }\n  return `slide-up`;\n};\nexport const getTransitionProps = function (transitionName) {\n  let opt = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const transitionProps = transitionName ? _extends({\n    name: transitionName,\n    appear: true,\n    // type: 'animation',\n    // appearFromClass: `${transitionName}-appear ${transitionName}-appear-prepare`,\n    // appearActiveClass: `antdv-base-transtion`,\n    // appearToClass: `${transitionName}-appear ${transitionName}-appear-active`,\n    enterFromClass: `${transitionName}-enter ${transitionName}-enter-prepare ${transitionName}-enter-start`,\n    enterActiveClass: `${transitionName}-enter ${transitionName}-enter-prepare`,\n    enterToClass: `${transitionName}-enter ${transitionName}-enter-active`,\n    leaveFromClass: ` ${transitionName}-leave`,\n    leaveActiveClass: `${transitionName}-leave ${transitionName}-leave-active`,\n    leaveToClass: `${transitionName}-leave ${transitionName}-leave-active`\n  }, opt) : _extends({\n    css: false\n  }, opt);\n  return transitionProps;\n};\nexport const getTransitionGroupProps = function (transitionName) {\n  let opt = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const transitionProps = transitionName ? _extends({\n    name: transitionName,\n    appear: true,\n    // appearFromClass: `${transitionName}-appear ${transitionName}-appear-prepare`,\n    appearActiveClass: `${transitionName}`,\n    appearToClass: `${transitionName}-appear ${transitionName}-appear-active`,\n    enterFromClass: `${transitionName}-appear ${transitionName}-enter ${transitionName}-appear-prepare ${transitionName}-enter-prepare`,\n    enterActiveClass: `${transitionName}`,\n    enterToClass: `${transitionName}-enter ${transitionName}-appear ${transitionName}-appear-active ${transitionName}-enter-active`,\n    leaveActiveClass: `${transitionName} ${transitionName}-leave`,\n    leaveToClass: `${transitionName}-leave-active`\n  }, opt) : _extends({\n    css: false\n  }, opt);\n  return transitionProps;\n};\n// ================== Collapse Motion ==================\nconst getCollapsedHeight = () => ({\n  height: 0,\n  opacity: 0\n});\nconst getRealHeight = node => ({\n  height: `${node.scrollHeight}px`,\n  opacity: 1\n});\nconst getCurrentHeight = node => ({\n  height: `${node.offsetHeight}px`\n});\nconst collapseMotion = function () {\n  let name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'ant-motion-collapse';\n  let style = arguments.length > 1 ? arguments[1] : undefined;\n  let className = arguments.length > 2 ? arguments[2] : undefined;\n  return {\n    name,\n    appear: true,\n    css: true,\n    onBeforeEnter: node => {\n      className.value = name;\n      style.value = getCollapsedHeight(node);\n    },\n    onEnter: node => {\n      nextTick(() => {\n        style.value = getRealHeight(node);\n      });\n    },\n    onAfterEnter: () => {\n      className.value = '';\n      style.value = {};\n    },\n    onBeforeLeave: node => {\n      className.value = name;\n      style.value = getCurrentHeight(node);\n    },\n    onLeave: node => {\n      setTimeout(() => {\n        style.value = getCollapsedHeight(node);\n      });\n    },\n    onAfterLeave: () => {\n      className.value = '';\n      style.value = {};\n    }\n  };\n};\nconst getTransitionName = (rootPrefixCls, motion, transitionName) => {\n  if (transitionName !== undefined) {\n    return transitionName;\n  }\n  return `${rootPrefixCls}-${motion}`;\n};\nexport { collapseMotion, getTransitionName, getTransitionDirection };", "// Test via a getter in the options object to see if the passive property is accessed\nlet supportsPassive = false;\ntry {\n  const opts = Object.defineProperty({}, 'passive', {\n    get() {\n      supportsPassive = true;\n    }\n  });\n  window.addEventListener('testPassive', null, opts);\n  window.removeEventListener('testPassive', null, opts);\n} catch (e) {}\nexport default supportsPassive;", "import supportsPassive from '../../_util/supportsPassive';\nexport default function addEventListenerWrap(target, eventType, cb, option) {\n  if (target && target.addEventListener) {\n    let opt = option;\n    if (opt === undefined && supportsPassive && (eventType === 'touchstart' || eventType === 'touchmove' || eventType === 'wheel')) {\n      opt = {\n        passive: false\n      };\n    }\n    target.addEventListener(eventType, cb, opt);\n  }\n  return {\n    remove: () => {\n      if (target && target.removeEventListener) {\n        target.removeEventListener(eventType, cb);\n      }\n    }\n  };\n}", "export function getMotion(_ref) {\n  let {\n    prefixCls,\n    animation,\n    transitionName\n  } = _ref;\n  if (animation) {\n    return {\n      name: `${prefixCls}-${animation}`\n    };\n  }\n  if (transitionName) {\n    return {\n      name: transitionName\n    };\n  }\n  return {};\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { nextTick } from 'vue';\nimport { getOptionProps } from './props-util';\nexport default {\n  methods: {\n    setState() {\n      let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      let callback = arguments.length > 1 ? arguments[1] : undefined;\n      let newState = typeof state === 'function' ? state(this.$data, this.$props) : state;\n      if (this.getDerivedStateFromProps) {\n        const s = this.getDerivedStateFromProps(getOptionProps(this), _extends(_extends({}, this.$data), newState));\n        if (s === null) {\n          return;\n        } else {\n          newState = _extends(_extends({}, newState), s || {});\n        }\n      }\n      _extends(this.$data, newState);\n      if (this._.isMounted) {\n        this.$forceUpdate();\n      }\n      nextTick(() => {\n        callback && callback();\n      });\n    },\n    __emit() {\n      // 直接调用事件，底层组件不需要vueTool记录events\n      // eslint-disable-next-line prefer-rest-params\n      const args = [].slice.call(arguments, 0);\n      let eventName = args[0];\n      eventName = `on${eventName[0].toUpperCase()}${eventName.substring(1)}`;\n      const event = this.$props[eventName] || this.$attrs[eventName];\n      if (args.length && event) {\n        if (Array.isArray(event)) {\n          for (let i = 0, l = event.length; i < l; i++) {\n            event[i](...args.slice(1));\n          }\n        } else {\n          event(...args.slice(1));\n        }\n      }\n    }\n  }\n};", "import PropTypes from '../_util/vue-types';\nfunction returnEmptyString() {\n  return '';\n}\nfunction returnDocument(element) {\n  if (element) {\n    return element.ownerDocument;\n  }\n  return window.document;\n}\nexport function noop() {}\nexport const triggerProps = () => ({\n  action: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]).def([]),\n  showAction: PropTypes.any.def([]),\n  hideAction: PropTypes.any.def([]),\n  getPopupClassNameFromAlign: PropTypes.any.def(returnEmptyString),\n  onPopupVisibleChange: Function,\n  afterPopupVisibleChange: PropTypes.func.def(noop),\n  popup: PropTypes.any,\n  arrow: PropTypes.bool.def(true),\n  popupStyle: {\n    type: Object,\n    default: undefined\n  },\n  prefixCls: PropTypes.string.def('rc-trigger-popup'),\n  popupClassName: PropTypes.string.def(''),\n  popupPlacement: String,\n  builtinPlacements: PropTypes.object,\n  popupTransitionName: String,\n  popupAnimation: PropTypes.any,\n  mouseEnterDelay: PropTypes.number.def(0),\n  mouseLeaveDelay: PropTypes.number.def(0.1),\n  zIndex: Number,\n  focusDelay: PropTypes.number.def(0),\n  blurDelay: PropTypes.number.def(0.15),\n  getPopupContainer: Function,\n  getDocument: PropTypes.func.def(returnDocument),\n  forceRender: {\n    type: Boolean,\n    default: undefined\n  },\n  destroyPopupOnHide: {\n    type: Boolean,\n    default: false\n  },\n  mask: {\n    type: Boolean,\n    default: false\n  },\n  maskClosable: {\n    type: Boolean,\n    default: true\n  },\n  // onPopupAlign: PropTypes.func.def(noop),\n  popupAlign: PropTypes.object.def(() => ({})),\n  popupVisible: {\n    type: Boolean,\n    default: undefined\n  },\n  defaultPopupVisible: {\n    type: Boolean,\n    default: false\n  },\n  maskTransitionName: String,\n  maskAnimation: String,\n  stretch: String,\n  alignPoint: {\n    type: Boolean,\n    default: undefined\n  },\n  autoDestroy: {\n    type: Boolean,\n    default: false\n  },\n  mobile: Object,\n  getTriggerDOMNode: Function\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const innerProps = {\n  visible: <PERSON><PERSON><PERSON>,\n  prefixCls: String,\n  zIndex: Number,\n  destroyPopupOnHide: <PERSON><PERSON><PERSON>,\n  forceRender: Boolean,\n  arrow: {\n    type: Boolean,\n    default: true\n  },\n  // Legacy Motion\n  animation: [String, Object],\n  transitionName: String,\n  // Measure\n  stretch: {\n    type: String\n  },\n  // Align\n  align: {\n    type: Object\n  },\n  point: {\n    type: Object\n  },\n  getRootDomNode: {\n    type: Function\n  },\n  getClassNameFromAlign: {\n    type: Function\n  },\n  onAlign: {\n    type: Function\n  },\n  onMouseenter: {\n    type: Function\n  },\n  onMouseleave: {\n    type: Function\n  },\n  onMousedown: {\n    type: Function\n  },\n  onTouchstart: {\n    type: Function\n  }\n};\nexport const mobileProps = _extends(_extends({}, innerProps), {\n  mobile: {\n    type: Object\n  }\n});\nexport const popupProps = _extends(_extends({}, innerProps), {\n  mask: Boolean,\n  mobile: {\n    type: Object\n  },\n  maskAnimation: String,\n  maskTransitionName: String\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { withDirectives as _withDirectives, createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport { Transition } from 'vue';\nimport { getMotion } from '../utils/motionUtil';\nexport default function Mask(props) {\n  const {\n    prefixCls,\n    visible,\n    zIndex,\n    mask,\n    maskAnimation,\n    maskTransitionName\n  } = props;\n  if (!mask) {\n    return null;\n  }\n  let motion = {};\n  if (maskTransitionName || maskAnimation) {\n    motion = getMotion({\n      prefixCls,\n      transitionName: maskTransitionName,\n      animation: maskAnimation\n    });\n  }\n  return _createVNode(Transition, _objectSpread({\n    \"appear\": true\n  }, motion), {\n    default: () => [_withDirectives(_createVNode(\"div\", {\n      \"style\": {\n        zIndex\n      },\n      \"class\": `${prefixCls}-mask`\n    }, null), [[_resolveDirective(\"if\"), visible]])]\n  });\n}\nMask.displayName = 'Mask';", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent, ref, Transition } from 'vue';\nimport { flattenChildren } from '../../_util/props-util';\nimport classNames from '../../_util/classNames';\nimport { mobileProps } from './interface';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'MobilePopupInner',\n  inheritAttrs: false,\n  props: mobileProps,\n  emits: ['mouseenter', 'mouseleave', 'mousedown', 'touchstart', 'align'],\n  setup(props, _ref) {\n    let {\n      expose,\n      slots\n    } = _ref;\n    const elementRef = ref();\n    expose({\n      forceAlign: () => {},\n      getElement: () => elementRef.value\n    });\n    return () => {\n      var _a;\n      const {\n        zIndex,\n        visible,\n        prefixCls,\n        mobile: {\n          popupClassName,\n          popupStyle,\n          popupMotion = {},\n          popupRender\n        } = {}\n      } = props;\n      // ======================== Render ========================\n      const mergedStyle = _extends({\n        zIndex\n      }, popupStyle);\n      let childNode = flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));\n      // Wrapper when multiple children\n      if (childNode.length > 1) {\n        const _childNode = function () {\n          return childNode;\n        }();\n        childNode = _createVNode(\"div\", {\n          \"class\": `${prefixCls}-content`\n        }, [childNode]);\n      }\n      // Mobile support additional render\n      if (popupRender) {\n        childNode = popupRender(childNode);\n      }\n      const mergedClassName = classNames(prefixCls, popupClassName);\n      return _createVNode(Transition, _objectSpread({\n        \"ref\": elementRef\n      }, popupMotion), {\n        default: () => [visible ? _createVNode(\"div\", {\n          \"class\": mergedClassName,\n          \"style\": mergedStyle\n        }, [childNode]) : null]\n      });\n    };\n  }\n});", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { onBeforeUnmount, shallowRef, watch, onMounted } from 'vue';\nimport raf from '../../_util/raf';\nconst StatusQueue = ['measure', 'align', null, 'motion'];\nexport default ((visible, doMeasure) => {\n  const status = shallowRef(null);\n  const rafRef = shallowRef();\n  const destroyRef = shallowRef(false);\n  function setStatus(nextStatus) {\n    if (!destroyRef.value) {\n      status.value = nextStatus;\n    }\n  }\n  function cancelRaf() {\n    raf.cancel(rafRef.value);\n  }\n  function goNextStatus(callback) {\n    cancelRaf();\n    rafRef.value = raf(() => {\n      // Only align should be manually trigger\n      let newStatus = status.value;\n      switch (status.value) {\n        case 'align':\n          newStatus = 'motion';\n          break;\n        case 'motion':\n          newStatus = 'stable';\n          break;\n        default:\n      }\n      setStatus(newStatus);\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }\n  watch(visible, () => {\n    setStatus('measure');\n  }, {\n    immediate: true,\n    flush: 'post'\n  });\n  onMounted(() => {\n    // Go next status\n    watch(status, () => {\n      switch (status.value) {\n        case 'measure':\n          doMeasure();\n          break;\n        default:\n      }\n      if (status.value) {\n        rafRef.value = raf(() => __awaiter(void 0, void 0, void 0, function* () {\n          const index = StatusQueue.indexOf(status.value);\n          const nextStatus = StatusQueue[index + 1];\n          if (nextStatus && index !== -1) {\n            setStatus(nextStatus);\n          }\n        }));\n      }\n    }, {\n      immediate: true,\n      flush: 'post'\n    });\n  });\n  onBeforeUnmount(() => {\n    destroyRef.value = true;\n    cancelRaf();\n  });\n  return [status, goNextStatus];\n});", "import { computed, shallowRef } from 'vue';\nexport default (stretch => {\n  const targetSize = shallowRef({\n    width: 0,\n    height: 0\n  });\n  function measureStretch(element) {\n    targetSize.value = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n  }\n  // Merge stretch style\n  const style = computed(() => {\n    const sizeStyle = {};\n    if (stretch.value) {\n      const {\n        width,\n        height\n      } = targetSize.value;\n      // Stretch with target\n      if (stretch.value.indexOf('height') !== -1 && height) {\n        sizeStyle.height = `${height}px`;\n      } else if (stretch.value.indexOf('minHeight') !== -1 && height) {\n        sizeStyle.minHeight = `${height}px`;\n      }\n      if (stretch.value.indexOf('width') !== -1 && width) {\n        sizeStyle.width = `${width}px`;\n      } else if (stretch.value.indexOf('minWidth') !== -1 && width) {\n        sizeStyle.minWidth = `${width}px`;\n      }\n    }\n    return sizeStyle;\n  });\n  return [style, measureStretch];\n});", "let vendorPrefix;\n\nconst jsCssMap = {\n  Webkit: '-webkit-',\n  Moz: '-moz-',\n  // IE did it wrong again ...\n  ms: '-ms-',\n  O: '-o-',\n};\n\nfunction getVendorPrefix() {\n  if (vendorPrefix !== undefined) {\n    return vendorPrefix;\n  }\n  vendorPrefix = '';\n  const style = document.createElement('p').style;\n  const testProp = 'Transform';\n  for (const key in jsCssMap) {\n    if (key + testProp in style) {\n      vendorPrefix = key;\n    }\n  }\n  return vendorPrefix;\n}\n\nfunction getTransitionName() {\n  return getVendorPrefix()\n    ? `${getVendorPrefix()}TransitionProperty`\n    : 'transitionProperty';\n}\n\nexport function getTransformName() {\n  return getVendorPrefix() ? `${getVendorPrefix()}Transform` : 'transform';\n}\n\nexport function setTransitionProperty(node, value) {\n  const name = getTransitionName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transitionProperty') {\n      node.style.transitionProperty = value;\n    }\n  }\n}\n\nfunction setTransform(node, value) {\n  const name = getTransformName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transform') {\n      node.style.transform = value;\n    }\n  }\n}\n\nexport function getTransitionProperty(node) {\n  return node.style.transitionProperty || node.style[getTransitionName()];\n}\n\nexport function getTransformXY(node) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    const matrix = transform.replace(/[^0-9\\-.,]/g, '').split(',');\n    return {\n      x: parseFloat(matrix[12] || matrix[4], 0),\n      y: parseFloat(matrix[13] || matrix[5], 0),\n    };\n  }\n  return {\n    x: 0,\n    y: 0,\n  };\n}\n\nconst matrix2d = /matrix\\((.*)\\)/;\nconst matrix3d = /matrix3d\\((.*)\\)/;\n\nexport function setTransformXY(node, xy) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    let arr;\n    let match2d = transform.match(matrix2d);\n    if (match2d) {\n      match2d = match2d[1];\n      arr = match2d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[4] = xy.x;\n      arr[5] = xy.y;\n      setTransform(node, `matrix(${arr.join(',')})`);\n    } else {\n      const match3d = transform.match(matrix3d)[1];\n      arr = match3d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[12] = xy.x;\n      arr[13] = xy.y;\n      setTransform(node, `matrix3d(${arr.join(',')})`);\n    }\n  } else {\n    setTransform(\n      node,\n      `translateX(${xy.x}px) translateY(${xy.y}px) translateZ(0)`,\n    );\n  }\n}\n", "import {\n  setTransitionProperty,\n  getTransitionProperty,\n  getTransformXY,\n  setTransformXY,\n  getTransformName,\n} from './propertyUtils';\n\nconst RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\n\nlet getComputedStyleX;\n\n// https://stackoverflow.com/a/3485654/3040605\nfunction forceRelayout(elem) {\n  const originalStyle = elem.style.display;\n  elem.style.display = 'none';\n  elem.offsetHeight; // eslint-disable-line\n  elem.style.display = originalStyle;\n}\n\nfunction css(el, name, v) {\n  let value = v;\n  if (typeof name === 'object') {\n    for (const i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value = `${value}px`;\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\n\nfunction getClientPosition(elem) {\n  let box;\n  let x;\n  let y;\n  const doc = elem.ownerDocument;\n  const body = doc.body;\n  const docElem = doc && doc.documentElement;\n  // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n  box = elem.getBoundingClientRect();\n\n  // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = Math.floor(box.left);\n  y = Math.floor(box.top);\n\n  // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nfunction getScroll(w, top) {\n  let ret = w[`page${top ? 'Y' : 'X'}Offset`];\n  const method = `scroll${top ? 'Top' : 'Left'}`;\n  if (typeof ret !== 'number') {\n    const d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\n\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\n\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\n\nfunction getOffset(el) {\n  const pos = getClientPosition(el);\n  const doc = el.ownerDocument;\n  const w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\nfunction isWindow(obj) {\n  // must use == for ie8\n  /* eslint eqeqeq:0 */\n  return obj !== null && obj !== undefined && obj == obj.window;\n}\n\nfunction getDocument(node) {\n  if (isWindow(node)) {\n    return node.document;\n  }\n  if (node.nodeType === 9) {\n    return node;\n  }\n  return node.ownerDocument;\n}\n\nfunction _getComputedStyle(elem, name, cs) {\n  let computedStyle = cs;\n  let val = '';\n  const d = getDocument(elem);\n  computedStyle = computedStyle || d.defaultView.getComputedStyle(elem, null);\n\n  // https://github.com/kissyteam/kissy/issues/61\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n\n  return val;\n}\n\nconst _RE_NUM_NO_PX = new RegExp(`^(${RE_NUM})(?!px)[a-z%]+$`, 'i');\nconst RE_POS = /^(top|right|bottom|left)$/;\nconst CURRENT_STYLE = 'currentStyle';\nconst RUNTIME_STYLE = 'runtimeStyle';\nconst LEFT = 'left';\nconst PX = 'px';\n\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  let ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name];\n\n  // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    const style = elem.style;\n    const left = style[LEFT];\n    const rsLeft = elem[RUNTIME_STYLE][LEFT];\n\n    // prevent flashing of content\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT];\n\n    // Put in the new values to get a computed value out\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX;\n\n    // Revert the changed values\n    style[LEFT] = left;\n\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\n\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle\n    ? _getComputedStyle\n    : _getComputedStyleIE;\n}\n\nfunction getOffsetDirection(dir, option) {\n  if (dir === 'left') {\n    return option.useCssRight ? 'right' : dir;\n  }\n  return option.useCssBottom ? 'bottom' : dir;\n}\n\nfunction oppositeOffsetDirection(dir) {\n  if (dir === 'left') {\n    return 'right';\n  } else if (dir === 'right') {\n    return 'left';\n  } else if (dir === 'top') {\n    return 'bottom';\n  } else if (dir === 'bottom') {\n    return 'top';\n  }\n}\n\n// 设置 elem 相对 elem.ownerDocument 的坐标\nfunction setLeftTop(elem, offset, option) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n  let presetH = -999;\n  let presetV = -999;\n  const horizontalProperty = getOffsetDirection('left', option);\n  const verticalProperty = getOffsetDirection('top', option);\n  const oppositeHorizontalProperty = oppositeOffsetDirection(\n    horizontalProperty,\n  );\n  const oppositeVerticalProperty = oppositeOffsetDirection(verticalProperty);\n\n  if (horizontalProperty !== 'left') {\n    presetH = 999;\n  }\n\n  if (verticalProperty !== 'top') {\n    presetV = 999;\n  }\n  let originalTransition = '';\n  const originalOffset = getOffset(elem);\n  if ('left' in offset || 'top' in offset) {\n    originalTransition = getTransitionProperty(elem) || '';\n    setTransitionProperty(elem, 'none');\n  }\n  if ('left' in offset) {\n    elem.style[oppositeHorizontalProperty] = '';\n    elem.style[horizontalProperty] = `${presetH}px`;\n  }\n  if ('top' in offset) {\n    elem.style[oppositeVerticalProperty] = '';\n    elem.style[verticalProperty] = `${presetV}px`;\n  }\n  // force relayout\n  forceRelayout(elem);\n  const old = getOffset(elem);\n  const originalStyle = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const preset = key === 'left' ? presetH : presetV;\n      const off = originalOffset[key] - old[key];\n      if (dir === key) {\n        originalStyle[dir] = preset + off;\n      } else {\n        originalStyle[dir] = preset - off;\n      }\n    }\n  }\n  css(elem, originalStyle);\n  // force relayout\n  forceRelayout(elem);\n  if ('left' in offset || 'top' in offset) {\n    setTransitionProperty(elem, originalTransition);\n  }\n  const ret = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const off = offset[key] - originalOffset[key];\n      if (key === dir) {\n        ret[dir] = originalStyle[dir] + off;\n      } else {\n        ret[dir] = originalStyle[dir] - off;\n      }\n    }\n  }\n  css(elem, ret);\n}\n\nfunction setTransform(elem, offset) {\n  const originalOffset = getOffset(elem);\n  const originalXY = getTransformXY(elem);\n  const resultXY = { x: originalXY.x, y: originalXY.y };\n  if ('left' in offset) {\n    resultXY.x = originalXY.x + offset.left - originalOffset.left;\n  }\n  if ('top' in offset) {\n    resultXY.y = originalXY.y + offset.top - originalOffset.top;\n  }\n  setTransformXY(elem, resultXY);\n}\n\nfunction setOffset(elem, offset, option) {\n  if (option.ignoreShake) {\n    const oriOffset = getOffset(elem);\n\n    const oLeft = oriOffset.left.toFixed(0);\n    const oTop = oriOffset.top.toFixed(0);\n    const tLeft = offset.left.toFixed(0);\n    const tTop = offset.top.toFixed(0);\n\n    if (oLeft === tLeft && oTop === tTop) {\n      return;\n    }\n  }\n\n  if (option.useCssRight || option.useCssBottom) {\n    setLeftTop(elem, offset, option);\n  } else if (\n    option.useCssTransform &&\n    getTransformName() in document.body.style\n  ) {\n    setTransform(elem, offset, option);\n  } else {\n    setLeftTop(elem, offset, option);\n  }\n}\n\nfunction each(arr, fn) {\n  for (let i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\n\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\n\nconst BOX_MODELS = ['margin', 'border', 'padding'];\nconst CONTENT_INDEX = -1;\nconst PADDING_INDEX = 2;\nconst BORDER_INDEX = 1;\nconst MARGIN_INDEX = 0;\n\nfunction swap(elem, options, callback) {\n  const old = {};\n  const style = elem.style;\n  let name;\n\n  // Remember the old values, and insert the new ones\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n\n  callback.call(elem);\n\n  // Revert the old values\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\n\nfunction getPBMWidth(elem, props, which) {\n  let value = 0;\n  let prop;\n  let j;\n  let i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        let cssProp;\n        if (prop === 'border') {\n          cssProp = `${prop}${which[i]}Width`;\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\n\nconst domUtils = {\n  getParent(element) {\n    let parent = element;\n    do {\n      if (parent.nodeType === 11 && parent.host) {\n        parent = parent.host;\n      } else {\n        parent = parent.parentNode;\n      }\n    } while (parent && parent.nodeType !== 1 && parent.nodeType !== 9);\n    return parent;\n  },\n};\n\neach(['Width', 'Height'], name => {\n  domUtils[`doc${name}`] = refWin => {\n    const d = refWin.document;\n    return Math.max(\n      // firefox chrome documentElement.scrollHeight< body.scrollHeight\n      // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n      d.documentElement[`scroll${name}`],\n      // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n      d.body[`scroll${name}`],\n      domUtils[`viewport${name}`](d),\n    );\n  };\n\n  domUtils[`viewport${name}`] = win => {\n    // pc browser includes scrollbar in window.innerWidth\n    const prop = `client${name}`;\n    const doc = win.document;\n    const body = doc.body;\n    const documentElement = doc.documentElement;\n    const documentElementProp = documentElement[prop];\n    // 标准模式取 documentElement\n    // backcompat 取 body\n    return (\n      (doc.compatMode === 'CSS1Compat' && documentElementProp) ||\n      (body && body[prop]) ||\n      documentElementProp\n    );\n  };\n});\n\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\nfunction getWH(elem, name, ex) {\n  let extra = ex;\n  if (isWindow(elem)) {\n    return name === 'width'\n      ? domUtils.viewportWidth(elem)\n      : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width'\n      ? domUtils.docWidth(elem)\n      : domUtils.docHeight(elem);\n  }\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  let borderBoxValue =\n    name === 'width'\n      ? Math.floor(elem.getBoundingClientRect().width)\n      : Math.floor(elem.getBoundingClientRect().height);\n  const isBorderBox = isBorderBoxFn(elem);\n  let cssBoxValue = 0;\n  if (\n    borderBoxValue === null ||\n    borderBoxValue === undefined ||\n    borderBoxValue <= 0\n  ) {\n    borderBoxValue = undefined;\n    // Fall back to computed then un computed css if necessary\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (\n      cssBoxValue === null ||\n      cssBoxValue === undefined ||\n      Number(cssBoxValue) < 0\n    ) {\n      cssBoxValue = elem.style[name] || 0;\n    }\n    // Normalize '', auto, and prepare for extra\n    cssBoxValue = Math.floor(parseFloat(cssBoxValue)) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  const borderBoxValueOrIsBorderBox =\n    borderBoxValue !== undefined || isBorderBox;\n  const val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which);\n    }\n    return cssBoxValue;\n  } else if (borderBoxValueOrIsBorderBox) {\n    if (extra === BORDER_INDEX) {\n      return val;\n    }\n    return (\n      val +\n      (extra === PADDING_INDEX\n        ? -getPBMWidth(elem, ['border'], which)\n        : getPBMWidth(elem, ['margin'], which))\n    );\n  }\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which);\n}\n\nconst cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block',\n};\n\n// fix #119 : https://github.com/kissyteam/kissy/issues/119\nfunction getWHIgnoreDisplay(...args) {\n  let val;\n  const elem = args[0];\n  // in case elem is window\n  // elem.offsetWidth === undefined\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, () => {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\n\neach(['width', 'height'], name => {\n  const first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[`outer${first}`] = (el, includeMargin) => {\n    return (\n      el &&\n      getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX)\n    );\n  };\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n\n  domUtils[name] = (elem, v) => {\n    let val = v;\n    if (val !== undefined) {\n      if (elem) {\n        const isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\n\nfunction mix(to, from) {\n  for (const i in from) {\n    if (from.hasOwnProperty(i)) {\n      to[i] = from[i];\n    }\n  }\n  return to;\n}\n\nconst utils = {\n  getWindow(node) {\n    if (node && node.document && node.setTimeout) {\n      return node;\n    }\n    const doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  getDocument,\n  offset(el, value, option) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value, option || {});\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow,\n  each,\n  css,\n  clone(obj) {\n    let i;\n    const ret = {};\n    for (i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    const overflow = obj.overflow;\n    if (overflow) {\n      for (i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  mix,\n  getWindowScrollLeft(w) {\n    return getScrollLeft(w);\n  },\n  getWindowScrollTop(w) {\n    return getScrollTop(w);\n  },\n  merge(...args) {\n    const ret = {};\n    for (let i = 0; i < args.length; i++) {\n      utils.mix(ret, args[i]);\n    }\n    return ret;\n  },\n  viewportWidth: 0,\n  viewportHeight: 0,\n};\n\nmix(utils, domUtils);\n\nexport default utils;\n", "import utils from './utils';\n\n/**\n * 得到会导致元素显示不全的祖先元素\n */\nconst { getParent } = utils;\n\nfunction getOffsetParent(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return null;\n  }\n  // ie 这个也不是完全可行\n  /*\n   <div style=\"width: 50px;height: 100px;overflow: hidden\">\n   <div style=\"width: 50px;height: 100px;position: relative;\" id=\"d6\">\n   元素 6 高 100px 宽 50px<br/>\n   </div>\n   </div>\n   */\n  // element.offsetParent does the right thing in ie7 and below. Return parent with layout!\n  //  In other browsers it only includes elements with position absolute, relative or\n  // fixed, not elements with overflow set to auto or scroll.\n  //        if (UA.ie && ieMode < 8) {\n  //            return element.offsetParent;\n  //        }\n  // 统一的 offsetParent 方法\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent;\n  let positionStyle = utils.css(element, 'position');\n  const skipStatic = positionStyle === 'fixed' || positionStyle === 'absolute';\n\n  if (!skipStatic) {\n    return element.nodeName.toLowerCase() === 'html'\n      ? null\n      : getParent(element);\n  }\n\n  for (\n    parent = getParent(element);\n    parent && parent !== body && parent.nodeType !== 9;\n    parent = getParent(parent)\n  ) {\n    positionStyle = utils.css(parent, 'position');\n    if (positionStyle !== 'static') {\n      return parent;\n    }\n  }\n  return null;\n}\n\nexport default getOffsetParent;\n", "import utils from './utils';\n\nconst { getParent } = utils;\n\nexport default function isAncestorFixed(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return false;\n  }\n\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent = null;\n  for (\n    parent = getParent(element);\n    // 修复元素位于 document.documentElement 下导致崩溃问题\n    parent && parent !== body && parent !== doc;\n    parent = getParent(parent)\n  ) {\n    const positionStyle = utils.css(parent, 'position');\n    if (positionStyle === 'fixed') {\n      return true;\n    }\n  }\n  return false;\n}\n", "import utils from './utils';\nimport getOffsetParent from './getOffsetParent';\nimport isAncestorFixed from './isAncestorFixed';\n\n/**\n * 获得元素的显示部分的区域\n */\nfunction getVisibleRectForElement(element, alwaysByViewport) {\n  const visibleRect = {\n    left: 0,\n    right: Infinity,\n    top: 0,\n    bottom: Infinity,\n  };\n  let el = getOffsetParent(element);\n  const doc = utils.getDocument(element);\n  const win = doc.defaultView || doc.parentWindow;\n  const body = doc.body;\n  const documentElement = doc.documentElement;\n\n  // Determine the size of the visible rect by climbing the dom accounting for\n  // all scrollable containers.\n  while (el) {\n    // clientWidth is zero for inline block elements in ie.\n    if (\n      (navigator.userAgent.indexOf('MSIE') === -1 || el.clientWidth !== 0) &&\n      // body may have overflow set on it, yet we still get the entire\n      // viewport. In some browsers, el.offsetParent may be\n      // document.documentElement, so check for that too.\n      (el !== body &&\n        el !== documentElement &&\n        utils.css(el, 'overflow') !== 'visible')\n    ) {\n      const pos = utils.offset(el);\n      // add border\n      pos.left += el.clientLeft;\n      pos.top += el.clientTop;\n      visibleRect.top = Math.max(visibleRect.top, pos.top);\n      visibleRect.right = Math.min(\n        visibleRect.right,\n        // consider area without scrollBar\n        pos.left + el.clientWidth,\n      );\n      visibleRect.bottom = Math.min(\n        visibleRect.bottom,\n        pos.top + el.clientHeight,\n      );\n      visibleRect.left = Math.max(visibleRect.left, pos.left);\n    } else if (el === body || el === documentElement) {\n      break;\n    }\n    el = getOffsetParent(el);\n  }\n\n  // Set element position to fixed\n  // make sure absolute element itself don't affect it's visible area\n  // https://github.com/ant-design/ant-design/issues/7601\n  let originalPosition = null;\n  if (!utils.isWindow(element) && element.nodeType !== 9) {\n    originalPosition = element.style.position;\n    const position = utils.css(element, 'position');\n    if (position === 'absolute') {\n      element.style.position = 'fixed';\n    }\n  }\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n  let documentWidth = documentElement.scrollWidth;\n  let documentHeight = documentElement.scrollHeight;\n\n  // scrollXXX on html is sync with body which means overflow: hidden on body gets wrong scrollXXX.\n  // We should cut this ourself.\n  const bodyStyle = window.getComputedStyle(body);\n  if (bodyStyle.overflowX === 'hidden') {\n    documentWidth = win.innerWidth;\n  }\n  if (bodyStyle.overflowY === 'hidden') {\n    documentHeight = win.innerHeight;\n  }\n\n  // Reset element position after calculate the visible area\n  if (element.style) {\n    element.style.position = originalPosition;\n  }\n\n  if (alwaysByViewport || isAncestorFixed(element)) {\n    // Clip by viewport's size.\n    visibleRect.left = Math.max(visibleRect.left, scrollX);\n    visibleRect.top = Math.max(visibleRect.top, scrollY);\n    visibleRect.right = Math.min(visibleRect.right, scrollX + viewportWidth);\n    visibleRect.bottom = Math.min(visibleRect.bottom, scrollY + viewportHeight);\n  } else {\n    // Clip by document's size.\n    const maxVisibleWidth = Math.max(documentWidth, scrollX + viewportWidth);\n    visibleRect.right = Math.min(visibleRect.right, maxVisibleWidth);\n\n    const maxVisibleHeight = Math.max(documentHeight, scrollY + viewportHeight);\n    visibleRect.bottom = Math.min(visibleRect.bottom, maxVisibleHeight);\n  }\n\n  return visibleRect.top >= 0 &&\n    visibleRect.left >= 0 &&\n    visibleRect.bottom > visibleRect.top &&\n    visibleRect.right > visibleRect.left\n    ? visibleRect\n    : null;\n}\n\nexport default getVisibleRectForElement;\n", "import utils from './utils';\n\nfunction adjustForViewport(elFuturePos, elRegion, visibleRect, overflow) {\n  const pos = utils.clone(elFuturePos);\n  const size = {\n    width: elRegion.width,\n    height: elRegion.height,\n  };\n\n  if (overflow.adjustX && pos.left < visibleRect.left) {\n    pos.left = visibleRect.left;\n  }\n\n  // Left edge inside and right edge outside viewport, try to resize it.\n  if (\n    overflow.resizeWidth &&\n    pos.left >= visibleRect.left &&\n    pos.left + size.width > visibleRect.right\n  ) {\n    size.width -= pos.left + size.width - visibleRect.right;\n  }\n\n  // Right edge outside viewport, try to move it.\n  if (overflow.adjustX && pos.left + size.width > visibleRect.right) {\n    // 保证左边界和可视区域左边界对齐\n    pos.left = Math.max(visibleRect.right - size.width, visibleRect.left);\n  }\n\n  // Top edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top < visibleRect.top) {\n    pos.top = visibleRect.top;\n  }\n\n  // Top edge inside and bottom edge outside viewport, try to resize it.\n  if (\n    overflow.resizeHeight &&\n    pos.top >= visibleRect.top &&\n    pos.top + size.height > visibleRect.bottom\n  ) {\n    size.height -= pos.top + size.height - visibleRect.bottom;\n  }\n\n  // Bottom edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top + size.height > visibleRect.bottom) {\n    // 保证上边界和可视区域上边界对齐\n    pos.top = Math.max(visibleRect.bottom - size.height, visibleRect.top);\n  }\n\n  return utils.mix(pos, size);\n}\n\nexport default adjustForViewport;\n", "import utils from './utils';\n\nfunction getRegion(node) {\n  let offset;\n  let w;\n  let h;\n  if (!utils.isWindow(node) && node.nodeType !== 9) {\n    offset = utils.offset(node);\n    w = utils.outerWidth(node);\n    h = utils.outerHeight(node);\n  } else {\n    const win = utils.getWindow(node);\n    offset = {\n      left: utils.getWindowScrollLeft(win),\n      top: utils.getWindowScrollTop(win),\n    };\n    w = utils.viewportWidth(win);\n    h = utils.viewportHeight(win);\n  }\n  offset.width = w;\n  offset.height = h;\n  return offset;\n}\n\nexport default getRegion;\n", "/**\n * 获取 node 上的 align 对齐点 相对于页面的坐标\n */\n\nfunction getAlignOffset(region, align) {\n  const V = align.charAt(0);\n  const H = align.charAt(1);\n  const w = region.width;\n  const h = region.height;\n\n  let x = region.left;\n  let y = region.top;\n\n  if (V === 'c') {\n    y += h / 2;\n  } else if (V === 'b') {\n    y += h;\n  }\n\n  if (H === 'c') {\n    x += w / 2;\n  } else if (H === 'r') {\n    x += w;\n  }\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nexport default getAlignOffset;\n", "import getAlignOffset from './getAlignOffset';\n\nfunction getElFuturePos(elRegion, refNodeRegion, points, offset, targetOffset) {\n  const p1 = getAlignOffset(refNodeRegion, points[1]);\n  const p2 = getAlignOffset(elRegion, points[0]);\n  const diff = [p2.left - p1.left, p2.top - p1.top];\n\n  return {\n    left: Math.round(elRegion.left - diff[0] + offset[0] - targetOffset[0]),\n    top: Math.round(elRegion.top - diff[1] + offset[1] - targetOffset[1]),\n  };\n}\n\nexport default getElFuturePos;\n", "/**\n * align dom node flexibly\n * <AUTHOR>\n */\n\nimport utils from '../utils';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport adjustForViewport from '../adjustForViewport';\nimport getRegion from '../getRegion';\nimport getElFuturePos from '../getElFuturePos';\n\n// http://yiminghe.iteye.com/blog/1124720\n\nfunction isFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left < visibleRect.left ||\n    elFuturePos.left + elRegion.width > visibleRect.right\n  );\n}\n\nfunction isFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top < visibleRect.top ||\n    elFuturePos.top + elRegion.height > visibleRect.bottom\n  );\n}\n\nfunction isCompleteFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left > visibleRect.right ||\n    elFuturePos.left + elRegion.width < visibleRect.left\n  );\n}\n\nfunction isCompleteFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top > visibleRect.bottom ||\n    elFuturePos.top + elRegion.height < visibleRect.top\n  );\n}\n\nfunction flip(points, reg, map) {\n  const ret = [];\n  utils.each(points, p => {\n    ret.push(\n      p.replace(reg, m => {\n        return map[m];\n      }),\n    );\n  });\n  return ret;\n}\n\nfunction flipOffset(offset, index) {\n  offset[index] = -offset[index];\n  return offset;\n}\n\nfunction convertOffset(str, offsetLen) {\n  let n;\n  if (/%$/.test(str)) {\n    n = (parseInt(str.substring(0, str.length - 1), 10) / 100) * offsetLen;\n  } else {\n    n = parseInt(str, 10);\n  }\n  return n || 0;\n}\n\nfunction normalizeOffset(offset, el) {\n  offset[0] = convertOffset(offset[0], el.width);\n  offset[1] = convertOffset(offset[1], el.height);\n}\n\n/**\n * @param el\n * @param tgtRegion 参照节点所占的区域: { left, top, width, height }\n * @param align\n */\nfunction doAlign(el, tgtRegion, align, isTgtRegionVisible) {\n  let points = align.points;\n  let offset = align.offset || [0, 0];\n  let targetOffset = align.targetOffset || [0, 0];\n  let overflow = align.overflow;\n  const source = align.source || el;\n  offset = [].concat(offset);\n  targetOffset = [].concat(targetOffset);\n  overflow = overflow || {};\n  const newOverflowCfg = {};\n  let fail = 0;\n  const alwaysByViewport = !!(overflow && overflow.alwaysByViewport);\n  // 当前节点可以被放置的显示区域\n  const visibleRect = getVisibleRectForElement(source, alwaysByViewport);\n  // 当前节点所占的区域, left/top/width/height\n  const elRegion = getRegion(source);\n  // 将 offset 转换成数值，支持百分比\n  normalizeOffset(offset, elRegion);\n  normalizeOffset(targetOffset, tgtRegion);\n  // 当前节点将要被放置的位置\n  let elFuturePos = getElFuturePos(\n    elRegion,\n    tgtRegion,\n    points,\n    offset,\n    targetOffset,\n  );\n  // 当前节点将要所处的区域\n  let newElRegion = utils.merge(elRegion, elFuturePos);\n\n  // 如果可视区域不能完全放置当前节点时允许调整\n  if (\n    visibleRect &&\n    (overflow.adjustX || overflow.adjustY) &&\n    isTgtRegionVisible\n  ) {\n    if (overflow.adjustX) {\n      // 如果横向不能放下\n      if (isFailX(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 0);\n        const newTargetOffset = flipOffset(targetOffset, 0);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailX(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    if (overflow.adjustY) {\n      // 如果纵向不能放下\n      if (isFailY(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 1);\n        const newTargetOffset = flipOffset(targetOffset, 1);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailY(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    // 如果失败，重新计算当前节点将要被放置的位置\n    if (fail) {\n      elFuturePos = getElFuturePos(\n        elRegion,\n        tgtRegion,\n        points,\n        offset,\n        targetOffset,\n      );\n      utils.mix(newElRegion, elFuturePos);\n    }\n    const isStillFailX = isFailX(elFuturePos, elRegion, visibleRect);\n    const isStillFailY = isFailY(elFuturePos, elRegion, visibleRect);\n    // 检查反下后的位置是否可以放下了，如果仍然放不下：\n    // 1. 复原修改过的定位参数\n    if (isStillFailX || isStillFailY) {\n      let newPoints = points;\n\n      // 重置对应部分的翻转逻辑\n      if (isStillFailX) {\n        newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n      }\n      if (isStillFailY) {\n        newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n      }\n\n      points = newPoints;\n\n      offset = align.offset || [0, 0];\n      targetOffset = align.targetOffset || [0, 0];\n    }\n    // 2. 只有指定了可以调整当前方向才调整\n    newOverflowCfg.adjustX = overflow.adjustX && isStillFailX;\n    newOverflowCfg.adjustY = overflow.adjustY && isStillFailY;\n\n    // 确实要调整，甚至可能会调整高度宽度\n    if (newOverflowCfg.adjustX || newOverflowCfg.adjustY) {\n      newElRegion = adjustForViewport(\n        elFuturePos,\n        elRegion,\n        visibleRect,\n        newOverflowCfg,\n      );\n    }\n  }\n\n  // need judge to in case set fixed with in css on height auto element\n  if (newElRegion.width !== elRegion.width) {\n    utils.css(\n      source,\n      'width',\n      utils.width(source) + newElRegion.width - elRegion.width,\n    );\n  }\n\n  if (newElRegion.height !== elRegion.height) {\n    utils.css(\n      source,\n      'height',\n      utils.height(source) + newElRegion.height - elRegion.height,\n    );\n  }\n\n  // https://github.com/kissyteam/kissy/issues/190\n  // 相对于屏幕位置没变，而 left/top 变了\n  // 例如 <div 'relative'><el absolute></div>\n  utils.offset(\n    source,\n    {\n      left: newElRegion.left,\n      top: newElRegion.top,\n    },\n    {\n      useCssRight: align.useCssRight,\n      useCssBottom: align.useCssBottom,\n      useCssTransform: align.useCssTransform,\n      ignoreShake: align.ignoreShake,\n    },\n  );\n\n  return {\n    points,\n    offset,\n    targetOffset,\n    overflow: newOverflowCfg,\n  };\n}\n\nexport default doAlign;\n/**\n *  2012-04-26 <EMAIL>\n *   - 优化智能对齐算法\n *   - 慎用 resizeXX\n *\n *  2011-07-13 <EMAIL> note:\n *   - 增加智能对齐，以及大小调整选项\n **/\n", "import doAlign from './align';\nimport getOffsetParent from '../getOffsetParent';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport getRegion from '../getRegion';\n\nfunction isOutOfVisibleRect(target, alwaysByViewport) {\n  const visibleRect = getVisibleRectForElement(target, alwaysByViewport);\n  const targetRegion = getRegion(target);\n\n  return (\n    !visibleRect ||\n    targetRegion.left + targetRegion.width <= visibleRect.left ||\n    targetRegion.top + targetRegion.height <= visibleRect.top ||\n    targetRegion.left >= visibleRect.right ||\n    targetRegion.top >= visibleRect.bottom\n  );\n}\n\nfunction alignElement(el, refNode, align) {\n  const target = align.target || refNode;\n  const refNodeRegion = getRegion(target);\n\n  const isTargetNotOutOfVisible = !isOutOfVisibleRect(\n    target,\n    align.overflow && align.overflow.alwaysByViewport,\n  );\n\n  return doAlign(el, refNodeRegion, align, isTargetNotOutOfVisible);\n}\n\nalignElement.__getOffsetParent = getOffsetParent;\n\nalignElement.__getVisibleRectForElement = getVisibleRectForElement;\n\nexport default alignElement;\n", "import utils from '../utils';\nimport doAlign from './align';\n\n/**\n * `tgtPoint`: { pageX, pageY } or { clientX, clientY }.\n * If client position provided, will internal convert to page position.\n */\n\nfunction alignPoint(el, tgtPoint, align) {\n  let pageX;\n  let pageY;\n\n  const doc = utils.getDocument(el);\n  const win = doc.defaultView || doc.parentWindow;\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n\n  if ('pageX' in tgtPoint) {\n    pageX = tgtPoint.pageX;\n  } else {\n    pageX = scrollX + tgtPoint.clientX;\n  }\n\n  if ('pageY' in tgtPoint) {\n    pageY = tgtPoint.pageY;\n  } else {\n    pageY = scrollY + tgtPoint.clientY;\n  }\n\n  const tgtRegion = {\n    left: pageX,\n    top: pageY,\n    width: 0,\n    height: 0,\n  };\n\n  const pointInView =\n    pageX >= 0 &&\n    pageX <= scrollX + viewportWidth &&\n    (pageY >= 0 && pageY <= scrollY + viewportHeight);\n\n  // Provide default target point\n  const points = [align.points[0], 'cc'];\n\n  return doAlign(el, tgtRegion, { ...align, points }, pointInView);\n}\n\nexport default alignPoint;\n", "import contains from '../vc-util/Dom/contains';\nimport ResizeObserver from 'resize-observer-polyfill';\nexport function isSamePoint(prev, next) {\n  if (prev === next) return true;\n  if (!prev || !next) return false;\n  if ('pageX' in next && 'pageY' in next) {\n    return prev.pageX === next.pageX && prev.pageY === next.pageY;\n  }\n  if ('clientX' in next && 'clientY' in next) {\n    return prev.clientX === next.clientX && prev.clientY === next.clientY;\n  }\n  return false;\n}\nexport function restoreFocus(activeElement, container) {\n  // Focus back if is in the container\n  if (activeElement !== document.activeElement && contains(container, activeElement) && typeof activeElement.focus === 'function') {\n    activeElement.focus();\n  }\n}\nexport function monitorResize(element, callback) {\n  let prevWidth = null;\n  let prevHeight = null;\n  function onResize(_ref) {\n    let [{\n      target\n    }] = _ref;\n    if (!document.documentElement.contains(target)) return;\n    const {\n      width,\n      height\n    } = target.getBoundingClientRect();\n    const fixedWidth = Math.floor(width);\n    const fixedHeight = Math.floor(height);\n    if (prevWidth !== fixedWidth || prevHeight !== fixedHeight) {\n      // https://webkit.org/blog/9997/resizeobserver-in-webkit/\n      Promise.resolve().then(() => {\n        callback({\n          width: fixedWidth,\n          height: fixedHeight\n        });\n      });\n    }\n    prevWidth = fixedWidth;\n    prevHeight = fixedHeight;\n  }\n  const resizeObserver = new ResizeObserver(onResize);\n  if (element) {\n    resizeObserver.observe(element);\n  }\n  return () => {\n    resizeObserver.disconnect();\n  };\n}", "export default ((callback, buffer) => {\n  let called = false;\n  let timeout = null;\n  function cancelTrigger() {\n    clearTimeout(timeout);\n  }\n  function trigger(force) {\n    if (!called || force === true) {\n      if (callback() === false) {\n        // Not delay since callback cancelled self\n        return;\n      }\n      called = true;\n      cancelTrigger();\n      timeout = setTimeout(() => {\n        called = false;\n      }, buffer.value);\n    } else {\n      cancelTrigger();\n      timeout = setTimeout(() => {\n        called = false;\n        trigger();\n      }, buffer.value);\n    }\n  }\n  return [trigger, () => {\n    called = false;\n    cancelTrigger();\n  }];\n});", "import { nextTick, defineComponent, ref, computed, onMounted, onUpdated, watch, onUnmounted } from 'vue';\nimport { alignElement, alignPoint } from 'dom-align';\nimport addEventListener from '../vc-util/Dom/addEventListener';\nimport { cloneElement } from '../_util/vnode';\nimport isVisible from '../vc-util/Dom/isVisible';\nimport { isSamePoint, restoreFocus, monitorResize } from './util';\nimport useBuffer from './hooks/useBuffer';\nimport isEqual from 'lodash-es/isEqual';\nexport const alignProps = {\n  align: Object,\n  target: [Object, Function],\n  onAlign: Function,\n  monitorBufferTime: Number,\n  monitorWindowResize: Boolean,\n  disabled: Boolean\n};\nfunction getElement(func) {\n  if (typeof func !== 'function') return null;\n  return func();\n}\nfunction getPoint(point) {\n  if (typeof point !== 'object' || !point) return null;\n  return point;\n}\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Align',\n  props: alignProps,\n  emits: ['align'],\n  setup(props, _ref) {\n    let {\n      expose,\n      slots\n    } = _ref;\n    const cacheRef = ref({});\n    const nodeRef = ref();\n    const [forceAlign, cancelForceAlign] = useBuffer(() => {\n      const {\n        disabled: latestDisabled,\n        target: latestTarget,\n        align: latestAlign,\n        onAlign: latestOnAlign\n      } = props;\n      if (!latestDisabled && latestTarget && nodeRef.value) {\n        const source = nodeRef.value;\n        let result;\n        const element = getElement(latestTarget);\n        const point = getPoint(latestTarget);\n        cacheRef.value.element = element;\n        cacheRef.value.point = point;\n        cacheRef.value.align = latestAlign;\n        // IE lose focus after element realign\n        // We should record activeElement and restore later\n        const {\n          activeElement\n        } = document;\n        // We only align when element is visible\n        if (element && isVisible(element)) {\n          result = alignElement(source, element, latestAlign);\n        } else if (point) {\n          result = alignPoint(source, point, latestAlign);\n        }\n        restoreFocus(activeElement, source);\n        if (latestOnAlign && result) {\n          latestOnAlign(source, result);\n        }\n        return true;\n      }\n      return false;\n    }, computed(() => props.monitorBufferTime));\n    // ===================== Effect =====================\n    // Listen for target updated\n    const resizeMonitor = ref({\n      cancel: () => {}\n    });\n    // Listen for source updated\n    const sourceResizeMonitor = ref({\n      cancel: () => {}\n    });\n    const goAlign = () => {\n      const target = props.target;\n      const element = getElement(target);\n      const point = getPoint(target);\n      if (nodeRef.value !== sourceResizeMonitor.value.element) {\n        sourceResizeMonitor.value.cancel();\n        sourceResizeMonitor.value.element = nodeRef.value;\n        sourceResizeMonitor.value.cancel = monitorResize(nodeRef.value, forceAlign);\n      }\n      if (cacheRef.value.element !== element || !isSamePoint(cacheRef.value.point, point) || !isEqual(cacheRef.value.align, props.align)) {\n        forceAlign();\n        // Add resize observer\n        if (resizeMonitor.value.element !== element) {\n          resizeMonitor.value.cancel();\n          resizeMonitor.value.element = element;\n          resizeMonitor.value.cancel = monitorResize(element, forceAlign);\n        }\n      }\n    };\n    onMounted(() => {\n      nextTick(() => {\n        goAlign();\n      });\n    });\n    onUpdated(() => {\n      nextTick(() => {\n        goAlign();\n      });\n    });\n    // Listen for disabled change\n    watch(() => props.disabled, disabled => {\n      if (!disabled) {\n        forceAlign();\n      } else {\n        cancelForceAlign();\n      }\n    }, {\n      immediate: true,\n      flush: 'post'\n    });\n    // Listen for window resize\n    const winResizeRef = ref(null);\n    watch(() => props.monitorWindowResize, monitorWindowResize => {\n      if (monitorWindowResize) {\n        if (!winResizeRef.value) {\n          winResizeRef.value = addEventListener(window, 'resize', forceAlign);\n        }\n      } else if (winResizeRef.value) {\n        winResizeRef.value.remove();\n        winResizeRef.value = null;\n      }\n    }, {\n      flush: 'post'\n    });\n    onUnmounted(() => {\n      resizeMonitor.value.cancel();\n      sourceResizeMonitor.value.cancel();\n      if (winResizeRef.value) winResizeRef.value.remove();\n      cancelForceAlign();\n    });\n    expose({\n      forceAlign: () => forceAlign(true)\n    });\n    return () => {\n      const child = slots === null || slots === void 0 ? void 0 : slots.default();\n      if (child) {\n        return cloneElement(child[0], {\n          ref: nodeRef\n        }, true, true);\n      }\n      return null;\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { withDirectives as _withDirectives, resolveDirective as _resolveDirective, vShow as _vShow, createVNode as _createVNode } from \"vue\";\nimport useVisibleStatus from './useVisibleStatus';\nimport useStretchStyle from './useStretchStyle';\nimport { computed, defineComponent, shallowRef, toRef, Transition, watch, withModifiers } from 'vue';\nimport Align from '../../vc-align/Align';\nimport { getMotion } from '../utils/motionUtil';\nimport { flattenChildren } from '../../_util/props-util';\nimport classNames from '../../_util/classNames';\nimport { innerProps } from './interface';\nimport { getTransitionProps } from '../../_util/transition';\nimport supportsPassive from '../../_util/supportsPassive';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'PopupInner',\n  inheritAttrs: false,\n  props: innerProps,\n  emits: ['mouseenter', 'mouseleave', 'mousedown', 'touchstart', 'align'],\n  setup(props, _ref) {\n    let {\n      expose,\n      attrs,\n      slots\n    } = _ref;\n    const alignRef = shallowRef();\n    const elementRef = shallowRef();\n    const alignedClassName = shallowRef();\n    // ======================= Measure ========================\n    const [stretchStyle, measureStretchStyle] = useStretchStyle(toRef(props, 'stretch'));\n    const doMeasure = () => {\n      if (props.stretch) {\n        measureStretchStyle(props.getRootDomNode());\n      }\n    };\n    const visible = shallowRef(false);\n    let timeoutId;\n    watch(() => props.visible, val => {\n      clearTimeout(timeoutId);\n      if (val) {\n        timeoutId = setTimeout(() => {\n          visible.value = props.visible;\n        });\n      } else {\n        visible.value = false;\n      }\n    }, {\n      immediate: true\n    });\n    // ======================== Status ========================\n    const [status, goNextStatus] = useVisibleStatus(visible, doMeasure);\n    // ======================== Aligns ========================\n    const prepareResolveRef = shallowRef();\n    // `target` on `rc-align` can accept as a function to get the bind element or a point.\n    // ref: https://www.npmjs.com/package/rc-align\n    const getAlignTarget = () => {\n      if (props.point) {\n        return props.point;\n      }\n      return props.getRootDomNode;\n    };\n    const forceAlign = () => {\n      var _a;\n      (_a = alignRef.value) === null || _a === void 0 ? void 0 : _a.forceAlign();\n    };\n    const onInternalAlign = (popupDomNode, matchAlign) => {\n      var _a;\n      const nextAlignedClassName = props.getClassNameFromAlign(matchAlign);\n      const preAlignedClassName = alignedClassName.value;\n      if (alignedClassName.value !== nextAlignedClassName) {\n        alignedClassName.value = nextAlignedClassName;\n      }\n      if (status.value === 'align') {\n        // Repeat until not more align needed\n        if (preAlignedClassName !== nextAlignedClassName) {\n          Promise.resolve().then(() => {\n            forceAlign();\n          });\n        } else {\n          goNextStatus(() => {\n            var _a;\n            (_a = prepareResolveRef.value) === null || _a === void 0 ? void 0 : _a.call(prepareResolveRef);\n          });\n        }\n        (_a = props.onAlign) === null || _a === void 0 ? void 0 : _a.call(props, popupDomNode, matchAlign);\n      }\n    };\n    // ======================== Motion ========================\n    const motion = computed(() => {\n      const m = typeof props.animation === 'object' ? props.animation : getMotion(props);\n      ['onAfterEnter', 'onAfterLeave'].forEach(eventName => {\n        const originFn = m[eventName];\n        m[eventName] = node => {\n          goNextStatus();\n          // 结束后，强制 stable\n          status.value = 'stable';\n          originFn === null || originFn === void 0 ? void 0 : originFn(node);\n        };\n      });\n      return m;\n    });\n    const onShowPrepare = () => {\n      return new Promise(resolve => {\n        prepareResolveRef.value = resolve;\n      });\n    };\n    watch([motion, status], () => {\n      if (!motion.value && status.value === 'motion') {\n        goNextStatus();\n      }\n    }, {\n      immediate: true\n    });\n    expose({\n      forceAlign,\n      getElement: () => {\n        return elementRef.value.$el || elementRef.value;\n      }\n    });\n    const alignDisabled = computed(() => {\n      var _a;\n      if (((_a = props.align) === null || _a === void 0 ? void 0 : _a.points) && (status.value === 'align' || status.value === 'stable')) {\n        return false;\n      }\n      return true;\n    });\n    return () => {\n      var _a;\n      const {\n        zIndex,\n        align,\n        prefixCls,\n        destroyPopupOnHide,\n        onMouseenter,\n        onMouseleave,\n        onTouchstart = () => {},\n        onMousedown\n      } = props;\n      const statusValue = status.value;\n      // ======================== Render ========================\n      const mergedStyle = [_extends(_extends({}, stretchStyle.value), {\n        zIndex,\n        opacity: statusValue === 'motion' || statusValue === 'stable' || !visible.value ? null : 0,\n        // pointerEvents: statusValue === 'stable' ? null : 'none',\n        pointerEvents: !visible.value && statusValue !== 'stable' ? 'none' : null\n      }), attrs.style];\n      let childNode = flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots, {\n        visible: props.visible\n      }));\n      // Wrapper when multiple children\n      if (childNode.length > 1) {\n        const _childNode = function () {\n          return childNode;\n        }();\n        childNode = _createVNode(\"div\", {\n          \"class\": `${prefixCls}-content`\n        }, [childNode]);\n      }\n      const mergedClassName = classNames(prefixCls, attrs.class, alignedClassName.value, !props.arrow && `${prefixCls}-arrow-hidden`);\n      const hasAnimate = visible.value || !props.visible;\n      const transitionProps = hasAnimate ? getTransitionProps(motion.value.name, motion.value) : {};\n      return _createVNode(Transition, _objectSpread(_objectSpread({\n        \"ref\": elementRef\n      }, transitionProps), {}, {\n        \"onBeforeEnter\": onShowPrepare\n      }), {\n        default: () => {\n          return !destroyPopupOnHide || props.visible ? _withDirectives(_createVNode(Align, {\n            \"target\": getAlignTarget(),\n            \"key\": \"popup\",\n            \"ref\": alignRef,\n            \"monitorWindowResize\": true,\n            \"disabled\": alignDisabled.value,\n            \"align\": align,\n            \"onAlign\": onInternalAlign\n          }, {\n            default: () => _createVNode(\"div\", {\n              \"class\": mergedClassName,\n              \"onMouseenter\": onMouseenter,\n              \"onMouseleave\": onMouseleave,\n              \"onMousedown\": withModifiers(onMousedown, ['capture']),\n              [supportsPassive ? 'onTouchstartPassive' : 'onTouchstart']: withModifiers(onTouchstart, ['capture']),\n              \"style\": mergedStyle\n            }, [childNode])\n          }), [[_vShow, visible.value]]) : null;\n        }\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport { defineComponent, shallowRef, watch } from 'vue';\nimport { popupProps } from './interface';\nimport Mask from './Mask';\nimport MobilePopupInner from './MobilePopupInner';\nimport PopupInner from './PopupInner';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Popup',\n  inheritAttrs: false,\n  props: popupProps,\n  setup(props, _ref) {\n    let {\n      attrs,\n      slots,\n      expose\n    } = _ref;\n    const innerVisible = shallowRef(false);\n    const inMobile = shallowRef(false);\n    const popupRef = shallowRef();\n    const rootRef = shallowRef();\n    watch([() => props.visible, () => props.mobile], () => {\n      innerVisible.value = props.visible;\n      if (props.visible && props.mobile) {\n        inMobile.value = true;\n      }\n    }, {\n      immediate: true,\n      flush: 'post'\n    });\n    expose({\n      forceAlign: () => {\n        var _a;\n        (_a = popupRef.value) === null || _a === void 0 ? void 0 : _a.forceAlign();\n      },\n      getElement: () => {\n        var _a;\n        return (_a = popupRef.value) === null || _a === void 0 ? void 0 : _a.getElement();\n      }\n    });\n    return () => {\n      const cloneProps = _extends(_extends(_extends({}, props), attrs), {\n        visible: innerVisible.value\n      });\n      const popupNode = inMobile.value ? _createVNode(MobilePopupInner, _objectSpread(_objectSpread({}, cloneProps), {}, {\n        \"mobile\": props.mobile,\n        \"ref\": popupRef\n      }), {\n        default: slots.default\n      }) : _createVNode(PopupInner, _objectSpread(_objectSpread({}, cloneProps), {}, {\n        \"ref\": popupRef\n      }), {\n        default: slots.default\n      });\n      return _createVNode(\"div\", {\n        \"ref\": rootRef\n      }, [_createVNode(Mask, cloneProps, null), popupNode]);\n    };\n  }\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nfunction isPointsEq(a1, a2, isAlignPoint) {\n  if (isAlignPoint) {\n    return a1[0] === a2[0];\n  }\n  return a1[0] === a2[0] && a1[1] === a2[1];\n}\nexport function getAlignFromPlacement(builtinPlacements, placementStr, align) {\n  const baseAlign = builtinPlacements[placementStr] || {};\n  return _extends(_extends({}, baseAlign), align);\n}\nexport function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {\n  const {\n    points\n  } = align;\n  const placements = Object.keys(builtinPlacements);\n  for (let i = 0; i < placements.length; i += 1) {\n    const placement = placements[i];\n    if (isPointsEq(builtinPlacements[placement].points, points, isAlignPoint)) {\n      return `${prefixCls}-placement-${placement}`;\n    }\n  }\n  return '';\n}", "import { computed, inject, provide } from 'vue';\nconst PortalContextKey = Symbol('PortalContextKey');\nexport const useProvidePortal = function (instance) {\n  let config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    inTriggerContext: true\n  };\n  provide(PortalContextKey, {\n    inTriggerContext: config.inTriggerContext,\n    shouldRender: computed(() => {\n      const {\n        sPopupVisible,\n        popupRef,\n        forceRender,\n        autoDestroy\n      } = instance || {};\n      // if (popPortal) return true;\n      let shouldRender = false;\n      if (sPopupVisible || popupRef || forceRender) {\n        shouldRender = true;\n      }\n      if (!sPopupVisible && autoDestroy) {\n        shouldRender = false;\n      }\n      return shouldRender;\n    })\n  });\n};\nexport const useInjectPortal = () => {\n  useProvidePortal({}, {\n    inTriggerContext: false\n  });\n  const portalContext = inject(PortalContextKey, {\n    shouldRender: computed(() => false),\n    inTriggerContext: false\n  });\n  return {\n    shouldRender: computed(() => portalContext.shouldRender.value || portalContext.inTriggerContext === false)\n  };\n};", "import { createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport PropTypes from './vue-types';\nimport { defineComponent, nextTick, onBeforeMount, onMounted, onUpdated, Teleport, watch } from 'vue';\nimport { useInjectPortal } from '../vc-trigger/context';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Portal',\n  inheritAttrs: false,\n  props: {\n    getContainer: PropTypes.func.isRequired,\n    didUpdate: Function\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    let isSSR = true;\n    // getContainer 不会改变，不用响应式\n    let container;\n    const {\n      shouldRender\n    } = useInjectPortal();\n    function setContainer() {\n      if (shouldRender.value) {\n        container = props.getContainer();\n      }\n    }\n    onBeforeMount(() => {\n      isSSR = false;\n      // drawer\n      setContainer();\n    });\n    onMounted(() => {\n      if (container) return;\n      // https://github.com/vueComponent/ant-design-vue/issues/6937\n      setContainer();\n    });\n    const stopWatch = watch(shouldRender, () => {\n      if (shouldRender.value && !container) {\n        container = props.getContainer();\n      }\n      if (container) {\n        stopWatch();\n      }\n    });\n    onUpdated(() => {\n      nextTick(() => {\n        var _a;\n        if (shouldRender.value) {\n          (_a = props.didUpdate) === null || _a === void 0 ? void 0 : _a.call(props, props);\n        }\n      });\n    });\n    // onBeforeUnmount(() => {\n    //   if (container && container.parentNode) {\n    //     container.parentNode.removeChild(container);\n    //   }\n    // });\n    return () => {\n      var _a;\n      if (!shouldRender.value) return null;\n      if (isSSR) {\n        return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      }\n      return container ? _createVNode(Teleport, {\n        \"to\": container\n      }, slots) : null;\n    };\n  }\n});", "/* eslint-disable no-param-reassign */\nlet cached;\nexport default function getScrollBarSize(fresh) {\n  if (typeof document === 'undefined') {\n    return 0;\n  }\n  if (fresh || cached === undefined) {\n    const inner = document.createElement('div');\n    inner.style.width = '100%';\n    inner.style.height = '200px';\n    const outer = document.createElement('div');\n    const outerStyle = outer.style;\n    outerStyle.position = 'absolute';\n    outerStyle.top = '0';\n    outerStyle.left = '0';\n    outerStyle.pointerEvents = 'none';\n    outerStyle.visibility = 'hidden';\n    outerStyle.width = '200px';\n    outerStyle.height = '150px';\n    outerStyle.overflow = 'hidden';\n    outer.appendChild(inner);\n    document.body.appendChild(outer);\n    const widthContained = inner.offsetWidth;\n    outer.style.overflow = 'scroll';\n    let widthScroll = inner.offsetWidth;\n    if (widthContained === widthScroll) {\n      widthScroll = outer.clientWidth;\n    }\n    document.body.removeChild(outer);\n    cached = widthContained - widthScroll;\n  }\n  return cached;\n}\nfunction ensureSize(str) {\n  const match = str.match(/^(.*)px$/);\n  const value = Number(match === null || match === void 0 ? void 0 : match[1]);\n  return Number.isNaN(value) ? getScrollBarSize() : value;\n}\nexport function getTargetScrollBarSize(target) {\n  if (typeof document === 'undefined' || !target || !(target instanceof Element)) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  const {\n    width,\n    height\n  } = getComputedStyle(target, '::-webkit-scrollbar');\n  return {\n    width: ensureSize(width),\n    height: ensureSize(height)\n  };\n}", "import { computed, watchEffect } from 'vue';\nimport { updateCSS, removeCSS } from '../../vc-util/Dom/dynamicCSS';\nimport getScrollBarSize from '../../_util/getScrollBarSize';\nimport canUseDom from '../../_util/canUseDom';\nconst UNIQUE_ID = `vc-util-locker-${Date.now()}`;\nlet uuid = 0;\n/**../vc-util/Dom/dynam\n * Test usage export. Do not use in your production\n */\nexport function isBodyOverflowing() {\n  return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;\n}\nexport default function useScrollLocker(lock) {\n  const mergedLock = computed(() => !!lock && !!lock.value);\n  uuid += 1;\n  const id = `${UNIQUE_ID}_${uuid}`;\n  watchEffect(onClear => {\n    if (!canUseDom()) {\n      return;\n    }\n    if (mergedLock.value) {\n      const scrollbarSize = getScrollBarSize();\n      const isOverflow = isBodyOverflowing();\n      updateCSS(`\nhtml body {\n  overflow-y: hidden;\n  ${isOverflow ? `width: calc(100% - ${scrollbarSize}px);` : ''}\n}`, id);\n    } else {\n      removeCSS(id);\n    }\n    onClear(() => {\n      removeCSS(id);\n    });\n  }, {\n    flush: 'post'\n  });\n}", "import { createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport PropTypes from './vue-types';\nimport Portal from './Portal';\nimport { defineComponent, shallowRef, watch, onMounted, onBeforeUnmount, onUpdated, nextTick, computed } from 'vue';\nimport canUseDom from './canUseDom';\nimport raf from './raf';\nimport { booleanType } from './type';\nimport useScrollLocker from './hooks/useScrollLocker';\nlet openCount = 0;\nconst supportDom = canUseDom();\n/** @private Test usage only */\nexport function getOpenCount() {\n  return process.env.NODE_ENV === 'test' ? openCount : 0;\n}\nconst getParent = getContainer => {\n  if (!supportDom) {\n    return null;\n  }\n  if (getContainer) {\n    if (typeof getContainer === 'string') {\n      return document.querySelectorAll(getContainer)[0];\n    }\n    if (typeof getContainer === 'function') {\n      return getContainer();\n    }\n    if (typeof getContainer === 'object' && getContainer instanceof window.HTMLElement) {\n      return getContainer;\n    }\n  }\n  return document.body;\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'PortalWrapper',\n  inheritAttrs: false,\n  props: {\n    wrapperClassName: String,\n    forceRender: {\n      type: Boolean,\n      default: undefined\n    },\n    getContainer: PropTypes.any,\n    visible: {\n      type: Boolean,\n      default: undefined\n    },\n    autoLock: booleanType(),\n    didUpdate: Function\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const container = shallowRef();\n    const componentRef = shallowRef();\n    const rafId = shallowRef();\n    const triggerUpdate = shallowRef(1);\n    const defaultContainer = canUseDom() && document.createElement('div');\n    const removeCurrentContainer = () => {\n      var _a, _b;\n      // Portal will remove from `parentNode`.\n      // Let's handle this again to avoid refactor issue.\n      if (container.value === defaultContainer) {\n        (_b = (_a = container.value) === null || _a === void 0 ? void 0 : _a.parentNode) === null || _b === void 0 ? void 0 : _b.removeChild(container.value);\n      }\n      container.value = null;\n    };\n    let parent = null;\n    const attachToParent = function () {\n      let force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (force || container.value && !container.value.parentNode) {\n        parent = getParent(props.getContainer);\n        if (parent) {\n          parent.appendChild(container.value);\n          return true;\n        }\n        return false;\n      }\n      return true;\n    };\n    const getContainer = () => {\n      if (!supportDom) {\n        return null;\n      }\n      if (!container.value) {\n        container.value = defaultContainer;\n        attachToParent(true);\n      }\n      setWrapperClassName();\n      return container.value;\n    };\n    const setWrapperClassName = () => {\n      const {\n        wrapperClassName\n      } = props;\n      if (container.value && wrapperClassName && wrapperClassName !== container.value.className) {\n        container.value.className = wrapperClassName;\n      }\n    };\n    onUpdated(() => {\n      setWrapperClassName();\n      attachToParent();\n    });\n    useScrollLocker(computed(() => {\n      return props.autoLock && props.visible && canUseDom() && (container.value === document.body || container.value === defaultContainer);\n    }));\n    onMounted(() => {\n      let init = false;\n      watch([() => props.visible, () => props.getContainer], (_ref2, _ref3) => {\n        let [visible, getContainer] = _ref2;\n        let [prevVisible, prevGetContainer] = _ref3;\n        // Update count\n        if (supportDom) {\n          parent = getParent(props.getContainer);\n          if (parent === document.body) {\n            if (visible && !prevVisible) {\n              openCount += 1;\n            } else if (init) {\n              openCount -= 1;\n            }\n          }\n        }\n        if (init) {\n          // Clean up container if needed\n          const getContainerIsFunc = typeof getContainer === 'function' && typeof prevGetContainer === 'function';\n          if (getContainerIsFunc ? getContainer.toString() !== prevGetContainer.toString() : getContainer !== prevGetContainer) {\n            removeCurrentContainer();\n          }\n        }\n        init = true;\n      }, {\n        immediate: true,\n        flush: 'post'\n      });\n      nextTick(() => {\n        if (!attachToParent()) {\n          rafId.value = raf(() => {\n            triggerUpdate.value += 1;\n          });\n        }\n      });\n    });\n    onBeforeUnmount(() => {\n      const {\n        visible\n      } = props;\n      if (supportDom && parent === document.body) {\n        // 离开时不会 render， 导到离开时数值不变，改用 func 。。\n        openCount = visible && openCount ? openCount - 1 : openCount;\n      }\n      removeCurrentContainer();\n      raf.cancel(rafId.value);\n    });\n    return () => {\n      const {\n        forceRender,\n        visible\n      } = props;\n      let portal = null;\n      const childProps = {\n        getOpenCount: () => openCount,\n        getContainer\n      };\n      if (triggerUpdate.value && (forceRender || visible || componentRef.value)) {\n        portal = _createVNode(Portal, {\n          \"getContainer\": getContainer,\n          \"ref\": componentRef,\n          \"didUpdate\": props.didUpdate\n        }, {\n          default: () => {\n            var _a;\n            return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots, childProps);\n          }\n        });\n      }\n      return portal;\n    };\n  }\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Fragment as _Fragment, createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport { computed, defineComponent, inject, provide, shallowRef } from 'vue';\nimport { triggerProps, noop } from './interface';\nimport contains from '../vc-util/Dom/contains';\nimport raf from '../_util/raf';\nimport { hasProp, getComponent, getEvents, filterEmpty, getSlot, findDOMNode } from '../_util/props-util';\nimport addEventListener from '../vc-util/Dom/addEventListener';\nimport Popup from './Popup';\nimport { getAlignFromPlacement, getAlignPopupClassName } from './utils/alignUtil';\nimport BaseMixin from '../_util/BaseMixin';\nimport Portal from '../_util/PortalWrapper';\nimport classNames from '../_util/classNames';\nimport { cloneElement } from '../_util/vnode';\nimport supportsPassive from '../_util/supportsPassive';\nimport { useProvidePortal } from './context';\nconst ALL_HANDLERS = ['onClick', 'onMousedown', 'onTouchstart', 'onMouseenter', 'onMouseleave', 'onFocus', 'onBlur', 'onContextmenu'];\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Trigger',\n  mixins: [BaseMixin],\n  inheritAttrs: false,\n  props: triggerProps(),\n  setup(props) {\n    const align = computed(() => {\n      const {\n        popupPlacement,\n        popupAlign,\n        builtinPlacements\n      } = props;\n      if (popupPlacement && builtinPlacements) {\n        return getAlignFromPlacement(builtinPlacements, popupPlacement, popupAlign);\n      }\n      return popupAlign;\n    });\n    const popupRef = shallowRef(null);\n    const setPopupRef = val => {\n      popupRef.value = val;\n    };\n    return {\n      vcTriggerContext: inject('vcTriggerContext', {}),\n      popupRef,\n      setPopupRef,\n      triggerRef: shallowRef(null),\n      align,\n      focusTime: null,\n      clickOutsideHandler: null,\n      contextmenuOutsideHandler1: null,\n      contextmenuOutsideHandler2: null,\n      touchOutsideHandler: null,\n      attachId: null,\n      delayTimer: null,\n      hasPopupMouseDown: false,\n      preClickTime: null,\n      preTouchTime: null,\n      mouseDownTimeout: null,\n      childOriginEvents: {}\n    };\n  },\n  data() {\n    const props = this.$props;\n    let popupVisible;\n    if (this.popupVisible !== undefined) {\n      popupVisible = !!props.popupVisible;\n    } else {\n      popupVisible = !!props.defaultPopupVisible;\n    }\n    ALL_HANDLERS.forEach(h => {\n      this[`fire${h}`] = e => {\n        this.fireEvents(h, e);\n      };\n    });\n    return {\n      prevPopupVisible: popupVisible,\n      sPopupVisible: popupVisible,\n      point: null\n    };\n  },\n  watch: {\n    popupVisible(val) {\n      if (val !== undefined) {\n        this.prevPopupVisible = this.sPopupVisible;\n        this.sPopupVisible = val;\n      }\n    }\n  },\n  created() {\n    provide('vcTriggerContext', {\n      onPopupMouseDown: this.onPopupMouseDown,\n      onPopupMouseenter: this.onPopupMouseenter,\n      onPopupMouseleave: this.onPopupMouseleave\n    });\n    useProvidePortal(this);\n  },\n  deactivated() {\n    this.setPopupVisible(false);\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.updatedCal();\n    });\n  },\n  updated() {\n    this.$nextTick(() => {\n      this.updatedCal();\n    });\n  },\n  beforeUnmount() {\n    this.clearDelayTimer();\n    this.clearOutsideHandler();\n    clearTimeout(this.mouseDownTimeout);\n    raf.cancel(this.attachId);\n  },\n  methods: {\n    updatedCal() {\n      const props = this.$props;\n      const state = this.$data;\n      // We must listen to `mousedown` or `touchstart`, edge case:\n      // https://github.com/ant-design/ant-design/issues/5804\n      // https://github.com/react-component/calendar/issues/250\n      // https://github.com/react-component/trigger/issues/50\n      if (state.sPopupVisible) {\n        let currentDocument;\n        if (!this.clickOutsideHandler && (this.isClickToHide() || this.isContextmenuToShow())) {\n          currentDocument = props.getDocument(this.getRootDomNode());\n          this.clickOutsideHandler = addEventListener(currentDocument, 'mousedown', this.onDocumentClick);\n        }\n        // always hide on mobile\n        if (!this.touchOutsideHandler) {\n          currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n          this.touchOutsideHandler = addEventListener(currentDocument, 'touchstart', this.onDocumentClick, supportsPassive ? {\n            passive: false\n          } : false);\n        }\n        // close popup when trigger type contains 'onContextmenu' and document is scrolling.\n        if (!this.contextmenuOutsideHandler1 && this.isContextmenuToShow()) {\n          currentDocument = currentDocument || props.getDocument(this.getRootDomNode());\n          this.contextmenuOutsideHandler1 = addEventListener(currentDocument, 'scroll', this.onContextmenuClose);\n        }\n        // close popup when trigger type contains 'onContextmenu' and window is blur.\n        if (!this.contextmenuOutsideHandler2 && this.isContextmenuToShow()) {\n          this.contextmenuOutsideHandler2 = addEventListener(window, 'blur', this.onContextmenuClose);\n        }\n      } else {\n        this.clearOutsideHandler();\n      }\n    },\n    onMouseenter(e) {\n      const {\n        mouseEnterDelay\n      } = this.$props;\n      this.fireEvents('onMouseenter', e);\n      this.delaySetPopupVisible(true, mouseEnterDelay, mouseEnterDelay ? null : e);\n    },\n    onMouseMove(e) {\n      this.fireEvents('onMousemove', e);\n      this.setPoint(e);\n    },\n    onMouseleave(e) {\n      this.fireEvents('onMouseleave', e);\n      this.delaySetPopupVisible(false, this.$props.mouseLeaveDelay);\n    },\n    onPopupMouseenter() {\n      const {\n        vcTriggerContext = {}\n      } = this;\n      if (vcTriggerContext.onPopupMouseenter) {\n        vcTriggerContext.onPopupMouseenter();\n      }\n      this.clearDelayTimer();\n    },\n    onPopupMouseleave(e) {\n      var _a;\n      if (e && e.relatedTarget && !e.relatedTarget.setTimeout && contains((_a = this.popupRef) === null || _a === void 0 ? void 0 : _a.getElement(), e.relatedTarget)) {\n        return;\n      }\n      if (this.isMouseLeaveToHide()) {\n        this.delaySetPopupVisible(false, this.$props.mouseLeaveDelay);\n      }\n      const {\n        vcTriggerContext = {}\n      } = this;\n      if (vcTriggerContext.onPopupMouseleave) {\n        vcTriggerContext.onPopupMouseleave(e);\n      }\n    },\n    onFocus(e) {\n      this.fireEvents('onFocus', e);\n      // incase focusin and focusout\n      this.clearDelayTimer();\n      if (this.isFocusToShow()) {\n        this.focusTime = Date.now();\n        this.delaySetPopupVisible(true, this.$props.focusDelay);\n      }\n    },\n    onMousedown(e) {\n      this.fireEvents('onMousedown', e);\n      this.preClickTime = Date.now();\n    },\n    onTouchstart(e) {\n      this.fireEvents('onTouchstart', e);\n      this.preTouchTime = Date.now();\n    },\n    onBlur(e) {\n      if (!contains(e.target, e.relatedTarget || document.activeElement)) {\n        this.fireEvents('onBlur', e);\n        this.clearDelayTimer();\n        if (this.isBlurToHide()) {\n          this.delaySetPopupVisible(false, this.$props.blurDelay);\n        }\n      }\n    },\n    onContextmenu(e) {\n      e.preventDefault();\n      this.fireEvents('onContextmenu', e);\n      this.setPopupVisible(true, e);\n    },\n    onContextmenuClose() {\n      if (this.isContextmenuToShow()) {\n        this.close();\n      }\n    },\n    onClick(event) {\n      this.fireEvents('onClick', event);\n      // focus will trigger click\n      if (this.focusTime) {\n        let preTime;\n        if (this.preClickTime && this.preTouchTime) {\n          preTime = Math.min(this.preClickTime, this.preTouchTime);\n        } else if (this.preClickTime) {\n          preTime = this.preClickTime;\n        } else if (this.preTouchTime) {\n          preTime = this.preTouchTime;\n        }\n        if (Math.abs(preTime - this.focusTime) < 20) {\n          return;\n        }\n        this.focusTime = 0;\n      }\n      this.preClickTime = 0;\n      this.preTouchTime = 0;\n      // Only prevent default when all the action is click.\n      // https://github.com/ant-design/ant-design/issues/17043\n      // https://github.com/ant-design/ant-design/issues/17291\n      if (this.isClickToShow() && (this.isClickToHide() || this.isBlurToHide()) && event && event.preventDefault) {\n        event.preventDefault();\n      }\n      if (event && event.domEvent) {\n        event.domEvent.preventDefault();\n      }\n      const nextVisible = !this.$data.sPopupVisible;\n      if (this.isClickToHide() && !nextVisible || nextVisible && this.isClickToShow()) {\n        this.setPopupVisible(!this.$data.sPopupVisible, event);\n      }\n    },\n    onPopupMouseDown() {\n      const {\n        vcTriggerContext = {}\n      } = this;\n      this.hasPopupMouseDown = true;\n      clearTimeout(this.mouseDownTimeout);\n      this.mouseDownTimeout = setTimeout(() => {\n        this.hasPopupMouseDown = false;\n      }, 0);\n      if (vcTriggerContext.onPopupMouseDown) {\n        vcTriggerContext.onPopupMouseDown(...arguments);\n      }\n    },\n    onDocumentClick(event) {\n      if (this.$props.mask && !this.$props.maskClosable) {\n        return;\n      }\n      const target = event.target;\n      const root = this.getRootDomNode();\n      const popupNode = this.getPopupDomNode();\n      if (\n      // mousedown on the target should also close popup when action is contextMenu.\n      // https://github.com/ant-design/ant-design/issues/29853\n      (!contains(root, target) || this.isContextMenuOnly()) && !contains(popupNode, target) && !this.hasPopupMouseDown) {\n        // https://github.com/vuejs/core/issues/4462\n        // vue 动画bug导致 https://github.com/vueComponent/ant-design-vue/issues/5259，\n        // 改成延时解决\n        this.delaySetPopupVisible(false, 0.1);\n      }\n    },\n    getPopupDomNode() {\n      var _a;\n      // for test\n      return ((_a = this.popupRef) === null || _a === void 0 ? void 0 : _a.getElement()) || null;\n    },\n    getRootDomNode() {\n      var _a, _b, _c, _d;\n      const {\n        getTriggerDOMNode\n      } = this.$props;\n      if (getTriggerDOMNode) {\n        const domNode = ((_b = (_a = this.triggerRef) === null || _a === void 0 ? void 0 : _a.$el) === null || _b === void 0 ? void 0 : _b.nodeName) === '#comment' ? null : findDOMNode(this.triggerRef);\n        return findDOMNode(getTriggerDOMNode(domNode));\n      }\n      try {\n        const domNode = ((_d = (_c = this.triggerRef) === null || _c === void 0 ? void 0 : _c.$el) === null || _d === void 0 ? void 0 : _d.nodeName) === '#comment' ? null : findDOMNode(this.triggerRef);\n        if (domNode) {\n          return domNode;\n        }\n      } catch (err) {\n        // Do nothing\n      }\n      return findDOMNode(this);\n    },\n    handleGetPopupClassFromAlign(align) {\n      const className = [];\n      const props = this.$props;\n      const {\n        popupPlacement,\n        builtinPlacements,\n        prefixCls,\n        alignPoint,\n        getPopupClassNameFromAlign\n      } = props;\n      if (popupPlacement && builtinPlacements) {\n        className.push(getAlignPopupClassName(builtinPlacements, prefixCls, align, alignPoint));\n      }\n      if (getPopupClassNameFromAlign) {\n        className.push(getPopupClassNameFromAlign(align));\n      }\n      return className.join(' ');\n    },\n    getPopupAlign() {\n      const props = this.$props;\n      const {\n        popupPlacement,\n        popupAlign,\n        builtinPlacements\n      } = props;\n      if (popupPlacement && builtinPlacements) {\n        return getAlignFromPlacement(builtinPlacements, popupPlacement, popupAlign);\n      }\n      return popupAlign;\n    },\n    getComponent() {\n      const mouseProps = {};\n      if (this.isMouseEnterToShow()) {\n        mouseProps.onMouseenter = this.onPopupMouseenter;\n      }\n      if (this.isMouseLeaveToHide()) {\n        mouseProps.onMouseleave = this.onPopupMouseleave;\n      }\n      mouseProps.onMousedown = this.onPopupMouseDown;\n      mouseProps[supportsPassive ? 'onTouchstartPassive' : 'onTouchstart'] = this.onPopupMouseDown;\n      const {\n        handleGetPopupClassFromAlign,\n        getRootDomNode,\n        $attrs\n      } = this;\n      const {\n        prefixCls,\n        destroyPopupOnHide,\n        popupClassName,\n        popupAnimation,\n        popupTransitionName,\n        popupStyle,\n        mask,\n        maskAnimation,\n        maskTransitionName,\n        zIndex,\n        stretch,\n        alignPoint,\n        mobile,\n        arrow,\n        forceRender\n      } = this.$props;\n      const {\n        sPopupVisible,\n        point\n      } = this.$data;\n      const popupProps = _extends(_extends({\n        prefixCls,\n        arrow,\n        destroyPopupOnHide,\n        visible: sPopupVisible,\n        point: alignPoint ? point : null,\n        align: this.align,\n        animation: popupAnimation,\n        getClassNameFromAlign: handleGetPopupClassFromAlign,\n        stretch,\n        getRootDomNode,\n        mask,\n        zIndex,\n        transitionName: popupTransitionName,\n        maskAnimation,\n        maskTransitionName,\n        class: popupClassName,\n        style: popupStyle,\n        onAlign: $attrs.onPopupAlign || noop\n      }, mouseProps), {\n        ref: this.setPopupRef,\n        mobile,\n        forceRender\n      });\n      return _createVNode(Popup, popupProps, {\n        default: this.$slots.popup || (() => getComponent(this, 'popup'))\n      });\n    },\n    attachParent(popupContainer) {\n      raf.cancel(this.attachId);\n      const {\n        getPopupContainer,\n        getDocument\n      } = this.$props;\n      const domNode = this.getRootDomNode();\n      let mountNode;\n      if (!getPopupContainer) {\n        mountNode = getDocument(this.getRootDomNode()).body;\n      } else if (domNode || getPopupContainer.length === 0) {\n        // Compatible for legacy getPopupContainer with domNode argument.\n        // If no need `domNode` argument, will call directly.\n        // https://codesandbox.io/s/eloquent-mclean-ss93m?file=/src/App.js\n        mountNode = getPopupContainer(domNode);\n      }\n      if (mountNode) {\n        mountNode.appendChild(popupContainer);\n      } else {\n        // Retry after frame render in case parent not ready\n        this.attachId = raf(() => {\n          this.attachParent(popupContainer);\n        });\n      }\n    },\n    getContainer() {\n      const {\n        $props: props\n      } = this;\n      const {\n        getDocument\n      } = props;\n      const popupContainer = getDocument(this.getRootDomNode()).createElement('div');\n      // Make sure default popup container will never cause scrollbar appearing\n      // https://github.com/react-component/trigger/issues/41\n      popupContainer.style.position = 'absolute';\n      popupContainer.style.top = '0';\n      popupContainer.style.left = '0';\n      popupContainer.style.width = '100%';\n      this.attachParent(popupContainer);\n      return popupContainer;\n    },\n    setPopupVisible(sPopupVisible, event) {\n      const {\n        alignPoint,\n        sPopupVisible: prevPopupVisible,\n        onPopupVisibleChange\n      } = this;\n      this.clearDelayTimer();\n      if (prevPopupVisible !== sPopupVisible) {\n        if (!hasProp(this, 'popupVisible')) {\n          this.setState({\n            sPopupVisible,\n            prevPopupVisible\n          });\n        }\n        onPopupVisibleChange && onPopupVisibleChange(sPopupVisible);\n      }\n      // Always record the point position since mouseEnterDelay will delay the show\n      if (alignPoint && event && sPopupVisible) {\n        this.setPoint(event);\n      }\n    },\n    setPoint(point) {\n      const {\n        alignPoint\n      } = this.$props;\n      if (!alignPoint || !point) return;\n      this.setState({\n        point: {\n          pageX: point.pageX,\n          pageY: point.pageY\n        }\n      });\n    },\n    handlePortalUpdate() {\n      if (this.prevPopupVisible !== this.sPopupVisible) {\n        this.afterPopupVisibleChange(this.sPopupVisible);\n      }\n    },\n    delaySetPopupVisible(visible, delayS, event) {\n      const delay = delayS * 1000;\n      this.clearDelayTimer();\n      if (delay) {\n        const point = event ? {\n          pageX: event.pageX,\n          pageY: event.pageY\n        } : null;\n        this.delayTimer = setTimeout(() => {\n          this.setPopupVisible(visible, point);\n          this.clearDelayTimer();\n        }, delay);\n      } else {\n        this.setPopupVisible(visible, event);\n      }\n    },\n    clearDelayTimer() {\n      if (this.delayTimer) {\n        clearTimeout(this.delayTimer);\n        this.delayTimer = null;\n      }\n    },\n    clearOutsideHandler() {\n      if (this.clickOutsideHandler) {\n        this.clickOutsideHandler.remove();\n        this.clickOutsideHandler = null;\n      }\n      if (this.contextmenuOutsideHandler1) {\n        this.contextmenuOutsideHandler1.remove();\n        this.contextmenuOutsideHandler1 = null;\n      }\n      if (this.contextmenuOutsideHandler2) {\n        this.contextmenuOutsideHandler2.remove();\n        this.contextmenuOutsideHandler2 = null;\n      }\n      if (this.touchOutsideHandler) {\n        this.touchOutsideHandler.remove();\n        this.touchOutsideHandler = null;\n      }\n    },\n    createTwoChains(event) {\n      let fn = () => {};\n      const events = getEvents(this);\n      if (this.childOriginEvents[event] && events[event]) {\n        return this[`fire${event}`];\n      }\n      fn = this.childOriginEvents[event] || events[event] || fn;\n      return fn;\n    },\n    isClickToShow() {\n      const {\n        action,\n        showAction\n      } = this.$props;\n      return action.indexOf('click') !== -1 || showAction.indexOf('click') !== -1;\n    },\n    isContextMenuOnly() {\n      const {\n        action\n      } = this.$props;\n      return action === 'contextmenu' || action.length === 1 && action[0] === 'contextmenu';\n    },\n    isContextmenuToShow() {\n      const {\n        action,\n        showAction\n      } = this.$props;\n      return action.indexOf('contextmenu') !== -1 || showAction.indexOf('contextmenu') !== -1;\n    },\n    isClickToHide() {\n      const {\n        action,\n        hideAction\n      } = this.$props;\n      return action.indexOf('click') !== -1 || hideAction.indexOf('click') !== -1;\n    },\n    isMouseEnterToShow() {\n      const {\n        action,\n        showAction\n      } = this.$props;\n      return action.indexOf('hover') !== -1 || showAction.indexOf('mouseenter') !== -1;\n    },\n    isMouseLeaveToHide() {\n      const {\n        action,\n        hideAction\n      } = this.$props;\n      return action.indexOf('hover') !== -1 || hideAction.indexOf('mouseleave') !== -1;\n    },\n    isFocusToShow() {\n      const {\n        action,\n        showAction\n      } = this.$props;\n      return action.indexOf('focus') !== -1 || showAction.indexOf('focus') !== -1;\n    },\n    isBlurToHide() {\n      const {\n        action,\n        hideAction\n      } = this.$props;\n      return action.indexOf('focus') !== -1 || hideAction.indexOf('blur') !== -1;\n    },\n    forcePopupAlign() {\n      var _a;\n      if (this.$data.sPopupVisible) {\n        (_a = this.popupRef) === null || _a === void 0 ? void 0 : _a.forceAlign();\n      }\n    },\n    fireEvents(type, e) {\n      if (this.childOriginEvents[type]) {\n        this.childOriginEvents[type](e);\n      }\n      const event = this.$props[type] || this.$attrs[type];\n      if (event) {\n        event(e);\n      }\n    },\n    close() {\n      this.setPopupVisible(false);\n    }\n  },\n  render() {\n    const {\n      $attrs\n    } = this;\n    const children = filterEmpty(getSlot(this));\n    const {\n      alignPoint,\n      getPopupContainer\n    } = this.$props;\n    const child = children[0];\n    this.childOriginEvents = getEvents(child);\n    const newChildProps = {\n      key: 'trigger'\n    };\n    if (this.isContextmenuToShow()) {\n      newChildProps.onContextmenu = this.onContextmenu;\n    } else {\n      newChildProps.onContextmenu = this.createTwoChains('onContextmenu');\n    }\n    if (this.isClickToHide() || this.isClickToShow()) {\n      newChildProps.onClick = this.onClick;\n      newChildProps.onMousedown = this.onMousedown;\n      newChildProps[supportsPassive ? 'onTouchstartPassive' : 'onTouchstart'] = this.onTouchstart;\n    } else {\n      newChildProps.onClick = this.createTwoChains('onClick');\n      newChildProps.onMousedown = this.createTwoChains('onMousedown');\n      newChildProps[supportsPassive ? 'onTouchstartPassive' : 'onTouchstart'] = this.createTwoChains('onTouchstart');\n    }\n    if (this.isMouseEnterToShow()) {\n      newChildProps.onMouseenter = this.onMouseenter;\n      if (alignPoint) {\n        newChildProps.onMousemove = this.onMouseMove;\n      }\n    } else {\n      newChildProps.onMouseenter = this.createTwoChains('onMouseenter');\n    }\n    if (this.isMouseLeaveToHide()) {\n      newChildProps.onMouseleave = this.onMouseleave;\n    } else {\n      newChildProps.onMouseleave = this.createTwoChains('onMouseleave');\n    }\n    if (this.isFocusToShow() || this.isBlurToHide()) {\n      newChildProps.onFocus = this.onFocus;\n      newChildProps.onBlur = this.onBlur;\n    } else {\n      newChildProps.onFocus = this.createTwoChains('onFocus');\n      newChildProps.onBlur = e => {\n        if (e && (!e.relatedTarget || !contains(e.target, e.relatedTarget))) {\n          this.createTwoChains('onBlur')(e);\n        }\n      };\n    }\n    const childrenClassName = classNames(child && child.props && child.props.class, $attrs.class);\n    if (childrenClassName) {\n      newChildProps.class = childrenClassName;\n    }\n    const trigger = cloneElement(child, _extends(_extends({}, newChildProps), {\n      ref: 'triggerRef'\n    }), true, true);\n    const portal = _createVNode(Portal, {\n      \"key\": \"portal\",\n      \"getContainer\": getPopupContainer && (() => getPopupContainer(this.getRootDomNode())),\n      \"didUpdate\": this.handlePortalUpdate,\n      \"visible\": this.$data.sPopupVisible\n    }, {\n      default: this.getComponent\n    });\n    return _createVNode(_Fragment, null, [trigger, portal]);\n  }\n});", "// based on rc-trigger 5.2.10\nimport Trigger from './Trigger';\nimport { triggerProps } from './interface';\nexport { triggerProps };\nexport default Trigger;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nconst initMotionCommon = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\n// FIXME: origin less code seems same as initMotionCommon. Maybe we can safe remove\nconst initMotionCommonLeave = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\nexport const initMotion = function (motionCls, inKeyframes, outKeyframes, duration) {\n  let sameLevel = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return {\n    [`\n      ${sameLevelPrefix}${motionCls}-enter,\n      ${sameLevelPrefix}${motionCls}-appear\n    `]: _extends(_extends({}, initMotionCommon(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`${sameLevelPrefix}${motionCls}-leave`]: _extends(_extends({}, initMotionCommonLeave(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`\n      ${sameLevelPrefix}${motionCls}-enter${motionCls}-enter-active,\n      ${sameLevelPrefix}${motionCls}-appear${motionCls}-appear-active\n    `]: {\n      animationName: inKeyframes,\n      animationPlayState: 'running'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave${motionCls}-leave-active`]: {\n      animationName: outKeyframes,\n      animationPlayState: 'running',\n      pointerEvents: 'none'\n    }\n  };\n};", "import { Keyframes } from '../../_util/cssinjs';\nimport { initMotion } from './motion';\nexport const fadeIn = new Keyframes('antFadeIn', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\nexport const fadeOut = new Keyframes('antFadeOut', {\n  '0%': {\n    opacity: 1\n  },\n  '100%': {\n    opacity: 0\n  }\n});\nexport const initFadeMotion = function (token) {\n  let sameLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-fade`;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return [initMotion(motionCls, fadeIn, fadeOut, token.motionDurationMid, sameLevel), {\n    [`\n        ${sameLevelPrefix}${motionCls}-enter,\n        ${sameLevelPrefix}${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: 'linear'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave`]: {\n      animationTimingFunction: 'linear'\n    }\n  }];\n};", "import { Keyframes } from '../../_util/cssinjs';\nimport { initMotion } from './motion';\nexport const moveDownIn = new Keyframes('antMoveDownIn', {\n  '0%': {\n    transform: 'translate3d(0, 100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveDownOut = new Keyframes('antMoveDownOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(0, 100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nexport const moveLeftIn = new Keyframes('antMoveLeftIn', {\n  '0%': {\n    transform: 'translate3d(-100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveLeftOut = new Keyframes('antMoveLeftOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(-100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nexport const moveRightIn = new Keyframes('antMoveRightIn', {\n  '0%': {\n    transform: 'translate3d(100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveRightOut = new Keyframes('antMoveRightOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nexport const moveUpIn = new Keyframes('antMoveUpIn', {\n  '0%': {\n    transform: 'translate3d(0, -100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveUpOut = new Keyframes('antMoveUpOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(0, -100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nconst moveMotion = {\n  'move-up': {\n    inKeyframes: moveUpIn,\n    outKeyframes: moveUpOut\n  },\n  'move-down': {\n    inKeyframes: moveDownIn,\n    outKeyframes: moveDownOut\n  },\n  'move-left': {\n    inKeyframes: moveLeftIn,\n    outKeyframes: moveLeftOut\n  },\n  'move-right': {\n    inKeyframes: moveRightIn,\n    outKeyframes: moveRightOut\n  }\n};\nexport const initMoveMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = moveMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, token.motionDurationMid), {\n    [`\n        ${motionCls}-enter,\n        ${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutCirc\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInOutCirc\n    }\n  }];\n};", "import { Keyframes } from '../../_util/cssinjs';\nimport { initMotion } from './motion';\nexport const slideUpIn = new Keyframes('antSlideUpIn', {\n  '0%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  }\n});\nexport const slideUpOut = new Keyframes('antSlideUpOut', {\n  '0%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  }\n});\nexport const slideDownIn = new Keyframes('antSlideDownIn', {\n  '0%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '100% 100%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '100% 100%',\n    opacity: 1\n  }\n});\nexport const slideDownOut = new Keyframes('antSlideDownOut', {\n  '0%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '100% 100%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '100% 100%',\n    opacity: 0\n  }\n});\nexport const slideLeftIn = new Keyframes('antSlideLeftIn', {\n  '0%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  }\n});\nexport const slideLeftOut = new Keyframes('antSlideLeftOut', {\n  '0%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  }\n});\nexport const slideRightIn = new Keyframes('antSlideRightIn', {\n  '0%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '100% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '100% 0%',\n    opacity: 1\n  }\n});\nexport const slideRightOut = new Keyframes('antSlideRightOut', {\n  '0%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '100% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '100% 0%',\n    opacity: 0\n  }\n});\nconst slideMotion = {\n  'slide-up': {\n    inKeyframes: slideUpIn,\n    outKeyframes: slideUpOut\n  },\n  'slide-down': {\n    inKeyframes: slideDownIn,\n    outKeyframes: slideDownOut\n  },\n  'slide-left': {\n    inKeyframes: slideLeftIn,\n    outKeyframes: slideLeftOut\n  },\n  'slide-right': {\n    inKeyframes: slideRightIn,\n    outKeyframes: slideRightOut\n  }\n};\nexport const initSlideMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = slideMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, token.motionDurationMid), {\n    [`\n      ${motionCls}-enter,\n      ${motionCls}-appear\n    `]: {\n      transform: 'scale(0)',\n      transformOrigin: '0% 0%',\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutQuint\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInQuint\n    }\n  }];\n};", "import { Keyframes } from '../../_util/cssinjs';\nimport { initMotion } from './motion';\nexport const zoomIn = new Keyframes('antZoomIn', {\n  '0%': {\n    transform: 'scale(0.2)',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    opacity: 1\n  }\n});\nexport const zoomOut = new Keyframes('antZoomOut', {\n  '0%': {\n    transform: 'scale(1)'\n  },\n  '100%': {\n    transform: 'scale(0.2)',\n    opacity: 0\n  }\n});\nexport const zoomBigIn = new Keyframes('antZoomBigIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    opacity: 1\n  }\n});\nexport const zoomBigOut = new Keyframes('antZoomBigOut', {\n  '0%': {\n    transform: 'scale(1)'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    opacity: 0\n  }\n});\nexport const zoomUpIn = new Keyframes('antZoomUpIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 0%'\n  }\n});\nexport const zoomUpOut = new Keyframes('antZoomUpOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 0%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 0%',\n    opacity: 0\n  }\n});\nexport const zoomLeftIn = new Keyframes('antZoomLeftIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '0% 50%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '0% 50%'\n  }\n});\nexport const zoomLeftOut = new Keyframes('antZoomLeftOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '0% 50%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '0% 50%',\n    opacity: 0\n  }\n});\nexport const zoomRightIn = new Keyframes('antZoomRightIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '100% 50%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '100% 50%'\n  }\n});\nexport const zoomRightOut = new Keyframes('antZoomRightOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '100% 50%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '100% 50%',\n    opacity: 0\n  }\n});\nexport const zoomDownIn = new Keyframes('antZoomDownIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 100%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 100%'\n  }\n});\nexport const zoomDownOut = new Keyframes('antZoomDownOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 100%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 100%',\n    opacity: 0\n  }\n});\nconst zoomMotion = {\n  zoom: {\n    inKeyframes: zoomIn,\n    outKeyframes: zoomOut\n  },\n  'zoom-big': {\n    inKeyframes: zoomBigIn,\n    outKeyframes: zoomBigOut\n  },\n  'zoom-big-fast': {\n    inKeyframes: zoomBigIn,\n    outKeyframes: zoomBigOut\n  },\n  'zoom-left': {\n    inKeyframes: zoomLeftIn,\n    outKeyframes: zoomLeftOut\n  },\n  'zoom-right': {\n    inKeyframes: zoomRightIn,\n    outKeyframes: zoomRightOut\n  },\n  'zoom-up': {\n    inKeyframes: zoomUpIn,\n    outKeyframes: zoomUpOut\n  },\n  'zoom-down': {\n    inKeyframes: zoomDownIn,\n    outKeyframes: zoomDownOut\n  }\n};\nexport const initZoomMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = zoomMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, motionName === 'zoom-big-fast' ? token.motionDurationFast : token.motionDurationMid), {\n    [`\n        ${motionCls}-enter,\n        ${motionCls}-appear\n      `]: {\n      transform: 'scale(0)',\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutCirc,\n      '&-prepare': {\n        transform: 'none'\n      }\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInOutCirc\n    }\n  }];\n};", "const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,mBAAmB,MAAM,cAAc,eAAe,WAAW,UAAU;AACjF,IAAM,yBAAyB,eAAa;AAC1C,MAAI,cAAc,WAAc,cAAc,aAAa,cAAc,aAAa;AACpF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACO,IAAM,qBAAqB,SAAU,gBAAgB;AAC1D,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,QAAM,kBAAkB,iBAAiB,SAAS;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKR,gBAAgB,GAAG,cAAc,UAAU,cAAc,kBAAkB,cAAc;AAAA,IACzF,kBAAkB,GAAG,cAAc,UAAU,cAAc;AAAA,IAC3D,cAAc,GAAG,cAAc,UAAU,cAAc;AAAA,IACvD,gBAAgB,IAAI,cAAc;AAAA,IAClC,kBAAkB,GAAG,cAAc,UAAU,cAAc;AAAA,IAC3D,cAAc,GAAG,cAAc,UAAU,cAAc;AAAA,EACzD,GAAG,GAAG,IAAI,SAAS;AAAA,IACjB,KAAK;AAAA,EACP,GAAG,GAAG;AACN,SAAO;AACT;AACO,IAAM,0BAA0B,SAAU,gBAAgB;AAC/D,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,QAAM,kBAAkB,iBAAiB,SAAS;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA,IAER,mBAAmB,GAAG,cAAc;AAAA,IACpC,eAAe,GAAG,cAAc,WAAW,cAAc;AAAA,IACzD,gBAAgB,GAAG,cAAc,WAAW,cAAc,UAAU,cAAc,mBAAmB,cAAc;AAAA,IACnH,kBAAkB,GAAG,cAAc;AAAA,IACnC,cAAc,GAAG,cAAc,UAAU,cAAc,WAAW,cAAc,kBAAkB,cAAc;AAAA,IAChH,kBAAkB,GAAG,cAAc,IAAI,cAAc;AAAA,IACrD,cAAc,GAAG,cAAc;AAAA,EACjC,GAAG,GAAG,IAAI,SAAS;AAAA,IACjB,KAAK;AAAA,EACP,GAAG,GAAG;AACN,SAAO;AACT;AAiDA,IAAM,oBAAoB,CAAC,eAAe,QAAQ,mBAAmB;AACnE,MAAI,mBAAmB,QAAW;AAChC,WAAO;AAAA,EACT;AACA,SAAO,GAAG,aAAa,IAAI,MAAM;AACnC;;;ACpGA,IAAI,kBAAkB;AACtB,IAAI;AACF,QAAM,OAAO,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,IAChD,MAAM;AACJ,wBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,SAAO,iBAAiB,eAAe,MAAM,IAAI;AACjD,SAAO,oBAAoB,eAAe,MAAM,IAAI;AACtD,SAAS,GAAG;AAAC;AACb,IAAO,0BAAQ;;;ACVA,SAAR,qBAAsC,QAAQ,WAAW,IAAI,QAAQ;AAC1E,MAAI,UAAU,OAAO,kBAAkB;AACrC,QAAI,MAAM;AACV,QAAI,QAAQ,UAAa,4BAAoB,cAAc,gBAAgB,cAAc,eAAe,cAAc,UAAU;AAC9H,YAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,iBAAiB,WAAW,IAAI,GAAG;AAAA,EAC5C;AACA,SAAO;AAAA,IACL,QAAQ,MAAM;AACZ,UAAI,UAAU,OAAO,qBAAqB;AACxC,eAAO,oBAAoB,WAAW,EAAE;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACF;;;AClBO,SAAS,UAAU,MAAM;AAC9B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,WAAW;AACb,WAAO;AAAA,MACL,MAAM,GAAG,SAAS,IAAI,SAAS;AAAA,IACjC;AAAA,EACF;AACA,MAAI,gBAAgB;AAClB,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO,CAAC;AACV;;;ACdA,IAAO,oBAAQ;AAAA,EACb,SAAS;AAAA,IACP,WAAW;AACT,UAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACjF,UAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,UAAI,WAAW,OAAO,UAAU,aAAa,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI;AAC9E,UAAI,KAAK,0BAA0B;AACjC,cAAM,IAAI,KAAK,yBAAyB,eAAe,IAAI,GAAG,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,QAAQ,CAAC;AAC1G,YAAI,MAAM,MAAM;AACd;AAAA,QACF,OAAO;AACL,qBAAW,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;AAAA,QACrD;AAAA,MACF;AACA,eAAS,KAAK,OAAO,QAAQ;AAC7B,UAAI,KAAK,EAAE,WAAW;AACpB,aAAK,aAAa;AAAA,MACpB;AACA,eAAS,MAAM;AACb,oBAAY,SAAS;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,IACA,SAAS;AAGP,YAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACvC,UAAI,YAAY,KAAK,CAAC;AACtB,kBAAY,KAAK,UAAU,CAAC,EAAE,YAAY,CAAC,GAAG,UAAU,UAAU,CAAC,CAAC;AACpE,YAAM,QAAQ,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,SAAS;AAC7D,UAAI,KAAK,UAAU,OAAO;AACxB,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,kBAAM,CAAC,EAAE,GAAG,KAAK,MAAM,CAAC,CAAC;AAAA,UAC3B;AAAA,QACF,OAAO;AACL,gBAAM,GAAG,KAAK,MAAM,CAAC,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC1CA,SAAS,oBAAoB;AAC3B,SAAO;AACT;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI,SAAS;AACX,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO,OAAO;AAChB;AACO,SAAS,OAAO;AAAC;AACjB,IAAM,eAAe,OAAO;AAAA,EACjC,QAAQ,kBAAU,UAAU,CAAC,kBAAU,QAAQ,kBAAU,QAAQ,kBAAU,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,EAC3F,YAAY,kBAAU,IAAI,IAAI,CAAC,CAAC;AAAA,EAChC,YAAY,kBAAU,IAAI,IAAI,CAAC,CAAC;AAAA,EAChC,4BAA4B,kBAAU,IAAI,IAAI,iBAAiB;AAAA,EAC/D,sBAAsB;AAAA,EACtB,yBAAyB,kBAAU,KAAK,IAAI,IAAI;AAAA,EAChD,OAAO,kBAAU;AAAA,EACjB,OAAO,kBAAU,KAAK,IAAI,IAAI;AAAA,EAC9B,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,WAAW,kBAAU,OAAO,IAAI,kBAAkB;AAAA,EAClD,gBAAgB,kBAAU,OAAO,IAAI,EAAE;AAAA,EACvC,gBAAgB;AAAA,EAChB,mBAAmB,kBAAU;AAAA,EAC7B,qBAAqB;AAAA,EACrB,gBAAgB,kBAAU;AAAA,EAC1B,iBAAiB,kBAAU,OAAO,IAAI,CAAC;AAAA,EACvC,iBAAiB,kBAAU,OAAO,IAAI,GAAG;AAAA,EACzC,QAAQ;AAAA,EACR,YAAY,kBAAU,OAAO,IAAI,CAAC;AAAA,EAClC,WAAW,kBAAU,OAAO,IAAI,IAAI;AAAA,EACpC,mBAAmB;AAAA,EACnB,aAAa,kBAAU,KAAK,IAAI,cAAc;AAAA,EAC9C,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,oBAAoB;AAAA,IAClB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,YAAY,kBAAU,OAAO,IAAI,OAAO,CAAC,EAAE;AAAA,EAC3C,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,qBAAqB;AAAA,IACnB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,EACR,mBAAmB;AACrB;;;AC3EO,IAAM,aAAa;AAAA,EACxB,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,WAAW,CAAC,QAAQ,MAAM;AAAA,EAC1B,gBAAgB;AAAA;AAAA,EAEhB,SAAS;AAAA,IACP,MAAM;AAAA,EACR;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,EACR;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,uBAAuB;AAAA,IACrB,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AACF;AACO,IAAM,cAAc,SAAS,SAAS,CAAC,GAAG,UAAU,GAAG;AAAA,EAC5D,QAAQ;AAAA,IACN,MAAM;AAAA,EACR;AACF,CAAC;AACM,IAAM,aAAa,SAAS,SAAS,CAAC,GAAG,UAAU,GAAG;AAAA,EAC3D,MAAM;AAAA,EACN,QAAQ;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,eAAe;AAAA,EACf,oBAAoB;AACtB,CAAC;;;ACvDc,SAAR,KAAsB,OAAO;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,SAAS,CAAC;AACd,MAAI,sBAAsB,eAAe;AACvC,aAAS,UAAU;AAAA,MACjB;AAAA,MACA,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO,YAAa,YAAY,eAAc;AAAA,IAC5C,UAAU;AAAA,EACZ,GAAG,MAAM,GAAG;AAAA,IACV,SAAS,MAAM,CAAC,eAAgB,YAAa,OAAO;AAAA,MAClD,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,SAAS,GAAG,SAAS;AAAA,IACvB,GAAG,IAAI,GAAG,CAAC,CAAC,iBAAkB,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,EACjD,CAAC;AACH;AACA,KAAK,cAAc;;;AC5BnB,IAAO,2BAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO,CAAC,cAAc,cAAc,aAAa,cAAc,OAAO;AAAA,EACtE,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,IAAI;AACvB,WAAO;AAAA,MACL,YAAY,MAAM;AAAA,MAAC;AAAA,MACnB,YAAY,MAAM,WAAW;AAAA,IAC/B,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA,cAAc,CAAC;AAAA,UACf;AAAA,QACF,IAAI,CAAC;AAAA,MACP,IAAI;AAEJ,YAAM,cAAc,SAAS;AAAA,QAC3B;AAAA,MACF,GAAG,UAAU;AACb,UAAI,YAAY,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAExG,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM,aAAa,2BAAY;AAC7B,iBAAO;AAAA,QACT,EAAE;AACF,oBAAY,YAAa,OAAO;AAAA,UAC9B,SAAS,GAAG,SAAS;AAAA,QACvB,GAAG,CAAC,SAAS,CAAC;AAAA,MAChB;AAEA,UAAI,aAAa;AACf,oBAAY,YAAY,SAAS;AAAA,MACnC;AACA,YAAM,kBAAkB,mBAAW,WAAW,cAAc;AAC5D,aAAO,YAAa,YAAY,eAAc;AAAA,QAC5C,OAAO;AAAA,MACT,GAAG,WAAW,GAAG;AAAA,QACf,SAAS,MAAM,CAAC,UAAU,YAAa,OAAO;AAAA,UAC5C,SAAS;AAAA,UACT,SAAS;AAAA,QACX,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACnED,IAAI,YAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAGA,IAAM,cAAc,CAAC,WAAW,SAAS,MAAM,QAAQ;AACvD,IAAO,2BAAS,CAAC,SAAS,cAAc;AACtC,QAAM,SAAS,WAAW,IAAI;AAC9B,QAAM,SAAS,WAAW;AAC1B,QAAM,aAAa,WAAW,KAAK;AACnC,WAAS,UAAU,YAAY;AAC7B,QAAI,CAAC,WAAW,OAAO;AACrB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,WAAS,YAAY;AACnB,eAAI,OAAO,OAAO,KAAK;AAAA,EACzB;AACA,WAAS,aAAa,UAAU;AAC9B,cAAU;AACV,WAAO,QAAQ,WAAI,MAAM;AAEvB,UAAI,YAAY,OAAO;AACvB,cAAQ,OAAO,OAAO;AAAA,QACpB,KAAK;AACH,sBAAY;AACZ;AAAA,QACF,KAAK;AACH,sBAAY;AACZ;AAAA,QACF;AAAA,MACF;AACA,gBAAU,SAAS;AACnB,mBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAAA,IAC/D,CAAC;AAAA,EACH;AACA,QAAM,SAAS,MAAM;AACnB,cAAU,SAAS;AAAA,EACrB,GAAG;AAAA,IACD,WAAW;AAAA,IACX,OAAO;AAAA,EACT,CAAC;AACD,YAAU,MAAM;AAEd,UAAM,QAAQ,MAAM;AAClB,cAAQ,OAAO,OAAO;AAAA,QACpB,KAAK;AACH,oBAAU;AACV;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,OAAO;AAChB,eAAO,QAAQ,WAAI,MAAM,UAAU,QAAQ,QAAQ,QAAQ,aAAa;AACtE,gBAAM,QAAQ,YAAY,QAAQ,OAAO,KAAK;AAC9C,gBAAM,aAAa,YAAY,QAAQ,CAAC;AACxC,cAAI,cAAc,UAAU,IAAI;AAC9B,sBAAU,UAAU;AAAA,UACtB;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACD,kBAAgB,MAAM;AACpB,eAAW,QAAQ;AACnB,cAAU;AAAA,EACZ,CAAC;AACD,SAAO,CAAC,QAAQ,YAAY;AAC9B;;;AC7FA,IAAO,0BAAS,aAAW;AACzB,QAAM,aAAa,WAAW;AAAA,IAC5B,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,WAAS,eAAe,SAAS;AAC/B,eAAW,QAAQ;AAAA,MACjB,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,QAAQ,SAAS,MAAM;AAC3B,UAAM,YAAY,CAAC;AACnB,QAAI,QAAQ,OAAO;AACjB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,WAAW;AAEf,UAAI,QAAQ,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ;AACpD,kBAAU,SAAS,GAAG,MAAM;AAAA,MAC9B,WAAW,QAAQ,MAAM,QAAQ,WAAW,MAAM,MAAM,QAAQ;AAC9D,kBAAU,YAAY,GAAG,MAAM;AAAA,MACjC;AACA,UAAI,QAAQ,MAAM,QAAQ,OAAO,MAAM,MAAM,OAAO;AAClD,kBAAU,QAAQ,GAAG,KAAK;AAAA,MAC5B,WAAW,QAAQ,MAAM,QAAQ,UAAU,MAAM,MAAM,OAAO;AAC5D,kBAAU,WAAW,GAAG,KAAK;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACD,SAAO,CAAC,OAAO,cAAc;AAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnCA,IAAIA;AAEJ,IAAMC,WAAW;EACfC,QAAQ;EACRC,KAAK;;EAELC,IAAI;EACJC,GAAG;AACL;AAEA,SAASC,kBAAkB;AACzB,MAAIN,iBAAiBO,QAAW;AAC9B,WAAOP;;AAETA,iBAAe;AACf,MAAMQ,QAAQC,SAASC,cAAc,GAAG,EAAEF;AAC1C,MAAMG,WAAW;AACjB,WAAWC,OAAOX,UAAU;AAC1B,QAAIW,MAAMD,YAAYH,OAAO;AAC3BR,qBAAeY;;;AAGnB,SAAOZ;AACT;AAEA,SAASa,qBAAoB;AAC3B,SAAOP,gBAAe,IAAE,GAAA,OACjBA,gBAAe,GAAE,oBAAA,IACpB;AACN;AAEO,SAASQ,mBAAmB;AACjC,SAAOR,gBAAe,IAAE,GAAA,OAAMA,gBAAe,GAAE,WAAA,IAAc;AAC/D;AAEO,SAASS,sBAAsBC,MAAMC,OAAO;AACjD,MAAMC,OAAOL,mBAAiB;AAC9B,MAAIK,MAAM;AACRF,SAAKR,MAAMU,IAAI,IAAID;AACnB,QAAIC,SAAS,sBAAsB;AACjCF,WAAKR,MAAMW,qBAAqBF;;;AAGtC;AAEA,SAASG,aAAaJ,MAAMC,OAAO;AACjC,MAAMC,OAAOJ,iBAAgB;AAC7B,MAAII,MAAM;AACRF,SAAKR,MAAMU,IAAI,IAAID;AACnB,QAAIC,SAAS,aAAa;AACxBF,WAAKR,MAAMa,YAAYJ;;;AAG7B;AAEO,SAASK,sBAAsBN,MAAM;AAC1C,SAAOA,KAAKR,MAAMW,sBAAsBH,KAAKR,MAAMK,mBAAiB,CAAE;AACxE;AAEO,SAASU,eAAeP,MAAM;AACnC,MAAMR,QAAQgB,OAAOC,iBAAiBT,MAAM,IAAI;AAChD,MAAMK,YACJb,MAAMkB,iBAAiB,WAAW,KAClClB,MAAMkB,iBAAiBZ,iBAAgB,CAAE;AAC3C,MAAIO,aAAaA,cAAc,QAAQ;AACrC,QAAMM,SAASN,UAAUO,QAAQ,eAAe,EAAE,EAAEC,MAAM,GAAG;AAC7D,WAAO;MACLC,GAAGC,WAAWJ,OAAO,EAAE,KAAKA,OAAO,CAAC,GAAG,CAAC;MACxCK,GAAGD,WAAWJ,OAAO,EAAE,KAAKA,OAAO,CAAC,GAAG,CAAC;;;AAG5C,SAAO;IACLG,GAAG;IACHE,GAAG;;AAEP;AAEA,IAAMC,WAAW;AACjB,IAAMC,WAAW;AAEV,SAASC,eAAenB,MAAMoB,IAAI;AACvC,MAAM5B,QAAQgB,OAAOC,iBAAiBT,MAAM,IAAI;AAChD,MAAMK,YACJb,MAAMkB,iBAAiB,WAAW,KAClClB,MAAMkB,iBAAiBZ,iBAAgB,CAAE;AAC3C,MAAIO,aAAaA,cAAc,QAAQ;AACrC,QAAIgB;AACJ,QAAIC,UAAUjB,UAAUkB,MAAMN,QAAQ;AACtC,QAAIK,SAAS;AACXA,gBAAUA,QAAQ,CAAC;AACnBD,YAAMC,QAAQT,MAAM,GAAG,EAAEW,IAAI,SAAAC,MAAQ;AACnC,eAAOV,WAAWU,MAAM,EAAE;OAC3B;AACDJ,UAAI,CAAC,IAAID,GAAGN;AACZO,UAAI,CAAC,IAAID,GAAGJ;AACZZ,mBAAaJ,MAAI,UAAA,OAAYqB,IAAIK,KAAK,GAAG,GAAC,GAAA,CAAA;WACrC;AACL,UAAMC,UAAUtB,UAAUkB,MAAML,QAAQ,EAAE,CAAC;AAC3CG,YAAMM,QAAQd,MAAM,GAAG,EAAEW,IAAI,SAAAC,MAAQ;AACnC,eAAOV,WAAWU,MAAM,EAAE;OAC3B;AACDJ,UAAI,EAAE,IAAID,GAAGN;AACbO,UAAI,EAAE,IAAID,GAAGJ;AACbZ,mBAAaJ,MAAI,YAAA,OAAcqB,IAAIK,KAAK,GAAG,GAAC,GAAA,CAAA;;SAEzC;AACLtB,iBACEJ,MAAI,cAAA,OACUoB,GAAGN,GAAC,iBAAA,EAAA,OAAkBM,GAAGJ,GAAC,mBAAA,CAAA;;AAG9C;ACvGA,IAAMY,SAAS,wCAAwCC;AAEvD,IAAIC;AAGJ,SAASC,cAAcC,MAAM;AAC3B,MAAMC,gBAAgBD,KAAKxC,MAAM0C;AACjCF,OAAKxC,MAAM0C,UAAU;AACrBF,OAAKG;AACLH,OAAKxC,MAAM0C,UAAUD;AACvB;AAEA,SAASG,IAAIC,IAAInC,MAAMoC,GAAG;AACxB,MAAIrC,QAAQqC;AACZ,MAAI,QAAOpC,IAAI,MAAK,UAAU;AAC5B,aAAWqC,KAAKrC,MAAM;AACpB,UAAIA,KAAKsC,eAAeD,CAAC,GAAG;AAC1BH,YAAIC,IAAIE,GAAGrC,KAAKqC,CAAC,CAAC;;;AAGtB,WAAOhD;;AAET,MAAI,OAAOU,UAAU,aAAa;AAChC,QAAI,OAAOA,UAAU,UAAU;AAC7BA,cAAK,GAAA,OAAMA,OAAK,IAAA;;AAElBoC,OAAG7C,MAAMU,IAAI,IAAID;AACjB,WAAOV;;AAET,SAAOuC,kBAAkBO,IAAInC,IAAI;AACnC;AAEA,SAASuC,kBAAkBT,MAAM;AAC/B,MAAIU;AACJ,MAAI5B;AACJ,MAAIE;AACJ,MAAM2B,MAAMX,KAAKY;AACjB,MAAMC,OAAOF,IAAIE;AACjB,MAAMC,UAAUH,OAAOA,IAAII;AAE3BL,QAAMV,KAAKgB,sBAAqB;AAMhClC,MAAImC,KAAKC,MAAMR,IAAIS,IAAI;AACvBnC,MAAIiC,KAAKC,MAAMR,IAAIU,GAAG;AAsBtBtC,OAAKgC,QAAQO,cAAcR,KAAKQ,cAAc;AAC9CrC,OAAK8B,QAAQQ,aAAaT,KAAKS,aAAa;AAE5C,SAAO;IACLH,MAAMrC;IACNsC,KAAKpC;;AAET;AAEA,SAASuC,UAAUC,GAAGJ,KAAK;AACzB,MAAIK,MAAMD,EAAC,OAAA,OAAQJ,MAAM,MAAM,KAAG,QAAA,CAAA;AAClC,MAAMM,SAAM,SAAA,OAAYN,MAAM,QAAQ,MAAM;AAC5C,MAAI,OAAOK,QAAQ,UAAU;AAC3B,QAAME,IAAIH,EAAE/D;AAEZgE,UAAME,EAAEZ,gBAAgBW,MAAM;AAC9B,QAAI,OAAOD,QAAQ,UAAU;AAE3BA,YAAME,EAAEd,KAAKa,MAAM;;;AAGvB,SAAOD;AACT;AAEA,SAASG,cAAcJ,GAAG;AACxB,SAAOD,UAAUC,CAAC;AACpB;AAEA,SAASK,aAAaL,GAAG;AACvB,SAAOD,UAAUC,GAAG,IAAI;AAC1B;AAEA,SAASM,UAAUzB,IAAI;AACrB,MAAM0B,MAAMtB,kBAAkBJ,EAAE;AAChC,MAAMM,MAAMN,GAAGO;AACf,MAAMY,IAAIb,IAAIqB,eAAerB,IAAIsB;AACjCF,MAAIZ,QAAQS,cAAcJ,CAAC;AAC3BO,MAAIX,OAAOS,aAAaL,CAAC;AACzB,SAAOO;AACT;AAMA,SAASG,SAASC,KAAK;AAGrB,SAAOA,QAAQ,QAAQA,QAAQ5E,UAAa4E,OAAOA,IAAI3D;AACzD;AAEA,SAAS4D,YAAYpE,MAAM;AACzB,MAAIkE,SAASlE,IAAI,GAAG;AAClB,WAAOA,KAAKP;;AAEd,MAAIO,KAAKqE,aAAa,GAAG;AACvB,WAAOrE;;AAET,SAAOA,KAAK4C;AACd;AAEA,SAAS0B,kBAAkBtC,MAAM9B,MAAMqE,IAAI;AACzC,MAAIC,gBAAgBD;AACpB,MAAIE,MAAM;AACV,MAAMd,IAAIS,YAAYpC,IAAI;AAC1BwC,kBAAgBA,iBAAiBb,EAAEK,YAAYvD,iBAAiBuB,MAAM,IAAI;AAG1E,MAAIwC,eAAe;AACjBC,UAAMD,cAAc9D,iBAAiBR,IAAI,KAAKsE,cAActE,IAAI;;AAGlE,SAAOuE;AACT;AAEA,IAAMC,gBAAgB,IAAIC,OAAM,KAAA,OAAM/C,QAAM,iBAAA,GAAmB,GAAG;AAClE,IAAMgD,SAAS;AACf,IAAMC,gBAAgB;AACtB,IAAMC,gBAAgB;AACtB,IAAMC,OAAO;AACb,IAAMC,KAAK;AAEX,SAASC,oBAAoBjD,MAAM9B,MAAM;AAGvC,MAAIuD,MAAMzB,KAAK6C,aAAa,KAAK7C,KAAK6C,aAAa,EAAE3E,IAAI;AAYzD,MAAIwE,cAAcQ,KAAKzB,GAAG,KAAK,CAACmB,OAAOM,KAAKhF,IAAI,GAAG;AAEjD,QAAMV,QAAQwC,KAAKxC;AACnB,QAAM2D,OAAO3D,MAAMuF,IAAI;AACvB,QAAMI,SAASnD,KAAK8C,aAAa,EAAEC,IAAI;AAGvC/C,SAAK8C,aAAa,EAAEC,IAAI,IAAI/C,KAAK6C,aAAa,EAAEE,IAAI;AAGpDvF,UAAMuF,IAAI,IAAI7E,SAAS,aAAa,QAAQuD,OAAO;AACnDA,UAAMjE,MAAM4F,YAAYJ;AAGxBxF,UAAMuF,IAAI,IAAI5B;AAEdnB,SAAK8C,aAAa,EAAEC,IAAI,IAAII;;AAE9B,SAAO1B,QAAQ,KAAK,SAASA;AAC/B;AAEA,IAAI,OAAOjD,WAAW,aAAa;AACjCsB,sBAAoBtB,OAAOC,mBACvB6D,oBACAW;AACN;AAEA,SAASI,mBAAmBC,KAAKC,QAAQ;AACvC,MAAID,QAAQ,QAAQ;AAClB,WAAOC,OAAOC,cAAc,UAAUF;;AAExC,SAAOC,OAAOE,eAAe,WAAWH;AAC1C;AAEA,SAASI,wBAAwBJ,KAAK;AACpC,MAAIA,QAAQ,QAAQ;AAClB,WAAO;aACEA,QAAQ,SAAS;AAC1B,WAAO;aACEA,QAAQ,OAAO;AACxB,WAAO;aACEA,QAAQ,UAAU;AAC3B,WAAO;;AAEX;AAGA,SAASK,WAAW3D,MAAM4D,SAAQL,QAAQ;AAExC,MAAInD,IAAIJ,MAAM,UAAU,MAAM,UAAU;AACtCA,SAAKxC,MAAMqG,WAAW;;AAExB,MAAIC,UAAU;AACd,MAAIC,UAAU;AACd,MAAMC,qBAAqBX,mBAAmB,QAAQE,MAAM;AAC5D,MAAMU,mBAAmBZ,mBAAmB,OAAOE,MAAM;AACzD,MAAMW,6BAA6BR,wBACjCM,kBAAkB;AAEpB,MAAMG,2BAA2BT,wBAAwBO,gBAAgB;AAEzE,MAAID,uBAAuB,QAAQ;AACjCF,cAAU;;AAGZ,MAAIG,qBAAqB,OAAO;AAC9BF,cAAU;;AAEZ,MAAIK,qBAAqB;AACzB,MAAMC,iBAAiBvC,UAAU9B,IAAI;AACrC,MAAI,UAAU4D,WAAU,SAASA,SAAQ;AACvCQ,yBAAqB9F,sBAAsB0B,IAAI,KAAK;AACpDjC,0BAAsBiC,MAAM,MAAM;;AAEpC,MAAI,UAAU4D,SAAQ;AACpB5D,SAAKxC,MAAM0G,0BAA0B,IAAI;AACzClE,SAAKxC,MAAMwG,kBAAkB,IAAC,GAAA,OAAMF,SAAO,IAAA;;AAE7C,MAAI,SAASF,SAAQ;AACnB5D,SAAKxC,MAAM2G,wBAAwB,IAAI;AACvCnE,SAAKxC,MAAMyG,gBAAgB,IAAC,GAAA,OAAMF,SAAO,IAAA;;AAG3ChE,gBAAcC,IAAI;AAClB,MAAMsE,MAAMxC,UAAU9B,IAAI;AAC1B,MAAMC,gBAAgB,CAAA;AACtB,WAAWrC,OAAOgG,SAAQ;AACxB,QAAIA,QAAOpD,eAAe5C,GAAG,GAAG;AAC9B,UAAM0F,MAAMD,mBAAmBzF,KAAK2F,MAAM;AAC1C,UAAMgB,SAAS3G,QAAQ,SAASkG,UAAUC;AAC1C,UAAMS,MAAMH,eAAezG,GAAG,IAAI0G,IAAI1G,GAAG;AACzC,UAAI0F,QAAQ1F,KAAK;AACfqC,sBAAcqD,GAAG,IAAIiB,SAASC;aACzB;AACLvE,sBAAcqD,GAAG,IAAIiB,SAASC;;;;AAIpCpE,MAAIJ,MAAMC,aAAa;AAEvBF,gBAAcC,IAAI;AAClB,MAAI,UAAU4D,WAAU,SAASA,SAAQ;AACvC7F,0BAAsBiC,MAAMoE,kBAAkB;;AAEhD,MAAM3C,MAAM,CAAA;AACZ,WAAW7D,QAAOgG,SAAQ;AACxB,QAAIA,QAAOpD,eAAe5C,IAAG,GAAG;AAC9B,UAAM0F,OAAMD,mBAAmBzF,MAAK2F,MAAM;AAC1C,UAAMiB,OAAMZ,QAAOhG,IAAG,IAAIyG,eAAezG,IAAG;AAC5C,UAAIA,SAAQ0F,MAAK;AACf7B,YAAI6B,IAAG,IAAIrD,cAAcqD,IAAG,IAAIkB;aAC3B;AACL/C,YAAI6B,IAAG,IAAIrD,cAAcqD,IAAG,IAAIkB;;;;AAItCpE,MAAIJ,MAAMyB,GAAG;AACf;AAEA,SAASrD,eAAa4B,MAAM4D,SAAQ;AAClC,MAAMS,iBAAiBvC,UAAU9B,IAAI;AACrC,MAAMyE,aAAalG,eAAeyB,IAAI;AACtC,MAAM0E,WAAW;IAAE5F,GAAG2F,WAAW3F;IAAGE,GAAGyF,WAAWzF;;AAClD,MAAI,UAAU4E,SAAQ;AACpBc,aAAS5F,IAAI2F,WAAW3F,IAAI8E,QAAOzC,OAAOkD,eAAelD;;AAE3D,MAAI,SAASyC,SAAQ;AACnBc,aAAS1F,IAAIyF,WAAWzF,IAAI4E,QAAOxC,MAAMiD,eAAejD;;AAE1DjC,iBAAea,MAAM0E,QAAQ;AAC/B;AAEA,SAASC,UAAU3E,MAAM4D,SAAQL,QAAQ;AACvC,MAAIA,OAAOqB,aAAa;AACtB,QAAMC,YAAY/C,UAAU9B,IAAI;AAEhC,QAAM8E,QAAQD,UAAU1D,KAAK4D,QAAQ,CAAC;AACtC,QAAMC,OAAOH,UAAUzD,IAAI2D,QAAQ,CAAC;AACpC,QAAME,QAAQrB,QAAOzC,KAAK4D,QAAQ,CAAC;AACnC,QAAMG,OAAOtB,QAAOxC,IAAI2D,QAAQ,CAAC;AAEjC,QAAID,UAAUG,SAASD,SAASE,MAAM;AACpC;;;AAIJ,MAAI3B,OAAOC,eAAeD,OAAOE,cAAc;AAC7CE,eAAW3D,MAAM4D,SAAQL,MAAM;aAE/BA,OAAO4B,mBACPrH,iBAAgB,KAAML,SAASoD,KAAKrD,OACpC;AACAY,mBAAa4B,MAAM4D,OAAc;SAC5B;AACLD,eAAW3D,MAAM4D,SAAQL,MAAM;;AAEnC;AAEA,SAAS6B,KAAK/F,KAAKgG,IAAI;AACrB,WAAS9E,IAAI,GAAGA,IAAIlB,IAAIiG,QAAQ/E,KAAK;AACnC8E,OAAGhG,IAAIkB,CAAC,CAAC;;AAEb;AAEA,SAASgF,cAAcvF,MAAM;AAC3B,SAAOF,kBAAkBE,MAAM,WAAW,MAAM;AAClD;AAEA,IAAMwF,aAAa,CAAC,UAAU,UAAU,SAAS;AACjD,IAAMC,gBAAgB;AACtB,IAAMC,gBAAgB;AACtB,IAAMC,eAAe;AACrB,IAAMC,eAAe;AAErB,SAASC,KAAK7F,MAAM8F,SAASC,UAAU;AACrC,MAAMzB,MAAM,CAAA;AACZ,MAAM9G,QAAQwC,KAAKxC;AACnB,MAAIU;AAGJ,OAAKA,QAAQ4H,SAAS;AACpB,QAAIA,QAAQtF,eAAetC,IAAI,GAAG;AAChCoG,UAAIpG,IAAI,IAAIV,MAAMU,IAAI;AACtBV,YAAMU,IAAI,IAAI4H,QAAQ5H,IAAI;;;AAI9B6H,WAASC,KAAKhG,IAAI;AAGlB,OAAK9B,QAAQ4H,SAAS;AACpB,QAAIA,QAAQtF,eAAetC,IAAI,GAAG;AAChCV,YAAMU,IAAI,IAAIoG,IAAIpG,IAAI;;;AAG5B;AAEA,SAAS+H,YAAYjG,MAAMkG,OAAOC,OAAO;AACvC,MAAIlI,QAAQ;AACZ,MAAImI;AACJ,MAAIC;AACJ,MAAI9F;AACJ,OAAK8F,IAAI,GAAGA,IAAIH,MAAMZ,QAAQe,KAAK;AACjCD,WAAOF,MAAMG,CAAC;AACd,QAAID,MAAM;AACR,WAAK7F,IAAI,GAAGA,IAAI4F,MAAMb,QAAQ/E,KAAK;AACjC,YAAI+F,UAAO;AACX,YAAIF,SAAS,UAAU;AACrBE,oBAAO,GAAA,OAAMF,IAAI,EAAA,OAAGD,MAAM5F,CAAC,GAAC,OAAA;eACvB;AACL+F,oBAAUF,OAAOD,MAAM5F,CAAC;;AAE1BtC,iBAASc,WAAWe,kBAAkBE,MAAMsG,OAAO,CAAC,KAAK;;;;AAI/D,SAAOrI;AACT;AAEA,IAAMsI,WAAW;EACfC,WAAS,SAAA,UAACC,SAAS;AACjB,QAAIC,SAASD;AACb,OAAG;AACD,UAAIC,OAAOrE,aAAa,MAAMqE,OAAOC,MAAM;AACzCD,iBAASA,OAAOC;aACX;AACLD,iBAASA,OAAOE;;aAEXF,UAAUA,OAAOrE,aAAa,KAAKqE,OAAOrE,aAAa;AAChE,WAAOqE;;AAEX;AAEAtB,KAAK,CAAC,SAAS,QAAQ,GAAG,SAAAlH,MAAQ;AAChCqI,WAAQ,MAAA,OAAOrI,IAAI,CAAA,IAAM,SAAA2I,QAAU;AACjC,QAAMlF,IAAIkF,OAAOpJ;AACjB,WAAOwD,KAAK6F;;;MAGVnF,EAAEZ,gBAAe,SAAA,OAAU7C,IAAI,CAAA;;MAE/ByD,EAAEd,KAAI,SAAA,OAAU3C,IAAI,CAAA;MACpBqI,SAAQ,WAAA,OAAYrI,IAAI,CAAA,EAAIyD,CAAC;IAAC;;AAIlC4E,WAAQ,WAAA,OAAYrI,IAAI,CAAA,IAAM,SAAA6I,KAAO;AAEnC,QAAMX,OAAI,SAAA,OAAYlI,IAAI;AAC1B,QAAMyC,MAAMoG,IAAItJ;AAChB,QAAMoD,OAAOF,IAAIE;AACjB,QAAME,kBAAkBJ,IAAII;AAC5B,QAAMiG,sBAAsBjG,gBAAgBqF,IAAI;AAGhD,WACGzF,IAAIsG,eAAe,gBAAgBD,uBACnCnG,QAAQA,KAAKuF,IAAI,KAClBY;;AAGN,CAAC;AAUD,SAASE,MAAMlH,MAAM9B,MAAMiJ,IAAI;AAC7B,MAAIC,QAAQD;AACZ,MAAIjF,SAASlC,IAAI,GAAG;AAClB,WAAO9B,SAAS,UACZqI,SAASc,cAAcrH,IAAI,IAC3BuG,SAASe,eAAetH,IAAI;aACvBA,KAAKqC,aAAa,GAAG;AAC9B,WAAOnE,SAAS,UACZqI,SAASgB,SAASvH,IAAI,IACtBuG,SAASiB,UAAUxH,IAAI;;AAE7B,MAAMmG,QAAQjI,SAAS,UAAU,CAAC,QAAQ,OAAO,IAAI,CAAC,OAAO,QAAQ;AACrE,MAAIuJ,iBACFvJ,SAAS,UACL+C,KAAKC,MAAMlB,KAAKgB,sBAAqB,EAAG0G,KAAK,IAC7CzG,KAAKC,MAAMlB,KAAKgB,sBAAqB,EAAG2G,MAAM;AACpD,MAAMC,cAAcrC,cAAcvF,IAAI;AACtC,MAAI6H,cAAc;AAClB,MACEJ,mBAAmB,QACnBA,mBAAmBlK,UACnBkK,kBAAkB,GAClB;AACAA,qBAAiBlK;AAEjBsK,kBAAc/H,kBAAkBE,MAAM9B,IAAI;AAC1C,QACE2J,gBAAgB,QAChBA,gBAAgBtK,UAChBuK,OAAOD,WAAW,IAAI,GACtB;AACAA,oBAAc7H,KAAKxC,MAAMU,IAAI,KAAK;;AAGpC2J,kBAAc5G,KAAKC,MAAMnC,WAAW8I,WAAW,CAAC,KAAK;;AAEvD,MAAIT,UAAU7J,QAAW;AACvB6J,YAAQQ,cAAcjC,eAAeF;;AAEvC,MAAMsC,8BACJN,mBAAmBlK,UAAaqK;AAClC,MAAMnF,MAAMgF,kBAAkBI;AAC9B,MAAIT,UAAU3B,eAAe;AAC3B,QAAIsC,6BAA6B;AAC/B,aAAOtF,MAAMwD,YAAYjG,MAAM,CAAC,UAAU,SAAS,GAAGmG,KAAK;;AAE7D,WAAO0B;aACEE,6BAA6B;AACtC,QAAIX,UAAUzB,cAAc;AAC1B,aAAOlD;;AAET,WACEA,OACC2E,UAAU1B,gBACP,CAACO,YAAYjG,MAAM,CAAC,QAAQ,GAAGmG,KAAK,IACpCF,YAAYjG,MAAM,CAAC,QAAQ,GAAGmG,KAAK;;AAG3C,SAAO0B,cAAc5B,YAAYjG,MAAMwF,WAAWwC,MAAMZ,KAAK,GAAGjB,KAAK;AACvE;AAEA,IAAM8B,UAAU;EACdpE,UAAU;EACVqE,YAAY;EACZhI,SAAS;AACX;AAGA,SAASiI,qBAA4B;AAAA,WAAA,OAAA,UAAA,QAANC,OAAI,IAAA,MAAA,IAAA,GAAA,QAAA,GAAA,QAAA,MAAA,SAAA;AAAJA,SAAI,KAAA,IAAA,UAAA,KAAA;;AACjC,MAAI3F;AACJ,MAAMzC,OAAOoI,KAAK,CAAC;AAGnB,MAAIpI,KAAKqI,gBAAgB,GAAG;AAC1B5F,UAAMyE,MAAMoB,MAAM/K,QAAW6K,IAAI;SAC5B;AACLvC,SAAK7F,MAAMiI,SAAS,WAAM;AACxBxF,YAAMyE,MAAMoB,MAAM/K,QAAW6K,IAAI;KAClC;;AAEH,SAAO3F;AACT;AAEA2C,KAAK,CAAC,SAAS,QAAQ,GAAG,SAAAlH,MAAQ;AAChC,MAAMqK,QAAQrK,KAAKsK,OAAO,CAAC,EAAEC,YAAW,IAAKvK,KAAK8J,MAAM,CAAC;AACzDzB,WAAQ,QAAA,OAASgC,KAAK,CAAA,IAAM,SAAClI,IAAIqI,eAAkB;AACjD,WACErI,MACA8H,mBAAmB9H,IAAInC,MAAMwK,gBAAgB9C,eAAeD,YAAY;;AAG5E,MAAMQ,QAAQjI,SAAS,UAAU,CAAC,QAAQ,OAAO,IAAI,CAAC,OAAO,QAAQ;AAErEqI,WAASrI,IAAI,IAAI,SAAC8B,MAAMM,GAAM;AAC5B,QAAImC,MAAMnC;AACV,QAAImC,QAAQlF,QAAW;AACrB,UAAIyC,MAAM;AACR,YAAM4H,cAAcrC,cAAcvF,IAAI;AACtC,YAAI4H,aAAa;AACfnF,iBAAOwD,YAAYjG,MAAM,CAAC,WAAW,QAAQ,GAAGmG,KAAK;;AAEvD,eAAO/F,IAAIJ,MAAM9B,MAAMuE,GAAG;;AAE5B,aAAOlF;;AAET,WAAOyC,QAAQmI,mBAAmBnI,MAAM9B,MAAMuH,aAAa;;AAE/D,CAAC;AAED,SAASkD,IAAIC,IAAIC,MAAM;AACrB,WAAWtI,KAAKsI,MAAM;AACpB,QAAIA,KAAKrI,eAAeD,CAAC,GAAG;AAC1BqI,SAAGrI,CAAC,IAAIsI,KAAKtI,CAAC;;;AAGlB,SAAOqI;AACT;AAEA,IAAME,QAAQ;EACZC,WAAS,SAAA,UAAC/K,MAAM;AACd,QAAIA,QAAQA,KAAKP,YAAYO,KAAKgL,YAAY;AAC5C,aAAOhL;;AAET,QAAM2C,MAAM3C,KAAK4C,iBAAiB5C;AAClC,WAAO2C,IAAIqB,eAAerB,IAAIsB;;EAEhCG;EACAwB,QAAM,SAAA,OAACvD,IAAIpC,OAAOsF,QAAQ;AACxB,QAAI,OAAOtF,UAAU,aAAa;AAChC0G,gBAAUtE,IAAIpC,OAAOsF,UAAU,CAAA,CAAE;WAC5B;AACL,aAAOzB,UAAUzB,EAAE;;;EAGvB6B;EACAkD;EACAhF;EACA6I,OAAK,SAAA,MAAC9G,KAAK;AACT,QAAI5B;AACJ,QAAMkB,MAAM,CAAA;AACZ,SAAKlB,KAAK4B,KAAK;AACb,UAAIA,IAAI3B,eAAeD,CAAC,GAAG;AACzBkB,YAAIlB,CAAC,IAAI4B,IAAI5B,CAAC;;;AAGlB,QAAM2I,WAAW/G,IAAI+G;AACrB,QAAIA,UAAU;AACZ,WAAK3I,KAAK4B,KAAK;AACb,YAAIA,IAAI3B,eAAeD,CAAC,GAAG;AACzBkB,cAAIyH,SAAS3I,CAAC,IAAI4B,IAAI+G,SAAS3I,CAAC;;;;AAItC,WAAOkB;;EAETkH;EACAQ,qBAAmB,SAAA,oBAAC3H,GAAG;AACrB,WAAOI,cAAcJ,CAAC;;EAExB4H,oBAAkB,SAAA,mBAAC5H,GAAG;AACpB,WAAOK,aAAaL,CAAC;;EAEvB6H,OAAK,SAAA,QAAU;AACb,QAAM5H,MAAM,CAAA;AACZ,aAASlB,IAAI,GAAGA,IAAI,UAAK+E,QAAQ/E,KAAK;AACpCuI,YAAMH,IAAIlH,KAAUlB,IAAC,KAAA,UAAA,UAADA,IAAC,SAAA,UAADA,CAAC,CAAA;;AAEvB,WAAOkB;;EAET4F,eAAe;EACfC,gBAAgB;AAClB;AAEAqB,IAAIG,OAAOvC,QAAQ;ACrmBnB,IAAQC,aAAcsC,MAAdtC;AAER,SAAS8C,gBAAgB7C,SAAS;AAChC,MAAIqC,MAAM5G,SAASuE,OAAO,KAAKA,QAAQpE,aAAa,GAAG;AACrD,WAAO;;AAiBT,MAAM1B,MAAMmI,MAAM1G,YAAYqE,OAAO;AACrC,MAAM5F,OAAOF,IAAIE;AACjB,MAAI6F;AACJ,MAAI6C,gBAAgBT,MAAM1I,IAAIqG,SAAS,UAAU;AACjD,MAAM+C,aAAaD,kBAAkB,WAAWA,kBAAkB;AAElE,MAAI,CAACC,YAAY;AACf,WAAO/C,QAAQgD,SAASC,YAAW,MAAO,SACtC,OACAlD,WAAUC,OAAO;;AAGvB,OACEC,SAASF,WAAUC,OAAO,GAC1BC,UAAUA,WAAW7F,QAAQ6F,OAAOrE,aAAa,GACjDqE,SAASF,WAAUE,MAAM,GACzB;AACA6C,oBAAgBT,MAAM1I,IAAIsG,QAAQ,UAAU;AAC5C,QAAI6C,kBAAkB,UAAU;AAC9B,aAAO7C;;;AAGX,SAAO;AACT;AC/CA,IAAQF,cAAcsC,MAAdtC;AAEO,SAASmD,gBAAgBlD,SAAS;AAC/C,MAAIqC,MAAM5G,SAASuE,OAAO,KAAKA,QAAQpE,aAAa,GAAG;AACrD,WAAO;;AAGT,MAAM1B,MAAMmI,MAAM1G,YAAYqE,OAAO;AACrC,MAAM5F,OAAOF,IAAIE;AACjB,MAAI6F,SAAS;AACb;IACEA,SAASF,YAAUC,OAAO;;IAE1BC,UAAUA,WAAW7F,QAAQ6F,WAAW/F;IACxC+F,SAASF,YAAUE,MAAM;IACzB;AACA,QAAM6C,gBAAgBT,MAAM1I,IAAIsG,QAAQ,UAAU;AAClD,QAAI6C,kBAAkB,SAAS;AAC7B,aAAO;;;AAGX,SAAO;AACT;ACjBA,SAASK,yBAAyBnD,SAASoD,kBAAkB;AAC3D,MAAMC,cAAc;IAClB3I,MAAM;IACN4I,OAAOC;IACP5I,KAAK;IACL6I,QAAQD;;AAEV,MAAI3J,KAAKiJ,gBAAgB7C,OAAO;AAChC,MAAM9F,MAAMmI,MAAM1G,YAAYqE,OAAO;AACrC,MAAMM,MAAMpG,IAAIqB,eAAerB,IAAIsB;AACnC,MAAMpB,OAAOF,IAAIE;AACjB,MAAME,kBAAkBJ,IAAII;AAI5B,SAAOV,IAAI;AAET,SACG6J,UAAUC,UAAUC,QAAQ,MAAM,MAAM,MAAM/J,GAAGgK,gBAAgB;;;IAIjEhK,OAAOQ,QACNR,OAAOU,mBACP+H,MAAM1I,IAAIC,IAAI,UAAU,MAAM,WAChC;AACA,UAAM0B,MAAM+G,MAAMlF,OAAOvD,EAAE;AAE3B0B,UAAIZ,QAAQd,GAAGgB;AACfU,UAAIX,OAAOf,GAAGiB;AACdwI,kBAAY1I,MAAMH,KAAK6F,IAAIgD,YAAY1I,KAAKW,IAAIX,GAAG;AACnD0I,kBAAYC,QAAQ9I,KAAKqJ;QACvBR,YAAYC;;QAEZhI,IAAIZ,OAAOd,GAAGgK;MAAW;AAE3BP,kBAAYG,SAAShJ,KAAKqJ,IACxBR,YAAYG,QACZlI,IAAIX,MAAMf,GAAGkK,YAAY;AAE3BT,kBAAY3I,OAAOF,KAAK6F,IAAIgD,YAAY3I,MAAMY,IAAIZ,IAAI;eAC7Cd,OAAOQ,QAAQR,OAAOU,iBAAiB;AAChD;;AAEFV,SAAKiJ,gBAAgBjJ,EAAE;;AAMzB,MAAImK,mBAAmB;AACvB,MAAI,CAAC1B,MAAM5G,SAASuE,OAAO,KAAKA,QAAQpE,aAAa,GAAG;AACtDmI,uBAAmB/D,QAAQjJ,MAAMqG;AACjC,QAAMA,WAAWiF,MAAM1I,IAAIqG,SAAS,UAAU;AAC9C,QAAI5C,aAAa,YAAY;AAC3B4C,cAAQjJ,MAAMqG,WAAW;;;AAI7B,MAAM4G,UAAU3B,MAAMK,oBAAoBpC,GAAG;AAC7C,MAAM2D,UAAU5B,MAAMM,mBAAmBrC,GAAG;AAC5C,MAAMM,gBAAgByB,MAAMzB,cAAcN,GAAG;AAC7C,MAAMO,iBAAiBwB,MAAMxB,eAAeP,GAAG;AAC/C,MAAI4D,gBAAgB5J,gBAAgB6J;AACpC,MAAIC,iBAAiB9J,gBAAgB+J;AAIrC,MAAMC,YAAYvM,OAAOC,iBAAiBoC,IAAI;AAC9C,MAAIkK,UAAUC,cAAc,UAAU;AACpCL,oBAAgB5D,IAAIkE;;AAEtB,MAAIF,UAAUG,cAAc,UAAU;AACpCL,qBAAiB9D,IAAIoE;;AAIvB,MAAI1E,QAAQjJ,OAAO;AACjBiJ,YAAQjJ,MAAMqG,WAAW2G;;AAG3B,MAAIX,oBAAoBF,gBAAgBlD,OAAO,GAAG;AAEhDqD,gBAAY3I,OAAOF,KAAK6F,IAAIgD,YAAY3I,MAAMsJ,OAAO;AACrDX,gBAAY1I,MAAMH,KAAK6F,IAAIgD,YAAY1I,KAAKsJ,OAAO;AACnDZ,gBAAYC,QAAQ9I,KAAKqJ,IAAIR,YAAYC,OAAOU,UAAUpD,aAAa;AACvEyC,gBAAYG,SAAShJ,KAAKqJ,IAAIR,YAAYG,QAAQS,UAAUpD,cAAc;SACrE;AAEL,QAAM8D,kBAAkBnK,KAAK6F,IAAI6D,eAAeF,UAAUpD,aAAa;AACvEyC,gBAAYC,QAAQ9I,KAAKqJ,IAAIR,YAAYC,OAAOqB,eAAe;AAE/D,QAAMC,mBAAmBpK,KAAK6F,IAAI+D,gBAAgBH,UAAUpD,cAAc;AAC1EwC,gBAAYG,SAAShJ,KAAKqJ,IAAIR,YAAYG,QAAQoB,gBAAgB;;AAGpE,SAAOvB,YAAY1I,OAAO,KACxB0I,YAAY3I,QAAQ,KACpB2I,YAAYG,SAASH,YAAY1I,OACjC0I,YAAYC,QAAQD,YAAY3I,OAC9B2I,cACA;AACN;AC3GA,SAASwB,kBAAkBC,aAAaC,UAAU1B,aAAaZ,UAAU;AACvE,MAAMnH,MAAM+G,MAAMG,MAAMsC,WAAW;AACnC,MAAME,OAAO;IACX/D,OAAO8D,SAAS9D;IAChBC,QAAQ6D,SAAS7D;;AAGnB,MAAIuB,SAASwC,WAAW3J,IAAIZ,OAAO2I,YAAY3I,MAAM;AACnDY,QAAIZ,OAAO2I,YAAY3I;;AAIzB,MACE+H,SAASyC,eACT5J,IAAIZ,QAAQ2I,YAAY3I,QACxBY,IAAIZ,OAAOsK,KAAK/D,QAAQoC,YAAYC,OACpC;AACA0B,SAAK/D,SAAS3F,IAAIZ,OAAOsK,KAAK/D,QAAQoC,YAAYC;;AAIpD,MAAIb,SAASwC,WAAW3J,IAAIZ,OAAOsK,KAAK/D,QAAQoC,YAAYC,OAAO;AAEjEhI,QAAIZ,OAAOF,KAAK6F,IAAIgD,YAAYC,QAAQ0B,KAAK/D,OAAOoC,YAAY3I,IAAI;;AAItE,MAAI+H,SAAS0C,WAAW7J,IAAIX,MAAM0I,YAAY1I,KAAK;AACjDW,QAAIX,MAAM0I,YAAY1I;;AAIxB,MACE8H,SAAS2C,gBACT9J,IAAIX,OAAO0I,YAAY1I,OACvBW,IAAIX,MAAMqK,KAAK9D,SAASmC,YAAYG,QACpC;AACAwB,SAAK9D,UAAU5F,IAAIX,MAAMqK,KAAK9D,SAASmC,YAAYG;;AAIrD,MAAIf,SAAS0C,WAAW7J,IAAIX,MAAMqK,KAAK9D,SAASmC,YAAYG,QAAQ;AAElElI,QAAIX,MAAMH,KAAK6F,IAAIgD,YAAYG,SAASwB,KAAK9D,QAAQmC,YAAY1I,GAAG;;AAGtE,SAAO0H,MAAMH,IAAI5G,KAAK0J,IAAI;AAC5B;AC/CA,SAASK,UAAU9N,MAAM;AACvB,MAAI4F;AACJ,MAAIpC;AACJ,MAAIuK;AACJ,MAAI,CAACjD,MAAM5G,SAASlE,IAAI,KAAKA,KAAKqE,aAAa,GAAG;AAChDuB,IAAAA,UAASkF,MAAMlF,OAAO5F,IAAI;AAC1BwD,QAAIsH,MAAMkD,WAAWhO,IAAI;AACzB+N,QAAIjD,MAAMmD,YAAYjO,IAAI;SACrB;AACL,QAAM+I,MAAM+B,MAAMC,UAAU/K,IAAI;AAChC4F,IAAAA,UAAS;MACPzC,MAAM2H,MAAMK,oBAAoBpC,GAAG;MACnC3F,KAAK0H,MAAMM,mBAAmBrC,GAAG;;AAEnCvF,QAAIsH,MAAMzB,cAAcN,GAAG;AAC3BgF,QAAIjD,MAAMxB,eAAeP,GAAG;;AAE9BnD,EAAAA,QAAO8D,QAAQlG;AACfoC,EAAAA,QAAO+D,SAASoE;AAChB,SAAOnI;AACT;AClBA,SAASsI,eAAeC,QAAQC,OAAO;AACrC,MAAMC,IAAID,MAAM5D,OAAO,CAAC;AACxB,MAAM8D,IAAIF,MAAM5D,OAAO,CAAC;AACxB,MAAMhH,IAAI2K,OAAOzE;AACjB,MAAMqE,IAAII,OAAOxE;AAEjB,MAAI7I,IAAIqN,OAAOhL;AACf,MAAInC,IAAImN,OAAO/K;AAEf,MAAIiL,MAAM,KAAK;AACbrN,SAAK+M,IAAI;aACAM,MAAM,KAAK;AACpBrN,SAAK+M;;AAGP,MAAIO,MAAM,KAAK;AACbxN,SAAK0C,IAAI;aACA8K,MAAM,KAAK;AACpBxN,SAAK0C;;AAGP,SAAO;IACLL,MAAMrC;IACNsC,KAAKpC;;AAET;AC3BA,SAASuN,eAAef,UAAUgB,eAAeC,QAAQ7I,SAAQ8I,cAAc;AAC7E,MAAMC,KAAKT,eAAeM,eAAeC,OAAO,CAAC,CAAC;AAClD,MAAMG,KAAKV,eAAeV,UAAUiB,OAAO,CAAC,CAAC;AAC7C,MAAMI,OAAO,CAACD,GAAGzL,OAAOwL,GAAGxL,MAAMyL,GAAGxL,MAAMuL,GAAGvL,GAAG;AAEhD,SAAO;IACLD,MAAMF,KAAK6L,MAAMtB,SAASrK,OAAO0L,KAAK,CAAC,IAAIjJ,QAAO,CAAC,IAAI8I,aAAa,CAAC,CAAC;IACtEtL,KAAKH,KAAK6L,MAAMtB,SAASpK,MAAMyL,KAAK,CAAC,IAAIjJ,QAAO,CAAC,IAAI8I,aAAa,CAAC,CAAC;;AAExE;ACEA,SAASK,QAAQxB,aAAaC,UAAU1B,aAAa;AACnD,SACEyB,YAAYpK,OAAO2I,YAAY3I,QAC/BoK,YAAYpK,OAAOqK,SAAS9D,QAAQoC,YAAYC;AAEpD;AAEA,SAASiD,QAAQzB,aAAaC,UAAU1B,aAAa;AACnD,SACEyB,YAAYnK,MAAM0I,YAAY1I,OAC9BmK,YAAYnK,MAAMoK,SAAS7D,SAASmC,YAAYG;AAEpD;AAEA,SAASgD,gBAAgB1B,aAAaC,UAAU1B,aAAa;AAC3D,SACEyB,YAAYpK,OAAO2I,YAAYC,SAC/BwB,YAAYpK,OAAOqK,SAAS9D,QAAQoC,YAAY3I;AAEpD;AAEA,SAAS+L,gBAAgB3B,aAAaC,UAAU1B,aAAa;AAC3D,SACEyB,YAAYnK,MAAM0I,YAAYG,UAC9BsB,YAAYnK,MAAMoK,SAAS7D,SAASmC,YAAY1I;AAEpD;AAEA,SAAS+L,KAAKV,QAAQW,KAAK5N,KAAK;AAC9B,MAAMiC,MAAM,CAAA;AACZqH,QAAM1D,KAAKqH,QAAQ,SAAAY,GAAK;AACtB5L,QAAI6L,KACFD,EAAEzO,QAAQwO,KAAK,SAAAG,GAAK;AAClB,aAAO/N,IAAI+N,CAAC;KACb,CAAC;GAEL;AACD,SAAO9L;AACT;AAEA,SAAS+L,WAAW5J,SAAQ6J,OAAO;AACjC7J,EAAAA,QAAO6J,KAAK,IAAI,CAAC7J,QAAO6J,KAAK;AAC7B,SAAO7J;AACT;AAEA,SAAS8J,cAAcC,KAAKC,WAAW;AACrC,MAAIC;AACJ,MAAI,KAAK3K,KAAKyK,GAAG,GAAG;AAClBE,QAAKC,SAASH,IAAII,UAAU,GAAGJ,IAAIrI,SAAS,CAAC,GAAG,EAAE,IAAI,MAAOsI;SACxD;AACLC,QAAIC,SAASH,KAAK,EAAE;;AAEtB,SAAOE,KAAK;AACd;AAEA,SAASG,gBAAgBpK,SAAQvD,IAAI;AACnCuD,EAAAA,QAAO,CAAC,IAAI8J,cAAc9J,QAAO,CAAC,GAAGvD,GAAGqH,KAAK;AAC7C9D,EAAAA,QAAO,CAAC,IAAI8J,cAAc9J,QAAO,CAAC,GAAGvD,GAAGsH,MAAM;AAChD;AAOA,SAASsG,QAAQ5N,IAAI6N,WAAW9B,OAAO+B,oBAAoB;AACzD,MAAI1B,SAASL,MAAMK;AACnB,MAAI7I,UAASwI,MAAMxI,UAAU,CAAC,GAAG,CAAC;AAClC,MAAI8I,eAAeN,MAAMM,gBAAgB,CAAC,GAAG,CAAC;AAC9C,MAAIxD,WAAWkD,MAAMlD;AACrB,MAAMrJ,SAASuM,MAAMvM,UAAUQ;AAC/BuD,EAAAA,UAAS,CAAA,EAAGwK,OAAOxK,OAAM;AACzB8I,iBAAe,CAAA,EAAG0B,OAAO1B,YAAY;AACrCxD,aAAWA,YAAY,CAAA;AACvB,MAAMmF,iBAAiB,CAAA;AACvB,MAAIC,OAAO;AACX,MAAMzE,mBAAmB,CAAC,EAAEX,YAAYA,SAASW;AAEjD,MAAMC,cAAcF,yBAAyB/J,QAAQgK,gBAAgB;AAErE,MAAM2B,WAAWM,UAAUjM,MAAM;AAEjCmO,kBAAgBpK,SAAQ4H,QAAQ;AAChCwC,kBAAgBtB,cAAcwB,SAAS;AAEvC,MAAI3C,cAAcgB,eAChBf,UACA0C,WACAzB,QACA7I,SACA8I,YAAY;AAGd,MAAI6B,cAAczF,MAAMO,MAAMmC,UAAUD,WAAW;AAGnD,MACEzB,gBACCZ,SAASwC,WAAWxC,SAAS0C,YAC9BuC,oBACA;AACA,QAAIjF,SAASwC,SAAS;AAEpB,UAAIqB,QAAQxB,aAAaC,UAAU1B,WAAW,GAAG;AAE/C,YAAM0E,YAAYrB,KAAKV,QAAQ,UAAU;UACvCgC,GAAG;UACHC,GAAG;SACJ;AAED,YAAMC,YAAYnB,WAAW5J,SAAQ,CAAC;AACtC,YAAMgL,kBAAkBpB,WAAWd,cAAc,CAAC;AAClD,YAAMmC,iBAAiBtC,eACrBf,UACA0C,WACAM,WACAG,WACAC,eAAe;AAGjB,YAAI,CAAC3B,gBAAgB4B,gBAAgBrD,UAAU1B,WAAW,GAAG;AAC3DwE,iBAAO;AACP7B,mBAAS+B;AACT5K,UAAAA,UAAS+K;AACTjC,yBAAekC;;;;AAKrB,QAAI1F,SAAS0C,SAAS;AAEpB,UAAIoB,QAAQzB,aAAaC,UAAU1B,WAAW,GAAG;AAE/C,YAAM0E,aAAYrB,KAAKV,QAAQ,UAAU;UACvCqC,GAAG;UACHC,GAAG;SACJ;AAED,YAAMJ,aAAYnB,WAAW5J,SAAQ,CAAC;AACtC,YAAMgL,mBAAkBpB,WAAWd,cAAc,CAAC;AAClD,YAAMmC,kBAAiBtC,eACrBf,UACA0C,WACAM,YACAG,YACAC,gBAAe;AAGjB,YAAI,CAAC1B,gBAAgB2B,iBAAgBrD,UAAU1B,WAAW,GAAG;AAC3DwE,iBAAO;AACP7B,mBAAS+B;AACT5K,UAAAA,UAAS+K;AACTjC,yBAAekC;;;;AAMrB,QAAIN,MAAM;AACR/C,oBAAcgB,eACZf,UACA0C,WACAzB,QACA7I,SACA8I,YAAY;AAEd5D,YAAMH,IAAI4F,aAAahD,WAAW;;AAEpC,QAAMyD,eAAejC,QAAQxB,aAAaC,UAAU1B,WAAW;AAC/D,QAAMmF,eAAejC,QAAQzB,aAAaC,UAAU1B,WAAW;AAG/D,QAAIkF,gBAAgBC,cAAc;AAChC,UAAIT,cAAY/B;AAGhB,UAAIuC,cAAc;AAChBR,sBAAYrB,KAAKV,QAAQ,UAAU;UACjCgC,GAAG;UACHC,GAAG;SACJ;;AAEH,UAAIO,cAAc;AAChBT,sBAAYrB,KAAKV,QAAQ,UAAU;UACjCqC,GAAG;UACHC,GAAG;SACJ;;AAGHtC,eAAS+B;AAET5K,MAAAA,UAASwI,MAAMxI,UAAU,CAAC,GAAG,CAAC;AAC9B8I,qBAAeN,MAAMM,gBAAgB,CAAC,GAAG,CAAC;;AAG5C2B,mBAAe3C,UAAUxC,SAASwC,WAAWsD;AAC7CX,mBAAezC,UAAU1C,SAAS0C,WAAWqD;AAG7C,QAAIZ,eAAe3C,WAAW2C,eAAezC,SAAS;AACpD2C,oBAAcjD,kBACZC,aACAC,UACA1B,aACAuE,cAAc;;;AAMpB,MAAIE,YAAY7G,UAAU8D,SAAS9D,OAAO;AACxCoB,UAAM1I,IACJP,QACA,SACAiJ,MAAMpB,MAAM7H,MAAM,IAAI0O,YAAY7G,QAAQ8D,SAAS9D,KAAK;;AAI5D,MAAI6G,YAAY5G,WAAW6D,SAAS7D,QAAQ;AAC1CmB,UAAM1I,IACJP,QACA,UACAiJ,MAAMnB,OAAO9H,MAAM,IAAI0O,YAAY5G,SAAS6D,SAAS7D,MAAM;;AAO/DmB,QAAMlF,OACJ/D,QACA;IACEsB,MAAMoN,YAAYpN;IAClBC,KAAKmN,YAAYnN;KAEnB;IACEoC,aAAa4I,MAAM5I;IACnBC,cAAc2I,MAAM3I;IACpB0B,iBAAiBiH,MAAMjH;IACvBP,aAAawH,MAAMxH;GACpB;AAGH,SAAO;IACL6H;IACA7I,QAAAA;IACA8I;IACAxD,UAAUmF;;AAEd;ACjQA,SAASa,mBAAmBC,QAAQtF,kBAAkB;AACpD,MAAMC,cAAcF,yBAAyBuF,QAAQtF,gBAAgB;AACrE,MAAMuF,eAAetD,UAAUqD,MAAM;AAErC,SACE,CAACrF,eACDsF,aAAajO,OAAOiO,aAAa1H,SAASoC,YAAY3I,QACtDiO,aAAahO,MAAMgO,aAAazH,UAAUmC,YAAY1I,OACtDgO,aAAajO,QAAQ2I,YAAYC,SACjCqF,aAAahO,OAAO0I,YAAYG;AAEpC;AAEA,SAASoF,aAAahP,IAAIiP,SAASlD,OAAO;AACxC,MAAM+C,SAAS/C,MAAM+C,UAAUG;AAC/B,MAAM9C,gBAAgBV,UAAUqD,MAAM;AAEtC,MAAMI,0BAA0B,CAACL,mBAC/BC,QACA/C,MAAMlD,YAAYkD,MAAMlD,SAASW,gBAAgB;AAGnD,SAAOoE,QAAQ5N,IAAImM,eAAeJ,OAAOmD,uBAAuB;AAClE;AAEAF,aAAaG,oBAAoBlG;AAEjC+F,aAAaI,6BAA6B7F;ACxB1C,SAAS8F,WAAWrP,IAAIsP,UAAUvD,OAAO;AACvC,MAAIwD;AACJ,MAAIC;AAEJ,MAAMlP,MAAMmI,MAAM1G,YAAY/B,EAAE;AAChC,MAAM0G,MAAMpG,IAAIqB,eAAerB,IAAIsB;AAEnC,MAAMwI,UAAU3B,MAAMK,oBAAoBpC,GAAG;AAC7C,MAAM2D,UAAU5B,MAAMM,mBAAmBrC,GAAG;AAC5C,MAAMM,gBAAgByB,MAAMzB,cAAcN,GAAG;AAC7C,MAAMO,iBAAiBwB,MAAMxB,eAAeP,GAAG;AAE/C,MAAI,WAAW4I,UAAU;AACvBC,YAAQD,SAASC;SACZ;AACLA,YAAQnF,UAAUkF,SAASG;;AAG7B,MAAI,WAAWH,UAAU;AACvBE,YAAQF,SAASE;SACZ;AACLA,YAAQnF,UAAUiF,SAASI;;AAG7B,MAAM7B,YAAY;IAChB/M,MAAMyO;IACNxO,KAAKyO;IACLnI,OAAO;IACPC,QAAQ;;AAGV,MAAMqI,cACJJ,SAAS,KACTA,SAASnF,UAAUpD,iBAClBwI,SAAS,KAAKA,SAASnF,UAAUpD;AAGpC,MAAMmF,SAAS,CAACL,MAAMK,OAAO,CAAC,GAAG,IAAI;AAErC,SAAOwB,QAAQ5N,IAAI6N,WAAS+B,gBAAAA,gBAAA,CAAA,GAAO7D,KAAK,GAAA,CAAA,GAAA;IAAEK;MAAUuD,WAAW;AACjE;;;AC9CO,SAAS,YAAY,MAAM,MAAM;AACtC,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,CAAC,QAAQ,CAAC,KAAM,QAAO;AAC3B,MAAI,WAAW,QAAQ,WAAW,MAAM;AACtC,WAAO,KAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AAAA,EAC1D;AACA,MAAI,aAAa,QAAQ,aAAa,MAAM;AAC1C,WAAO,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY,KAAK;AAAA,EAChE;AACA,SAAO;AACT;AACO,SAAS,aAAa,eAAe,WAAW;AAErD,MAAI,kBAAkB,SAAS,iBAAiB,SAAS,WAAW,aAAa,KAAK,OAAO,cAAc,UAAU,YAAY;AAC/H,kBAAc,MAAM;AAAA,EACtB;AACF;AACO,SAAS,cAAc,SAAS,UAAU;AAC/C,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,WAAS,SAAS,MAAM;AACtB,QAAI,CAAC;AAAA,MACH;AAAA,IACF,CAAC,IAAI;AACL,QAAI,CAAC,SAAS,gBAAgB,SAAS,MAAM,EAAG;AAChD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,sBAAsB;AACjC,UAAM,aAAa,KAAK,MAAM,KAAK;AACnC,UAAM,cAAc,KAAK,MAAM,MAAM;AACrC,QAAI,cAAc,cAAc,eAAe,aAAa;AAE1D,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,iBAAS;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,gBAAY;AACZ,iBAAa;AAAA,EACf;AACA,QAAM,iBAAiB,IAAI,0BAAe,QAAQ;AAClD,MAAI,SAAS;AACX,mBAAe,QAAQ,OAAO;AAAA,EAChC;AACA,SAAO,MAAM;AACX,mBAAe,WAAW;AAAA,EAC5B;AACF;;;ACpDA,IAAO,oBAAS,CAAC,UAAU,WAAW;AACpC,MAAI,SAAS;AACb,MAAI,UAAU;AACd,WAAS,gBAAgB;AACvB,iBAAa,OAAO;AAAA,EACtB;AACA,WAAS,QAAQ,OAAO;AACtB,QAAI,CAAC,UAAU,UAAU,MAAM;AAC7B,UAAI,SAAS,MAAM,OAAO;AAExB;AAAA,MACF;AACA,eAAS;AACT,oBAAc;AACd,gBAAU,WAAW,MAAM;AACzB,iBAAS;AAAA,MACX,GAAG,OAAO,KAAK;AAAA,IACjB,OAAO;AACL,oBAAc;AACd,gBAAU,WAAW,MAAM;AACzB,iBAAS;AACT,gBAAQ;AAAA,MACV,GAAG,OAAO,KAAK;AAAA,IACjB;AAAA,EACF;AACA,SAAO,CAAC,SAAS,MAAM;AACrB,aAAS;AACT,kBAAc;AAAA,EAChB,CAAC;AACH;;;ACrBO,IAAM,aAAa;AAAA,EACxB,OAAO;AAAA,EACP,QAAQ,CAAC,QAAQ,QAAQ;AAAA,EACzB,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,UAAU;AACZ;AACA,SAAS,WAAW,MAAM;AACxB,MAAI,OAAO,SAAS,WAAY,QAAO;AACvC,SAAO,KAAK;AACd;AACA,SAAS,SAAS,OAAO;AACvB,MAAI,OAAO,UAAU,YAAY,CAAC,MAAO,QAAO;AAChD,SAAO;AACT;AACA,IAAO,gBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO,CAAC,OAAO;AAAA,EACf,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,IAAI,CAAC,CAAC;AACvB,UAAM,UAAU,IAAI;AACpB,UAAM,CAAC,YAAY,gBAAgB,IAAI,kBAAU,MAAM;AACrD,YAAM;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI;AACJ,UAAI,CAAC,kBAAkB,gBAAgB,QAAQ,OAAO;AACpD,cAAM,SAAS,QAAQ;AACvB,YAAI;AACJ,cAAM,UAAU,WAAW,YAAY;AACvC,cAAM,QAAQ,SAAS,YAAY;AACnC,iBAAS,MAAM,UAAU;AACzB,iBAAS,MAAM,QAAQ;AACvB,iBAAS,MAAM,QAAQ;AAGvB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AAEJ,YAAI,WAAW,kBAAU,OAAO,GAAG;AACjC,mBAAS,aAAa,QAAQ,SAAS,WAAW;AAAA,QACpD,WAAW,OAAO;AAChB,mBAAS,WAAW,QAAQ,OAAO,WAAW;AAAA,QAChD;AACA,qBAAa,eAAe,MAAM;AAClC,YAAI,iBAAiB,QAAQ;AAC3B,wBAAc,QAAQ,MAAM;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,GAAG,SAAS,MAAM,MAAM,iBAAiB,CAAC;AAG1C,UAAM,gBAAgB,IAAI;AAAA,MACxB,QAAQ,MAAM;AAAA,MAAC;AAAA,IACjB,CAAC;AAED,UAAM,sBAAsB,IAAI;AAAA,MAC9B,QAAQ,MAAM;AAAA,MAAC;AAAA,IACjB,CAAC;AACD,UAAM,UAAU,MAAM;AACpB,YAAM,SAAS,MAAM;AACrB,YAAM,UAAU,WAAW,MAAM;AACjC,YAAM,QAAQ,SAAS,MAAM;AAC7B,UAAI,QAAQ,UAAU,oBAAoB,MAAM,SAAS;AACvD,4BAAoB,MAAM,OAAO;AACjC,4BAAoB,MAAM,UAAU,QAAQ;AAC5C,4BAAoB,MAAM,SAAS,cAAc,QAAQ,OAAO,UAAU;AAAA,MAC5E;AACA,UAAI,SAAS,MAAM,YAAY,WAAW,CAAC,YAAY,SAAS,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAQ,SAAS,MAAM,OAAO,MAAM,KAAK,GAAG;AAClI,mBAAW;AAEX,YAAI,cAAc,MAAM,YAAY,SAAS;AAC3C,wBAAc,MAAM,OAAO;AAC3B,wBAAc,MAAM,UAAU;AAC9B,wBAAc,MAAM,SAAS,cAAc,SAAS,UAAU;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AACA,cAAU,MAAM;AACd,eAAS,MAAM;AACb,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AACD,cAAU,MAAM;AACd,eAAS,MAAM;AACb,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAED,UAAM,MAAM,MAAM,UAAU,cAAY;AACtC,UAAI,CAAC,UAAU;AACb,mBAAW;AAAA,MACb,OAAO;AACL,yBAAiB;AAAA,MACnB;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AAED,UAAM,eAAe,IAAI,IAAI;AAC7B,UAAM,MAAM,MAAM,qBAAqB,yBAAuB;AAC5D,UAAI,qBAAqB;AACvB,YAAI,CAAC,aAAa,OAAO;AACvB,uBAAa,QAAQ,qBAAiB,QAAQ,UAAU,UAAU;AAAA,QACpE;AAAA,MACF,WAAW,aAAa,OAAO;AAC7B,qBAAa,MAAM,OAAO;AAC1B,qBAAa,QAAQ;AAAA,MACvB;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,IACT,CAAC;AACD,gBAAY,MAAM;AAChB,oBAAc,MAAM,OAAO;AAC3B,0BAAoB,MAAM,OAAO;AACjC,UAAI,aAAa,MAAO,cAAa,MAAM,OAAO;AAClD,uBAAiB;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,MACL,YAAY,MAAM,WAAW,IAAI;AAAA,IACnC,CAAC;AACD,WAAO,MAAM;AACX,YAAM,QAAQ,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,QAAQ;AAC1E,UAAI,OAAO;AACT,eAAO,aAAa,MAAM,CAAC,GAAG;AAAA,UAC5B,KAAK;AAAA,QACP,GAAG,MAAM,IAAI;AAAA,MACf;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;;;AC7ID,IAAO,qBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO,CAAC,cAAc,cAAc,aAAa,cAAc,OAAO;AAAA,EACtE,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,WAAW;AAC5B,UAAM,aAAa,WAAW;AAC9B,UAAM,mBAAmB,WAAW;AAEpC,UAAM,CAAC,cAAc,mBAAmB,IAAI,wBAAgB,MAAM,OAAO,SAAS,CAAC;AACnF,UAAM,YAAY,MAAM;AACtB,UAAI,MAAM,SAAS;AACjB,4BAAoB,MAAM,eAAe,CAAC;AAAA,MAC5C;AAAA,IACF;AACA,UAAM,UAAU,WAAW,KAAK;AAChC,QAAI;AACJ,UAAM,MAAM,MAAM,SAAS,SAAO;AAChC,mBAAa,SAAS;AACtB,UAAI,KAAK;AACP,oBAAY,WAAW,MAAM;AAC3B,kBAAQ,QAAQ,MAAM;AAAA,QACxB,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAED,UAAM,CAAC,QAAQ,YAAY,IAAI,yBAAiB,SAAS,SAAS;AAElE,UAAM,oBAAoB,WAAW;AAGrC,UAAM,iBAAiB,MAAM;AAC3B,UAAI,MAAM,OAAO;AACf,eAAO,MAAM;AAAA,MACf;AACA,aAAO,MAAM;AAAA,IACf;AACA,UAAM,aAAa,MAAM;AACvB,UAAI;AACJ,OAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,IAC3E;AACA,UAAM,kBAAkB,CAAC,cAAc,eAAe;AACpD,UAAI;AACJ,YAAM,uBAAuB,MAAM,sBAAsB,UAAU;AACnE,YAAM,sBAAsB,iBAAiB;AAC7C,UAAI,iBAAiB,UAAU,sBAAsB;AACnD,yBAAiB,QAAQ;AAAA,MAC3B;AACA,UAAI,OAAO,UAAU,SAAS;AAE5B,YAAI,wBAAwB,sBAAsB;AAChD,kBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,uBAAW;AAAA,UACb,CAAC;AAAA,QACH,OAAO;AACL,uBAAa,MAAM;AACjB,gBAAIE;AACJ,aAACA,MAAK,kBAAkB,WAAW,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,iBAAiB;AAAA,UAC/F,CAAC;AAAA,QACH;AACA,SAAC,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,cAAc,UAAU;AAAA,MACnG;AAAA,IACF;AAEA,UAAM,SAAS,SAAS,MAAM;AAC5B,YAAM,IAAI,OAAO,MAAM,cAAc,WAAW,MAAM,YAAY,UAAU,KAAK;AACjF,OAAC,gBAAgB,cAAc,EAAE,QAAQ,eAAa;AACpD,cAAM,WAAW,EAAE,SAAS;AAC5B,UAAE,SAAS,IAAI,UAAQ;AACrB,uBAAa;AAEb,iBAAO,QAAQ;AACf,uBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI;AAAA,QACnE;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,CAAC;AACD,UAAM,gBAAgB,MAAM;AAC1B,aAAO,IAAI,QAAQ,aAAW;AAC5B,0BAAkB,QAAQ;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,UAAM,CAAC,QAAQ,MAAM,GAAG,MAAM;AAC5B,UAAI,CAAC,OAAO,SAAS,OAAO,UAAU,UAAU;AAC9C,qBAAa;AAAA,MACf;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,WAAO;AAAA,MACL;AAAA,MACA,YAAY,MAAM;AAChB,eAAO,WAAW,MAAM,OAAO,WAAW;AAAA,MAC5C;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,SAAS,MAAM;AACnC,UAAI;AACJ,YAAM,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,OAAO,UAAU,WAAW,OAAO,UAAU,WAAW;AAClI,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,MAAM;AAAA,QAAC;AAAA,QACtB;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,OAAO;AAE3B,YAAM,cAAc,CAAC,SAAS,SAAS,CAAC,GAAG,aAAa,KAAK,GAAG;AAAA,QAC9D;AAAA,QACA,SAAS,gBAAgB,YAAY,gBAAgB,YAAY,CAAC,QAAQ,QAAQ,OAAO;AAAA;AAAA,QAEzF,eAAe,CAAC,QAAQ,SAAS,gBAAgB,WAAW,SAAS;AAAA,MACvE,CAAC,GAAG,MAAM,KAAK;AACf,UAAI,YAAY,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO;AAAA,QACvG,SAAS,MAAM;AAAA,MACjB,CAAC,CAAC;AAEF,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM,aAAa,2BAAY;AAC7B,iBAAO;AAAA,QACT,EAAE;AACF,oBAAY,YAAa,OAAO;AAAA,UAC9B,SAAS,GAAG,SAAS;AAAA,QACvB,GAAG,CAAC,SAAS,CAAC;AAAA,MAChB;AACA,YAAM,kBAAkB,mBAAW,WAAW,MAAM,OAAO,iBAAiB,OAAO,CAAC,MAAM,SAAS,GAAG,SAAS,eAAe;AAC9H,YAAM,aAAa,QAAQ,SAAS,CAAC,MAAM;AAC3C,YAAM,kBAAkB,aAAa,mBAAmB,OAAO,MAAM,MAAM,OAAO,KAAK,IAAI,CAAC;AAC5F,aAAO,YAAa,YAAY,eAAc,eAAc;AAAA,QAC1D,OAAO;AAAA,MACT,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,QACvB,iBAAiB;AAAA,MACnB,CAAC,GAAG;AAAA,QACF,SAAS,MAAM;AACb,iBAAO,CAAC,sBAAsB,MAAM,UAAU,eAAgB,YAAa,eAAO;AAAA,YAChF,UAAU,eAAe;AAAA,YACzB,OAAO;AAAA,YACP,OAAO;AAAA,YACP,uBAAuB;AAAA,YACvB,YAAY,cAAc;AAAA,YAC1B,SAAS;AAAA,YACT,WAAW;AAAA,UACb,GAAG;AAAA,YACD,SAAS,MAAM,YAAa,OAAO;AAAA,cACjC,SAAS;AAAA,cACT,gBAAgB;AAAA,cAChB,gBAAgB;AAAA,cAChB,eAAe,cAAc,aAAa,CAAC,SAAS,CAAC;AAAA,cACrD,CAAC,0BAAkB,wBAAwB,cAAc,GAAG,cAAc,cAAc,CAAC,SAAS,CAAC;AAAA,cACnG,SAAS;AAAA,YACX,GAAG,CAAC,SAAS,CAAC;AAAA,UAChB,CAAC,GAAG,CAAC,CAAC,OAAQ,QAAQ,KAAK,CAAC,CAAC,IAAI;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACvLD,IAAO,gBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,WAAW,KAAK;AACrC,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,WAAW,WAAW;AAC5B,UAAM,UAAU,WAAW;AAC3B,UAAM,CAAC,MAAM,MAAM,SAAS,MAAM,MAAM,MAAM,GAAG,MAAM;AACrD,mBAAa,QAAQ,MAAM;AAC3B,UAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,iBAAS,QAAQ;AAAA,MACnB;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AACD,WAAO;AAAA,MACL,YAAY,MAAM;AAChB,YAAI;AACJ,SAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,MAC3E;AAAA,MACA,YAAY,MAAM;AAChB,YAAI;AACJ,gBAAQ,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,MAClF;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,YAAM,aAAa,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG;AAAA,QAChE,SAAS,aAAa;AAAA,MACxB,CAAC;AACD,YAAM,YAAY,SAAS,QAAQ,YAAa,0BAAkB,eAAc,eAAc,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG;AAAA,QACjH,UAAU,MAAM;AAAA,QAChB,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,SAAS,MAAM;AAAA,MACjB,CAAC,IAAI,YAAa,oBAAY,eAAc,eAAc,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG;AAAA,QAC7E,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,SAAS,MAAM;AAAA,MACjB,CAAC;AACD,aAAO,YAAa,OAAO;AAAA,QACzB,OAAO;AAAA,MACT,GAAG,CAAC,YAAa,MAAM,YAAY,IAAI,GAAG,SAAS,CAAC;AAAA,IACtD;AAAA,EACF;AACF,CAAC;;;AC9DD,SAAS,WAAW,IAAI,IAAI,cAAc;AACxC,MAAI,cAAc;AAChB,WAAO,GAAG,CAAC,MAAM,GAAG,CAAC;AAAA,EACvB;AACA,SAAO,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC;AAC1C;AACO,SAAS,sBAAsB,mBAAmB,cAAc,OAAO;AAC5E,QAAM,YAAY,kBAAkB,YAAY,KAAK,CAAC;AACtD,SAAO,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK;AAChD;AACO,SAAS,uBAAuB,mBAAmB,WAAW,OAAO,cAAc;AACxF,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,OAAO,KAAK,iBAAiB;AAChD,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,UAAM,YAAY,WAAW,CAAC;AAC9B,QAAI,WAAW,kBAAkB,SAAS,EAAE,QAAQ,QAAQ,YAAY,GAAG;AACzE,aAAO,GAAG,SAAS,cAAc,SAAS;AAAA,IAC5C;AAAA,EACF;AACA,SAAO;AACT;;;ACtBA,IAAM,mBAAmB,OAAO,kBAAkB;AAC3C,IAAM,mBAAmB,SAAU,UAAU;AAClD,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAC/E,kBAAkB;AAAA,EACpB;AACA,UAAQ,kBAAkB;AAAA,IACxB,kBAAkB,OAAO;AAAA,IACzB,cAAc,SAAS,MAAM;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,YAAY,CAAC;AAEjB,UAAI,eAAe;AACnB,UAAI,iBAAiB,YAAY,aAAa;AAC5C,uBAAe;AAAA,MACjB;AACA,UAAI,CAAC,iBAAiB,aAAa;AACjC,uBAAe;AAAA,MACjB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AACO,IAAM,kBAAkB,MAAM;AACnC,mBAAiB,CAAC,GAAG;AAAA,IACnB,kBAAkB;AAAA,EACpB,CAAC;AACD,QAAM,gBAAgB,OAAO,kBAAkB;AAAA,IAC7C,cAAc,SAAS,MAAM,KAAK;AAAA,IAClC,kBAAkB;AAAA,EACpB,CAAC;AACD,SAAO;AAAA,IACL,cAAc,SAAS,MAAM,cAAc,aAAa,SAAS,cAAc,qBAAqB,KAAK;AAAA,EAC3G;AACF;;;AClCA,IAAO,iBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,cAAc,kBAAU,KAAK;AAAA,IAC7B,WAAW;AAAA,EACb;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AAEZ,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,gBAAgB;AACpB,aAAS,eAAe;AACtB,UAAI,aAAa,OAAO;AACtB,oBAAY,MAAM,aAAa;AAAA,MACjC;AAAA,IACF;AACA,kBAAc,MAAM;AAClB,cAAQ;AAER,mBAAa;AAAA,IACf,CAAC;AACD,cAAU,MAAM;AACd,UAAI,UAAW;AAEf,mBAAa;AAAA,IACf,CAAC;AACD,UAAM,YAAY,MAAM,cAAc,MAAM;AAC1C,UAAI,aAAa,SAAS,CAAC,WAAW;AACpC,oBAAY,MAAM,aAAa;AAAA,MACjC;AACA,UAAI,WAAW;AACb,kBAAU;AAAA,MACZ;AAAA,IACF,CAAC;AACD,cAAU,MAAM;AACd,eAAS,MAAM;AACb,YAAI;AACJ,YAAI,aAAa,OAAO;AACtB,WAAC,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,KAAK;AAAA,QAClF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAMD,WAAO,MAAM;AACX,UAAI;AACJ,UAAI,CAAC,aAAa,MAAO,QAAO;AAChC,UAAI,OAAO;AACT,gBAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,MAChF;AACA,aAAO,YAAY,YAAa,UAAU;AAAA,QACxC,MAAM;AAAA,MACR,GAAG,KAAK,IAAI;AAAA,IACd;AAAA,EACF;AACF,CAAC;;;ACtED,IAAI;AACW,SAAR,iBAAkC,OAAO;AAC9C,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,WAAW,QAAW;AACjC,UAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,UAAM,MAAM,QAAQ;AACpB,UAAM,MAAM,SAAS;AACrB,UAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,UAAM,aAAa,MAAM;AACzB,eAAW,WAAW;AACtB,eAAW,MAAM;AACjB,eAAW,OAAO;AAClB,eAAW,gBAAgB;AAC3B,eAAW,aAAa;AACxB,eAAW,QAAQ;AACnB,eAAW,SAAS;AACpB,eAAW,WAAW;AACtB,UAAM,YAAY,KAAK;AACvB,aAAS,KAAK,YAAY,KAAK;AAC/B,UAAM,iBAAiB,MAAM;AAC7B,UAAM,MAAM,WAAW;AACvB,QAAI,cAAc,MAAM;AACxB,QAAI,mBAAmB,aAAa;AAClC,oBAAc,MAAM;AAAA,IACtB;AACA,aAAS,KAAK,YAAY,KAAK;AAC/B,aAAS,iBAAiB;AAAA,EAC5B;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,QAAM,QAAQ,IAAI,MAAM,UAAU;AAClC,QAAM,QAAQ,OAAO,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,CAAC,CAAC;AAC3E,SAAO,OAAO,MAAM,KAAK,IAAI,iBAAiB,IAAI;AACpD;AACO,SAAS,uBAAuB,QAAQ;AAC7C,MAAI,OAAO,aAAa,eAAe,CAAC,UAAU,EAAE,kBAAkB,UAAU;AAC9E,WAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,QAAQ,qBAAqB;AAClD,SAAO;AAAA,IACL,OAAO,WAAW,KAAK;AAAA,IACvB,QAAQ,WAAW,MAAM;AAAA,EAC3B;AACF;;;ACjDA,IAAM,YAAY,kBAAkB,KAAK,IAAI,CAAC;AAC9C,IAAI,OAAO;AAIJ,SAAS,oBAAoB;AAClC,SAAO,SAAS,KAAK,gBAAgB,OAAO,eAAe,SAAS,gBAAgB,iBAAiB,OAAO,aAAa,SAAS,KAAK;AACzI;AACe,SAAR,gBAAiC,MAAM;AAC5C,QAAM,aAAa,SAAS,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK;AACxD,UAAQ;AACR,QAAM,KAAK,GAAG,SAAS,IAAI,IAAI;AAC/B,cAAY,aAAW;AACrB,QAAI,CAAC,kBAAU,GAAG;AAChB;AAAA,IACF;AACA,QAAI,WAAW,OAAO;AACpB,YAAM,gBAAgB,iBAAiB;AACvC,YAAM,aAAa,kBAAkB;AACrC,gBAAU;AAAA;AAAA;AAAA,IAGZ,aAAa,sBAAsB,aAAa,SAAS,EAAE;AAAA,IAC3D,EAAE;AAAA,IACF,OAAO;AACL,gBAAU,EAAE;AAAA,IACd;AACA,YAAQ,MAAM;AACZ,gBAAU,EAAE;AAAA,IACd,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO;AAAA,EACT,CAAC;AACH;;;AC7BA,IAAI,YAAY;AAChB,IAAM,aAAa,kBAAU;AAK7B,IAAMC,aAAY,kBAAgB;AAChC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,MAAI,cAAc;AAChB,QAAI,OAAO,iBAAiB,UAAU;AACpC,aAAO,SAAS,iBAAiB,YAAY,EAAE,CAAC;AAAA,IAClD;AACA,QAAI,OAAO,iBAAiB,YAAY;AACtC,aAAO,aAAa;AAAA,IACtB;AACA,QAAI,OAAO,iBAAiB,YAAY,wBAAwB,OAAO,aAAa;AAClF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,SAAS;AAClB;AACA,IAAO,wBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,kBAAkB;AAAA,IAClB,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,cAAc,kBAAU;AAAA,IACxB,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU,YAAY;AAAA,IACtB,WAAW;AAAA,EACb;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,WAAW;AAC7B,UAAM,eAAe,WAAW;AAChC,UAAM,QAAQ,WAAW;AACzB,UAAM,gBAAgB,WAAW,CAAC;AAClC,UAAM,mBAAmB,kBAAU,KAAK,SAAS,cAAc,KAAK;AACpE,UAAM,yBAAyB,MAAM;AACnC,UAAI,IAAI;AAGR,UAAI,UAAU,UAAU,kBAAkB;AACxC,SAAC,MAAM,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,UAAU,KAAK;AAAA,MACtJ;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,QAAI,SAAS;AACb,UAAM,iBAAiB,WAAY;AACjC,UAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,UAAI,SAAS,UAAU,SAAS,CAAC,UAAU,MAAM,YAAY;AAC3D,iBAASA,WAAU,MAAM,YAAY;AACrC,YAAI,QAAQ;AACV,iBAAO,YAAY,UAAU,KAAK;AAClC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,MACT;AACA,UAAI,CAAC,UAAU,OAAO;AACpB,kBAAU,QAAQ;AAClB,uBAAe,IAAI;AAAA,MACrB;AACA,0BAAoB;AACpB,aAAO,UAAU;AAAA,IACnB;AACA,UAAM,sBAAsB,MAAM;AAChC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,UAAU,SAAS,oBAAoB,qBAAqB,UAAU,MAAM,WAAW;AACzF,kBAAU,MAAM,YAAY;AAAA,MAC9B;AAAA,IACF;AACA,cAAU,MAAM;AACd,0BAAoB;AACpB,qBAAe;AAAA,IACjB,CAAC;AACD,oBAAgB,SAAS,MAAM;AAC7B,aAAO,MAAM,YAAY,MAAM,WAAW,kBAAU,MAAM,UAAU,UAAU,SAAS,QAAQ,UAAU,UAAU;AAAA,IACrH,CAAC,CAAC;AACF,cAAU,MAAM;AACd,UAAI,OAAO;AACX,YAAM,CAAC,MAAM,MAAM,SAAS,MAAM,MAAM,YAAY,GAAG,CAAC,OAAO,UAAU;AACvE,YAAI,CAAC,SAASC,aAAY,IAAI;AAC9B,YAAI,CAAC,aAAa,gBAAgB,IAAI;AAEtC,YAAI,YAAY;AACd,mBAASD,WAAU,MAAM,YAAY;AACrC,cAAI,WAAW,SAAS,MAAM;AAC5B,gBAAI,WAAW,CAAC,aAAa;AAC3B,2BAAa;AAAA,YACf,WAAW,MAAM;AACf,2BAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AAER,gBAAM,qBAAqB,OAAOC,kBAAiB,cAAc,OAAO,qBAAqB;AAC7F,cAAI,qBAAqBA,cAAa,SAAS,MAAM,iBAAiB,SAAS,IAAIA,kBAAiB,kBAAkB;AACpH,mCAAuB;AAAA,UACzB;AAAA,QACF;AACA,eAAO;AAAA,MACT,GAAG;AAAA,QACD,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,eAAS,MAAM;AACb,YAAI,CAAC,eAAe,GAAG;AACrB,gBAAM,QAAQ,WAAI,MAAM;AACtB,0BAAc,SAAS;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,MAAM;AACpB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,cAAc,WAAW,SAAS,MAAM;AAE1C,oBAAY,WAAW,YAAY,YAAY,IAAI;AAAA,MACrD;AACA,6BAAuB;AACvB,iBAAI,OAAO,MAAM,KAAK;AAAA,IACxB,CAAC;AACD,WAAO,MAAM;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,SAAS;AACb,YAAM,aAAa;AAAA,QACjB,cAAc,MAAM;AAAA,QACpB;AAAA,MACF;AACA,UAAI,cAAc,UAAU,eAAe,WAAW,aAAa,QAAQ;AACzE,iBAAS,YAAa,gBAAQ;AAAA,UAC5B,gBAAgB;AAAA,UAChB,OAAO;AAAA,UACP,aAAa,MAAM;AAAA,QACrB,GAAG;AAAA,UACD,SAAS,MAAM;AACb,gBAAI;AACJ,oBAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,UAAU;AAAA,UAC5F;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;;;ACpKD,IAAM,eAAe,CAAC,WAAW,eAAe,gBAAgB,gBAAgB,gBAAgB,WAAW,UAAU,eAAe;AACpI,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,QAAQ,CAAC,iBAAS;AAAA,EAClB,cAAc;AAAA,EACd,OAAO,aAAa;AAAA,EACpB,MAAM,OAAO;AACX,UAAM,QAAQ,SAAS,MAAM;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,kBAAkB,mBAAmB;AACvC,eAAO,sBAAsB,mBAAmB,gBAAgB,UAAU;AAAA,MAC5E;AACA,aAAO;AAAA,IACT,CAAC;AACD,UAAM,WAAW,WAAW,IAAI;AAChC,UAAM,cAAc,SAAO;AACzB,eAAS,QAAQ;AAAA,IACnB;AACA,WAAO;AAAA,MACL,kBAAkB,OAAO,oBAAoB,CAAC,CAAC;AAAA,MAC/C;AAAA,MACA;AAAA,MACA,YAAY,WAAW,IAAI;AAAA,MAC3B;AAAA,MACA,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,4BAA4B;AAAA,MAC5B,4BAA4B;AAAA,MAC5B,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,mBAAmB,CAAC;AAAA,IACtB;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,QAAQ,KAAK;AACnB,QAAI;AACJ,QAAI,KAAK,iBAAiB,QAAW;AACnC,qBAAe,CAAC,CAAC,MAAM;AAAA,IACzB,OAAO;AACL,qBAAe,CAAC,CAAC,MAAM;AAAA,IACzB;AACA,iBAAa,QAAQ,OAAK;AACxB,WAAK,OAAO,CAAC,EAAE,IAAI,OAAK;AACtB,aAAK,WAAW,GAAG,CAAC;AAAA,MACtB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa,KAAK;AAChB,UAAI,QAAQ,QAAW;AACrB,aAAK,mBAAmB,KAAK;AAC7B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AACR,YAAQ,oBAAoB;AAAA,MAC1B,kBAAkB,KAAK;AAAA,MACvB,mBAAmB,KAAK;AAAA,MACxB,mBAAmB,KAAK;AAAA,IAC1B,CAAC;AACD,qBAAiB,IAAI;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA,EACA,UAAU;AACR,SAAK,UAAU,MAAM;AACnB,WAAK,WAAW;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,SAAK,UAAU,MAAM;AACnB,WAAK,WAAW;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB;AACd,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,iBAAa,KAAK,gBAAgB;AAClC,eAAI,OAAO,KAAK,QAAQ;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,aAAa;AACX,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,KAAK;AAKnB,UAAI,MAAM,eAAe;AACvB,YAAI;AACJ,YAAI,CAAC,KAAK,wBAAwB,KAAK,cAAc,KAAK,KAAK,oBAAoB,IAAI;AACrF,4BAAkB,MAAM,YAAY,KAAK,eAAe,CAAC;AACzD,eAAK,sBAAsB,qBAAiB,iBAAiB,aAAa,KAAK,eAAe;AAAA,QAChG;AAEA,YAAI,CAAC,KAAK,qBAAqB;AAC7B,4BAAkB,mBAAmB,MAAM,YAAY,KAAK,eAAe,CAAC;AAC5E,eAAK,sBAAsB,qBAAiB,iBAAiB,cAAc,KAAK,iBAAiB,0BAAkB;AAAA,YACjH,SAAS;AAAA,UACX,IAAI,KAAK;AAAA,QACX;AAEA,YAAI,CAAC,KAAK,8BAA8B,KAAK,oBAAoB,GAAG;AAClE,4BAAkB,mBAAmB,MAAM,YAAY,KAAK,eAAe,CAAC;AAC5E,eAAK,6BAA6B,qBAAiB,iBAAiB,UAAU,KAAK,kBAAkB;AAAA,QACvG;AAEA,YAAI,CAAC,KAAK,8BAA8B,KAAK,oBAAoB,GAAG;AAClE,eAAK,6BAA6B,qBAAiB,QAAQ,QAAQ,KAAK,kBAAkB;AAAA,QAC5F;AAAA,MACF,OAAO;AACL,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,aAAa,GAAG;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK;AACT,WAAK,WAAW,gBAAgB,CAAC;AACjC,WAAK,qBAAqB,MAAM,iBAAiB,kBAAkB,OAAO,CAAC;AAAA,IAC7E;AAAA,IACA,YAAY,GAAG;AACb,WAAK,WAAW,eAAe,CAAC;AAChC,WAAK,SAAS,CAAC;AAAA,IACjB;AAAA,IACA,aAAa,GAAG;AACd,WAAK,WAAW,gBAAgB,CAAC;AACjC,WAAK,qBAAqB,OAAO,KAAK,OAAO,eAAe;AAAA,IAC9D;AAAA,IACA,oBAAoB;AAClB,YAAM;AAAA,QACJ,mBAAmB,CAAC;AAAA,MACtB,IAAI;AACJ,UAAI,iBAAiB,mBAAmB;AACtC,yBAAiB,kBAAkB;AAAA,MACrC;AACA,WAAK,gBAAgB;AAAA,IACvB;AAAA,IACA,kBAAkB,GAAG;AACnB,UAAI;AACJ,UAAI,KAAK,EAAE,iBAAiB,CAAC,EAAE,cAAc,cAAc,UAAU,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,GAAG,EAAE,aAAa,GAAG;AAC/J;AAAA,MACF;AACA,UAAI,KAAK,mBAAmB,GAAG;AAC7B,aAAK,qBAAqB,OAAO,KAAK,OAAO,eAAe;AAAA,MAC9D;AACA,YAAM;AAAA,QACJ,mBAAmB,CAAC;AAAA,MACtB,IAAI;AACJ,UAAI,iBAAiB,mBAAmB;AACtC,yBAAiB,kBAAkB,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,IACA,QAAQ,GAAG;AACT,WAAK,WAAW,WAAW,CAAC;AAE5B,WAAK,gBAAgB;AACrB,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK,YAAY,KAAK,IAAI;AAC1B,aAAK,qBAAqB,MAAM,KAAK,OAAO,UAAU;AAAA,MACxD;AAAA,IACF;AAAA,IACA,YAAY,GAAG;AACb,WAAK,WAAW,eAAe,CAAC;AAChC,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AAAA,IACA,aAAa,GAAG;AACd,WAAK,WAAW,gBAAgB,CAAC;AACjC,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AAAA,IACA,OAAO,GAAG;AACR,UAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,iBAAiB,SAAS,aAAa,GAAG;AAClE,aAAK,WAAW,UAAU,CAAC;AAC3B,aAAK,gBAAgB;AACrB,YAAI,KAAK,aAAa,GAAG;AACvB,eAAK,qBAAqB,OAAO,KAAK,OAAO,SAAS;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,IACA,cAAc,GAAG;AACf,QAAE,eAAe;AACjB,WAAK,WAAW,iBAAiB,CAAC;AAClC,WAAK,gBAAgB,MAAM,CAAC;AAAA,IAC9B;AAAA,IACA,qBAAqB;AACnB,UAAI,KAAK,oBAAoB,GAAG;AAC9B,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,IACA,QAAQ,OAAO;AACb,WAAK,WAAW,WAAW,KAAK;AAEhC,UAAI,KAAK,WAAW;AAClB,YAAI;AACJ,YAAI,KAAK,gBAAgB,KAAK,cAAc;AAC1C,oBAAU,KAAK,IAAI,KAAK,cAAc,KAAK,YAAY;AAAA,QACzD,WAAW,KAAK,cAAc;AAC5B,oBAAU,KAAK;AAAA,QACjB,WAAW,KAAK,cAAc;AAC5B,oBAAU,KAAK;AAAA,QACjB;AACA,YAAI,KAAK,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI;AAC3C;AAAA,QACF;AACA,aAAK,YAAY;AAAA,MACnB;AACA,WAAK,eAAe;AACpB,WAAK,eAAe;AAIpB,UAAI,KAAK,cAAc,MAAM,KAAK,cAAc,KAAK,KAAK,aAAa,MAAM,SAAS,MAAM,gBAAgB;AAC1G,cAAM,eAAe;AAAA,MACvB;AACA,UAAI,SAAS,MAAM,UAAU;AAC3B,cAAM,SAAS,eAAe;AAAA,MAChC;AACA,YAAM,cAAc,CAAC,KAAK,MAAM;AAChC,UAAI,KAAK,cAAc,KAAK,CAAC,eAAe,eAAe,KAAK,cAAc,GAAG;AAC/E,aAAK,gBAAgB,CAAC,KAAK,MAAM,eAAe,KAAK;AAAA,MACvD;AAAA,IACF;AAAA,IACA,mBAAmB;AACjB,YAAM;AAAA,QACJ,mBAAmB,CAAC;AAAA,MACtB,IAAI;AACJ,WAAK,oBAAoB;AACzB,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB,WAAW,MAAM;AACvC,aAAK,oBAAoB;AAAA,MAC3B,GAAG,CAAC;AACJ,UAAI,iBAAiB,kBAAkB;AACrC,yBAAiB,iBAAiB,GAAG,SAAS;AAAA,MAChD;AAAA,IACF;AAAA,IACA,gBAAgB,OAAO;AACrB,UAAI,KAAK,OAAO,QAAQ,CAAC,KAAK,OAAO,cAAc;AACjD;AAAA,MACF;AACA,YAAM,SAAS,MAAM;AACrB,YAAM,OAAO,KAAK,eAAe;AACjC,YAAM,YAAY,KAAK,gBAAgB;AACvC;AAAA;AAAA;AAAA,SAGC,CAAC,SAAS,MAAM,MAAM,KAAK,KAAK,kBAAkB,MAAM,CAAC,SAAS,WAAW,MAAM,KAAK,CAAC,KAAK;AAAA,QAAmB;AAIhH,aAAK,qBAAqB,OAAO,GAAG;AAAA,MACtC;AAAA,IACF;AAAA,IACA,kBAAkB;AAChB,UAAI;AAEJ,eAAS,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,MAAM;AAAA,IACxF;AAAA,IACA,iBAAiB;AACf,UAAI,IAAI,IAAI,IAAI;AAChB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK;AACT,UAAI,mBAAmB;AACrB,cAAM,YAAY,MAAM,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,aAAa,OAAO,YAAY,KAAK,UAAU;AAChM,eAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,MAC/C;AACA,UAAI;AACF,cAAM,YAAY,MAAM,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,aAAa,OAAO,YAAY,KAAK,UAAU;AAChM,YAAI,SAAS;AACX,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,KAAK;AAAA,MAEd;AACA,aAAO,YAAY,IAAI;AAAA,IACzB;AAAA,IACA,6BAA6B,OAAO;AAClC,YAAM,YAAY,CAAC;AACnB,YAAM,QAAQ,KAAK;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,kBAAkB,mBAAmB;AACvC,kBAAU,KAAK,uBAAuB,mBAAmB,WAAW,OAAOA,WAAU,CAAC;AAAA,MACxF;AACA,UAAI,4BAA4B;AAC9B,kBAAU,KAAK,2BAA2B,KAAK,CAAC;AAAA,MAClD;AACA,aAAO,UAAU,KAAK,GAAG;AAAA,IAC3B;AAAA,IACA,gBAAgB;AACd,YAAM,QAAQ,KAAK;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,kBAAkB,mBAAmB;AACvC,eAAO,sBAAsB,mBAAmB,gBAAgB,UAAU;AAAA,MAC5E;AACA,aAAO;AAAA,IACT;AAAA,IACA,eAAe;AACb,YAAM,aAAa,CAAC;AACpB,UAAI,KAAK,mBAAmB,GAAG;AAC7B,mBAAW,eAAe,KAAK;AAAA,MACjC;AACA,UAAI,KAAK,mBAAmB,GAAG;AAC7B,mBAAW,eAAe,KAAK;AAAA,MACjC;AACA,iBAAW,cAAc,KAAK;AAC9B,iBAAW,0BAAkB,wBAAwB,cAAc,IAAI,KAAK;AAC5E,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAMC,cAAa,SAAS,SAAS;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,OAAOD,cAAa,QAAQ;AAAA,QAC5B,OAAO,KAAK;AAAA,QACZ,WAAW;AAAA,QACX,uBAAuB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,OAAO;AAAA,QACP,SAAS,OAAO,gBAAgB;AAAA,MAClC,GAAG,UAAU,GAAG;AAAA,QACd,KAAK,KAAK;AAAA,QACV;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,YAAa,eAAOC,aAAY;AAAA,QACrC,SAAS,KAAK,OAAO,UAAU,MAAM,aAAa,MAAM,OAAO;AAAA,MACjE,CAAC;AAAA,IACH;AAAA,IACA,aAAa,gBAAgB;AAC3B,iBAAI,OAAO,KAAK,QAAQ;AACxB,YAAM;AAAA,QACJ;AAAA,QACA,aAAAC;AAAA,MACF,IAAI,KAAK;AACT,YAAM,UAAU,KAAK,eAAe;AACpC,UAAI;AACJ,UAAI,CAAC,mBAAmB;AACtB,oBAAYA,aAAY,KAAK,eAAe,CAAC,EAAE;AAAA,MACjD,WAAW,WAAW,kBAAkB,WAAW,GAAG;AAIpD,oBAAY,kBAAkB,OAAO;AAAA,MACvC;AACA,UAAI,WAAW;AACb,kBAAU,YAAY,cAAc;AAAA,MACtC,OAAO;AAEL,aAAK,WAAW,WAAI,MAAM;AACxB,eAAK,aAAa,cAAc;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,eAAe;AACb,YAAM;AAAA,QACJ,QAAQ;AAAA,MACV,IAAI;AACJ,YAAM;AAAA,QACJ,aAAAA;AAAA,MACF,IAAI;AACJ,YAAM,iBAAiBA,aAAY,KAAK,eAAe,CAAC,EAAE,cAAc,KAAK;AAG7E,qBAAe,MAAM,WAAW;AAChC,qBAAe,MAAM,MAAM;AAC3B,qBAAe,MAAM,OAAO;AAC5B,qBAAe,MAAM,QAAQ;AAC7B,WAAK,aAAa,cAAc;AAChC,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,eAAe,OAAO;AACpC,YAAM;AAAA,QACJ,YAAAF;AAAA,QACA,eAAe;AAAA,QACf;AAAA,MACF,IAAI;AACJ,WAAK,gBAAgB;AACrB,UAAI,qBAAqB,eAAe;AACtC,YAAI,CAAC,QAAQ,MAAM,cAAc,GAAG;AAClC,eAAK,SAAS;AAAA,YACZ;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AACA,gCAAwB,qBAAqB,aAAa;AAAA,MAC5D;AAEA,UAAIA,eAAc,SAAS,eAAe;AACxC,aAAK,SAAS,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,IACA,SAAS,OAAO;AACd,YAAM;AAAA,QACJ,YAAAA;AAAA,MACF,IAAI,KAAK;AACT,UAAI,CAACA,eAAc,CAAC,MAAO;AAC3B,WAAK,SAAS;AAAA,QACZ,OAAO;AAAA,UACL,OAAO,MAAM;AAAA,UACb,OAAO,MAAM;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB;AACnB,UAAI,KAAK,qBAAqB,KAAK,eAAe;AAChD,aAAK,wBAAwB,KAAK,aAAa;AAAA,MACjD;AAAA,IACF;AAAA,IACA,qBAAqB,SAAS,QAAQ,OAAO;AAC3C,YAAM,QAAQ,SAAS;AACvB,WAAK,gBAAgB;AACrB,UAAI,OAAO;AACT,cAAM,QAAQ,QAAQ;AAAA,UACpB,OAAO,MAAM;AAAA,UACb,OAAO,MAAM;AAAA,QACf,IAAI;AACJ,aAAK,aAAa,WAAW,MAAM;AACjC,eAAK,gBAAgB,SAAS,KAAK;AACnC,eAAK,gBAAgB;AAAA,QACvB,GAAG,KAAK;AAAA,MACV,OAAO;AACL,aAAK,gBAAgB,SAAS,KAAK;AAAA,MACrC;AAAA,IACF;AAAA,IACA,kBAAkB;AAChB,UAAI,KAAK,YAAY;AACnB,qBAAa,KAAK,UAAU;AAC5B,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,IACA,sBAAsB;AACpB,UAAI,KAAK,qBAAqB;AAC5B,aAAK,oBAAoB,OAAO;AAChC,aAAK,sBAAsB;AAAA,MAC7B;AACA,UAAI,KAAK,4BAA4B;AACnC,aAAK,2BAA2B,OAAO;AACvC,aAAK,6BAA6B;AAAA,MACpC;AACA,UAAI,KAAK,4BAA4B;AACnC,aAAK,2BAA2B,OAAO;AACvC,aAAK,6BAA6B;AAAA,MACpC;AACA,UAAI,KAAK,qBAAqB;AAC5B,aAAK,oBAAoB,OAAO;AAChC,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,gBAAgB,OAAO;AACrB,UAAI,KAAK,MAAM;AAAA,MAAC;AAChB,YAAM,SAAS,UAAU,IAAI;AAC7B,UAAI,KAAK,kBAAkB,KAAK,KAAK,OAAO,KAAK,GAAG;AAClD,eAAO,KAAK,OAAO,KAAK,EAAE;AAAA,MAC5B;AACA,WAAK,KAAK,kBAAkB,KAAK,KAAK,OAAO,KAAK,KAAK;AACvD,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,QAAQ,OAAO,MAAM;AAAA,IAC3E;AAAA,IACA,oBAAoB;AAClB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK;AACT,aAAO,WAAW,iBAAiB,OAAO,WAAW,KAAK,OAAO,CAAC,MAAM;AAAA,IAC1E;AAAA,IACA,sBAAsB;AACpB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,aAAa,MAAM,MAAM,WAAW,QAAQ,aAAa,MAAM;AAAA,IACvF;AAAA,IACA,gBAAgB;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,QAAQ,OAAO,MAAM;AAAA,IAC3E;AAAA,IACA,qBAAqB;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,QAAQ,YAAY,MAAM;AAAA,IAChF;AAAA,IACA,qBAAqB;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,QAAQ,YAAY,MAAM;AAAA,IAChF;AAAA,IACA,gBAAgB;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,QAAQ,OAAO,MAAM;AAAA,IAC3E;AAAA,IACA,eAAe;AACb,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,aAAO,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,QAAQ,MAAM,MAAM;AAAA,IAC1E;AAAA,IACA,kBAAkB;AAChB,UAAI;AACJ,UAAI,KAAK,MAAM,eAAe;AAC5B,SAAC,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,WAAW,MAAM,GAAG;AAClB,UAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAK,kBAAkB,IAAI,EAAE,CAAC;AAAA,MAChC;AACA,YAAM,QAAQ,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI;AACnD,UAAI,OAAO;AACT,cAAM,CAAC;AAAA,MACT;AAAA,IACF;AAAA,IACA,QAAQ;AACN,WAAK,gBAAgB,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,YAAY,QAAQ,IAAI,CAAC;AAC1C,UAAM;AAAA,MACJ,YAAAA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,QAAQ,SAAS,CAAC;AACxB,SAAK,oBAAoB,UAAU,KAAK;AACxC,UAAM,gBAAgB;AAAA,MACpB,KAAK;AAAA,IACP;AACA,QAAI,KAAK,oBAAoB,GAAG;AAC9B,oBAAc,gBAAgB,KAAK;AAAA,IACrC,OAAO;AACL,oBAAc,gBAAgB,KAAK,gBAAgB,eAAe;AAAA,IACpE;AACA,QAAI,KAAK,cAAc,KAAK,KAAK,cAAc,GAAG;AAChD,oBAAc,UAAU,KAAK;AAC7B,oBAAc,cAAc,KAAK;AACjC,oBAAc,0BAAkB,wBAAwB,cAAc,IAAI,KAAK;AAAA,IACjF,OAAO;AACL,oBAAc,UAAU,KAAK,gBAAgB,SAAS;AACtD,oBAAc,cAAc,KAAK,gBAAgB,aAAa;AAC9D,oBAAc,0BAAkB,wBAAwB,cAAc,IAAI,KAAK,gBAAgB,cAAc;AAAA,IAC/G;AACA,QAAI,KAAK,mBAAmB,GAAG;AAC7B,oBAAc,eAAe,KAAK;AAClC,UAAIA,aAAY;AACd,sBAAc,cAAc,KAAK;AAAA,MACnC;AAAA,IACF,OAAO;AACL,oBAAc,eAAe,KAAK,gBAAgB,cAAc;AAAA,IAClE;AACA,QAAI,KAAK,mBAAmB,GAAG;AAC7B,oBAAc,eAAe,KAAK;AAAA,IACpC,OAAO;AACL,oBAAc,eAAe,KAAK,gBAAgB,cAAc;AAAA,IAClE;AACA,QAAI,KAAK,cAAc,KAAK,KAAK,aAAa,GAAG;AAC/C,oBAAc,UAAU,KAAK;AAC7B,oBAAc,SAAS,KAAK;AAAA,IAC9B,OAAO;AACL,oBAAc,UAAU,KAAK,gBAAgB,SAAS;AACtD,oBAAc,SAAS,OAAK;AAC1B,YAAI,MAAM,CAAC,EAAE,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,aAAa,IAAI;AACnE,eAAK,gBAAgB,QAAQ,EAAE,CAAC;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,UAAM,oBAAoB,mBAAW,SAAS,MAAM,SAAS,MAAM,MAAM,OAAO,OAAO,KAAK;AAC5F,QAAI,mBAAmB;AACrB,oBAAc,QAAQ;AAAA,IACxB;AACA,UAAM,UAAU,aAAa,OAAO,SAAS,SAAS,CAAC,GAAG,aAAa,GAAG;AAAA,MACxE,KAAK;AAAA,IACP,CAAC,GAAG,MAAM,IAAI;AACd,UAAM,SAAS,YAAa,uBAAQ;AAAA,MAClC,OAAO;AAAA,MACP,gBAAgB,sBAAsB,MAAM,kBAAkB,KAAK,eAAe,CAAC;AAAA,MACnF,aAAa,KAAK;AAAA,MAClB,WAAW,KAAK,MAAM;AAAA,IACxB,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,WAAO,YAAa,UAAW,MAAM,CAAC,SAAS,MAAM,CAAC;AAAA,EACxD;AACF,CAAC;;;ACjqBD,IAAO,qBAAQ;;;ACHf,IAAM,mBAAmB,eAAa;AAAA,EACpC,mBAAmB;AAAA,EACnB,mBAAmB;AACrB;AAEA,IAAM,wBAAwB,eAAa;AAAA,EACzC,mBAAmB;AAAA,EACnB,mBAAmB;AACrB;AACO,IAAM,aAAa,SAAU,WAAW,aAAa,cAAc,UAAU;AAClF,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACpF,QAAM,kBAAkB,YAAY,MAAM;AAC1C,SAAO;AAAA,IACL,CAAC;AAAA,QACG,eAAe,GAAG,SAAS;AAAA,QAC3B,eAAe,GAAG,SAAS;AAAA,KAC9B,GAAG,SAAS,SAAS,CAAC,GAAG,iBAAiB,QAAQ,CAAC,GAAG;AAAA,MACrD,oBAAoB;AAAA,IACtB,CAAC;AAAA,IACD,CAAC,GAAG,eAAe,GAAG,SAAS,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,sBAAsB,QAAQ,CAAC,GAAG;AAAA,MAChG,oBAAoB;AAAA,IACtB,CAAC;AAAA,IACD,CAAC;AAAA,QACG,eAAe,GAAG,SAAS,SAAS,SAAS;AAAA,QAC7C,eAAe,GAAG,SAAS,UAAU,SAAS;AAAA,KACjD,GAAG;AAAA,MACF,eAAe;AAAA,MACf,oBAAoB;AAAA,IACtB;AAAA,IACA,CAAC,GAAG,eAAe,GAAG,SAAS,SAAS,SAAS,eAAe,GAAG;AAAA,MACjE,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,eAAe;AAAA,IACjB;AAAA,EACF;AACF;;;AClCO,IAAM,SAAS,IAAI,kBAAU,aAAa;AAAA,EAC/C,MAAM;AAAA,IACJ,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,UAAU,IAAI,kBAAU,cAAc;AAAA,EACjD,MAAM;AAAA,IACJ,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,iBAAiB,SAAU,OAAO;AAC7C,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACpF,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,GAAG,MAAM;AAC3B,QAAM,kBAAkB,YAAY,MAAM;AAC1C,SAAO,CAAC,WAAW,WAAW,QAAQ,SAAS,MAAM,mBAAmB,SAAS,GAAG;AAAA,IAClF,CAAC;AAAA,UACK,eAAe,GAAG,SAAS;AAAA,UAC3B,eAAe,GAAG,SAAS;AAAA,OAC9B,GAAG;AAAA,MACJ,SAAS;AAAA,MACT,yBAAyB;AAAA,IAC3B;AAAA,IACA,CAAC,GAAG,eAAe,GAAG,SAAS,QAAQ,GAAG;AAAA,MACxC,yBAAyB;AAAA,IAC3B;AAAA,EACF,CAAC;AACH;;;ACnCO,IAAM,aAAa,IAAI,kBAAU,iBAAiB;AAAA,EACvD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,aAAa,IAAI,kBAAU,iBAAiB;AAAA,EACvD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,eAAe,IAAI,kBAAU,mBAAmB;AAAA,EAC3D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,WAAW,IAAI,kBAAU,eAAe;AAAA,EACnD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,YAAY,IAAI,kBAAU,gBAAgB;AAAA,EACrD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACD,IAAM,aAAa;AAAA,EACjB,WAAW;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AACF;AACO,IAAM,iBAAiB,CAAC,OAAO,eAAe;AACnD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,GAAG,MAAM,IAAI,UAAU;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,WAAW,UAAU;AACzB,SAAO,CAAC,WAAW,WAAW,aAAa,cAAc,MAAM,iBAAiB,GAAG;AAAA,IACjF,CAAC;AAAA,UACK,SAAS;AAAA,UACT,SAAS;AAAA,OACZ,GAAG;AAAA,MACJ,SAAS;AAAA,MACT,yBAAyB,MAAM;AAAA,IACjC;AAAA,IACA,CAAC,GAAG,SAAS,QAAQ,GAAG;AAAA,MACtB,yBAAyB,MAAM;AAAA,IACjC;AAAA,EACF,CAAC;AACH;;;ACvIO,IAAM,YAAY,IAAI,kBAAU,gBAAgB;AAAA,EACrD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,aAAa,IAAI,kBAAU,iBAAiB;AAAA,EACvD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,eAAe,IAAI,kBAAU,mBAAmB;AAAA,EAC3D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,eAAe,IAAI,kBAAU,mBAAmB;AAAA,EAC3D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,eAAe,IAAI,kBAAU,mBAAmB;AAAA,EAC3D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,gBAAgB,IAAI,kBAAU,oBAAoB;AAAA,EAC7D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACD,IAAM,cAAc;AAAA,EAClB,YAAY;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,eAAe;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AACF;AACO,IAAM,kBAAkB,CAAC,OAAO,eAAe;AACpD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,GAAG,MAAM,IAAI,UAAU;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,YAAY,UAAU;AAC1B,SAAO,CAAC,WAAW,WAAW,aAAa,cAAc,MAAM,iBAAiB,GAAG;AAAA,IACjF,CAAC;AAAA,QACG,SAAS;AAAA,QACT,SAAS;AAAA,KACZ,GAAG;AAAA,MACF,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,yBAAyB,MAAM;AAAA,IACjC;AAAA,IACA,CAAC,GAAG,SAAS,QAAQ,GAAG;AAAA,MACtB,yBAAyB,MAAM;AAAA,IACjC;AAAA,EACF,CAAC;AACH;;;ACzIO,IAAM,SAAS,IAAI,kBAAU,aAAa;AAAA,EAC/C,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,UAAU,IAAI,kBAAU,cAAc;AAAA,EACjD,MAAM;AAAA,IACJ,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,YAAY,IAAI,kBAAU,gBAAgB;AAAA,EACrD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,aAAa,IAAI,kBAAU,iBAAiB;AAAA,EACvD,MAAM;AAAA,IACJ,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,WAAW,IAAI,kBAAU,eAAe;AAAA,EACnD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AACF,CAAC;AACM,IAAM,YAAY,IAAI,kBAAU,gBAAgB;AAAA,EACrD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,aAAa,IAAI,kBAAU,iBAAiB;AAAA,EACvD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AACF,CAAC;AACM,IAAM,eAAe,IAAI,kBAAU,mBAAmB;AAAA,EAC3D,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACM,IAAM,aAAa,IAAI,kBAAU,iBAAiB;AAAA,EACvD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AACF,CAAC;AACM,IAAM,cAAc,IAAI,kBAAU,kBAAkB;AAAA,EACzD,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AACD,IAAM,aAAa;AAAA,EACjB,MAAM;AAAA,IACJ,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,iBAAiB;AAAA,IACf,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,WAAW;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AACF;AACO,IAAM,iBAAiB,CAAC,OAAO,eAAe;AACnD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,GAAG,MAAM,IAAI,UAAU;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,WAAW,UAAU;AACzB,SAAO,CAAC,WAAW,WAAW,aAAa,cAAc,eAAe,kBAAkB,MAAM,qBAAqB,MAAM,iBAAiB,GAAG;AAAA,IAC7I,CAAC;AAAA,UACK,SAAS;AAAA,UACT,SAAS;AAAA,OACZ,GAAG;AAAA,MACJ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,yBAAyB,MAAM;AAAA,MAC/B,aAAa;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,CAAC,GAAG,SAAS,QAAQ,GAAG;AAAA,MACtB,yBAAyB,MAAM;AAAA,IACjC;AAAA,EACF,CAAC;AACH;;;ACvLA,IAAM,oBAAoB,YAAU;AAAA,EAClC,CAAC,MAAM,YAAY,GAAG;AAAA;AAAA,IAEpB,CAAC,GAAG,MAAM,MAAM,yBAAyB,GAAG;AAAA,MAC1C,UAAU;AAAA,MACV,YAAY;AAAA,QACV,YAAY,UAAU,MAAM,iBAAiB,IAAI,MAAM,eAAe;AAAA,kBAC5D,MAAM,iBAAiB,IAAI,MAAM,eAAe;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,CAAC,GAAG,MAAM,MAAM,kBAAkB,GAAG;AAAA,MACnC,UAAU;AAAA,MACV,YAAY,UAAU,MAAM,iBAAiB,IAAI,MAAM,eAAe;AAAA,kBAC1D,MAAM,iBAAiB,IAAI,MAAM,eAAe;AAAA,IAC9D;AAAA,EACF;AACF;AACA,IAAO,mBAAQ;", "names": ["vendorPrefix", "jsCssMap", "Webkit", "<PERSON><PERSON>", "ms", "O", "getVendorPrefix", "undefined", "style", "document", "createElement", "testProp", "key", "getTransitionName", "getTransformName", "setTransitionProperty", "node", "value", "name", "transitionProperty", "setTransform", "transform", "getTransitionProperty", "getTransformXY", "window", "getComputedStyle", "getPropertyValue", "matrix", "replace", "split", "x", "parseFloat", "y", "matrix2d", "matrix3d", "setTransformXY", "xy", "arr", "match2d", "match", "map", "item", "join", "match3d", "RE_NUM", "source", "getComputedStyleX", "forceRelayout", "elem", "originalStyle", "display", "offsetHeight", "css", "el", "v", "i", "hasOwnProperty", "getClientPosition", "box", "doc", "ownerDocument", "body", "doc<PERSON><PERSON>", "documentElement", "getBoundingClientRect", "Math", "floor", "left", "top", "clientLeft", "clientTop", "getScroll", "w", "ret", "method", "d", "getScrollLeft", "getScrollTop", "getOffset", "pos", "defaultView", "parentWindow", "isWindow", "obj", "getDocument", "nodeType", "_getComputedStyle", "cs", "computedStyle", "val", "_RE_NUM_NO_PX", "RegExp", "RE_POS", "CURRENT_STYLE", "RUNTIME_STYLE", "LEFT", "PX", "_getComputedStyleIE", "test", "rsLeft", "pixelLeft", "getOffsetDirection", "dir", "option", "useCssRight", "useCssBottom", "oppositeOffsetDirection", "setLeftTop", "offset", "position", "presetH", "presetV", "horizontalProperty", "verticalProperty", "oppositeHorizontalProperty", "oppositeVerticalProperty", "originalTransition", "originalOffset", "old", "preset", "off", "originalXY", "resultXY", "setOffset", "ignoreShake", "oriOffset", "oLeft", "toFixed", "oTop", "tLeft", "tTop", "useCssTransform", "each", "fn", "length", "isBorderBoxFn", "BOX_MODELS", "CONTENT_INDEX", "PADDING_INDEX", "BORDER_INDEX", "MARGIN_INDEX", "swap", "options", "callback", "call", "getPBMWidth", "props", "which", "prop", "j", "cssProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getParent", "element", "parent", "host", "parentNode", "refWin", "max", "win", "documentElementProp", "compatMode", "getWH", "ex", "extra", "viewportWidth", "viewportHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "borderBoxValue", "width", "height", "isBorderBox", "cssBoxValue", "Number", "borderBoxValueOrIsBorderBox", "slice", "cssShow", "visibility", "getWHIgnoreDisplay", "args", "offsetWidth", "apply", "first", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "mix", "to", "from", "utils", "getWindow", "setTimeout", "clone", "overflow", "getWindowScrollLeft", "getWindowScrollTop", "merge", "getOffsetParent", "positionStyle", "skipStatic", "nodeName", "toLowerCase", "isAncestorFixed", "getVisibleRectForElement", "alwaysByViewport", "visibleRect", "right", "Infinity", "bottom", "navigator", "userAgent", "indexOf", "clientWidth", "min", "clientHeight", "originalPosition", "scrollX", "scrollY", "documentWidth", "scrollWidth", "documentHeight", "scrollHeight", "bodyStyle", "overflowX", "innerWidth", "overflowY", "innerHeight", "maxVisibleWidth", "maxVisibleHeight", "adjustForViewport", "elFuturePos", "elRegion", "size", "adjustX", "resizeWidth", "adjustY", "resizeHeight", "getRegion", "h", "outerWidth", "outerHeight", "getAlignOffset", "region", "align", "V", "H", "getElFuturePos", "refNodeRegion", "points", "targetOffset", "p1", "p2", "diff", "round", "isFailX", "isFailY", "isCompleteFailX", "isCompleteFailY", "flip", "reg", "p", "push", "m", "flipOffset", "index", "convertOffset", "str", "offsetLen", "n", "parseInt", "substring", "normalizeOffset", "doAlign", "tgtRegion", "isTgtRegionVisible", "concat", "newOverflowCfg", "fail", "newElRegion", "newPoints", "l", "r", "newOffset", "newTargetOffset", "newElFuturePos", "t", "b", "isStillFailX", "isStillFailY", "isOutOfVisibleRect", "target", "targetRegion", "alignElement", "refNode", "isTargetNotOutOfVisible", "__getOffsetParent", "__getVisibleRectForElement", "alignPoint", "tgtPoint", "pageX", "pageY", "clientX", "clientY", "pointInView", "_objectSpread2", "_a", "getParent", "getContainer", "alignPoint", "popupProps", "getDocument"]}