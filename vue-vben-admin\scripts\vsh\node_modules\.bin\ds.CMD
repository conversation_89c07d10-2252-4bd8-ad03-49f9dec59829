@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\circular-dependency-scanner@2.3.0\node_modules\circular-dependency-scanner\dist\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\circular-dependency-scanner@2.3.0\node_modules\circular-dependency-scanner\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\circular-dependency-scanner@2.3.0\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\circular-dependency-scanner@2.3.0\node_modules\circular-dependency-scanner\dist\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\circular-dependency-scanner@2.3.0\node_modules\circular-dependency-scanner\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\circular-dependency-scanner@2.3.0\node_modules;C:\Users\<USER>\Downloads\Acumatica Dev\Cluade\Envent Bridge New\vue-vben-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\circular-dependency-scanner\dist\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\circular-dependency-scanner\dist\cli.js" %*
)
