{"$schema": "https://json.schemastore.org/tsconfig", "display": "Base", "compilerOptions": {"composite": false, "target": "ESNext", "moduleDetection": "force", "experimentalDecorators": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "strictNullChecks": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitThis": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "inlineSources": false, "noEmit": true, "removeComments": true, "sourceMap": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true, "preserveWatchOutput": true}, "exclude": ["**/node_modules/**", "**/dist/**", "**/.turbo/**"]}